{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\tool\\build\\TreeNodeDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\tool\\build\\TreeNodeDialog.vue", "mtime": 1749104047651}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["TreeNodeDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "TreeNodeDialog.vue", "sourceRoot": "src/views/tool/build", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog\r\n      v-bind=\"$attrs\"\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\"\r\n      v-on=\"$listeners\"\r\n      @open=\"onOpen\"\r\n      @close=\"onClose\"\r\n    >\r\n      <el-row :gutter=\"0\">\r\n        <el-form\r\n          ref=\"elForm\"\r\n          :model=\"formData\"\r\n          :rules=\"rules\"\r\n          size=\"small\"\r\n          label-width=\"100px\"\r\n        >\r\n          <el-col :span=\"24\">\r\n            <el-form-item\r\n              label=\"选项名\"\r\n              prop=\"label\"\r\n            >\r\n              <el-input\r\n                v-model=\"formData.label\"\r\n                placeholder=\"请输入选项名\"\r\n                clearable\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item\r\n              label=\"选项值\"\r\n              prop=\"value\"\r\n            >\r\n              <el-input\r\n                v-model=\"formData.value\"\r\n                placeholder=\"请输入选项值\"\r\n                clearable\r\n              >\r\n                <el-select\r\n                  slot=\"append\"\r\n                  v-model=\"dataType\"\r\n                  :style=\"{width: '100px'}\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"(item, index) in dataTypeOptions\"\r\n                    :key=\"index\"\r\n                    :label=\"item.label\"\r\n                    :value=\"item.value\"\r\n                    :disabled=\"item.disabled\"\r\n                  />\r\n                </el-select>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-form>\r\n      </el-row>\r\n      <div slot=\"footer\">\r\n        <el-button\r\n          type=\"primary\"\r\n          @click=\"handleConfirm\"\r\n        >\r\n          确定\r\n        </el-button>\r\n        <el-button @click=\"close\">\r\n          取消\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { isNumberStr } from '@/utils/index'\r\n\r\nexport default {\r\n  components: {},\r\n  inheritAttrs: false,\r\n  props: [],\r\n  data() {\r\n    return {\r\n      id: 100,\r\n      formData: {\r\n        label: undefined,\r\n        value: undefined\r\n      },\r\n      rules: {\r\n        label: [\r\n          {\r\n            required: true,\r\n            message: '请输入选项名',\r\n            trigger: 'blur'\r\n          }\r\n        ],\r\n        value: [\r\n          {\r\n            required: true,\r\n            message: '请输入选项值',\r\n            trigger: 'blur'\r\n          }\r\n        ]\r\n      },\r\n      dataType: 'string',\r\n      dataTypeOptions: [\r\n        {\r\n          label: '字符串',\r\n          value: 'string'\r\n        },\r\n        {\r\n          label: '数字',\r\n          value: 'number'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {},\r\n  watch: {\r\n    // eslint-disable-next-line func-names\r\n    'formData.value': function (val) {\r\n      this.dataType = isNumberStr(val) ? 'number' : 'string'\r\n    }\r\n  },\r\n  created() {},\r\n  mounted() {},\r\n  methods: {\r\n    onOpen() {\r\n      this.formData = {\r\n        label: undefined,\r\n        value: undefined\r\n      }\r\n    },\r\n    onClose() {},\r\n    close() {\r\n      this.$emit('update:visible', false)\r\n    },\r\n    handleConfirm() {\r\n      this.$refs.elForm.validate(valid => {\r\n        if (!valid) return\r\n        if (this.dataType === 'number') {\r\n          this.formData.value = parseFloat(this.formData.value)\r\n        }\r\n        this.formData.id = this.id++\r\n        this.$emit('commit', this.formData)\r\n        this.close()\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}