{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\system\\role\\selectUser.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\system\\role\\selectUser.vue", "mtime": 1749104047647}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["selectUser.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "selectUser.vue", "sourceRoot": "src/views/system/role", "sourcesContent": ["<template>\r\n  <!-- 授权用户 -->\r\n  <el-dialog title=\"选择用户\" :visible.sync=\"visible\" width=\"800px\" top=\"5vh\" append-to-body>\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\">\r\n      <el-form-item label=\"用户名称\" prop=\"userName\">\r\n        <el-input\r\n          v-model=\"queryParams.userName\"\r\n          placeholder=\"请输入用户名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"手机号码\" prop=\"phonenumber\">\r\n        <el-input\r\n          v-model=\"queryParams.phonenumber\"\r\n          placeholder=\"请输入手机号码\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n    <el-row>\r\n      <el-table @row-click=\"clickRow\" ref=\"table\" :data=\"userList\" @selection-change=\"handleSelectionChange\" height=\"260px\">\r\n        <el-table-column type=\"selection\" width=\"55\"></el-table-column>\r\n        <el-table-column label=\"用户名称\" prop=\"userName\" :show-overflow-tooltip=\"true\" />\r\n        <el-table-column label=\"用户昵称\" prop=\"nickName\" :show-overflow-tooltip=\"true\" />\r\n        <el-table-column label=\"邮箱\" prop=\"email\" :show-overflow-tooltip=\"true\" />\r\n        <el-table-column label=\"手机\" prop=\"phonenumber\" :show-overflow-tooltip=\"true\" />\r\n        <el-table-column label=\"状态\" align=\"center\" prop=\"status\">\r\n          <template slot-scope=\"scope\">\r\n            <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.createTime) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <pagination\r\n        v-show=\"total>0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\"\r\n      />\r\n    </el-row>\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button type=\"primary\" @click=\"handleSelectUser\">确 定</el-button>\r\n      <el-button @click=\"visible = false\">取 消</el-button>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport { unallocatedUserList, authUserSelectAll } from \"@/api/system/role\";\r\nexport default {\r\n  dicts: ['sys_normal_disable'],\r\n  props: {\r\n    // 角色编号\r\n    roleId: {\r\n      type: [Number, String]\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      visible: false,\r\n      // 选中数组值\r\n      userIds: [],\r\n      // 总条数\r\n      total: 0,\r\n      // 未授权用户数据\r\n      userList: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        roleId: undefined,\r\n        userName: undefined,\r\n        phonenumber: undefined\r\n      }\r\n    };\r\n  },\r\n  methods: {\r\n    // 显示弹框\r\n    show() {\r\n      this.queryParams.roleId = this.roleId;\r\n      this.getList();\r\n      this.visible = true;\r\n    },\r\n    clickRow(row) {\r\n      this.$refs.table.toggleRowSelection(row);\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.userIds = selection.map(item => item.userId);\r\n    },\r\n    // 查询表数据\r\n    getList() {\r\n      unallocatedUserList(this.queryParams).then(res => {\r\n        this.userList = res.rows;\r\n        this.total = res.total;\r\n      });\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 选择授权用户操作 */\r\n    handleSelectUser() {\r\n      const roleId = this.queryParams.roleId;\r\n      const userIds = this.userIds.join(\",\");\r\n      if (userIds == \"\") {\r\n        this.$modal.msgError(\"请选择要分配的用户\");\r\n        return;\r\n      }\r\n      authUserSelectAll({ roleId: roleId, userIds: userIds }).then(res => {\r\n        this.$modal.msgSuccess(res.msg);\r\n        this.visible = false;\r\n        this.$emit(\"ok\");\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}