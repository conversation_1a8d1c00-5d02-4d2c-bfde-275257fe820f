{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\strike.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\strike.js", "mtime": 1749104422670}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfY3JlYXRlQ2xhc3MyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvY3JlYXRlQ2xhc3MuanMiKSk7CnZhciBfY2xhc3NDYWxsQ2hlY2syID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvY2xhc3NDYWxsQ2hlY2suanMiKSk7CnZhciBfY2FsbFN1cGVyMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovdGhpbmt0YW5rL3RoaW5rdGFua3VpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2NhbGxTdXBlci5qcyIpKTsKdmFyIF9pbmhlcml0czIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkQ6L3RoaW5rdGFuay90aGlua3Rhbmt1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbmhlcml0cy5qcyIpKTsKdmFyIF9kZWZpbmVQcm9wZXJ0eTIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkQ6L3RoaW5rdGFuay90aGlua3Rhbmt1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9kZWZpbmVQcm9wZXJ0eS5qcyIpKTsKdmFyIF9ib2xkID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL2JvbGQuanMiKSk7CnZhciBTdHJpa2UgPSAvKiNfX1BVUkVfXyovZnVuY3Rpb24gKF9Cb2xkKSB7CiAgZnVuY3Rpb24gU3RyaWtlKCkgewogICAgKDAsIF9jbGFzc0NhbGxDaGVjazIuZGVmYXVsdCkodGhpcywgU3RyaWtlKTsKICAgIHJldHVybiAoMCwgX2NhbGxTdXBlcjIuZGVmYXVsdCkodGhpcywgU3RyaWtlLCBhcmd1bWVudHMpOwogIH0KICAoMCwgX2luaGVyaXRzMi5kZWZhdWx0KShTdHJpa2UsIF9Cb2xkKTsKICByZXR1cm4gKDAsIF9jcmVhdGVDbGFzczIuZGVmYXVsdCkoU3RyaWtlKTsKfShfYm9sZC5kZWZhdWx0KTsKKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoU3RyaWtlLCAiYmxvdE5hbWUiLCAnc3RyaWtlJyk7CigwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKFN0cmlrZSwgInRhZ05hbWUiLCBbJ1MnLCAnU1RSSUtFJ10pOwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSBTdHJpa2U7"}, {"version": 3, "names": ["_bold", "_interopRequireDefault", "require", "Strike", "_Bold", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "Bold", "_defineProperty2", "_default", "exports"], "sources": ["../../src/formats/strike.ts"], "sourcesContent": ["import Bold from './bold.js';\n\nclass Strike extends Bold {\n  static blotName = 'strike';\n  static tagName = ['S', 'STRIKE'];\n}\n\nexport default Strike;\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA4B,IAEtBC,MAAM,0BAAAC,KAAA;EAAA,SAAAD,OAAA;IAAA,IAAAE,gBAAA,CAAAC,OAAA,QAAAH,MAAA;IAAA,WAAAI,WAAA,CAAAD,OAAA,QAAAH,MAAA,EAAAK,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAH,MAAA,EAAAC,KAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAH,MAAA;AAAA,EAASQ,aAAI;AAAA,IAAAC,gBAAA,CAAAN,OAAA,EAAnBH,MAAM,cACQ,QAAQ;AAAA,IAAAS,gBAAA,CAAAN,OAAA,EADtBH,MAAM,aAEO,CAAC,GAAG,EAAE,QAAQ,CAAC;AAAA,IAAAU,QAAA,GAAAC,OAAA,CAAAR,OAAA,GAGnBH,MAAM", "ignoreList": []}]}