{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\api\\system\\config.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\api\\system\\config.js", "mtime": 1749104047591}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749104418875}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listConfig", "query", "request", "url", "method", "params", "getConfig", "configId", "getConfigKey", "config<PERSON><PERSON>", "addConfig", "data", "updateConfig", "delConfig", "refreshCache"], "sources": ["D:/thinktank/thinktankui/src/api/system/config.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询参数列表\r\nexport function listConfig(query) {\r\n  return request({\r\n    url: '/system/config/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询参数详细\r\nexport function getConfig(configId) {\r\n  return request({\r\n    url: '/system/config/' + configId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 根据参数键名查询参数值\r\nexport function getConfigKey(configKey) {\r\n  return request({\r\n    url: '/system/config/configKey/' + configKey,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增参数配置\r\nexport function addConfig(data) {\r\n  return request({\r\n    url: '/system/config',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改参数配置\r\nexport function updateConfig(data) {\r\n  return request({\r\n    url: '/system/config',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除参数配置\r\nexport function delConfig(configId) {\r\n  return request({\r\n    url: '/system/config/' + configId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 刷新参数缓存\r\nexport function refreshCache() {\r\n  return request({\r\n    url: '/system/config/refreshCache',\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,SAASA,CAACC,QAAQ,EAAE;EAClC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB,GAAGI,QAAQ;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,YAAYA,CAACC,SAAS,EAAE;EACtC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B,GAAGM,SAAS;IAC5CL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,SAASA,CAACC,IAAI,EAAE;EAC9B,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,MAAM;IACdO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,YAAYA,CAACD,IAAI,EAAE;EACjC,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,SAASA,CAACN,QAAQ,EAAE;EAClC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB,GAAGI,QAAQ;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,YAAYA,CAAA,EAAG;EAC7B,OAAO,IAAAZ,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}