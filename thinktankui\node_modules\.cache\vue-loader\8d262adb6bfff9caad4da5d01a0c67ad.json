{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\dashboard\\LineChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\dashboard\\LineChart.vue", "mtime": 1749104047638}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["LineChart.vue"], "names": [], "mappings": ";;;;;AAKA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "LineChart.vue", "sourceRoot": "src/views/dashboard", "sourcesContent": ["<template>\r\n  <div :class=\"className\" :style=\"{height:height,width:width}\" />\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nrequire('echarts/theme/macarons') // echarts theme\r\nimport resize from './mixins/resize'\r\n\r\nexport default {\r\n  mixins: [resize],\r\n  props: {\r\n    className: {\r\n      type: String,\r\n      default: 'chart'\r\n    },\r\n    width: {\r\n      type: String,\r\n      default: '100%'\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '350px'\r\n    },\r\n    autoResize: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chartData: {\r\n      type: Object,\r\n      required: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      chart: null\r\n    }\r\n  },\r\n  watch: {\r\n    chartData: {\r\n      deep: true,\r\n      handler(val) {\r\n        this.setOptions(val)\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.initChart()\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    if (!this.chart) {\r\n      return\r\n    }\r\n    this.chart.dispose()\r\n    this.chart = null\r\n  },\r\n  methods: {\r\n    initChart() {\r\n      this.chart = echarts.init(this.$el, 'macarons')\r\n      this.setOptions(this.chartData)\r\n    },\r\n    setOptions({ expectedData, actualData } = {}) {\r\n      this.chart.setOption({\r\n        xAxis: {\r\n          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\r\n          boundaryGap: false,\r\n          axisTick: {\r\n            show: false\r\n          }\r\n        },\r\n        grid: {\r\n          left: 10,\r\n          right: 10,\r\n          bottom: 20,\r\n          top: 30,\r\n          containLabel: true\r\n        },\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross'\r\n          },\r\n          padding: [5, 10]\r\n        },\r\n        yAxis: {\r\n          axisTick: {\r\n            show: false\r\n          }\r\n        },\r\n        legend: {\r\n          data: ['expected', 'actual']\r\n        },\r\n        series: [{\r\n          name: 'expected', itemStyle: {\r\n            normal: {\r\n              color: '#FF005A',\r\n              lineStyle: {\r\n                color: '#FF005A',\r\n                width: 2\r\n              }\r\n            }\r\n          },\r\n          smooth: true,\r\n          type: 'line',\r\n          data: expectedData,\r\n          animationDuration: 2800,\r\n          animationEasing: 'cubicInOut'\r\n        },\r\n        {\r\n          name: 'actual',\r\n          smooth: true,\r\n          type: 'line',\r\n          itemStyle: {\r\n            normal: {\r\n              color: '#3888fa',\r\n              lineStyle: {\r\n                color: '#3888fa',\r\n                width: 2\r\n              },\r\n              areaStyle: {\r\n                color: '#f3f8ff'\r\n              }\r\n            }\r\n          },\r\n          data: actualData,\r\n          animationDuration: 2800,\r\n          animationEasing: 'quadraticOut'\r\n        }]\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}