{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\account\\user-management.vue?vue&type=style&index=0&id=2deb1e0e&lang=scss&scoped=true", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\account\\user-management.vue", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["user-management.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAySA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "user-management.vue", "sourceRoot": "src/views/account", "sourcesContent": ["<template>\r\n  <div class=\"user-management-container\">\r\n    <div class=\"user-management-header\">\r\n      <div class=\"title\">用户管理</div>\r\n      <div class=\"actions\">\r\n        <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"handleAdd\">添加用户</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"user-management-content\">\r\n      <el-table\r\n        :data=\"userList\"\r\n        style=\"width: 100%\"\r\n        border\r\n        stripe\r\n        :header-cell-style=\"{background:'#f5f7fa', color:'#606266'}\"\r\n      >\r\n        <el-table-column\r\n          prop=\"userId\"\r\n          label=\"用户ID\"\r\n          width=\"100\"\r\n          align=\"center\"\r\n        />\r\n        <el-table-column\r\n          prop=\"userName\"\r\n          label=\"用户名称\"\r\n          width=\"150\"\r\n          align=\"center\"\r\n        />\r\n        <el-table-column\r\n          prop=\"phoneNumber\"\r\n          label=\"手机号\"\r\n          width=\"150\"\r\n          align=\"center\"\r\n        />\r\n        <el-table-column\r\n          prop=\"email\"\r\n          label=\"邮箱\"\r\n          width=\"180\"\r\n          align=\"center\"\r\n        />\r\n        <el-table-column\r\n          prop=\"maxVisits\"\r\n          label=\"访问权限\"\r\n          width=\"100\"\r\n          align=\"center\"\r\n        />\r\n        <el-table-column\r\n          prop=\"status\"\r\n          label=\"状态\"\r\n          width=\"100\"\r\n          align=\"center\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <el-switch\r\n              v-model=\"scope.row.status\"\r\n              active-color=\"#13ce66\"\r\n              inactive-color=\"#ff4949\"\r\n              :active-value=\"1\"\r\n              :inactive-value=\"0\"\r\n              @change=\"handleStatusChange(scope.row)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"操作\"\r\n          align=\"center\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"handleEdit(scope.row)\"\r\n            >编辑</el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-delete\"\r\n              class=\"delete-btn\"\r\n              @click=\"handleDelete(scope.row)\"\r\n            >删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          background\r\n          layout=\"prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          :current-page.sync=\"currentPage\"\r\n          :page-size=\"pageSize\"\r\n          @current-change=\"handleCurrentChange\"\r\n        />\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 添加用户对话框 -->\r\n    <el-dialog title=\"添加用户\" :visible.sync=\"dialogVisible\" width=\"500px\" center class=\"user-dialog\">\r\n      <el-form ref=\"userForm\" :model=\"userForm\" :rules=\"userFormRules\" label-width=\"100px\">\r\n        <el-form-item label=\"用户名称\" prop=\"userName\">\r\n          <div class=\"required-mark\">*</div>\r\n          <el-input v-model=\"userForm.userName\" placeholder=\"请输入用户名/手机号/邮箱等登录名\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号码\" prop=\"phoneNumber\">\r\n          <div class=\"required-mark\">*</div>\r\n          <el-input v-model=\"userForm.phoneNumber\" placeholder=\"输入手机号码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱\" prop=\"email\">\r\n          <div class=\"required-mark\">*</div>\r\n          <el-input v-model=\"userForm.email\" placeholder=\"输入邮箱\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"密码\" prop=\"password\">\r\n          <div class=\"required-mark\">*</div>\r\n          <el-input v-model=\"userForm.password\" type=\"password\" placeholder=\"8-16 密码必须同时包含数字、大小写字母和符号\" show-password />\r\n        </el-form-item>\r\n        <el-form-item label=\"最大访问量\" prop=\"maxVisits\">\r\n          <el-input v-model=\"userForm.maxVisits\" placeholder=\"0\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\">\r\n          <el-switch\r\n            v-model=\"userForm.status\"\r\n            active-color=\"#13ce66\"\r\n            inactive-color=\"#ff4949\"\r\n            :active-value=\"1\"\r\n            :inactive-value=\"0\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\" class=\"confirm-btn\">确定</el-button>\r\n        <el-button @click=\"dialogVisible = false\" class=\"cancel-btn\">取消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"UserManagement\",\r\n  data() {\r\n    return {\r\n      // 用户列表\r\n      userList: [\r\n        {\r\n          userId: 1,\r\n          userName: \"admin\",\r\n          phoneNumber: \"13800138000\",\r\n          email: \"<EMAIL>\",\r\n          password: \"******\",\r\n          maxVisits: 1000,\r\n          role: \"管理员\",\r\n          department: \"技术部\",\r\n          status: 1\r\n        },\r\n        {\r\n          userId: 2,\r\n          userName: \"user1\",\r\n          phoneNumber: \"13800138001\",\r\n          email: \"<EMAIL>\",\r\n          password: \"******\",\r\n          maxVisits: 500,\r\n          role: \"普通用户\",\r\n          department: \"市场部\",\r\n          status: 1\r\n        },\r\n        {\r\n          userId: 3,\r\n          userName: \"user2\",\r\n          phoneNumber: \"13800138002\",\r\n          email: \"<EMAIL>\",\r\n          password: \"******\",\r\n          maxVisits: 300,\r\n          role: \"普通用户\",\r\n          department: \"销售部\",\r\n          status: 0\r\n        }\r\n      ],\r\n      // 分页相关\r\n      total: 3,\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      // 对话框相关\r\n      dialogVisible: false,\r\n      dialogTitle: \"添加用户\",\r\n      userForm: {\r\n        userId: null,\r\n        userName: \"\",\r\n        phoneNumber: \"\",\r\n        email: \"\",\r\n        password: \"\",\r\n        maxVisits: 0,\r\n        role: \"\",\r\n        department: \"\",\r\n        status: 1\r\n      },\r\n      userFormRules: {\r\n        userName: [\r\n          { required: true, message: \"请输入用户名称\", trigger: \"blur\" },\r\n          { min: 3, max: 20, message: \"长度在 3 到 20 个字符\", trigger: \"blur\" }\r\n        ],\r\n        phoneNumber: [\r\n          { required: true, message: \"请输入手机号码\", trigger: \"blur\" },\r\n          { pattern: /^1[3-9]\\d{9}$/, message: \"请输入正确的手机号码\", trigger: \"blur\" }\r\n        ],\r\n        email: [\r\n          { required: true, message: \"请输入邮箱\", trigger: \"blur\" },\r\n          { type: \"email\", message: \"请输入正确的邮箱地址\", trigger: \"blur\" }\r\n        ],\r\n        password: [\r\n          { required: true, message: \"请输入密码\", trigger: \"blur\" },\r\n          { min: 8, max: 16, message: \"长度在 8 到 16 个字符\", trigger: \"blur\" },\r\n          {\r\n            pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[^\\da-zA-Z]).{8,16}$/,\r\n            message: \"密码必须同时包含数字、大小写字母和符号\",\r\n            trigger: \"blur\"\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  methods: {\r\n    // 处理页码变化\r\n    handleCurrentChange(val) {\r\n      this.currentPage = val;\r\n      // 实际应用中这里需要调用接口获取对应页的数据\r\n    },\r\n    // 添加用户\r\n    handleAdd() {\r\n      this.dialogTitle = \"添加用户\";\r\n      this.userForm = {\r\n        userId: null,\r\n        userName: \"\",\r\n        phoneNumber: \"\",\r\n        email: \"\",\r\n        password: \"\",\r\n        maxVisits: 0,\r\n        role: \"\",\r\n        department: \"\",\r\n        status: 1\r\n      };\r\n      this.dialogVisible = true;\r\n    },\r\n    // 编辑用户\r\n    handleEdit(row) {\r\n      this.dialogTitle = \"编辑用户\";\r\n      this.userForm = JSON.parse(JSON.stringify(row));\r\n      this.dialogVisible = true;\r\n    },\r\n    // 删除用户\r\n    handleDelete(row) {\r\n      this.$confirm(`确定要删除用户\"${row.userName}\"吗？`, \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        // 实际应用中这里需要调用接口删除用户\r\n        this.userList = this.userList.filter(item => item.userId !== row.userId);\r\n        this.total = this.userList.length;\r\n        this.$message.success(\"删除成功\");\r\n      }).catch(() => {\r\n        this.$message.info(\"已取消删除\");\r\n      });\r\n    },\r\n    // 修改用户状态\r\n    handleStatusChange(row) {\r\n      // 实际应用中这里需要调用接口修改用户状态\r\n      this.$message.success(`用户\"${row.userName}\"状态已${row.status === 1 ? '启用' : '禁用'}`);\r\n    },\r\n    // 提交表单\r\n    submitForm() {\r\n      this.$refs.userForm.validate(valid => {\r\n        if (valid) {\r\n          if (this.userForm.userId) {\r\n            // 编辑用户\r\n            const index = this.userList.findIndex(item => item.userId === this.userForm.userId);\r\n            if (index !== -1) {\r\n              this.userList.splice(index, 1, this.userForm);\r\n              this.$message.success(\"修改成功\");\r\n            }\r\n          } else {\r\n            // 添加用户\r\n            this.userForm.userId = this.userList.length + 1;\r\n            this.userList.push(this.userForm);\r\n            this.total = this.userList.length;\r\n            this.$message.success(\"添加成功\");\r\n          }\r\n          this.dialogVisible = false;\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.user-management-container {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.user-management-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n\r\n  .title {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n.user-management-content {\r\n  .el-table {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .delete-btn {\r\n    color: #f56c6c;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 20px;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: center;\r\n\r\n  .confirm-btn {\r\n    background-color: #409EFF;\r\n    border-color: #409EFF;\r\n    margin-right: 10px;\r\n  }\r\n\r\n  .cancel-btn {\r\n    color: #606266;\r\n    background-color: #fff;\r\n    border-color: #dcdfe6;\r\n  }\r\n}\r\n\r\n.user-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e4e7ed;\r\n\r\n    .el-dialog__title {\r\n      font-size: 16px;\r\n      font-weight: 500;\r\n    }\r\n\r\n    .el-dialog__headerbtn {\r\n      top: 15px;\r\n    }\r\n  }\r\n\r\n  .el-dialog__body {\r\n    padding: 20px;\r\n  }\r\n\r\n  .el-form-item {\r\n    position: relative;\r\n    margin-bottom: 20px;\r\n\r\n    .required-mark {\r\n      position: absolute;\r\n      left: -10px;\r\n      top: 10px;\r\n      color: #f56c6c;\r\n      font-size: 14px;\r\n    }\r\n\r\n    .el-form-item__label {\r\n      color: #606266;\r\n      font-weight: normal;\r\n    }\r\n\r\n    .el-input__inner {\r\n      border-radius: 3px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}