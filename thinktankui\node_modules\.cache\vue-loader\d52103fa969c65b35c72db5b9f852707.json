{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\DictTag\\index.vue?vue&type=style&index=0&id=7e7e1b87&scoped=true&lang=css", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\DictTag\\index.vue", "mtime": 1749104047618}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749104419119}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749104421224}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749104419872}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouZWwtdGFnICsgLmVsLXRhZyB7DQogIG1hcmdpbi1sZWZ0OiAxMHB4Ow0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqFA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/DictTag", "sourcesContent": ["<template>\r\n  <div>\r\n    <template v-for=\"(item, index) in options\">\r\n      <template v-if=\"values.includes(item.value)\">\r\n        <span\r\n          v-if=\"(item.raw.listClass == 'default' || item.raw.listClass == '') && (item.raw.cssClass == '' || item.raw.cssClass == null)\"\r\n          :key=\"item.value\"\r\n          :index=\"index\"\r\n          :class=\"item.raw.cssClass\"\r\n          >{{ item.label + ' ' }}</span\r\n        >\r\n        <el-tag\r\n          v-else\r\n          :disable-transitions=\"true\"\r\n          :key=\"item.value\"\r\n          :index=\"index\"\r\n          :type=\"item.raw.listClass == 'primary' ? '' : item.raw.listClass\"\r\n          :class=\"item.raw.cssClass\"\r\n        >\r\n          {{ item.label + ' ' }}\r\n        </el-tag>\r\n      </template>\r\n    </template>\r\n    <template v-if=\"unmatch && showValue\">\r\n      {{ unmatchArray | handleArray }}\r\n    </template>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"DictTag\",\r\n  props: {\r\n    options: {\r\n      type: Array,\r\n      default: null,\r\n    },\r\n    value: [Number, String, Array],\r\n    // 当未找到匹配的数据时，显示value\r\n    showValue: {\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    separator: {\r\n      type: String,\r\n      default: \",\"\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      unmatchArray: [], // 记录未匹配的项\r\n    }\r\n  },\r\n  computed: {\r\n    values() {\r\n      if (this.value === null || typeof this.value === 'undefined' || this.value === '') return []\r\n      return Array.isArray(this.value) ? this.value.map(item => '' + item) : String(this.value).split(this.separator)\r\n    },\r\n    unmatch() {\r\n      this.unmatchArray = []\r\n      // 没有value不显示\r\n      if (this.value === null || typeof this.value === 'undefined' || this.value === '' || this.options.length === 0) return false\r\n      // 传入值为数组\r\n      let unmatch = false // 添加一个标志来判断是否有未匹配项\r\n      this.values.forEach(item => {\r\n        if (!this.options.some(v => v.value === item)) {\r\n          this.unmatchArray.push(item)\r\n          unmatch = true // 如果有未匹配项，将标志设置为true\r\n        }\r\n      })\r\n      return unmatch // 返回标志的值\r\n    },\r\n\r\n  },\r\n  filters: {\r\n    handleArray(array) {\r\n      if (array.length === 0) return '';\r\n      return array.reduce((pre, cur) => {\r\n        return pre + ' ' + cur;\r\n      })\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style scoped>\r\n.el-tag + .el-tag {\r\n  margin-left: 10px;\r\n}\r\n</style>\r\n"]}]}