{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\report-center\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\report-center\\index.vue", "mtime": 1749104047642}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "date<PERSON><PERSON><PERSON>", "filterType", "searchKeyword", "reportList", "total", "currentPage", "pageSize", "activeTab", "current<PERSON>iew", "templateType", "templateList", "createTime", "operation", "sidebarCollapsed", "sidebarSearchText", "activeMenuItem", "menuCategories", "count", "children", "isItem", "methods", "toggleSidebar", "handleMenuSelect", "index", "fetchReportList", "createNewScheme", "$message", "message", "type", "searchSidebar", "console", "log", "editReport", "success", "exportReport", "createReport", "viewReport", "row", "concat", "editReportItem", "deleteReport", "_this", "$confirm", "confirmButtonText", "cancelButtonText", "then", "catch", "info", "handleCurrentChange", "val", "handleQuery", "handleReset", "switchTab", "tab", "createTemplate", "createProductTemplate", "viewTemplate", "editTemplate", "deleteTemplate", "_this2", "goBack", "switchTemplateType", "created"], "sources": ["src/views/report-center/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"page-container\">\r\n      <!-- 左侧导航栏 -->\r\n      <div class=\"left-sidebar\" :class=\"{ 'collapsed': sidebarCollapsed }\">\r\n        <div class=\"sidebar-header\">\r\n          <el-button type=\"warning\" class=\"new-scheme-btn\" @click=\"createNewScheme\">\r\n            <i class=\"el-icon-plus\"></i> 新建方案\r\n          </el-button>\r\n          <div class=\"sidebar-btn\" @click=\"toggleSidebar\">\r\n            <i class=\"el-icon-s-fold\"></i>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"sidebar-search\">\r\n          <el-input\r\n            v-model=\"sidebarSearchText\"\r\n            placeholder=\"搜索\"\r\n            prefix-icon=\"el-icon-search\"\r\n            size=\"small\"\r\n            @input=\"searchSidebar\"\r\n          ></el-input>\r\n        </div>\r\n\r\n        <div class=\"sidebar-menu\">\r\n          <el-menu\r\n            :default-active=\"activeMenuItem\"\r\n            class=\"sidebar-menu-list\"\r\n            @select=\"handleMenuSelect\"\r\n          >\r\n            <template v-for=\"(item, index) in menuCategories\">\r\n              <!-- 使用唯一的key -->\r\n              <el-menu-item\r\n                v-if=\"item.isItem\"\r\n                :key=\"'item-' + item.name\"\r\n                :index=\"item.name\"\r\n                :class=\"{ 'active-menu-item': activeMenuItem === item.name }\"\r\n              >\r\n                <span>{{ item.name }}</span>\r\n              </el-menu-item>\r\n\r\n              <!-- 如果是子菜单 -->\r\n              <el-submenu\r\n                v-else\r\n                :key=\"'submenu-' + item.name\"\r\n                :index=\"item.name\"\r\n              >\r\n                <template slot=\"title\">\r\n                  <span>{{ item.name }}({{ item.count }})</span>\r\n                </template>\r\n                <!-- 子菜单项 -->\r\n                <el-menu-item\r\n                  v-for=\"child in item.children\"\r\n                  :key=\"child.name\"\r\n                  :index=\"child.name\"\r\n                >\r\n                  {{ child.name }}\r\n                </el-menu-item>\r\n              </el-submenu>\r\n            </template>\r\n          </el-menu>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧内容区 -->\r\n      <div class=\"right-content\">\r\n        <div class=\"report-container\">\r\n          <div class=\"report-header\">\r\n            <div class=\"title\">方案报告</div>\r\n            <div class=\"actions\">\r\n              <el-button type=\"primary\" size=\"small\" icon=\"el-icon-edit\" @click=\"editReport\">模板管理</el-button>\r\n              <el-button type=\"primary\" size=\"small\" icon=\"el-icon-document\" @click=\"exportReport\">定时任务管理</el-button>\r\n              <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"createReport\">新建报告</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 报告列表视图 -->\r\n          <div v-if=\"currentView === 'report'\">\r\n            <div class=\"filter-options\">\r\n              <div class=\"filter-row\">\r\n                <div class=\"filter-item\">\r\n                  <span>时间范围：</span>\r\n                  <el-date-picker\r\n                    v-model=\"dateRange\"\r\n                    type=\"daterange\"\r\n                    range-separator=\"-\"\r\n                    start-placeholder=\"开始日期\"\r\n                    end-placeholder=\"结束日期\"\r\n                    size=\"small\"\r\n                    style=\"width: 240px;\"\r\n                  ></el-date-picker>\r\n                </div>\r\n                <div class=\"filter-item\">\r\n                  <el-select v-model=\"filterType\" placeholder=\"全部\" size=\"small\" style=\"width: 100px;\">\r\n                    <el-option label=\"全部\" value=\"all\"></el-option>\r\n                    <el-option label=\"名称\" value=\"name\"></el-option>\r\n                    <el-option label=\"类型\" value=\"type\"></el-option>\r\n                  </el-select>\r\n                  <el-input\r\n                    v-model=\"searchKeyword\"\r\n                    placeholder=\"请输入内容\"\r\n                    size=\"small\"\r\n                    style=\"width: 200px; margin-left: 5px;\"\r\n                  ></el-input>\r\n                </div>\r\n                <div class=\"tab-container\">\r\n                  <div\r\n                    class=\"tab-item\"\r\n                    :class=\"{ 'active': activeTab === 'normal' }\"\r\n                    @click=\"switchTab('normal')\"\r\n                  >\r\n                    普通报告\r\n                  </div>\r\n                  <div\r\n                    class=\"tab-item\"\r\n                    :class=\"{ 'active': activeTab === 'competitor' }\"\r\n                    @click=\"switchTab('competitor')\"\r\n                  >\r\n                    竞对报告\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"report-table\">\r\n              <el-table\r\n                :data=\"reportList\"\r\n                style=\"width: 100%\"\r\n                border\r\n                stripe\r\n                :header-cell-style=\"{background:'#f5f7fa', color:'#606266'}\"\r\n              >\r\n                <el-table-column\r\n                  prop=\"name\"\r\n                  label=\"配置名称\"\r\n                  width=\"180\"\r\n                ></el-table-column>\r\n                <el-table-column\r\n                  prop=\"createTime\"\r\n                  label=\"创建时间\"\r\n                  width=\"180\"\r\n                ></el-table-column>\r\n                <el-table-column\r\n                  prop=\"type\"\r\n                  label=\"配置类型\"\r\n                  width=\"120\"\r\n                ></el-table-column>\r\n                <el-table-column\r\n                  prop=\"status\"\r\n                  label=\"状态\"\r\n                  width=\"100\"\r\n                ></el-table-column>\r\n                <el-table-column\r\n                  prop=\"reportStatus\"\r\n                  label=\"报告状态\"\r\n                  width=\"120\"\r\n                ></el-table-column>\r\n                <el-table-column\r\n                  label=\"操作\"\r\n                  align=\"center\"\r\n                >\r\n                  <template slot-scope=\"scope\">\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      icon=\"el-icon-view\"\r\n                      @click=\"viewReport(scope.row)\"\r\n                    >查看</el-button>\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      icon=\"el-icon-edit\"\r\n                      @click=\"editReportItem(scope.row)\"\r\n                    >编辑</el-button>\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      icon=\"el-icon-delete\"\r\n                      class=\"delete-btn\"\r\n                      @click=\"deleteReport(scope.row)\"\r\n                    >删除</el-button>\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n\r\n              <!-- 空数据显示 -->\r\n              <div v-if=\"reportList.length === 0\" class=\"empty-data\">\r\n                <i class=\"el-icon-data-analysis empty-icon\"></i>\r\n                <p>暂无数据</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 模板管理视图 -->\r\n          <div v-if=\"currentView === 'template'\">\r\n            <div class=\"back-button\">\r\n              <el-button type=\"text\" icon=\"el-icon-arrow-left\" @click=\"goBack\">返回</el-button>\r\n            </div>\r\n            <div class=\"template-header\">\r\n              <div class=\"template-title\">\r\n                <i class=\"el-icon-document\"></i> 报告模板\r\n              </div>\r\n              <div class=\"template-actions\">\r\n                <div class=\"template-tab-container\">\r\n                  <div\r\n                    class=\"template-tab\"\r\n                    :class=\"{ 'active': templateType === 'normal' }\"\r\n                    @click=\"switchTemplateType('normal')\"\r\n                  >\r\n                    普通模板\r\n                  </div>\r\n                  <div\r\n                    class=\"template-tab\"\r\n                    :class=\"{ 'active': templateType === 'competitor' }\"\r\n                    @click=\"switchTemplateType('competitor')\"\r\n                  >\r\n                    竞对模板\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"create-template-btn\">\r\n              <el-button type=\"primary\" plain @click=\"createProductTemplate\">\r\n                <i class=\"el-icon-plus\"></i> 创建产品模板\r\n              </el-button>\r\n            </div>\r\n\r\n            <div class=\"template-table\">\r\n              <el-table\r\n                :data=\"templateList\"\r\n                style=\"width: 100%\"\r\n                border\r\n                stripe\r\n                :header-cell-style=\"{background:'#f5f7fa', color:'#606266'}\"\r\n              >\r\n                <el-table-column\r\n                  prop=\"name\"\r\n                  label=\"模板名称\"\r\n                  width=\"180\"\r\n                ></el-table-column>\r\n                <el-table-column\r\n                  prop=\"createTime\"\r\n                  label=\"创建时间\"\r\n                  width=\"180\"\r\n                ></el-table-column>\r\n                <el-table-column\r\n                  label=\"操作\"\r\n                  align=\"center\"\r\n                >\r\n                  <template slot-scope=\"scope\">\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      icon=\"el-icon-view\"\r\n                      @click=\"viewTemplate(scope.row)\"\r\n                    >查看</el-button>\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      icon=\"el-icon-edit\"\r\n                      @click=\"editTemplate(scope.row)\"\r\n                    >编辑</el-button>\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      icon=\"el-icon-delete\"\r\n                      class=\"delete-btn\"\r\n                      @click=\"deleteTemplate(scope.row)\"\r\n                    >删除</el-button>\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"pagination-container\">\r\n            <el-pagination\r\n              background\r\n              layout=\"prev, pager, next\"\r\n              :total=\"total\"\r\n              :current-page.sync=\"currentPage\"\r\n              :page-size=\"pageSize\"\r\n              @current-change=\"handleCurrentChange\"\r\n            ></el-pagination>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"ReportList\",\r\n  data() {\r\n    return {\r\n      dateRange: [],\r\n      filterType: 'all',\r\n      searchKeyword: '',\r\n      reportList: [],\r\n      total: 0,\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      activeTab: 'normal', // 当前激活的选项卡：normal-普通报告，competitor-竞对报告\r\n      currentView: 'report', // 当前视图：report-报告列表，template-模板管理\r\n      templateType: 'normal', // 当前模板类型：normal-普通模板，competitor-竞对模板\r\n      // 模板列表数据\r\n      templateList: [\r\n        { name: '品牌-热议话题', createTime: '2019-11-18 14:02:08', operation: '' },\r\n        { name: '品牌-舆论', createTime: '2019-11-18 14:06:52', operation: '' },\r\n        { name: '品牌-竞品', createTime: '2021-04-07 15:15:00', operation: '' }\r\n      ],\r\n      // 侧边栏数据\r\n      sidebarCollapsed: false,\r\n      sidebarSearchText: '',\r\n      activeMenuItem: '方太',\r\n      menuCategories: [\r\n        { name: '总监', count: 1, children: [] },\r\n        { name: '品牌', count: 1, children: [] },\r\n        { name: '方太', count: 0, isItem: true },\r\n        { name: '人物', count: 0, children: [] },\r\n        { name: '机构', count: 0, children: [] },\r\n        { name: '产品', count: 0, children: [] },\r\n        { name: '事件', count: 0, children: [] },\r\n        { name: '话题', count: 0, children: [] }\r\n      ]\r\n    };\r\n  },\r\n  methods: {\r\n    // 侧边栏相关方法\r\n    toggleSidebar() {\r\n      this.sidebarCollapsed = !this.sidebarCollapsed;\r\n    },\r\n    handleMenuSelect(index) {\r\n      this.activeMenuItem = index;\r\n      // 这里可以添加切换菜单项后的逻辑，如重新获取数据等\r\n      this.fetchReportList();\r\n    },\r\n    createNewScheme() {\r\n      // 新建方案的逻辑\r\n      this.$message({\r\n        message: '新建方案功能待实现',\r\n        type: 'info'\r\n      });\r\n    },\r\n    searchSidebar() {\r\n      // 侧边栏搜索逻辑\r\n      console.log('搜索关键词：', this.sidebarSearchText);\r\n      // 实现搜索逻辑\r\n    },\r\n    // 模板管理\r\n    editReport() {\r\n      this.currentView = 'template';\r\n      this.$message.success(\"切换到模板管理\");\r\n    },\r\n    // 导出报告\r\n    exportReport() {\r\n      this.$message.success(\"定时任务管理\");\r\n    },\r\n    // 创建新报告\r\n    createReport() {\r\n      this.$message.success(\"新建报告\");\r\n    },\r\n    // 查看报告\r\n    viewReport(row) {\r\n      this.$message.success(`查看报告: ${row.name}`);\r\n    },\r\n    // 编辑报告项\r\n    editReportItem(row) {\r\n      this.$message.success(`编辑报告: ${row.name}`);\r\n    },\r\n    // 删除报告\r\n    deleteReport(row) {\r\n      this.$confirm(`确认删除报告 \"${row.name}\"?`, \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.$message.success(`删除报告: ${row.name}`);\r\n      }).catch(() => {\r\n        this.$message.info(\"已取消删除\");\r\n      });\r\n    },\r\n    // 处理页码变化\r\n    handleCurrentChange(val) {\r\n      this.currentPage = val;\r\n      this.fetchReportList();\r\n    },\r\n    // 获取报告列表\r\n    fetchReportList() {\r\n      // 实际应用中这里需要调用接口获取数据\r\n      // 这里模拟空数据\r\n      this.reportList = [];\r\n      this.total = 0;\r\n    },\r\n    // 查询按钮点击事件\r\n    handleQuery() {\r\n      this.$message.success(\"执行查询操作\");\r\n      this.fetchReportList();\r\n    },\r\n    // 重置按钮点击事件\r\n    handleReset() {\r\n      this.dateRange = [];\r\n      this.filterType = 'all';\r\n      this.searchKeyword = '';\r\n      this.$message.success(\"重置筛选条件\");\r\n    },\r\n    // 切换选项卡\r\n    switchTab(tab) {\r\n      this.activeTab = tab;\r\n      if (tab === 'normal') {\r\n        this.handleQuery();\r\n      } else {\r\n        this.handleReset();\r\n      }\r\n    },\r\n    // 创建模板\r\n    createTemplate() {\r\n      this.$message.success(\"创建新模板\");\r\n    },\r\n    // 创建产品模板\r\n    createProductTemplate() {\r\n      this.$message.success(\"创建产品模板\");\r\n    },\r\n    // 查看模板\r\n    viewTemplate(row) {\r\n      this.$message.success(`查看模板: ${row.name}`);\r\n    },\r\n    // 编辑模板\r\n    editTemplate(row) {\r\n      this.$message.success(`编辑模板: ${row.name}`);\r\n    },\r\n    // 删除模板\r\n    deleteTemplate(row) {\r\n      this.$confirm(`确认删除模板 \"${row.name}\"?`, \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.$message.success(`删除模板: ${row.name}`);\r\n      }).catch(() => {\r\n        this.$message.info(\"已取消删除\");\r\n      });\r\n    },\r\n    // 返回按钮点击事件\r\n    goBack() {\r\n      this.currentView = 'report';\r\n      this.$message.success(\"返回报告列表\");\r\n    },\r\n    // 切换模板类型\r\n    switchTemplateType(type) {\r\n      this.templateType = type;\r\n      this.$message.success(`切换到${type === 'normal' ? '普通模板' : '竞对模板'}`);\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchReportList();\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.page-container {\r\n  display: flex;\r\n  height: 100%;\r\n}\r\n\r\n/* 左侧导航栏样式 */\r\n.left-sidebar {\r\n  width: 200px;\r\n  background-color: #fff;\r\n  border-right: 1px solid #e6e6e6;\r\n  display: flex;\r\n  flex-direction: column;\r\n  flex-shrink: 0;\r\n  transition: width 0.3s;\r\n}\r\n\r\n/* 折叠状态的侧边栏 */\r\n.left-sidebar.collapsed {\r\n  width: 64px;\r\n}\r\n\r\n.left-sidebar.collapsed .sidebar-search,\r\n.left-sidebar.collapsed .el-menu-item span,\r\n.left-sidebar.collapsed .el-submenu__title span {\r\n  display: none;\r\n}\r\n\r\n.left-sidebar.collapsed .new-scheme-btn {\r\n  padding: 8px 0;\r\n  font-size: 0;\r\n}\r\n\r\n.left-sidebar.collapsed .new-scheme-btn i {\r\n  font-size: 16px;\r\n  margin: 0;\r\n}\r\n\r\n.sidebar-header {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.new-scheme-btn {\r\n  flex: 1;\r\n  font-size: 12px;\r\n  padding: 8px 10px;\r\n}\r\n\r\n.sidebar-btn {\r\n  width: 30px;\r\n  height: 30px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-left: 5px;\r\n  cursor: pointer;\r\n  color: #909399;\r\n}\r\n\r\n.sidebar-search {\r\n  padding: 10px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.sidebar-menu {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n}\r\n\r\n.sidebar-menu-list {\r\n  border-right: none;\r\n}\r\n\r\n.active-menu-item {\r\n  background-color: #ecf5ff !important;\r\n  color: #409EFF !important;\r\n}\r\n\r\n/* 右侧内容区样式 */\r\n.right-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 覆盖Element UI的一些默认样式 */\r\n::v-deep .el-menu-item, ::v-deep .el-submenu__title {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  font-size: 14px;\r\n}\r\n\r\n::v-deep .el-submenu .el-menu-item {\r\n  height: 36px;\r\n  line-height: 36px;\r\n  padding: 0 20px 0 40px;\r\n}\r\n\r\n/* 报告中心样式 */\r\n.report-container {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n  height: 100%;\r\n  overflow-y: auto;\r\n}\r\n\r\n.report-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.report-header .title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.report-header .actions {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.filter-options {\r\n  margin-bottom: 20px;\r\n  background-color: #f5f7fa;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.filter-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.filter-item {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.filter-buttons {\r\n  display: flex;\r\n  gap: 10px;\r\n  margin-left: auto;\r\n}\r\n\r\n.tab-container {\r\n  display: flex;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  border: 1px solid #dcdfe6;\r\n  width: fit-content;\r\n  margin-left: auto;\r\n}\r\n\r\n.tab-item {\r\n  padding: 8px 20px;\r\n  cursor: pointer;\r\n  background-color: #fff;\r\n  color: #606266;\r\n  font-size: 14px;\r\n  text-align: center;\r\n  border-right: 1px solid #dcdfe6;\r\n  min-width: 100px;\r\n}\r\n\r\n.tab-item:last-child {\r\n  border-right: none;\r\n}\r\n\r\n.tab-item.active {\r\n  background-color: #409EFF;\r\n  color: #fff;\r\n}\r\n\r\n.filter-item span {\r\n  margin-right: 10px;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.report-table {\r\n  margin-bottom: 20px;\r\n  position: relative;\r\n  min-height: 300px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n}\r\n\r\n.report-table ::v-deep .el-table th {\r\n  background-color: #f5f7fa;\r\n  color: #606266;\r\n  font-weight: 500;\r\n  text-align: center;\r\n}\r\n\r\n.report-table ::v-deep .el-table td {\r\n  text-align: center;\r\n}\r\n\r\n.empty-data {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  text-align: center;\r\n}\r\n\r\n.empty-data .empty-icon {\r\n  font-size: 60px;\r\n  color: #c0c4cc;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.empty-data p {\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.delete-btn {\r\n  color: #f56c6c;\r\n}\r\n\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 20px;\r\n}\r\n\r\n/* 模板管理样式 */\r\n.template-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.template-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.template-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.template-tab-container {\r\n  display: flex;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  border: 1px solid #dcdfe6;\r\n  width: fit-content;\r\n}\r\n\r\n.template-tab {\r\n  padding: 8px 20px;\r\n  cursor: pointer;\r\n  background-color: #fff;\r\n  color: #606266;\r\n  font-size: 14px;\r\n  text-align: center;\r\n  border-right: 1px solid #dcdfe6;\r\n  min-width: 100px;\r\n}\r\n\r\n.template-tab:last-child {\r\n  border-right: none;\r\n}\r\n\r\n.template-tab.active {\r\n  background-color: #409EFF;\r\n  color: #fff;\r\n}\r\n\r\n.template-table {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.create-template-btn {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.create-template-btn .el-button {\r\n  border-color: #f56c6c;\r\n  color: #f56c6c;\r\n}\r\n\r\n.create-template-btn .el-button:hover {\r\n  background-color: #fef0f0;\r\n}\r\n\r\n.back-button {\r\n  text-align: right;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.back-button .el-button {\r\n  color: #409EFF;\r\n  font-size: 14px;\r\n  padding: 0;\r\n}\r\n\r\n.back-button .el-button:hover {\r\n  color: #66b1ff;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAqSA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,UAAA;MACAC,aAAA;MACAC,UAAA;MACAC,KAAA;MACAC,WAAA;MACAC,QAAA;MACAC,SAAA;MAAA;MACAC,WAAA;MAAA;MACAC,YAAA;MAAA;MACA;MACAC,YAAA,GACA;QAAAZ,IAAA;QAAAa,UAAA;QAAAC,SAAA;MAAA,GACA;QAAAd,IAAA;QAAAa,UAAA;QAAAC,SAAA;MAAA,GACA;QAAAd,IAAA;QAAAa,UAAA;QAAAC,SAAA;MAAA,EACA;MACA;MACAC,gBAAA;MACAC,iBAAA;MACAC,cAAA;MACAC,cAAA,GACA;QAAAlB,IAAA;QAAAmB,KAAA;QAAAC,QAAA;MAAA,GACA;QAAApB,IAAA;QAAAmB,KAAA;QAAAC,QAAA;MAAA,GACA;QAAApB,IAAA;QAAAmB,KAAA;QAAAE,MAAA;MAAA,GACA;QAAArB,IAAA;QAAAmB,KAAA;QAAAC,QAAA;MAAA,GACA;QAAApB,IAAA;QAAAmB,KAAA;QAAAC,QAAA;MAAA,GACA;QAAApB,IAAA;QAAAmB,KAAA;QAAAC,QAAA;MAAA,GACA;QAAApB,IAAA;QAAAmB,KAAA;QAAAC,QAAA;MAAA,GACA;QAAApB,IAAA;QAAAmB,KAAA;QAAAC,QAAA;MAAA;IAEA;EACA;EACAE,OAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA,KAAAR,gBAAA,SAAAA,gBAAA;IACA;IACAS,gBAAA,WAAAA,iBAAAC,KAAA;MACA,KAAAR,cAAA,GAAAQ,KAAA;MACA;MACA,KAAAC,eAAA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA;MACA,KAAAC,QAAA;QACAC,OAAA;QACAC,IAAA;MACA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA;MACAC,OAAA,CAAAC,GAAA,gBAAAjB,iBAAA;MACA;IACA;IACA;IACAkB,UAAA,WAAAA,WAAA;MACA,KAAAxB,WAAA;MACA,KAAAkB,QAAA,CAAAO,OAAA;IACA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAR,QAAA,CAAAO,OAAA;IACA;IACA;IACAE,YAAA,WAAAA,aAAA;MACA,KAAAT,QAAA,CAAAO,OAAA;IACA;IACA;IACAG,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAAX,QAAA,CAAAO,OAAA,8BAAAK,MAAA,CAAAD,GAAA,CAAAvC,IAAA;IACA;IACA;IACAyC,cAAA,WAAAA,eAAAF,GAAA;MACA,KAAAX,QAAA,CAAAO,OAAA,8BAAAK,MAAA,CAAAD,GAAA,CAAAvC,IAAA;IACA;IACA;IACA0C,YAAA,WAAAA,aAAAH,GAAA;MAAA,IAAAI,KAAA;MACA,KAAAC,QAAA,2CAAAJ,MAAA,CAAAD,GAAA,CAAAvC,IAAA;QACA6C,iBAAA;QACAC,gBAAA;QACAhB,IAAA;MACA,GAAAiB,IAAA;QACAJ,KAAA,CAAAf,QAAA,CAAAO,OAAA,8BAAAK,MAAA,CAAAD,GAAA,CAAAvC,IAAA;MACA,GAAAgD,KAAA;QACAL,KAAA,CAAAf,QAAA,CAAAqB,IAAA;MACA;IACA;IACA;IACAC,mBAAA,WAAAA,oBAAAC,GAAA;MACA,KAAA5C,WAAA,GAAA4C,GAAA;MACA,KAAAzB,eAAA;IACA;IACA;IACAA,eAAA,WAAAA,gBAAA;MACA;MACA;MACA,KAAArB,UAAA;MACA,KAAAC,KAAA;IACA;IACA;IACA8C,WAAA,WAAAA,YAAA;MACA,KAAAxB,QAAA,CAAAO,OAAA;MACA,KAAAT,eAAA;IACA;IACA;IACA2B,WAAA,WAAAA,YAAA;MACA,KAAAnD,SAAA;MACA,KAAAC,UAAA;MACA,KAAAC,aAAA;MACA,KAAAwB,QAAA,CAAAO,OAAA;IACA;IACA;IACAmB,SAAA,WAAAA,UAAAC,GAAA;MACA,KAAA9C,SAAA,GAAA8C,GAAA;MACA,IAAAA,GAAA;QACA,KAAAH,WAAA;MACA;QACA,KAAAC,WAAA;MACA;IACA;IACA;IACAG,cAAA,WAAAA,eAAA;MACA,KAAA5B,QAAA,CAAAO,OAAA;IACA;IACA;IACAsB,qBAAA,WAAAA,sBAAA;MACA,KAAA7B,QAAA,CAAAO,OAAA;IACA;IACA;IACAuB,YAAA,WAAAA,aAAAnB,GAAA;MACA,KAAAX,QAAA,CAAAO,OAAA,8BAAAK,MAAA,CAAAD,GAAA,CAAAvC,IAAA;IACA;IACA;IACA2D,YAAA,WAAAA,aAAApB,GAAA;MACA,KAAAX,QAAA,CAAAO,OAAA,8BAAAK,MAAA,CAAAD,GAAA,CAAAvC,IAAA;IACA;IACA;IACA4D,cAAA,WAAAA,eAAArB,GAAA;MAAA,IAAAsB,MAAA;MACA,KAAAjB,QAAA,2CAAAJ,MAAA,CAAAD,GAAA,CAAAvC,IAAA;QACA6C,iBAAA;QACAC,gBAAA;QACAhB,IAAA;MACA,GAAAiB,IAAA;QACAc,MAAA,CAAAjC,QAAA,CAAAO,OAAA,8BAAAK,MAAA,CAAAD,GAAA,CAAAvC,IAAA;MACA,GAAAgD,KAAA;QACAa,MAAA,CAAAjC,QAAA,CAAAqB,IAAA;MACA;IACA;IACA;IACAa,MAAA,WAAAA,OAAA;MACA,KAAApD,WAAA;MACA,KAAAkB,QAAA,CAAAO,OAAA;IACA;IACA;IACA4B,kBAAA,WAAAA,mBAAAjC,IAAA;MACA,KAAAnB,YAAA,GAAAmB,IAAA;MACA,KAAAF,QAAA,CAAAO,OAAA,sBAAAK,MAAA,CAAAV,IAAA;IACA;EACA;EACAkC,OAAA,WAAAA,QAAA;IACA,KAAAtC,eAAA;EACA;AACA", "ignoreList": []}]}