{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\utils\\dict\\DictOptions.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\utils\\dict\\DictOptions.js", "mtime": 1749104047634}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749104418875}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_ruoyi", "require", "_DictConverter", "_interopRequireDefault", "options", "exports", "metas", "request", "dictMeta", "console", "log", "concat", "type", "Promise", "resolve", "responseConverter", "labelField", "valueField", "DEFAULT_LABEL_FIELDS", "DEFAULT_VALUE_FIELDS", "response", "dicts", "content", "Array", "undefined", "warn", "map", "d", "dictConverter", "mergeOptions", "src", "mergeRecursive", "_default", "default"], "sources": ["D:/thinktank/thinktankui/src/utils/dict/DictOptions.js"], "sourcesContent": ["import { mergeRecursive } from \"@/utils/ruoyi\";\r\nimport dictConverter from './DictConverter'\r\n\r\nexport const options = {\r\n  metas: {\r\n    '*': {\r\n      /**\r\n       * 字典请求，方法签名为function(dictMeta: DictMeta): Promise\r\n       */\r\n      request: (dictMeta) => {\r\n        console.log(`load dict ${dictMeta.type}`)\r\n        return Promise.resolve([])\r\n      },\r\n      /**\r\n       * 字典响应数据转换器，方法签名为function(response: Object, dictMeta: DictMeta): DictData\r\n       */\r\n      responseConverter,\r\n      labelField: 'label',\r\n      valueField: 'value',\r\n    },\r\n  },\r\n  /**\r\n   * 默认标签字段\r\n   */\r\n  DEFAULT_LABEL_FIELDS: ['label', 'name', 'title'],\r\n  /**\r\n   * 默认值字段\r\n   */\r\n  DEFAULT_VALUE_FIELDS: ['value', 'id', 'uid', 'key'],\r\n}\r\n\r\n/**\r\n * 映射字典\r\n * @param {Object} response 字典数据\r\n * @param {DictMeta} dictMeta 字典元数据\r\n * @returns {DictData}\r\n */\r\nfunction responseConverter(response, dictMeta) {\r\n  const dicts = response.content instanceof Array ? response.content : response\r\n  if (dicts === undefined) {\r\n    console.warn(`no dict data of \"${dictMeta.type}\" found in the response`)\r\n    return []\r\n  }\r\n  return dicts.map(d => dictConverter(d, dictMeta))\r\n}\r\n\r\nexport function mergeOptions(src) {\r\n  mergeRecursive(options, src)\r\n}\r\n\r\nexport default options\r\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEO,IAAMG,OAAO,GAAAC,OAAA,CAAAD,OAAA,GAAG;EACrBE,KAAK,EAAE;IACL,GAAG,EAAE;MACH;AACN;AACA;MACMC,OAAO,EAAE,SAATA,OAAOA,CAAGC,QAAQ,EAAK;QACrBC,OAAO,CAACC,GAAG,cAAAC,MAAA,CAAcH,QAAQ,CAACI,IAAI,CAAE,CAAC;QACzC,OAAOC,OAAO,CAACC,OAAO,CAAC,EAAE,CAAC;MAC5B,CAAC;MACD;AACN;AACA;MACMC,iBAAiB,EAAjBA,iBAAiB;MACjBC,UAAU,EAAE,OAAO;MACnBC,UAAU,EAAE;IACd;EACF,CAAC;EACD;AACF;AACA;EACEC,oBAAoB,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;EAChD;AACF;AACA;EACEC,oBAAoB,EAAE,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK;AACpD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,SAASJ,iBAAiBA,CAACK,QAAQ,EAAEZ,QAAQ,EAAE;EAC7C,IAAMa,KAAK,GAAGD,QAAQ,CAACE,OAAO,YAAYC,KAAK,GAAGH,QAAQ,CAACE,OAAO,GAAGF,QAAQ;EAC7E,IAAIC,KAAK,KAAKG,SAAS,EAAE;IACvBf,OAAO,CAACgB,IAAI,sBAAAd,MAAA,CAAqBH,QAAQ,CAACI,IAAI,6BAAyB,CAAC;IACxE,OAAO,EAAE;EACX;EACA,OAAOS,KAAK,CAACK,GAAG,CAAC,UAAAC,CAAC;IAAA,OAAI,IAAAC,sBAAa,EAACD,CAAC,EAAEnB,QAAQ,CAAC;EAAA,EAAC;AACnD;AAEO,SAASqB,YAAYA,CAACC,GAAG,EAAE;EAChC,IAAAC,qBAAc,EAAC3B,OAAO,EAAE0B,GAAG,CAAC;AAC9B;AAAC,IAAAE,QAAA,GAAA3B,OAAA,CAAA4B,OAAA,GAEc7B,OAAO", "ignoreList": []}]}