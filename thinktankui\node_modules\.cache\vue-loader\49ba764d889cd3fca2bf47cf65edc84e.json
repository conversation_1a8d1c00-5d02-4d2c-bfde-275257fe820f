{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\Editor\\index.vue?vue&type=style&index=0&id=7480c5e0&lang=css", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\Editor\\index.vue", "mtime": 1749104047618}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749104419119}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749104421224}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749104419872}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Editor", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-upload\r\n      :action=\"uploadUrl\"\r\n      :before-upload=\"handleBeforeUpload\"\r\n      :on-success=\"handleUploadSuccess\"\r\n      :on-error=\"handleUploadError\"\r\n      name=\"file\"\r\n      :show-file-list=\"false\"\r\n      :headers=\"headers\"\r\n      style=\"display: none\"\r\n      ref=\"upload\"\r\n      v-if=\"this.type == 'url'\"\r\n    >\r\n    </el-upload>\r\n    <div class=\"editor\" ref=\"editor\" :style=\"styles\"></div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Quill from \"quill\";\r\nimport \"quill/dist/quill.core.css\";\r\nimport \"quill/dist/quill.snow.css\";\r\nimport \"quill/dist/quill.bubble.css\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"Editor\",\r\n  props: {\r\n    /* 编辑器的内容 */\r\n    value: {\r\n      type: String,\r\n      default: \"\",\r\n    },\r\n    /* 高度 */\r\n    height: {\r\n      type: Number,\r\n      default: null,\r\n    },\r\n    /* 最小高度 */\r\n    minHeight: {\r\n      type: Number,\r\n      default: null,\r\n    },\r\n    /* 只读 */\r\n    readOnly: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    /* 上传文件大小限制(MB) */\r\n    fileSize: {\r\n      type: Number,\r\n      default: 5,\r\n    },\r\n    /* 类型（base64格式、url格式） */\r\n    type: {\r\n      type: String,\r\n      default: \"url\",\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/common/upload\", // 上传的图片服务器地址\r\n      headers: {\r\n        Authorization: \"Bearer \" + getToken()\r\n      },\r\n      Quill: null,\r\n      currentValue: \"\",\r\n      options: {\r\n        theme: \"snow\",\r\n        bounds: document.body,\r\n        debug: \"warn\",\r\n        modules: {\r\n          // 工具栏配置\r\n          toolbar: [\r\n            [\"bold\", \"italic\", \"underline\", \"strike\"],       // 加粗 斜体 下划线 删除线\r\n            [\"blockquote\", \"code-block\"],                    // 引用  代码块\r\n            [{ list: \"ordered\" }, { list: \"bullet\" }],       // 有序、无序列表\r\n            [{ indent: \"-1\" }, { indent: \"+1\" }],            // 缩进\r\n            [{ size: [\"small\", false, \"large\", \"huge\"] }],   // 字体大小\r\n            [{ header: [1, 2, 3, 4, 5, 6, false] }],         // 标题\r\n            [{ color: [] }, { background: [] }],             // 字体颜色、字体背景颜色\r\n            [{ align: [] }],                                 // 对齐方式\r\n            [\"clean\"],                                       // 清除文本格式\r\n            [\"link\", \"image\", \"video\"]                       // 链接、图片、视频\r\n          ],\r\n        },\r\n        placeholder: \"请输入内容\",\r\n        readOnly: this.readOnly,\r\n      },\r\n    };\r\n  },\r\n  computed: {\r\n    styles() {\r\n      let style = {};\r\n      if (this.minHeight) {\r\n        style.minHeight = `${this.minHeight}px`;\r\n      }\r\n      if (this.height) {\r\n        style.height = `${this.height}px`;\r\n      }\r\n      return style;\r\n    },\r\n  },\r\n  watch: {\r\n    value: {\r\n      handler(val) {\r\n        if (val !== this.currentValue) {\r\n          this.currentValue = val === null ? \"\" : val;\r\n          if (this.Quill) {\r\n            this.Quill.clipboard.dangerouslyPasteHTML(this.currentValue);\r\n          }\r\n        }\r\n      },\r\n      immediate: true,\r\n    },\r\n  },\r\n  mounted() {\r\n    this.init();\r\n  },\r\n  beforeDestroy() {\r\n    this.Quill = null;\r\n  },\r\n  methods: {\r\n    init() {\r\n      const editor = this.$refs.editor;\r\n      this.Quill = new Quill(editor, this.options);\r\n      // 如果设置了上传地址则自定义图片上传事件\r\n      if (this.type == 'url') {\r\n        let toolbar = this.Quill.getModule(\"toolbar\");\r\n        toolbar.addHandler(\"image\", (value) => {\r\n          if (value) {\r\n            this.$refs.upload.$children[0].$refs.input.click();\r\n          } else {\r\n            this.quill.format(\"image\", false);\r\n          }\r\n        });\r\n      }\r\n      this.Quill.clipboard.dangerouslyPasteHTML(this.currentValue);\r\n      this.Quill.on(\"text-change\", (delta, oldDelta, source) => {\r\n        const html = this.$refs.editor.children[0].innerHTML;\r\n        const text = this.Quill.getText();\r\n        const quill = this.Quill;\r\n        this.currentValue = html;\r\n        this.$emit(\"input\", html);\r\n        this.$emit(\"on-change\", { html, text, quill });\r\n      });\r\n      this.Quill.on(\"text-change\", (delta, oldDelta, source) => {\r\n        this.$emit(\"on-text-change\", delta, oldDelta, source);\r\n      });\r\n      this.Quill.on(\"selection-change\", (range, oldRange, source) => {\r\n        this.$emit(\"on-selection-change\", range, oldRange, source);\r\n      });\r\n      this.Quill.on(\"editor-change\", (eventName, ...args) => {\r\n        this.$emit(\"on-editor-change\", eventName, ...args);\r\n      });\r\n    },\r\n    // 上传前校检格式和大小\r\n    handleBeforeUpload(file) {\r\n      const type = [\"image/jpeg\", \"image/jpg\", \"image/png\", \"image/svg\"];\r\n      const isJPG = type.includes(file.type);\r\n      // 检验文件格式\r\n      if (!isJPG) {\r\n        this.$message.error(`图片格式错误!`);\r\n        return false;\r\n      }\r\n      // 校检文件大小\r\n      if (this.fileSize) {\r\n        const isLt = file.size / 1024 / 1024 < this.fileSize;\r\n        if (!isLt) {\r\n          this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`);\r\n          return false;\r\n        }\r\n      }\r\n      return true;\r\n    },\r\n    handleUploadSuccess(res, file) {\r\n      // 如果上传成功\r\n      if (res.code == 200) {\r\n        // 获取富文本组件实例\r\n        let quill = this.Quill;\r\n        // 获取光标所在位置\r\n        let length = quill.getSelection().index;\r\n        // 插入图片  res.url为服务器返回的图片地址\r\n        quill.insertEmbed(length, \"image\", process.env.VUE_APP_BASE_API + res.fileName);\r\n        // 调整光标到最后\r\n        quill.setSelection(length + 1);\r\n      } else {\r\n        this.$message.error(\"图片插入失败\");\r\n      }\r\n    },\r\n    handleUploadError() {\r\n      this.$message.error(\"图片插入失败\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.editor, .ql-toolbar {\r\n  white-space: pre-wrap !important;\r\n  line-height: normal !important;\r\n}\r\n.quill-img {\r\n  display: none;\r\n}\r\n.ql-snow .ql-tooltip[data-mode=\"link\"]::before {\r\n  content: \"请输入链接地址:\";\r\n}\r\n.ql-snow .ql-tooltip.ql-editing a.ql-action::after {\r\n  border-right: 0px;\r\n  content: \"保存\";\r\n  padding-right: 0px;\r\n}\r\n.ql-snow .ql-tooltip[data-mode=\"video\"]::before {\r\n  content: \"请输入视频地址:\";\r\n}\r\n.ql-snow .ql-picker.ql-size .ql-picker-label::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item::before {\r\n  content: \"14px\";\r\n}\r\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"small\"]::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"small\"]::before {\r\n  content: \"10px\";\r\n}\r\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"large\"]::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"large\"]::before {\r\n  content: \"18px\";\r\n}\r\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"huge\"]::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"huge\"]::before {\r\n  content: \"32px\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item::before {\r\n  content: \"文本\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"1\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"1\"]::before {\r\n  content: \"标题1\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"2\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"2\"]::before {\r\n  content: \"标题2\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"3\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"3\"]::before {\r\n  content: \"标题3\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"4\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"4\"]::before {\r\n  content: \"标题4\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"5\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"5\"]::before {\r\n  content: \"标题5\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"6\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"6\"]::before {\r\n  content: \"标题6\";\r\n}\r\n.ql-snow .ql-picker.ql-font .ql-picker-label::before,\r\n.ql-snow .ql-picker.ql-font .ql-picker-item::before {\r\n  content: \"标准字体\";\r\n}\r\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"serif\"]::before,\r\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"serif\"]::before {\r\n  content: \"衬线字体\";\r\n}\r\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"monospace\"]::before,\r\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"monospace\"]::before {\r\n  content: \"等宽字体\";\r\n}\r\n</style>\r\n"]}]}