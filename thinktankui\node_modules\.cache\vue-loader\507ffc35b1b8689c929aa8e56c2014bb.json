{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\redirect.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\redirect.vue", "mtime": 1749104047642}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIGNyZWF0ZWQoKSB7DQogICAgY29uc3QgeyBwYXJhbXMsIHF1ZXJ5IH0gPSB0aGlzLiRyb3V0ZQ0KICAgIGNvbnN0IHsgcGF0aCB9ID0gcGFyYW1zDQogICAgdGhpcy4kcm91dGVyLnJlcGxhY2UoeyBwYXRoOiAnLycgKyBwYXRoLCBxdWVyeSB9KQ0KICB9LA0KICByZW5kZXI6IGZ1bmN0aW9uKGgpIHsNCiAgICByZXR1cm4gaCgpIC8vIGF2b2lkIHdhcm5pbmcgbWVzc2FnZQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["redirect.vue"], "names": [], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "redirect.vue", "sourceRoot": "src/views", "sourcesContent": ["<script>\r\nexport default {\r\n  created() {\r\n    const { params, query } = this.$route\r\n    const { path } = params\r\n    this.$router.replace({ path: '/' + path, query })\r\n  },\r\n  render: function(h) {\r\n    return h() // avoid warning message\r\n  }\r\n}\r\n</script>\r\n"]}]}