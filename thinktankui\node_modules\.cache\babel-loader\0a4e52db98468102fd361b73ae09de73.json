{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\monitor\\operlog\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\monitor\\operlog\\index.vue", "mtime": 1749104047642}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfb2JqZWN0U3ByZWFkMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovdGhpbmt0YW5rL3RoaW5rdGFua3VpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL29iamVjdFNwcmVhZDIuanMiKSk7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnNvcnQuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5jb25zdHJ1Y3Rvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLm1hcC5qcyIpOwp2YXIgX29wZXJsb2cgPSByZXF1aXJlKCJAL2FwaS9tb25pdG9yL29wZXJsb2ciKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICJPcGVybG9nIiwKICBkaWN0czogWydzeXNfb3Blcl90eXBlJywgJ3N5c19jb21tb25fc3RhdHVzJ10sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDooajmoLzmlbDmja4KICAgICAgbGlzdDogW10sCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgLy8g5pel5pyf6IyD5Zu0CiAgICAgIGRhdGVSYW5nZTogW10sCiAgICAgIC8vIOm7mOiupOaOkuW6jwogICAgICBkZWZhdWx0U29ydDogewogICAgICAgIHByb3A6ICdvcGVyVGltZScsCiAgICAgICAgb3JkZXI6ICdkZXNjZW5kaW5nJwogICAgICB9LAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIG9wZXJJcDogdW5kZWZpbmVkLAogICAgICAgIHRpdGxlOiB1bmRlZmluZWQsCiAgICAgICAgb3Blck5hbWU6IHVuZGVmaW5lZCwKICAgICAgICBidXNpbmVzc1R5cGU6IHVuZGVmaW5lZCwKICAgICAgICBzdGF0dXM6IHVuZGVmaW5lZAogICAgICB9CiAgICB9OwogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOafpeivoueZu+W9leaXpeW/lyAqL2dldExpc3Q6IGZ1bmN0aW9uIGdldExpc3QoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgICgwLCBfb3BlcmxvZy5saXN0KSh0aGlzLmFkZERhdGVSYW5nZSh0aGlzLnF1ZXJ5UGFyYW1zLCB0aGlzLmRhdGVSYW5nZSkpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMubGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgX3RoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOaTjeS9nOaXpeW/l+exu+Wei+Wtl+WFuOe/u+ivkQogICAgdHlwZUZvcm1hdDogZnVuY3Rpb24gdHlwZUZvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWwodGhpcy5kaWN0LnR5cGUuc3lzX29wZXJfdHlwZSwgcm93LmJ1c2luZXNzVHlwZSk7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqL2hhbmRsZVF1ZXJ5OiBmdW5jdGlvbiBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqL3Jlc2V0UXVlcnk6IGZ1bmN0aW9uIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gW107CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy4kcmVmcy50YWJsZXMuc29ydCh0aGlzLmRlZmF1bHRTb3J0LnByb3AsIHRoaXMuZGVmYXVsdFNvcnQub3JkZXIpOwogICAgfSwKICAgIC8qKiDlpJrpgInmoYbpgInkuK3mlbDmja4gKi9oYW5kbGVTZWxlY3Rpb25DaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgcmV0dXJuIGl0ZW0ub3BlcklkOwogICAgICB9KTsKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOwogICAgfSwKICAgIC8qKiDmjpLluo/op6blj5Hkuovku7YgKi9oYW5kbGVTb3J0Q2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTb3J0Q2hhbmdlKGNvbHVtbiwgcHJvcCwgb3JkZXIpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5vcmRlckJ5Q29sdW1uID0gY29sdW1uLnByb3A7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuaXNBc2MgPSBjb2x1bW4ub3JkZXI7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDor6bnu4bmjInpkq7mk43kvZwgKi9oYW5kbGVWaWV3OiBmdW5jdGlvbiBoYW5kbGVWaWV3KHJvdykgewogICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICB0aGlzLmZvcm0gPSByb3c7CiAgICB9LAogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqL2hhbmRsZURlbGV0ZTogZnVuY3Rpb24gaGFuZGxlRGVsZXRlKHJvdykgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgdmFyIG9wZXJJZHMgPSByb3cub3BlcklkIHx8IHRoaXMuaWRzOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTml6Xlv5fnvJblj7fkuLoiJyArIG9wZXJJZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiAoMCwgX29wZXJsb2cuZGVsT3BlcmxvZykob3Blcklkcyk7CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzMi5nZXRMaXN0KCk7CiAgICAgICAgX3RoaXMyLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkge30pOwogICAgfSwKICAgIC8qKiDmuIXnqbrmjInpkq7mk43kvZwgKi9oYW5kbGVDbGVhbjogZnVuY3Rpb24gaGFuZGxlQ2xlYW4oKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTmuIXnqbrmiYDmnInmk43kvZzml6Xlv5fmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gKDAsIF9vcGVybG9nLmNsZWFuT3BlcmxvZykoKTsKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMzLmdldExpc3QoKTsKICAgICAgICBfdGhpczMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIua4heepuuaIkOWKnyIpOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7fSk7CiAgICB9LAogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqL2hhbmRsZUV4cG9ydDogZnVuY3Rpb24gaGFuZGxlRXhwb3J0KCkgewogICAgICB0aGlzLmRvd25sb2FkKCdtb25pdG9yL29wZXJsb2cvZXhwb3J0JywgKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCB0aGlzLnF1ZXJ5UGFyYW1zKSwgIm9wZXJsb2dfIi5jb25jYXQobmV3IERhdGUoKS5nZXRUaW1lKCksICIueGxzeCIpKTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_operlog", "require", "name", "dicts", "data", "loading", "ids", "multiple", "showSearch", "total", "list", "open", "date<PERSON><PERSON><PERSON>", "defaultSort", "prop", "order", "form", "queryParams", "pageNum", "pageSize", "operIp", "undefined", "title", "operName", "businessType", "status", "created", "getList", "methods", "_this", "addDateRange", "then", "response", "rows", "typeFormat", "row", "column", "selectDictLabel", "dict", "type", "sys_oper_type", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "$refs", "tables", "sort", "handleSelectionChange", "selection", "map", "item", "operId", "length", "handleSortChange", "orderByColumn", "isAsc", "handleView", "handleDelete", "_this2", "operIds", "$modal", "confirm", "delOperlog", "msgSuccess", "catch", "handleClean", "_this3", "cleanOperlog", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime"], "sources": ["src/views/monitor/operlog/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"操作地址\" prop=\"operIp\">\r\n        <el-input\r\n          v-model=\"queryParams.operIp\"\r\n          placeholder=\"请输入操作地址\"\r\n          clearable\r\n          style=\"width: 240px;\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"系统模块\" prop=\"title\">\r\n        <el-input\r\n          v-model=\"queryParams.title\"\r\n          placeholder=\"请输入系统模块\"\r\n          clearable\r\n          style=\"width: 240px;\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"操作人员\" prop=\"operName\">\r\n        <el-input\r\n          v-model=\"queryParams.operName\"\r\n          placeholder=\"请输入操作人员\"\r\n          clearable\r\n          style=\"width: 240px;\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"类型\" prop=\"businessType\">\r\n        <el-select\r\n          v-model=\"queryParams.businessType\"\r\n          placeholder=\"操作类型\"\r\n          clearable\r\n          style=\"width: 240px\"\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_oper_type\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select\r\n          v-model=\"queryParams.status\"\r\n          placeholder=\"操作状态\"\r\n          clearable\r\n          style=\"width: 240px\"\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_common_status\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"操作时间\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          :default-time=\"['00:00:00', '23:59:59']\"\r\n        ></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['monitor:operlog:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          @click=\"handleClean\"\r\n          v-hasPermi=\"['monitor:operlog:remove']\"\r\n        >清空</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['monitor:operlog:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table ref=\"tables\" v-loading=\"loading\" :data=\"list\" @selection-change=\"handleSelectionChange\" :default-sort=\"defaultSort\" @sort-change=\"handleSortChange\">\r\n      <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\r\n      <el-table-column label=\"日志编号\" align=\"center\" prop=\"operId\" />\r\n      <el-table-column label=\"系统模块\" align=\"center\" prop=\"title\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"操作类型\" align=\"center\" prop=\"businessType\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_oper_type\" :value=\"scope.row.businessType\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作人员\" align=\"center\" prop=\"operName\" width=\"110\" :show-overflow-tooltip=\"true\" sortable=\"custom\" :sort-orders=\"['descending', 'ascending']\" />\r\n      <el-table-column label=\"操作地址\" align=\"center\" prop=\"operIp\" width=\"130\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"操作地点\" align=\"center\" prop=\"operLocation\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"操作状态\" align=\"center\" prop=\"status\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_common_status\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作日期\" align=\"center\" prop=\"operTime\" width=\"160\" sortable=\"custom\" :sort-orders=\"['descending', 'ascending']\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.operTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"消耗时间\" align=\"center\" prop=\"costTime\" width=\"110\" :show-overflow-tooltip=\"true\" sortable=\"custom\" :sort-orders=\"['descending', 'ascending']\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ scope.row.costTime }}毫秒</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleView(scope.row,scope.index)\"\r\n            v-hasPermi=\"['monitor:operlog:query']\"\r\n          >详细</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 操作日志详细 -->\r\n    <el-dialog title=\"操作日志详细\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"100px\" size=\"mini\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"操作模块：\">{{ form.title }} / {{ typeFormat(form) }}</el-form-item>\r\n            <el-form-item\r\n              label=\"登录信息：\"\r\n            >{{ form.operName }} / {{ form.operIp }} / {{ form.operLocation }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"请求地址：\">{{ form.operUrl }}</el-form-item>\r\n            <el-form-item label=\"请求方式：\">{{ form.requestMethod }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"操作方法：\">{{ form.method }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"请求参数：\">{{ form.operParam }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"返回参数：\">{{ form.jsonResult }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"操作状态：\">\r\n              <div v-if=\"form.status === 0\">正常</div>\r\n              <div v-else-if=\"form.status === 1\">失败</div>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"消耗时间：\">{{ form.costTime }}毫秒</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"操作时间：\">{{ parseTime(form.operTime) }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"异常信息：\" v-if=\"form.status === 1\">{{ form.errorMsg }}</el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"open = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { list, delOperlog, cleanOperlog } from \"@/api/monitor/operlog\";\r\n\r\nexport default {\r\n  name: \"Operlog\",\r\n  dicts: ['sys_oper_type', 'sys_common_status'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 表格数据\r\n      list: [],\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 默认排序\r\n      defaultSort: {prop: 'operTime', order: 'descending'},\r\n      // 表单参数\r\n      form: {},\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        operIp: undefined,\r\n        title: undefined,\r\n        operName: undefined,\r\n        businessType: undefined,\r\n        status: undefined\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询登录日志 */\r\n    getList() {\r\n      this.loading = true;\r\n      list(this.addDateRange(this.queryParams, this.dateRange)).then( response => {\r\n          this.list = response.rows;\r\n          this.total = response.total;\r\n          this.loading = false;\r\n        }\r\n      );\r\n    },\r\n    // 操作日志类型字典翻译\r\n    typeFormat(row, column) {\r\n      return this.selectDictLabel(this.dict.type.sys_oper_type, row.businessType);\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.queryParams.pageNum = 1;\r\n      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order)\r\n    },\r\n    /** 多选框选中数据 */\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.operId)\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 排序触发事件 */\r\n    handleSortChange(column, prop, order) {\r\n      this.queryParams.orderByColumn = column.prop;\r\n      this.queryParams.isAsc = column.order;\r\n      this.getList();\r\n    },\r\n    /** 详细按钮操作 */\r\n    handleView(row) {\r\n      this.open = true;\r\n      this.form = row;\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const operIds = row.operId || this.ids;\r\n      this.$modal.confirm('是否确认删除日志编号为\"' + operIds + '\"的数据项？').then(function() {\r\n        return delOperlog(operIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 清空按钮操作 */\r\n    handleClean() {\r\n      this.$modal.confirm('是否确认清空所有操作日志数据项？').then(function() {\r\n        return cleanOperlog();\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"清空成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('monitor/operlog/export', {\r\n        ...this.queryParams\r\n      }, `operlog_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n"], "mappings": ";;;;;;;;;;;;;AAiNA,IAAAA,QAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,IAAA;MACA;MACAC,SAAA;MACA;MACAC,WAAA;QAAAC,IAAA;QAAAC,KAAA;MAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA,EAAAC,SAAA;QACAC,KAAA,EAAAD,SAAA;QACAE,QAAA,EAAAF,SAAA;QACAG,YAAA,EAAAH,SAAA;QACAI,MAAA,EAAAJ;MACA;IACA;EACA;EACAK,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,aACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAxB,OAAA;MACA,IAAAK,aAAA,OAAAoB,YAAA,MAAAb,WAAA,OAAAL,SAAA,GAAAmB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAnB,IAAA,GAAAsB,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAApB,KAAA,GAAAuB,QAAA,CAAAvB,KAAA;QACAoB,KAAA,CAAAxB,OAAA;MACA,CACA;IACA;IACA;IACA6B,UAAA,WAAAA,WAAAC,GAAA,EAAAC,MAAA;MACA,YAAAC,eAAA,MAAAC,IAAA,CAAAC,IAAA,CAAAC,aAAA,EAAAL,GAAA,CAAAX,YAAA;IACA;IACA,aACAiB,WAAA,WAAAA,YAAA;MACA,KAAAxB,WAAA,CAAAC,OAAA;MACA,KAAAS,OAAA;IACA;IACA,aACAe,UAAA,WAAAA,WAAA;MACA,KAAA9B,SAAA;MACA,KAAA+B,SAAA;MACA,KAAA1B,WAAA,CAAAC,OAAA;MACA,KAAA0B,KAAA,CAAAC,MAAA,CAAAC,IAAA,MAAAjC,WAAA,CAAAC,IAAA,OAAAD,WAAA,CAAAE,KAAA;IACA;IACA,cACAgC,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA1C,GAAA,GAAA0C,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,MAAA;MAAA;MACA,KAAA5C,QAAA,IAAAyC,SAAA,CAAAI,MAAA;IACA;IACA,aACAC,gBAAA,WAAAA,iBAAAjB,MAAA,EAAAtB,IAAA,EAAAC,KAAA;MACA,KAAAE,WAAA,CAAAqC,aAAA,GAAAlB,MAAA,CAAAtB,IAAA;MACA,KAAAG,WAAA,CAAAsC,KAAA,GAAAnB,MAAA,CAAArB,KAAA;MACA,KAAAY,OAAA;IACA;IACA,aACA6B,UAAA,WAAAA,WAAArB,GAAA;MACA,KAAAxB,IAAA;MACA,KAAAK,IAAA,GAAAmB,GAAA;IACA;IACA,aACAsB,YAAA,WAAAA,aAAAtB,GAAA;MAAA,IAAAuB,MAAA;MACA,IAAAC,OAAA,GAAAxB,GAAA,CAAAgB,MAAA,SAAA7C,GAAA;MACA,KAAAsD,MAAA,CAAAC,OAAA,kBAAAF,OAAA,aAAA5B,IAAA;QACA,WAAA+B,mBAAA,EAAAH,OAAA;MACA,GAAA5B,IAAA;QACA2B,MAAA,CAAA/B,OAAA;QACA+B,MAAA,CAAAE,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAN,MAAA,CAAAC,OAAA,qBAAA9B,IAAA;QACA,WAAAoC,qBAAA;MACA,GAAApC,IAAA;QACAmC,MAAA,CAAAvC,OAAA;QACAuC,MAAA,CAAAN,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA;IACA;IACA,aACAI,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,+BAAAC,cAAA,CAAAC,OAAA,MACA,KAAAtD,WAAA,cAAAuD,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}