{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\monitor\\job\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\monitor\\job\\index.vue", "mtime": 1749104047642}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_job", "require", "_Crontab", "_interopRequireDefault", "components", "Crontab", "name", "dicts", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "jobList", "title", "open", "openView", "openCron", "expression", "queryParams", "pageNum", "pageSize", "job<PERSON>ame", "undefined", "jobGroup", "status", "form", "rules", "required", "message", "trigger", "invoke<PERSON><PERSON><PERSON>", "cronExpression", "created", "getList", "methods", "_this", "listJob", "then", "response", "rows", "jobGroupFormat", "row", "column", "selectDictLabel", "dict", "type", "sys_job_group", "jobExecutorFormat", "sys_job_executor", "jobExecutor", "cancel", "reset", "jobId", "misfirePolicy", "concurrent", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleCommand", "command", "handleRun", "handleView", "handleJobLog", "handleStatusChange", "_this2", "text", "$modal", "confirm", "changeJobStatus", "msgSuccess", "catch", "_this3", "runJob", "_this4", "get<PERSON>ob", "handleShowCron", "crontabFill", "value", "$router", "push", "handleAdd", "handleUpdate", "_this5", "submitForm", "_this6", "$refs", "validate", "valid", "updateJob", "addJob", "handleDelete", "_this7", "jobIds", "<PERSON><PERSON><PERSON>", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime"], "sources": ["src/views/monitor/job/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"任务名称\" prop=\"jobName\">\r\n        <el-input\r\n          v-model=\"queryParams.jobName\"\r\n          placeholder=\"请输入任务名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"任务组名\" prop=\"jobGroup\">\r\n        <el-select v-model=\"queryParams.jobGroup\" placeholder=\"请选择任务组名\" clearable>\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_job_group\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"任务状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择任务状态\" clearable>\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_job_status\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['monitor:job:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['monitor:job:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['monitor:job:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['monitor:job:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"info\"\r\n          plain\r\n          icon=\"el-icon-s-operation\"\r\n          size=\"mini\"\r\n          @click=\"handleJobLog\"\r\n          v-hasPermi=\"['monitor:job:query']\"\r\n        >日志</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"jobList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"任务编号\" width=\"100\" align=\"center\" prop=\"jobId\" />\r\n      <el-table-column label=\"任务名称\" align=\"center\" prop=\"jobName\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"任务组名\" align=\"center\" prop=\"jobGroup\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_job_group\" :value=\"scope.row.jobGroup\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"调用目标字符串\" align=\"center\" prop=\"invokeTarget\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"cron执行表达式\" align=\"center\" prop=\"cronExpression\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"状态\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <el-switch\r\n            v-model=\"scope.row.status\"\r\n            active-value=\"0\"\r\n            inactive-value=\"1\"\r\n            @change=\"handleStatusChange(scope.row)\"\r\n          ></el-switch>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['monitor:job:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['monitor:job:remove']\"\r\n          >删除</el-button>\r\n          <el-dropdown size=\"mini\" @command=\"(command) => handleCommand(command, scope.row)\" v-hasPermi=\"['monitor:job:changeStatus', 'monitor:job:query']\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-d-arrow-right\">更多</el-button>\r\n            <el-dropdown-menu slot=\"dropdown\">\r\n              <el-dropdown-item command=\"handleRun\" icon=\"el-icon-caret-right\"\r\n                v-hasPermi=\"['monitor:job:changeStatus']\">执行一次</el-dropdown-item>\r\n              <el-dropdown-item command=\"handleView\" icon=\"el-icon-view\"\r\n                v-hasPermi=\"['monitor:job:query']\">任务详细</el-dropdown-item>\r\n              <el-dropdown-item command=\"handleJobLog\" icon=\"el-icon-s-operation\"\r\n                v-hasPermi=\"['monitor:job:query']\">调度日志</el-dropdown-item>\r\n            </el-dropdown-menu>\r\n          </el-dropdown>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改定时任务对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"任务名称\" prop=\"jobName\">\r\n              <el-input v-model=\"form.jobName\" placeholder=\"请输入任务名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"任务分组\" prop=\"jobGroup\">\r\n              <el-select v-model=\"form.jobGroup\" placeholder=\"请选择任务分组\">\r\n                <el-option\r\n                  v-for=\"dict in dict.type.sys_job_group\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                ></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item prop=\"jobGroup\">\r\n              <span slot=\"label\">\r\n                任务执行器\r\n                <el-tooltip placement=\"top\">\r\n                  <div slot=\"content\">\r\n                    调用方法为异步函数时此选项无效\r\n                  </div>\r\n                  <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n              </span>\r\n              <el-select v-model=\"form.jobExecutor\" placeholder=\"请选择任务执行器\">\r\n                <el-option\r\n                  v-for=\"dict in dict.type.sys_job_executor\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                ></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item prop=\"invokeTarget\">\r\n              <span slot=\"label\">\r\n                调用方法\r\n                <el-tooltip placement=\"top\">\r\n                  <div slot=\"content\">\r\n                    调用示例：module_task.scheduler_test.job\r\n                  </div>\r\n                  <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n              </span>\r\n              <el-input v-model=\"form.invokeTarget\" placeholder=\"请输入调用目标字符串\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"位置参数\" prop=\"jobArgs\">\r\n              <el-input v-model=\"form.jobArgs\" placeholder=\"请输入位置参数\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"关键字参数\" prop=\"jobKwargs\">\r\n              <el-input v-model=\"form.jobKwargs\" placeholder=\"请输入关键字参数\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"cron表达式\" prop=\"cronExpression\">\r\n              <el-input v-model=\"form.cronExpression\" placeholder=\"请输入cron执行表达式\">\r\n                <template slot=\"append\">\r\n                  <el-button type=\"primary\" @click=\"handleShowCron\">\r\n                    生成表达式\r\n                    <i class=\"el-icon-time el-icon--right\"></i>\r\n                  </el-button>\r\n                </template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\" v-if=\"form.jobId !== undefined\">\r\n            <el-form-item label=\"状态\">\r\n              <el-radio-group v-model=\"form.status\">\r\n                <el-radio\r\n                  v-for=\"dict in dict.type.sys_job_status\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.value\"\r\n                >{{dict.label}}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"执行策略\" prop=\"misfirePolicy\">\r\n              <el-radio-group v-model=\"form.misfirePolicy\" size=\"small\">\r\n                <el-radio-button label=\"1\">立即执行</el-radio-button>\r\n                <el-radio-button label=\"2\">执行一次</el-radio-button>\r\n                <el-radio-button label=\"3\">放弃执行</el-radio-button>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"是否并发\" prop=\"concurrent\">\r\n              <el-radio-group v-model=\"form.concurrent\" size=\"small\">\r\n                <el-radio-button label=\"0\">允许</el-radio-button>\r\n                <el-radio-button label=\"1\">禁止</el-radio-button>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"Cron表达式生成器\" :visible.sync=\"openCron\" append-to-body destroy-on-close class=\"scrollbar\">\r\n      <crontab @hide=\"openCron=false\" @fill=\"crontabFill\" :expression=\"expression\"></crontab>\r\n    </el-dialog>\r\n\r\n    <!-- 任务日志详细 -->\r\n    <el-dialog title=\"任务详细\" :visible.sync=\"openView\" width=\"700px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"120px\" size=\"mini\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"任务编号：\">{{ form.jobId }}</el-form-item>\r\n            <el-form-item label=\"任务名称：\">{{ form.jobName }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"任务分组：\">{{ jobGroupFormat(form) }}</el-form-item>\r\n            <el-form-item label=\"创建时间：\">{{ parseTime(form.createTime) }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"cron表达式：\">{{ form.cronExpression }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"下次执行时间：\">{{ parseTime(form.nextValidTime) }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"任务执行器：\">{{ jobExecutorFormat(form) }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"调用目标方法：\">{{ form.invokeTarget }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"位置参数：\">{{ form.jobArgs }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"关键字参数：\">{{ form.jobKwargs }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"任务状态：\">\r\n              <div v-if=\"form.status == 0\">正常</div>\r\n              <div v-else-if=\"form.status == 1\">暂停</div>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"是否并发：\">\r\n              <div v-if=\"form.concurrent == '0'\">允许</div>\r\n              <div v-else-if=\"form.concurrent == '1'\">禁止</div>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"执行策略：\">\r\n              <div v-if=\"form.misfirePolicy == '0'\">默认策略</div>\r\n              <div v-else-if=\"form.misfirePolicy == '1'\">立即执行</div>\r\n              <div v-else-if=\"form.misfirePolicy == '2'\">执行一次</div>\r\n              <div v-else-if=\"form.misfirePolicy == '3'\">放弃执行</div>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"openView = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listJob, getJob, delJob, addJob, updateJob, runJob, changeJobStatus } from \"@/api/monitor/job\";\r\nimport Crontab from '@/components/Crontab'\r\n\r\nexport default {\r\n  components: { Crontab },\r\n  name: \"Job\",\r\n  dicts: ['sys_job_group', 'sys_job_status', 'sys_job_executor'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 定时任务表格数据\r\n      jobList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否显示详细弹出层\r\n      openView: false,\r\n      // 是否显示Cron表达式弹出层\r\n      openCron: false,\r\n      // 传入的表达式\r\n      expression: \"\",\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        jobName: undefined,\r\n        jobGroup: undefined,\r\n        status: undefined\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        jobName: [\r\n          { required: true, message: \"任务名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        invokeTarget: [\r\n          { required: true, message: \"调用目标字符串不能为空\", trigger: \"blur\" }\r\n        ],\r\n        cronExpression: [\r\n          { required: true, message: \"cron执行表达式不能为空\", trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询定时任务列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listJob(this.queryParams).then(response => {\r\n        this.jobList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 任务组名字典翻译\r\n    jobGroupFormat(row, column) {\r\n      return this.selectDictLabel(this.dict.type.sys_job_group, row.jobGroup);\r\n    },\r\n    // 任务执行器名字典翻译\r\n    jobExecutorFormat(row, column) {\r\n      return this.selectDictLabel(this.dict.type.sys_job_executor, row.jobExecutor);\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        jobId: undefined,\r\n        jobName: undefined,\r\n        jobGroup: undefined,\r\n        invokeTarget: undefined,\r\n        cronExpression: undefined,\r\n        misfirePolicy: \"1\",\r\n        concurrent: \"1\",\r\n        status: \"1\"\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.jobId);\r\n      this.single = selection.length != 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    // 更多操作触发\r\n    handleCommand(command, row) {\r\n      switch (command) {\r\n        case \"handleRun\":\r\n          this.handleRun(row);\r\n          break;\r\n        case \"handleView\":\r\n          this.handleView(row);\r\n          break;\r\n        case \"handleJobLog\":\r\n          this.handleJobLog(row);\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n    // 任务状态修改\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\r\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.jobName + '\"任务吗？').then(function() {\r\n        return changeJobStatus(row.jobId, row.status);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function() {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    /* 立即执行一次 */\r\n    handleRun(row) {\r\n      this.$modal.confirm('确认要立即执行一次\"' + row.jobName + '\"任务吗？').then(function() {\r\n        return runJob(row.jobId, row.jobGroup);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(\"执行成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 任务详细信息 */\r\n    handleView(row) {\r\n      getJob(row.jobId).then(response => {\r\n        this.form = response.data;\r\n        this.openView = true;\r\n      });\r\n    },\r\n    /** cron表达式按钮操作 */\r\n    handleShowCron() {\r\n      this.expression = this.form.cronExpression;\r\n      this.openCron = true;\r\n    },\r\n    /** 确定后回传值 */\r\n    crontabFill(value) {\r\n      this.form.cronExpression = value;\r\n    },\r\n    /** 任务日志列表查询 */\r\n    handleJobLog(row) {\r\n      const jobId = row.jobId || 0;\r\n      this.$router.push('/monitor/job-log/index/' + jobId)\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加任务\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const jobId = row.jobId || this.ids;\r\n      getJob(jobId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改任务\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.jobId != undefined) {\r\n            updateJob(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addJob(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const jobIds = row.jobId || this.ids;\r\n      this.$modal.confirm('是否确认删除定时任务编号为\"' + jobIds + '\"的数据项？').then(function() {\r\n        return delJob(jobIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('monitor/job/export', {\r\n        ...this.queryParams\r\n      }, `job_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;AA2UA,IAAAA,IAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,UAAA;IAAAC,OAAA,EAAAA;EAAA;EACAC,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,OAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,QAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,OAAA,EAAAC,SAAA;QACAC,QAAA,EAAAD,SAAA;QACAE,MAAA,EAAAF;MACA;MACA;MACAG,IAAA;MACA;MACAC,KAAA;QACAL,OAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,YAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,cAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,eACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAA7B,OAAA;MACA,IAAA8B,YAAA,OAAAlB,WAAA,EAAAmB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAvB,OAAA,GAAA0B,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAxB,KAAA,GAAA2B,QAAA,CAAA3B,KAAA;QACAwB,KAAA,CAAA7B,OAAA;MACA;IACA;IACA;IACAkC,cAAA,WAAAA,eAAAC,GAAA,EAAAC,MAAA;MACA,YAAAC,eAAA,MAAAC,IAAA,CAAAC,IAAA,CAAAC,aAAA,EAAAL,GAAA,CAAAlB,QAAA;IACA;IACA;IACAwB,iBAAA,WAAAA,kBAAAN,GAAA,EAAAC,MAAA;MACA,YAAAC,eAAA,MAAAC,IAAA,CAAAC,IAAA,CAAAG,gBAAA,EAAAP,GAAA,CAAAQ,WAAA;IACA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAApC,IAAA;MACA,KAAAqC,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA1B,IAAA;QACA2B,KAAA,EAAA9B,SAAA;QACAD,OAAA,EAAAC,SAAA;QACAC,QAAA,EAAAD,SAAA;QACAQ,YAAA,EAAAR,SAAA;QACAS,cAAA,EAAAT,SAAA;QACA+B,aAAA;QACAC,UAAA;QACA9B,MAAA;MACA;MACA,KAAA+B,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAtC,WAAA,CAAAC,OAAA;MACA,KAAAc,OAAA;IACA;IACA,aACAwB,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAApD,GAAA,GAAAoD,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAT,KAAA;MAAA;MACA,KAAA5C,MAAA,GAAAmD,SAAA,CAAAG,MAAA;MACA,KAAArD,QAAA,IAAAkD,SAAA,CAAAG,MAAA;IACA;IACA;IACAC,aAAA,WAAAA,cAAAC,OAAA,EAAAvB,GAAA;MACA,QAAAuB,OAAA;QACA;UACA,KAAAC,SAAA,CAAAxB,GAAA;UACA;QACA;UACA,KAAAyB,UAAA,CAAAzB,GAAA;UACA;QACA;UACA,KAAA0B,YAAA,CAAA1B,GAAA;UACA;QACA;UACA;MACA;IACA;IACA;IACA2B,kBAAA,WAAAA,mBAAA3B,GAAA;MAAA,IAAA4B,MAAA;MACA,IAAAC,IAAA,GAAA7B,GAAA,CAAAjB,MAAA;MACA,KAAA+C,MAAA,CAAAC,OAAA,UAAAF,IAAA,UAAA7B,GAAA,CAAApB,OAAA,YAAAgB,IAAA;QACA,WAAAoC,oBAAA,EAAAhC,GAAA,CAAAW,KAAA,EAAAX,GAAA,CAAAjB,MAAA;MACA,GAAAa,IAAA;QACAgC,MAAA,CAAAE,MAAA,CAAAG,UAAA,CAAAJ,IAAA;MACA,GAAAK,KAAA;QACAlC,GAAA,CAAAjB,MAAA,GAAAiB,GAAA,CAAAjB,MAAA;MACA;IACA;IACA,YACAyC,SAAA,WAAAA,UAAAxB,GAAA;MAAA,IAAAmC,MAAA;MACA,KAAAL,MAAA,CAAAC,OAAA,gBAAA/B,GAAA,CAAApB,OAAA,YAAAgB,IAAA;QACA,WAAAwC,WAAA,EAAApC,GAAA,CAAAW,KAAA,EAAAX,GAAA,CAAAlB,QAAA;MACA,GAAAc,IAAA;QACAuC,MAAA,CAAAL,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA;IACA;IACA,aACAT,UAAA,WAAAA,WAAAzB,GAAA;MAAA,IAAAqC,MAAA;MACA,IAAAC,WAAA,EAAAtC,GAAA,CAAAW,KAAA,EAAAf,IAAA,WAAAC,QAAA;QACAwC,MAAA,CAAArD,IAAA,GAAAa,QAAA,CAAAjC,IAAA;QACAyE,MAAA,CAAA/D,QAAA;MACA;IACA;IACA,kBACAiE,cAAA,WAAAA,eAAA;MACA,KAAA/D,UAAA,QAAAQ,IAAA,CAAAM,cAAA;MACA,KAAAf,QAAA;IACA;IACA,aACAiE,WAAA,WAAAA,YAAAC,KAAA;MACA,KAAAzD,IAAA,CAAAM,cAAA,GAAAmD,KAAA;IACA;IACA,eACAf,YAAA,WAAAA,aAAA1B,GAAA;MACA,IAAAW,KAAA,GAAAX,GAAA,CAAAW,KAAA;MACA,KAAA+B,OAAA,CAAAC,IAAA,6BAAAhC,KAAA;IACA;IACA,aACAiC,SAAA,WAAAA,UAAA;MACA,KAAAlC,KAAA;MACA,KAAArC,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAyE,YAAA,WAAAA,aAAA7C,GAAA;MAAA,IAAA8C,MAAA;MACA,KAAApC,KAAA;MACA,IAAAC,KAAA,GAAAX,GAAA,CAAAW,KAAA,SAAA7C,GAAA;MACA,IAAAwE,WAAA,EAAA3B,KAAA,EAAAf,IAAA,WAAAC,QAAA;QACAiD,MAAA,CAAA9D,IAAA,GAAAa,QAAA,CAAAjC,IAAA;QACAkF,MAAA,CAAAzE,IAAA;QACAyE,MAAA,CAAA1E,KAAA;MACA;IACA;IACA;IACA2E,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAhE,IAAA,CAAA2B,KAAA,IAAA9B,SAAA;YACA,IAAAuE,cAAA,EAAAJ,MAAA,CAAAhE,IAAA,EAAAY,IAAA,WAAAC,QAAA;cACAmD,MAAA,CAAAlB,MAAA,CAAAG,UAAA;cACAe,MAAA,CAAA3E,IAAA;cACA2E,MAAA,CAAAxD,OAAA;YACA;UACA;YACA,IAAA6D,WAAA,EAAAL,MAAA,CAAAhE,IAAA,EAAAY,IAAA,WAAAC,QAAA;cACAmD,MAAA,CAAAlB,MAAA,CAAAG,UAAA;cACAe,MAAA,CAAA3E,IAAA;cACA2E,MAAA,CAAAxD,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA8D,YAAA,WAAAA,aAAAtD,GAAA;MAAA,IAAAuD,MAAA;MACA,IAAAC,MAAA,GAAAxD,GAAA,CAAAW,KAAA,SAAA7C,GAAA;MACA,KAAAgE,MAAA,CAAAC,OAAA,oBAAAyB,MAAA,aAAA5D,IAAA;QACA,WAAA6D,WAAA,EAAAD,MAAA;MACA,GAAA5D,IAAA;QACA2D,MAAA,CAAA/D,OAAA;QACA+D,MAAA,CAAAzB,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA;IACA;IACA,aACAwB,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,2BAAAC,cAAA,CAAAC,OAAA,MACA,KAAApF,WAAA,UAAAqF,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}