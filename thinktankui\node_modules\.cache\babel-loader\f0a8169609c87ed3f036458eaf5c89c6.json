{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\directive\\index.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\directive\\index.js", "mtime": 1749104047626}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749104418875}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfaGFzUm9sZSA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9wZXJtaXNzaW9uL2hhc1JvbGUiKSk7CnZhciBfaGFzUGVybWkgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4vcGVybWlzc2lvbi9oYXNQZXJtaSIpKTsKdmFyIF9kcmFnID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL2RpYWxvZy9kcmFnIikpOwp2YXIgX2RyYWdXaWR0aCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9kaWFsb2cvZHJhZ1dpZHRoIikpOwp2YXIgX2RyYWdIZWlnaHQgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4vZGlhbG9nL2RyYWdIZWlnaHQiKSk7CnZhciBfY2xpcGJvYXJkID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL21vZHVsZS9jbGlwYm9hcmQiKSk7CnZhciBpbnN0YWxsID0gZnVuY3Rpb24gaW5zdGFsbChWdWUpIHsKICBWdWUuZGlyZWN0aXZlKCdoYXNSb2xlJywgX2hhc1JvbGUuZGVmYXVsdCk7CiAgVnVlLmRpcmVjdGl2ZSgnaGFzUGVybWknLCBfaGFzUGVybWkuZGVmYXVsdCk7CiAgVnVlLmRpcmVjdGl2ZSgnY2xpcGJvYXJkJywgX2NsaXBib2FyZC5kZWZhdWx0KTsKICBWdWUuZGlyZWN0aXZlKCdkaWFsb2dEcmFnJywgX2RyYWcuZGVmYXVsdCk7CiAgVnVlLmRpcmVjdGl2ZSgnZGlhbG9nRHJhZ1dpZHRoJywgX2RyYWdXaWR0aC5kZWZhdWx0KTsKICBWdWUuZGlyZWN0aXZlKCdkaWFsb2dEcmFnSGVpZ2h0JywgX2RyYWdIZWlnaHQuZGVmYXVsdCk7Cn07CmlmICh3aW5kb3cuVnVlKSB7CiAgd2luZG93WydoYXNSb2xlJ10gPSBfaGFzUm9sZS5kZWZhdWx0OwogIHdpbmRvd1snaGFzUGVybWknXSA9IF9oYXNQZXJtaS5kZWZhdWx0OwogIFZ1ZS51c2UoaW5zdGFsbCk7IC8vIGVzbGludC1kaXNhYmxlLWxpbmUKfQp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSBpbnN0YWxsOw=="}, {"version": 3, "names": ["_hasRole", "_interopRequireDefault", "require", "_has<PERSON>ermi", "_drag", "_dragWidth", "_dragHeight", "_clipboard", "install", "<PERSON><PERSON>", "directive", "hasRole", "<PERSON><PERSON><PERSON><PERSON>", "clipboard", "dialogDrag", "dialogDragWidth", "dialogDragHeight", "window", "use", "_default", "exports", "default"], "sources": ["D:/thinktank/thinktankui/src/directive/index.js"], "sourcesContent": ["import hasRole from './permission/hasRole'\r\nimport hasPermi from './permission/hasPermi'\r\nimport dialogDrag from './dialog/drag'\r\nimport dialogDragWidth from './dialog/dragWidth'\r\nimport dialogDragHeight from './dialog/dragHeight'\r\nimport clipboard from './module/clipboard'\r\n\r\nconst install = function(Vue) {\r\n  Vue.directive('hasRole', hasRole)\r\n  Vue.directive('hasPermi', hasPermi)\r\n  Vue.directive('clipboard', clipboard)\r\n  Vue.directive('dialogDrag', dialogDrag)\r\n  Vue.directive('dialogDragWidth', dialogDragWidth)\r\n  Vue.directive('dialogDragHeight', dialogDragHeight)\r\n}\r\n\r\nif (window.Vue) {\r\n  window['hasRole'] = hasRole\r\n  window['hasPermi'] = hasPermi\r\n  Vue.use(install); // eslint-disable-line\r\n}\r\n\r\nexport default install\r\n"], "mappings": ";;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,KAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,UAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,WAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,UAAA,GAAAN,sBAAA,CAAAC,OAAA;AAEA,IAAMM,OAAO,GAAG,SAAVA,OAAOA,CAAYC,GAAG,EAAE;EAC5BA,GAAG,CAACC,SAAS,CAAC,SAAS,EAAEC,gBAAO,CAAC;EACjCF,GAAG,CAACC,SAAS,CAAC,UAAU,EAAEE,iBAAQ,CAAC;EACnCH,GAAG,CAACC,SAAS,CAAC,WAAW,EAAEG,kBAAS,CAAC;EACrCJ,GAAG,CAACC,SAAS,CAAC,YAAY,EAAEI,aAAU,CAAC;EACvCL,GAAG,CAACC,SAAS,CAAC,iBAAiB,EAAEK,kBAAe,CAAC;EACjDN,GAAG,CAACC,SAAS,CAAC,kBAAkB,EAAEM,mBAAgB,CAAC;AACrD,CAAC;AAED,IAAIC,MAAM,CAACR,GAAG,EAAE;EACdQ,MAAM,CAAC,SAAS,CAAC,GAAGN,gBAAO;EAC3BM,MAAM,CAAC,UAAU,CAAC,GAAGL,iBAAQ;EAC7BH,GAAG,CAACS,GAAG,CAACV,OAAO,CAAC,CAAC,CAAC;AACpB;AAAC,IAAAW,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEcb,OAAO", "ignoreList": []}]}