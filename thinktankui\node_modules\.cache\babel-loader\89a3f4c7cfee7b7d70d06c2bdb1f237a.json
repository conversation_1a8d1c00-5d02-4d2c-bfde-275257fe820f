{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\modules\\toolbar.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\modules\\toolbar.js", "mtime": 1749104422775}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_quill<PERSON><PERSON><PERSON>", "_interopRequireDefault", "require", "_parchment", "_quill", "_logger", "_module", "debug", "logger", "<PERSON><PERSON><PERSON>", "exports", "default", "_Module", "quill", "options", "_this", "_classCallCheck2", "_callSuper2", "Array", "isArray", "container", "_quill$container", "document", "createElement", "setAttribute", "addControls", "parentNode", "insertBefore", "querySelector", "HTMLElement", "error", "_possibleConstructorReturn2", "classList", "add", "controls", "handlers", "Object", "keys", "for<PERSON>ach", "format", "_this$options$handler", "handler", "add<PERSON><PERSON><PERSON>", "from", "querySelectorAll", "input", "attach", "on", "<PERSON><PERSON><PERSON>", "events", "EDITOR_CHANGE", "_this$quill$selection", "selection", "getRange", "_this$quill$selection2", "_slicedToArray2", "range", "update", "_inherits2", "_createClass2", "key", "value", "_this2", "find", "className", "indexOf", "slice", "length", "tagName", "scroll", "query", "warn", "eventName", "addEventListener", "e", "selectedIndex", "selected", "hasAttribute", "contains", "preventDefault", "focus", "_this2$quill$selectio", "_this2$quill$selectio2", "call", "prototype", "EmbedBlot", "prompt", "concat", "updateContents", "Delta", "retain", "index", "delete", "insert", "_defineProperty2", "sources", "USER", "push", "formats", "getFormat", "pair", "_pair", "option", "replace", "remove", "isActive", "getAttribute", "toString", "toggle", "<PERSON><PERSON><PERSON>", "DEFAULTS", "addButton", "append<PERSON><PERSON><PERSON>", "groups", "group", "control", "addSelect", "values", "String", "clean", "_this3", "getSelection", "name", "<PERSON><PERSON>", "INLINE", "removeFormat", "direction", "_this$quill$getFormat", "align", "indent", "parseInt", "modifier", "link", "list"], "sources": ["../../src/modules/toolbar.ts"], "sourcesContent": ["import Delta from 'quill-delta';\nimport { EmbedBlot, Scope } from 'parchment';\nimport Quill from '../core/quill.js';\nimport logger from '../core/logger.js';\nimport Module from '../core/module.js';\nimport type { Range } from '../core/selection.js';\n\nconst debug = logger('quill:toolbar');\n\ntype Handler = (this: Toolbar, value: any) => void;\n\nexport type ToolbarConfig = Array<\n  string[] | Array<string | Record<string, unknown>>\n>;\nexport interface ToolbarProps {\n  container?: HTMLElement | ToolbarConfig | null;\n  handlers?: Record<string, Handler>;\n  option?: number;\n  module?: boolean;\n  theme?: boolean;\n}\n\nclass Toolbar extends Module<ToolbarProps> {\n  static DEFAULTS: ToolbarProps;\n\n  container?: HTMLElement | null;\n  controls: [string, HTMLElement][];\n  handlers: Record<string, Handler>;\n\n  constructor(quill: Quill, options: Partial<ToolbarProps>) {\n    super(quill, options);\n    if (Array.isArray(this.options.container)) {\n      const container = document.createElement('div');\n      container.setAttribute('role', 'toolbar');\n      addControls(container, this.options.container);\n      quill.container?.parentNode?.insertBefore(container, quill.container);\n      this.container = container;\n    } else if (typeof this.options.container === 'string') {\n      this.container = document.querySelector(this.options.container);\n    } else {\n      this.container = this.options.container;\n    }\n    if (!(this.container instanceof HTMLElement)) {\n      debug.error('Container required for toolbar', this.options);\n      return;\n    }\n    this.container.classList.add('ql-toolbar');\n    this.controls = [];\n    this.handlers = {};\n    if (this.options.handlers) {\n      Object.keys(this.options.handlers).forEach((format) => {\n        const handler = this.options.handlers?.[format];\n        if (handler) {\n          this.addHandler(format, handler);\n        }\n      });\n    }\n    Array.from(this.container.querySelectorAll('button, select')).forEach(\n      (input) => {\n        // @ts-expect-error\n        this.attach(input);\n      },\n    );\n    this.quill.on(Quill.events.EDITOR_CHANGE, () => {\n      const [range] = this.quill.selection.getRange(); // quill.getSelection triggers update\n      this.update(range);\n    });\n  }\n\n  addHandler(format: string, handler: Handler) {\n    this.handlers[format] = handler;\n  }\n\n  attach(input: HTMLElement) {\n    let format = Array.from(input.classList).find((className) => {\n      return className.indexOf('ql-') === 0;\n    });\n    if (!format) return;\n    format = format.slice('ql-'.length);\n    if (input.tagName === 'BUTTON') {\n      input.setAttribute('type', 'button');\n    }\n    if (\n      this.handlers[format] == null &&\n      this.quill.scroll.query(format) == null\n    ) {\n      debug.warn('ignoring attaching to nonexistent format', format, input);\n      return;\n    }\n    const eventName = input.tagName === 'SELECT' ? 'change' : 'click';\n    input.addEventListener(eventName, (e) => {\n      let value;\n      if (input.tagName === 'SELECT') {\n        // @ts-expect-error\n        if (input.selectedIndex < 0) return;\n        // @ts-expect-error\n        const selected = input.options[input.selectedIndex];\n        if (selected.hasAttribute('selected')) {\n          value = false;\n        } else {\n          value = selected.value || false;\n        }\n      } else {\n        if (input.classList.contains('ql-active')) {\n          value = false;\n        } else {\n          // @ts-expect-error\n          value = input.value || !input.hasAttribute('value');\n        }\n        e.preventDefault();\n      }\n      this.quill.focus();\n      const [range] = this.quill.selection.getRange();\n      if (this.handlers[format] != null) {\n        this.handlers[format].call(this, value);\n      } else if (\n        // @ts-expect-error\n        this.quill.scroll.query(format).prototype instanceof EmbedBlot\n      ) {\n        value = prompt(`Enter ${format}`); // eslint-disable-line no-alert\n        if (!value) return;\n        this.quill.updateContents(\n          new Delta()\n            // @ts-expect-error Fix me later\n            .retain(range.index)\n            // @ts-expect-error Fix me later\n            .delete(range.length)\n            .insert({ [format]: value }),\n          Quill.sources.USER,\n        );\n      } else {\n        this.quill.format(format, value, Quill.sources.USER);\n      }\n      this.update(range);\n    });\n    this.controls.push([format, input]);\n  }\n\n  update(range: Range | null) {\n    const formats = range == null ? {} : this.quill.getFormat(range);\n    this.controls.forEach((pair) => {\n      const [format, input] = pair;\n      if (input.tagName === 'SELECT') {\n        let option: HTMLOptionElement | null = null;\n        if (range == null) {\n          option = null;\n        } else if (formats[format] == null) {\n          option = input.querySelector('option[selected]');\n        } else if (!Array.isArray(formats[format])) {\n          let value = formats[format];\n          if (typeof value === 'string') {\n            value = value.replace(/\"/g, '\\\\\"');\n          }\n          option = input.querySelector(`option[value=\"${value}\"]`);\n        }\n        if (option == null) {\n          // @ts-expect-error TODO fix me later\n          input.value = ''; // TODO make configurable?\n          // @ts-expect-error TODO fix me later\n          input.selectedIndex = -1;\n        } else {\n          option.selected = true;\n        }\n      } else if (range == null) {\n        input.classList.remove('ql-active');\n        input.setAttribute('aria-pressed', 'false');\n      } else if (input.hasAttribute('value')) {\n        // both being null should match (default values)\n        // '1' should match with 1 (headers)\n        const value = formats[format] as boolean | number | string | object;\n        const isActive =\n          value === input.getAttribute('value') ||\n          (value != null && value.toString() === input.getAttribute('value')) ||\n          (value == null && !input.getAttribute('value'));\n        input.classList.toggle('ql-active', isActive);\n        input.setAttribute('aria-pressed', isActive.toString());\n      } else {\n        const isActive = formats[format] != null;\n        input.classList.toggle('ql-active', isActive);\n        input.setAttribute('aria-pressed', isActive.toString());\n      }\n    });\n  }\n}\nToolbar.DEFAULTS = {};\n\nfunction addButton(container: HTMLElement, format: string, value?: string) {\n  const input = document.createElement('button');\n  input.setAttribute('type', 'button');\n  input.classList.add(`ql-${format}`);\n  input.setAttribute('aria-pressed', 'false');\n  if (value != null) {\n    input.value = value;\n    input.setAttribute('aria-label', `${format}: ${value}`);\n  } else {\n    input.setAttribute('aria-label', format);\n  }\n  container.appendChild(input);\n}\n\nfunction addControls(\n  container: HTMLElement,\n  groups:\n    | (string | Record<string, unknown>)[][]\n    | (string | Record<string, unknown>)[],\n) {\n  if (!Array.isArray(groups[0])) {\n    // @ts-expect-error\n    groups = [groups];\n  }\n  groups.forEach((controls: any) => {\n    const group = document.createElement('span');\n    group.classList.add('ql-formats');\n    controls.forEach((control: any) => {\n      if (typeof control === 'string') {\n        addButton(group, control);\n      } else {\n        const format = Object.keys(control)[0];\n        const value = control[format];\n        if (Array.isArray(value)) {\n          addSelect(group, format, value);\n        } else {\n          addButton(group, format, value);\n        }\n      }\n    });\n    container.appendChild(group);\n  });\n}\n\nfunction addSelect(\n  container: HTMLElement,\n  format: string,\n  values: Array<string | boolean>,\n) {\n  const input = document.createElement('select');\n  input.classList.add(`ql-${format}`);\n  values.forEach((value) => {\n    const option = document.createElement('option');\n    if (value !== false) {\n      option.setAttribute('value', String(value));\n    } else {\n      option.setAttribute('selected', 'selected');\n    }\n    input.appendChild(option);\n  });\n  container.appendChild(input);\n}\n\nToolbar.DEFAULTS = {\n  container: null,\n  handlers: {\n    clean() {\n      const range = this.quill.getSelection();\n      if (range == null) return;\n      if (range.length === 0) {\n        const formats = this.quill.getFormat();\n        Object.keys(formats).forEach((name) => {\n          // Clean functionality in existing apps only clean inline formats\n          if (this.quill.scroll.query(name, Scope.INLINE) != null) {\n            this.quill.format(name, false, Quill.sources.USER);\n          }\n        });\n      } else {\n        this.quill.removeFormat(range.index, range.length, Quill.sources.USER);\n      }\n    },\n    direction(value) {\n      const { align } = this.quill.getFormat();\n      if (value === 'rtl' && align == null) {\n        this.quill.format('align', 'right', Quill.sources.USER);\n      } else if (!value && align === 'right') {\n        this.quill.format('align', false, Quill.sources.USER);\n      }\n      this.quill.format('direction', value, Quill.sources.USER);\n    },\n    indent(value) {\n      const range = this.quill.getSelection();\n      // @ts-expect-error\n      const formats = this.quill.getFormat(range);\n      // @ts-expect-error\n      const indent = parseInt(formats.indent || 0, 10);\n      if (value === '+1' || value === '-1') {\n        let modifier = value === '+1' ? 1 : -1;\n        if (formats.direction === 'rtl') modifier *= -1;\n        this.quill.format('indent', indent + modifier, Quill.sources.USER);\n      }\n    },\n    link(value) {\n      if (value === true) {\n        value = prompt('Enter link URL:'); // eslint-disable-line no-alert\n      }\n      this.quill.format('link', value, Quill.sources.USER);\n    },\n    list(value) {\n      const range = this.quill.getSelection();\n      // @ts-expect-error\n      const formats = this.quill.getFormat(range);\n      if (value === 'check') {\n        if (formats.list === 'checked' || formats.list === 'unchecked') {\n          this.quill.format('list', false, Quill.sources.USER);\n        } else {\n          this.quill.format('list', 'unchecked', Quill.sources.USER);\n        }\n      } else {\n        this.quill.format('list', value, Quill.sources.USER);\n      }\n    },\n  },\n};\n\nexport { Toolbar as default, addControls };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,OAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,OAAA,GAAAL,sBAAA,CAAAC,OAAA;AAGA,IAAMK,KAAK,GAAG,IAAAC,eAAM,EAAC,eAAe,CAAC;AAAA,IAe/BC,OAAO,GAAAC,OAAA,CAAAC,OAAA,0BAAAC,OAAA;EAOX,SAAAH,QAAYI,KAAY,EAAEC,OAA8B,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAL,OAAA,QAAAF,OAAA;IACxDM,KAAA,OAAAE,WAAA,CAAAN,OAAA,QAAAF,OAAA,GAAMI,KAAK,EAAEC,OAAO;IACpB,IAAII,KAAK,CAACC,OAAO,CAACJ,KAAA,CAAKD,OAAO,CAACM,SAAS,CAAC,EAAE;MAAA,IAAAC,gBAAA;MACzC,IAAMD,SAAS,GAAGE,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC/CH,SAAS,CAACI,YAAY,CAAC,MAAM,EAAE,SAAS,CAAC;MACzCC,WAAW,CAACL,SAAS,EAAEL,KAAA,CAAKD,OAAO,CAACM,SAAS,CAAC;MAC9C,CAAAC,gBAAA,GAAAR,KAAK,CAACO,SAAS,cAAAC,gBAAA,gBAAAA,gBAAA,GAAfA,gBAAA,CAAiBK,UAAU,cAAAL,gBAAA,eAA3BA,gBAAA,CAA6BM,YAAY,CAACP,SAAS,EAAEP,KAAK,CAACO,SAAS,CAAC;MACrEL,KAAA,CAAKK,SAAS,GAAGA,SAAS;IAC5B,CAAC,MAAM,IAAI,OAAOL,KAAA,CAAKD,OAAO,CAACM,SAAS,KAAK,QAAQ,EAAE;MACrDL,KAAA,CAAKK,SAAS,GAAGE,QAAQ,CAACM,aAAa,CAACb,KAAA,CAAKD,OAAO,CAACM,SAAS,CAAC;IACjE,CAAC,MAAM;MACLL,KAAA,CAAKK,SAAS,GAAGL,KAAA,CAAKD,OAAO,CAACM,SAAS;IACzC;IACA,IAAI,EAAEL,KAAA,CAAKK,SAAS,YAAYS,WAAW,CAAC,EAAE;MAC5CtB,KAAK,CAACuB,KAAK,CAAC,gCAAgC,EAAEf,KAAA,CAAKD,OAAO,CAAC;MAC3D,WAAAiB,2BAAA,CAAApB,OAAA,EAAAI,KAAA;IACF;IACAA,KAAA,CAAKK,SAAS,CAACY,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;IAC1ClB,KAAA,CAAKmB,QAAQ,GAAG,EAAE;IAClBnB,KAAA,CAAKoB,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAIpB,KAAA,CAAKD,OAAO,CAACqB,QAAQ,EAAE;MACzBC,MAAM,CAACC,IAAI,CAACtB,KAAA,CAAKD,OAAO,CAACqB,QAAQ,CAAC,CAACG,OAAO,CAAE,UAAAC,MAAM,EAAK;QAAA,IAAAC,qBAAA;QACrD,IAAMC,OAAO,IAAAD,qBAAA,GAAGzB,KAAA,CAAKD,OAAO,CAACqB,QAAQ,cAAAK,qBAAA,uBAArBA,qBAAA,CAAwBD,MAAM,CAAC;QAC/C,IAAIE,OAAO,EAAE;UACX1B,KAAA,CAAK2B,UAAU,CAACH,MAAM,EAAEE,OAAO,CAAC;QAClC;MACF,CAAC,CAAC;IACJ;IACAvB,KAAK,CAACyB,IAAI,CAAC5B,KAAA,CAAKK,SAAS,CAACwB,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,CAACN,OAAO,CAClE,UAAAO,KAAK,EAAK;MACT;MACA9B,KAAA,CAAK+B,MAAM,CAACD,KAAK,CAAC;IACpB,CACF,CAAC;IACD9B,KAAA,CAAKF,KAAK,CAACkC,EAAE,CAACC,cAAK,CAACC,MAAM,CAACC,aAAa,EAAE,YAAM;MAC9C,IAAAC,qBAAA,GAAgBpC,KAAA,CAAKF,KAAK,CAACuC,SAAS,CAACC,QAAQ,CAAC,CAAC;QAAAC,sBAAA,OAAAC,eAAA,CAAA5C,OAAA,EAAAwC,qBAAA;QAAxCK,KAAK,GAAAF,sBAAA,IAAoC,CAAC;MACjDvC,KAAA,CAAK0C,MAAM,CAACD,KAAK,CAAC;IACpB,CAAC,CAAC;IAAA,OAAAzC,KAAA;EACJ;EAAA,IAAA2C,UAAA,CAAA/C,OAAA,EAAAF,OAAA,EAAAG,OAAA;EAAA,WAAA+C,aAAA,CAAAhD,OAAA,EAAAF,OAAA;IAAAmD,GAAA;IAAAC,KAAA,EAEA,SAAAnB,UAAUA,CAACH,MAAc,EAAEE,OAAgB,EAAE;MAC3C,IAAI,CAACN,QAAQ,CAACI,MAAM,CAAC,GAAGE,OAAO;IACjC;EAAA;IAAAmB,GAAA;IAAAC,KAAA,EAEA,SAAAf,MAAMA,CAACD,KAAkB,EAAE;MAAA,IAAAiB,MAAA;MACzB,IAAIvB,MAAM,GAAGrB,KAAK,CAACyB,IAAI,CAACE,KAAK,CAACb,SAAS,CAAC,CAAC+B,IAAI,CAAE,UAAAC,SAAS,EAAK;QAC3D,OAAOA,SAAS,CAACC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;MACvC,CAAC,CAAC;MACF,IAAI,CAAC1B,MAAM,EAAE;MACbA,MAAM,GAAGA,MAAM,CAAC2B,KAAK,CAAC,KAAK,CAACC,MAAM,CAAC;MACnC,IAAItB,KAAK,CAACuB,OAAO,KAAK,QAAQ,EAAE;QAC9BvB,KAAK,CAACrB,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;MACtC;MACA,IACE,IAAI,CAACW,QAAQ,CAACI,MAAM,CAAC,IAAI,IAAI,IAC7B,IAAI,CAAC1B,KAAK,CAACwD,MAAM,CAACC,KAAK,CAAC/B,MAAM,CAAC,IAAI,IAAI,EACvC;QACAhC,KAAK,CAACgE,IAAI,CAAC,0CAA0C,EAAEhC,MAAM,EAAEM,KAAK,CAAC;QACrE;MACF;MACA,IAAM2B,SAAS,GAAG3B,KAAK,CAACuB,OAAO,KAAK,QAAQ,GAAG,QAAQ,GAAG,OAAO;MACjEvB,KAAK,CAAC4B,gBAAgB,CAACD,SAAS,EAAG,UAAAE,CAAC,EAAK;QACvC,IAAIb,KAAK;QACT,IAAIhB,KAAK,CAACuB,OAAO,KAAK,QAAQ,EAAE;UAC9B;UACA,IAAIvB,KAAK,CAAC8B,aAAa,GAAG,CAAC,EAAE;UAC7B;UACA,IAAMC,QAAQ,GAAG/B,KAAK,CAAC/B,OAAO,CAAC+B,KAAK,CAAC8B,aAAa,CAAC;UACnD,IAAIC,QAAQ,CAACC,YAAY,CAAC,UAAU,CAAC,EAAE;YACrChB,KAAK,GAAG,KAAK;UACf,CAAC,MAAM;YACLA,KAAK,GAAGe,QAAQ,CAACf,KAAK,IAAI,KAAK;UACjC;QACF,CAAC,MAAM;UACL,IAAIhB,KAAK,CAACb,SAAS,CAAC8C,QAAQ,CAAC,WAAW,CAAC,EAAE;YACzCjB,KAAK,GAAG,KAAK;UACf,CAAC,MAAM;YACL;YACAA,KAAK,GAAGhB,KAAK,CAACgB,KAAK,IAAI,CAAChB,KAAK,CAACgC,YAAY,CAAC,OAAO,CAAC;UACrD;UACAH,CAAC,CAACK,cAAc,CAAC,CAAC;QACpB;QACAjB,MAAI,CAACjD,KAAK,CAACmE,KAAK,CAAC,CAAC;QAClB,IAAAC,qBAAA,GAAgBnB,MAAI,CAACjD,KAAK,CAACuC,SAAS,CAACC,QAAQ,CAAC,CAAC;UAAA6B,sBAAA,OAAA3B,eAAA,CAAA5C,OAAA,EAAAsE,qBAAA;UAAxCzB,KAAK,GAAA0B,sBAAA;QACZ,IAAIpB,MAAI,CAAC3B,QAAQ,CAACI,MAAM,CAAC,IAAI,IAAI,EAAE;UACjCuB,MAAI,CAAC3B,QAAQ,CAACI,MAAM,CAAC,CAAC4C,IAAI,CAACrB,MAAI,EAAED,KAAK,CAAC;QACzC,CAAC,MAAM;QACL;QACAC,MAAI,CAACjD,KAAK,CAACwD,MAAM,CAACC,KAAK,CAAC/B,MAAM,CAAC,CAAC6C,SAAS,YAAYC,oBAAS,EAC9D;UACAxB,KAAK,GAAGyB,MAAM,UAAAC,MAAA,CAAUhD,MAAO,CAAC,CAAC,CAAC,CAAC;UACnC,IAAI,CAACsB,KAAK,EAAE;UACZC,MAAI,CAACjD,KAAK,CAAC2E,cAAc,CACvB,IAAIC,mBAAK,CAAC;UACR;UAAA,CACCC,MAAM,CAAClC,KAAK,CAACmC,KAAK;UACnB;UAAA,CACCC,MAAM,CAACpC,KAAK,CAACW,MAAM,CAAC,CACpB0B,MAAM,KAAAC,gBAAA,CAAAnF,OAAA,MAAI4B,MAAM,EAAGsB,KAAA,CAAO,CAAC,EAC9Bb,cAAK,CAAC+C,OAAO,CAACC,IAChB,CAAC;QACH,CAAC,MAAM;UACLlC,MAAI,CAACjD,KAAK,CAAC0B,MAAM,CAACA,MAAM,EAAEsB,KAAK,EAAEb,cAAK,CAAC+C,OAAO,CAACC,IAAI,CAAC;QACtD;QACAlC,MAAI,CAACL,MAAM,CAACD,KAAK,CAAC;MACpB,CAAC,CAAC;MACF,IAAI,CAACtB,QAAQ,CAAC+D,IAAI,CAAC,CAAC1D,MAAM,EAAEM,KAAK,CAAC,CAAC;IACrC;EAAA;IAAAe,GAAA;IAAAC,KAAA,EAEA,SAAAJ,MAAMA,CAACD,KAAmB,EAAE;MAC1B,IAAM0C,OAAO,GAAG1C,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC3C,KAAK,CAACsF,SAAS,CAAC3C,KAAK,CAAC;MAChE,IAAI,CAACtB,QAAQ,CAACI,OAAO,CAAE,UAAA8D,IAAI,EAAK;QAC9B,IAAAC,KAAA,OAAA9C,eAAA,CAAA5C,OAAA,EAAwByF,IAAI;UAArB7D,MAAM,GAAA8D,KAAA;UAAExD,KAAK,GAAAwD,KAAA;QACpB,IAAIxD,KAAK,CAACuB,OAAO,KAAK,QAAQ,EAAE;UAC9B,IAAIkC,MAAgC,GAAG,IAAI;UAC3C,IAAI9C,KAAK,IAAI,IAAI,EAAE;YACjB8C,MAAM,GAAG,IAAI;UACf,CAAC,MAAM,IAAIJ,OAAO,CAAC3D,MAAM,CAAC,IAAI,IAAI,EAAE;YAClC+D,MAAM,GAAGzD,KAAK,CAACjB,aAAa,CAAC,kBAAkB,CAAC;UAClD,CAAC,MAAM,IAAI,CAACV,KAAK,CAACC,OAAO,CAAC+E,OAAO,CAAC3D,MAAM,CAAC,CAAC,EAAE;YAC1C,IAAIsB,KAAK,GAAGqC,OAAO,CAAC3D,MAAM,CAAC;YAC3B,IAAI,OAAOsB,KAAK,KAAK,QAAQ,EAAE;cAC7BA,KAAK,GAAGA,KAAK,CAAC0C,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;YACpC;YACAD,MAAM,GAAGzD,KAAK,CAACjB,aAAa,mBAAA2D,MAAA,CAAkB1B,KAAM,QAAG,CAAC;UAC1D;UACA,IAAIyC,MAAM,IAAI,IAAI,EAAE;YAClB;YACAzD,KAAK,CAACgB,KAAK,GAAG,EAAE,CAAC,CAAC;YAClB;YACAhB,KAAK,CAAC8B,aAAa,GAAG,CAAC,CAAC;UAC1B,CAAC,MAAM;YACL2B,MAAM,CAAC1B,QAAQ,GAAG,IAAI;UACxB;QACF,CAAC,MAAM,IAAIpB,KAAK,IAAI,IAAI,EAAE;UACxBX,KAAK,CAACb,SAAS,CAACwE,MAAM,CAAC,WAAW,CAAC;UACnC3D,KAAK,CAACrB,YAAY,CAAC,cAAc,EAAE,OAAO,CAAC;QAC7C,CAAC,MAAM,IAAIqB,KAAK,CAACgC,YAAY,CAAC,OAAO,CAAC,EAAE;UACtC;UACA;UACA,IAAMhB,MAAK,GAAGqC,OAAO,CAAC3D,MAAM,CAAuC;UACnE,IAAMkE,QAAQ,GACZ5C,MAAK,KAAKhB,KAAK,CAAC6D,YAAY,CAAC,OAAO,CAAC,IACpC7C,MAAK,IAAI,IAAI,IAAIA,MAAK,CAAC8C,QAAQ,CAAC,CAAC,KAAK9D,KAAK,CAAC6D,YAAY,CAAC,OAAO,CAAE,IAClE7C,MAAK,IAAI,IAAI,IAAI,CAAChB,KAAK,CAAC6D,YAAY,CAAC,OAAO,CAAE;UACjD7D,KAAK,CAACb,SAAS,CAAC4E,MAAM,CAAC,WAAW,EAAEH,QAAQ,CAAC;UAC7C5D,KAAK,CAACrB,YAAY,CAAC,cAAc,EAAEiF,QAAQ,CAACE,QAAQ,CAAC,CAAC,CAAC;QACzD,CAAC,MAAM;UACL,IAAMF,SAAQ,GAAGP,OAAO,CAAC3D,MAAM,CAAC,IAAI,IAAI;UACxCM,KAAK,CAACb,SAAS,CAAC4E,MAAM,CAAC,WAAW,EAAEH,SAAQ,CAAC;UAC7C5D,KAAK,CAACrB,YAAY,CAAC,cAAc,EAAEiF,SAAQ,CAACE,QAAQ,CAAC,CAAC,CAAC;QACzD;MACF,CAAC,CAAC;IACJ;EAAA;AAAA,EAhKoBE,eAAM;AAkK5BpG,OAAO,CAACqG,QAAQ,GAAG,CAAC,CAAC;AAErB,SAASC,SAASA,CAAC3F,SAAsB,EAAEmB,MAAc,EAAEsB,KAAc,EAAE;EACzE,IAAMhB,KAAK,GAAGvB,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC9CsB,KAAK,CAACrB,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;EACpCqB,KAAK,CAACb,SAAS,CAACC,GAAG,OAAAsD,MAAA,CAAOhD,MAAO,CAAC,CAAC;EACnCM,KAAK,CAACrB,YAAY,CAAC,cAAc,EAAE,OAAO,CAAC;EAC3C,IAAIqC,KAAK,IAAI,IAAI,EAAE;IACjBhB,KAAK,CAACgB,KAAK,GAAGA,KAAK;IACnBhB,KAAK,CAACrB,YAAY,CAAC,YAAY,KAAA+D,MAAA,CAAKhD,MAAO,QAAAgD,MAAA,CAAI1B,KAAM,CAAC,CAAC;EACzD,CAAC,MAAM;IACLhB,KAAK,CAACrB,YAAY,CAAC,YAAY,EAAEe,MAAM,CAAC;EAC1C;EACAnB,SAAS,CAAC4F,WAAW,CAACnE,KAAK,CAAC;AAC9B;AAEA,SAASpB,WAAWA,CAClBL,SAAsB,EACtB6F,MAEwC,EACxC;EACA,IAAI,CAAC/F,KAAK,CAACC,OAAO,CAAC8F,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;IAC7B;IACAA,MAAM,GAAG,CAACA,MAAM,CAAC;EACnB;EACAA,MAAM,CAAC3E,OAAO,CAAE,UAAAJ,QAAa,EAAK;IAChC,IAAMgF,KAAK,GAAG5F,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAC5C2F,KAAK,CAAClF,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;IACjCC,QAAQ,CAACI,OAAO,CAAE,UAAA6E,OAAY,EAAK;MACjC,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QAC/BJ,SAAS,CAACG,KAAK,EAAEC,OAAO,CAAC;MAC3B,CAAC,MAAM;QACL,IAAM5E,MAAM,GAAGH,MAAM,CAACC,IAAI,CAAC8E,OAAO,CAAC,CAAC,CAAC,CAAC;QACtC,IAAMtD,KAAK,GAAGsD,OAAO,CAAC5E,MAAM,CAAC;QAC7B,IAAIrB,KAAK,CAACC,OAAO,CAAC0C,KAAK,CAAC,EAAE;UACxBuD,SAAS,CAACF,KAAK,EAAE3E,MAAM,EAAEsB,KAAK,CAAC;QACjC,CAAC,MAAM;UACLkD,SAAS,CAACG,KAAK,EAAE3E,MAAM,EAAEsB,KAAK,CAAC;QACjC;MACF;IACF,CAAC,CAAC;IACFzC,SAAS,CAAC4F,WAAW,CAACE,KAAK,CAAC;EAC9B,CAAC,CAAC;AACJ;AAEA,SAASE,SAASA,CAChBhG,SAAsB,EACtBmB,MAAc,EACd8E,MAA+B,EAC/B;EACA,IAAMxE,KAAK,GAAGvB,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC9CsB,KAAK,CAACb,SAAS,CAACC,GAAG,OAAAsD,MAAA,CAAOhD,MAAO,CAAC,CAAC;EACnC8E,MAAM,CAAC/E,OAAO,CAAE,UAAAuB,KAAK,EAAK;IACxB,IAAMyC,MAAM,GAAGhF,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/C,IAAIsC,KAAK,KAAK,KAAK,EAAE;MACnByC,MAAM,CAAC9E,YAAY,CAAC,OAAO,EAAE8F,MAAM,CAACzD,KAAK,CAAC,CAAC;IAC7C,CAAC,MAAM;MACLyC,MAAM,CAAC9E,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC;IAC7C;IACAqB,KAAK,CAACmE,WAAW,CAACV,MAAM,CAAC;EAC3B,CAAC,CAAC;EACFlF,SAAS,CAAC4F,WAAW,CAACnE,KAAK,CAAC;AAC9B;AAEApC,OAAO,CAACqG,QAAQ,GAAG;EACjB1F,SAAS,EAAE,IAAI;EACfe,QAAQ,EAAE;IACRoF,KAAK,WAALA,KAAKA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACN,IAAMhE,KAAK,GAAG,IAAI,CAAC3C,KAAK,CAAC4G,YAAY,CAAC,CAAC;MACvC,IAAIjE,KAAK,IAAI,IAAI,EAAE;MACnB,IAAIA,KAAK,CAACW,MAAM,KAAK,CAAC,EAAE;QACtB,IAAM+B,OAAO,GAAG,IAAI,CAACrF,KAAK,CAACsF,SAAS,CAAC,CAAC;QACtC/D,MAAM,CAACC,IAAI,CAAC6D,OAAO,CAAC,CAAC5D,OAAO,CAAE,UAAAoF,IAAI,EAAK;UACrC;UACA,IAAIF,MAAI,CAAC3G,KAAK,CAACwD,MAAM,CAACC,KAAK,CAACoD,IAAI,EAAEC,gBAAK,CAACC,MAAM,CAAC,IAAI,IAAI,EAAE;YACvDJ,MAAI,CAAC3G,KAAK,CAAC0B,MAAM,CAACmF,IAAI,EAAE,KAAK,EAAE1E,cAAK,CAAC+C,OAAO,CAACC,IAAI,CAAC;UACpD;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACnF,KAAK,CAACgH,YAAY,CAACrE,KAAK,CAACmC,KAAK,EAAEnC,KAAK,CAACW,MAAM,EAAEnB,cAAK,CAAC+C,OAAO,CAACC,IAAI,CAAC;MACxE;IACF,CAAC;IACD8B,SAAS,WAATA,SAASA,CAACjE,KAAK,EAAE;MACf,IAAAkE,qBAAA,GAAkB,IAAI,CAAClH,KAAK,CAACsF,SAAS,CAAC,CAAC;QAAhC6B,KAAA,GAAAD,qBAAA,CAAAC,KAAA;MACR,IAAInE,KAAK,KAAK,KAAK,IAAImE,KAAK,IAAI,IAAI,EAAE;QACpC,IAAI,CAACnH,KAAK,CAAC0B,MAAM,CAAC,OAAO,EAAE,OAAO,EAAES,cAAK,CAAC+C,OAAO,CAACC,IAAI,CAAC;MACzD,CAAC,MAAM,IAAI,CAACnC,KAAK,IAAImE,KAAK,KAAK,OAAO,EAAE;QACtC,IAAI,CAACnH,KAAK,CAAC0B,MAAM,CAAC,OAAO,EAAE,KAAK,EAAES,cAAK,CAAC+C,OAAO,CAACC,IAAI,CAAC;MACvD;MACA,IAAI,CAACnF,KAAK,CAAC0B,MAAM,CAAC,WAAW,EAAEsB,KAAK,EAAEb,cAAK,CAAC+C,OAAO,CAACC,IAAI,CAAC;IAC3D,CAAC;IACDiC,MAAM,WAANA,MAAMA,CAACpE,KAAK,EAAE;MACZ,IAAML,KAAK,GAAG,IAAI,CAAC3C,KAAK,CAAC4G,YAAY,CAAC,CAAC;MACvC;MACA,IAAMvB,OAAO,GAAG,IAAI,CAACrF,KAAK,CAACsF,SAAS,CAAC3C,KAAK,CAAC;MAC3C;MACA,IAAMyE,MAAM,GAAGC,QAAQ,CAAChC,OAAO,CAAC+B,MAAM,IAAI,CAAC,EAAE,EAAE,CAAC;MAChD,IAAIpE,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,IAAI,EAAE;QACpC,IAAIsE,QAAQ,GAAGtE,KAAK,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QACtC,IAAIqC,OAAO,CAAC4B,SAAS,KAAK,KAAK,EAAEK,QAAQ,IAAI,CAAC,CAAC;QAC/C,IAAI,CAACtH,KAAK,CAAC0B,MAAM,CAAC,QAAQ,EAAE0F,MAAM,GAAGE,QAAQ,EAAEnF,cAAK,CAAC+C,OAAO,CAACC,IAAI,CAAC;MACpE;IACF,CAAC;IACDoC,IAAI,WAAJA,IAAIA,CAACvE,KAAK,EAAE;MACV,IAAIA,KAAK,KAAK,IAAI,EAAE;QAClBA,KAAK,GAAGyB,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC;MACrC;MACA,IAAI,CAACzE,KAAK,CAAC0B,MAAM,CAAC,MAAM,EAAEsB,KAAK,EAAEb,cAAK,CAAC+C,OAAO,CAACC,IAAI,CAAC;IACtD,CAAC;IACDqC,IAAI,WAAJA,IAAIA,CAACxE,KAAK,EAAE;MACV,IAAML,KAAK,GAAG,IAAI,CAAC3C,KAAK,CAAC4G,YAAY,CAAC,CAAC;MACvC;MACA,IAAMvB,OAAO,GAAG,IAAI,CAACrF,KAAK,CAACsF,SAAS,CAAC3C,KAAK,CAAC;MAC3C,IAAIK,KAAK,KAAK,OAAO,EAAE;QACrB,IAAIqC,OAAO,CAACmC,IAAI,KAAK,SAAS,IAAInC,OAAO,CAACmC,IAAI,KAAK,WAAW,EAAE;UAC9D,IAAI,CAACxH,KAAK,CAAC0B,MAAM,CAAC,MAAM,EAAE,KAAK,EAAES,cAAK,CAAC+C,OAAO,CAACC,IAAI,CAAC;QACtD,CAAC,MAAM;UACL,IAAI,CAACnF,KAAK,CAAC0B,MAAM,CAAC,MAAM,EAAE,WAAW,EAAES,cAAK,CAAC+C,OAAO,CAACC,IAAI,CAAC;QAC5D;MACF,CAAC,MAAM;QACL,IAAI,CAACnF,KAAK,CAAC0B,MAAM,CAAC,MAAM,EAAEsB,KAAK,EAAEb,cAAK,CAAC+C,OAAO,CAACC,IAAI,CAAC;MACtD;IACF;EACF;AACF,CAAC", "ignoreList": []}]}