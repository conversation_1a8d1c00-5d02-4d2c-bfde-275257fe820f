{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\system\\role\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\system\\role\\index.vue", "mtime": 1749104047647}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_role", "require", "_menu", "name", "dicts", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "roleList", "title", "open", "openDataScope", "menuExpand", "menuNodeAll", "deptExpand", "deptNodeAll", "date<PERSON><PERSON><PERSON>", "dataScopeOptions", "value", "label", "menuOptions", "deptOptions", "queryParams", "pageNum", "pageSize", "<PERSON><PERSON><PERSON>", "undefined", "<PERSON><PERSON><PERSON>", "status", "form", "defaultProps", "children", "rules", "required", "message", "trigger", "roleSort", "created", "getList", "methods", "_this", "listRole", "addDateRange", "then", "response", "rows", "getMenuTreeselect", "_this2", "menuTreeselect", "getMenuAllCheckedKeys", "checked<PERSON>eys", "$refs", "menu", "getChe<PERSON><PERSON>eys", "halfC<PERSON>cked<PERSON>eys", "getHalfCheckedKeys", "unshift", "apply", "getDeptAllCheckedKeys", "dept", "getRoleMenuTreeselect", "roleId", "_this3", "roleMenuTreeselect", "menus", "getDeptTree", "_this4", "deptTreeSelect", "depts", "handleStatusChange", "row", "_this5", "text", "$modal", "confirm", "changeRoleStatus", "msgSuccess", "catch", "cancel", "reset", "cancelDataScope", "set<PERSON><PERSON><PERSON><PERSON>eys", "menuIds", "deptIds", "menu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "remark", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleCommand", "command", "handleDataScope", "handleAuthUser", "handleCheckedTreeExpand", "type", "treeList", "i", "store", "nodesMap", "id", "expanded", "handleCheckedTreeNodeAll", "setCheckedNodes", "handleCheckedTreeConnect", "handleAdd", "handleUpdate", "_this6", "roleMenu", "getRole", "$nextTick", "res", "for<PERSON>ach", "v", "setChecked", "dataScopeSelectChange", "_this7", "$router", "push", "submitForm", "_this8", "validate", "valid", "updateRole", "addRole", "submitDataScope", "_this9", "dataScope", "handleDelete", "_this0", "roleIds", "delRole", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime"], "sources": ["src/views/system/role/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\r\n      <el-form-item label=\"角色名称\" prop=\"roleName\">\r\n        <el-input\r\n          v-model=\"queryParams.roleName\"\r\n          placeholder=\"请输入角色名称\"\r\n          clearable\r\n          style=\"width: 240px\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"权限字符\" prop=\"roleKey\">\r\n        <el-input\r\n          v-model=\"queryParams.roleKey\"\r\n          placeholder=\"请输入权限字符\"\r\n          clearable\r\n          style=\"width: 240px\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select\r\n          v-model=\"queryParams.status\"\r\n          placeholder=\"角色状态\"\r\n          clearable\r\n          style=\"width: 240px\"\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_normal_disable\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"创建时间\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        ></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['system:role:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['system:role:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['system:role:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['system:role:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"roleList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"角色编号\" prop=\"roleId\" width=\"120\" />\r\n      <el-table-column label=\"角色名称\" prop=\"roleName\" :show-overflow-tooltip=\"true\" width=\"150\" />\r\n      <el-table-column label=\"权限字符\" prop=\"roleKey\" :show-overflow-tooltip=\"true\" width=\"150\" />\r\n      <el-table-column label=\"显示顺序\" prop=\"roleSort\" width=\"100\" />\r\n      <el-table-column label=\"状态\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-switch\r\n            v-model=\"scope.row.status\"\r\n            active-value=\"0\"\r\n            inactive-value=\"1\"\r\n            @change=\"handleStatusChange(scope.row)\"\r\n          ></el-switch>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\" v-if=\"scope.row.roleId !== 1\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['system:role:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['system:role:remove']\"\r\n          >删除</el-button>\r\n          <el-dropdown size=\"mini\" @command=\"(command) => handleCommand(command, scope.row)\" v-hasPermi=\"['system:role:edit']\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-d-arrow-right\">更多</el-button>\r\n            <el-dropdown-menu slot=\"dropdown\">\r\n              <el-dropdown-item command=\"handleDataScope\" icon=\"el-icon-circle-check\"\r\n                v-hasPermi=\"['system:role:edit']\">数据权限</el-dropdown-item>\r\n              <el-dropdown-item command=\"handleAuthUser\" icon=\"el-icon-user\"\r\n                v-hasPermi=\"['system:role:edit']\">分配用户</el-dropdown-item>\r\n            </el-dropdown-menu>\r\n          </el-dropdown>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改角色配置对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-form-item label=\"角色名称\" prop=\"roleName\">\r\n          <el-input v-model=\"form.roleName\" placeholder=\"请输入角色名称\" />\r\n        </el-form-item>\r\n        <el-form-item prop=\"roleKey\">\r\n          <span slot=\"label\">\r\n            <el-tooltip content=\"控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasRole('admin')`)\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n            权限字符\r\n          </span>\r\n          <el-input v-model=\"form.roleKey\" placeholder=\"请输入权限字符\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"角色顺序\" prop=\"roleSort\">\r\n          <el-input-number v-model=\"form.roleSort\" controls-position=\"right\" :min=\"0\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\">\r\n          <el-radio-group v-model=\"form.status\">\r\n            <el-radio\r\n              v-for=\"dict in dict.type.sys_normal_disable\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.value\"\r\n            >{{dict.label}}</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"菜单权限\">\r\n          <el-checkbox v-model=\"menuExpand\" @change=\"handleCheckedTreeExpand($event, 'menu')\">展开/折叠</el-checkbox>\r\n          <el-checkbox v-model=\"menuNodeAll\" @change=\"handleCheckedTreeNodeAll($event, 'menu')\">全选/全不选</el-checkbox>\r\n          <el-checkbox v-model=\"form.menuCheckStrictly\" @change=\"handleCheckedTreeConnect($event, 'menu')\">父子联动</el-checkbox>\r\n          <el-tree\r\n            class=\"tree-border\"\r\n            :data=\"menuOptions\"\r\n            show-checkbox\r\n            ref=\"menu\"\r\n            node-key=\"id\"\r\n            :check-strictly=\"!form.menuCheckStrictly\"\r\n            empty-text=\"加载中，请稍候\"\r\n            :props=\"defaultProps\"\r\n          ></el-tree>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 分配角色数据权限对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"openDataScope\" width=\"500px\" append-to-body>\r\n      <el-form :model=\"form\" label-width=\"80px\">\r\n        <el-form-item label=\"角色名称\">\r\n          <el-input v-model=\"form.roleName\" :disabled=\"true\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"权限字符\">\r\n          <el-input v-model=\"form.roleKey\" :disabled=\"true\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"权限范围\">\r\n          <el-select v-model=\"form.dataScope\" @change=\"dataScopeSelectChange\">\r\n            <el-option\r\n              v-for=\"item in dataScopeOptions\"\r\n              :key=\"item.value\"\r\n              :label=\"item.label\"\r\n              :value=\"item.value\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"数据权限\" v-show=\"form.dataScope == 2\">\r\n          <el-checkbox v-model=\"deptExpand\" @change=\"handleCheckedTreeExpand($event, 'dept')\">展开/折叠</el-checkbox>\r\n          <el-checkbox v-model=\"deptNodeAll\" @change=\"handleCheckedTreeNodeAll($event, 'dept')\">全选/全不选</el-checkbox>\r\n          <el-checkbox v-model=\"form.deptCheckStrictly\" @change=\"handleCheckedTreeConnect($event, 'dept')\">父子联动</el-checkbox>\r\n          <el-tree\r\n            class=\"tree-border\"\r\n            :data=\"deptOptions\"\r\n            show-checkbox\r\n            default-expand-all\r\n            ref=\"dept\"\r\n            node-key=\"id\"\r\n            :check-strictly=\"!form.deptCheckStrictly\"\r\n            empty-text=\"加载中，请稍候\"\r\n            :props=\"defaultProps\"\r\n          ></el-tree>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitDataScope\">确 定</el-button>\r\n        <el-button @click=\"cancelDataScope\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listRole, getRole, delRole, addRole, updateRole, dataScope, changeRoleStatus, deptTreeSelect } from \"@/api/system/role\";\r\nimport { treeselect as menuTreeselect, roleMenuTreeselect } from \"@/api/system/menu\";\r\n\r\nexport default {\r\n  name: \"Role\",\r\n  dicts: ['sys_normal_disable'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 角色表格数据\r\n      roleList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否显示弹出层（数据权限）\r\n      openDataScope: false,\r\n      menuExpand: false,\r\n      menuNodeAll: false,\r\n      deptExpand: true,\r\n      deptNodeAll: false,\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 数据范围选项\r\n      dataScopeOptions: [\r\n        {\r\n          value: \"1\",\r\n          label: \"全部数据权限\"\r\n        },\r\n        {\r\n          value: \"2\",\r\n          label: \"自定数据权限\"\r\n        },\r\n        {\r\n          value: \"3\",\r\n          label: \"本部门数据权限\"\r\n        },\r\n        {\r\n          value: \"4\",\r\n          label: \"本部门及以下数据权限\"\r\n        },\r\n        {\r\n          value: \"5\",\r\n          label: \"仅本人数据权限\"\r\n        }\r\n      ],\r\n      // 菜单列表\r\n      menuOptions: [],\r\n      // 部门列表\r\n      deptOptions: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        roleName: undefined,\r\n        roleKey: undefined,\r\n        status: undefined\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      defaultProps: {\r\n        children: \"children\",\r\n        label: \"label\"\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        roleName: [\r\n          { required: true, message: \"角色名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        roleKey: [\r\n          { required: true, message: \"权限字符不能为空\", trigger: \"blur\" }\r\n        ],\r\n        roleSort: [\r\n          { required: true, message: \"角色顺序不能为空\", trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询角色列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listRole(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\r\n          this.roleList = response.rows;\r\n          this.total = response.total;\r\n          this.loading = false;\r\n        }\r\n      );\r\n    },\r\n    /** 查询菜单树结构 */\r\n    getMenuTreeselect() {\r\n      menuTreeselect().then(response => {\r\n        this.menuOptions = response.data;\r\n      });\r\n    },\r\n    // 所有菜单节点数据\r\n    getMenuAllCheckedKeys() {\r\n      // 目前被选中的菜单节点\r\n      let checkedKeys = this.$refs.menu.getCheckedKeys();\r\n      // 半选中的菜单节点\r\n      let halfCheckedKeys = this.$refs.menu.getHalfCheckedKeys();\r\n      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);\r\n      return checkedKeys;\r\n    },\r\n    // 所有部门节点数据\r\n    getDeptAllCheckedKeys() {\r\n      // 目前被选中的部门节点\r\n      let checkedKeys = this.$refs.dept.getCheckedKeys();\r\n      // 半选中的部门节点\r\n      let halfCheckedKeys = this.$refs.dept.getHalfCheckedKeys();\r\n      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);\r\n      return checkedKeys;\r\n    },\r\n    /** 根据角色ID查询菜单树结构 */\r\n    getRoleMenuTreeselect(roleId) {\r\n      return roleMenuTreeselect(roleId).then(response => {\r\n        this.menuOptions = response.menus;\r\n        return response;\r\n      });\r\n    },\r\n    /** 根据角色ID查询部门树结构 */\r\n    getDeptTree(roleId) {\r\n      return deptTreeSelect(roleId).then(response => {\r\n        this.deptOptions = response.depts;\r\n        return response;\r\n      });\r\n    },\r\n    // 角色状态修改\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\r\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.roleName + '\"角色吗？').then(function() {\r\n        return changeRoleStatus(row.roleId, row.status);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function() {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 取消按钮（数据权限）\r\n    cancelDataScope() {\r\n      this.openDataScope = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      if (this.$refs.menu != undefined) {\r\n        this.$refs.menu.setCheckedKeys([]);\r\n      }\r\n      this.menuExpand = false,\r\n      this.menuNodeAll = false,\r\n      this.deptExpand = true,\r\n      this.deptNodeAll = false,\r\n      this.form = {\r\n        roleId: undefined,\r\n        roleName: undefined,\r\n        roleKey: undefined,\r\n        roleSort: 0,\r\n        status: \"0\",\r\n        menuIds: [],\r\n        deptIds: [],\r\n        menuCheckStrictly: true,\r\n        deptCheckStrictly: true,\r\n        remark: undefined\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.roleId)\r\n      this.single = selection.length!=1\r\n      this.multiple = !selection.length\r\n    },\r\n    // 更多操作触发\r\n    handleCommand(command, row) {\r\n      switch (command) {\r\n        case \"handleDataScope\":\r\n          this.handleDataScope(row);\r\n          break;\r\n        case \"handleAuthUser\":\r\n          this.handleAuthUser(row);\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n    // 树权限（展开/折叠）\r\n    handleCheckedTreeExpand(value, type) {\r\n      if (type == 'menu') {\r\n        let treeList = this.menuOptions;\r\n        for (let i = 0; i < treeList.length; i++) {\r\n          this.$refs.menu.store.nodesMap[treeList[i].id].expanded = value;\r\n        }\r\n      } else if (type == 'dept') {\r\n        let treeList = this.deptOptions;\r\n        for (let i = 0; i < treeList.length; i++) {\r\n          this.$refs.dept.store.nodesMap[treeList[i].id].expanded = value;\r\n        }\r\n      }\r\n    },\r\n    // 树权限（全选/全不选）\r\n    handleCheckedTreeNodeAll(value, type) {\r\n      if (type == 'menu') {\r\n        this.$refs.menu.setCheckedNodes(value ? this.menuOptions: []);\r\n      } else if (type == 'dept') {\r\n        this.$refs.dept.setCheckedNodes(value ? this.deptOptions: []);\r\n      }\r\n    },\r\n    // 树权限（父子联动）\r\n    handleCheckedTreeConnect(value, type) {\r\n      if (type == 'menu') {\r\n        this.form.menuCheckStrictly = value ? true: false;\r\n      } else if (type == 'dept') {\r\n        this.form.deptCheckStrictly = value ? true: false;\r\n      }\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.getMenuTreeselect();\r\n      this.open = true;\r\n      this.title = \"添加角色\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const roleId = row.roleId || this.ids\r\n      const roleMenu = this.getRoleMenuTreeselect(roleId);\r\n      getRole(roleId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.$nextTick(() => {\r\n          roleMenu.then(res => {\r\n            let checkedKeys = res.checkedKeys\r\n            checkedKeys.forEach((v) => {\r\n                this.$nextTick(()=>{\r\n                    this.$refs.menu.setChecked(v, true ,false);\r\n                })\r\n            })\r\n          });\r\n        });\r\n      });\r\n      this.title = \"修改角色\";\r\n    },\r\n    /** 选择角色权限范围触发 */\r\n    dataScopeSelectChange(value) {\r\n      if(value !== '2') {\r\n        this.$refs.dept.setCheckedKeys([]);\r\n      }\r\n    },\r\n    /** 分配数据权限操作 */\r\n    handleDataScope(row) {\r\n      this.reset();\r\n      const deptTreeSelect = this.getDeptTree(row.roleId);\r\n      getRole(row.roleId).then(response => {\r\n        this.form = response.data;\r\n        this.openDataScope = true;\r\n        this.$nextTick(() => {\r\n          deptTreeSelect.then(res => {\r\n            this.$refs.dept.setCheckedKeys(res.checkedKeys);\r\n          });\r\n        });\r\n      });\r\n      this.title = \"分配数据权限\";\r\n    },\r\n    /** 分配用户操作 */\r\n    handleAuthUser: function(row) {\r\n      const roleId = row.roleId;\r\n      this.$router.push(\"/system/role-auth/user/\" + roleId);\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.roleId != undefined) {\r\n            this.form.menuIds = this.getMenuAllCheckedKeys();\r\n            updateRole(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            this.form.menuIds = this.getMenuAllCheckedKeys();\r\n            addRole(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 提交按钮（数据权限） */\r\n    submitDataScope: function() {\r\n      if (this.form.roleId != undefined) {\r\n        this.form.deptIds = this.getDeptAllCheckedKeys();\r\n        dataScope(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\");\r\n          this.openDataScope = false;\r\n          this.getList();\r\n        });\r\n      }\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const roleIds = row.roleId || this.ids;\r\n      this.$modal.confirm('是否确认删除角色编号为\"' + roleIds + '\"的数据项？').then(function() {\r\n        return delRole(roleIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/role/export', {\r\n        ...this.queryParams\r\n      }, `role_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script>"], "mappings": ";;;;;;;;;;;;;;;AA8PA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,aAAA;MACAC,UAAA;MACAC,WAAA;MACAC,UAAA;MACAC,WAAA;MACA;MACAC,SAAA;MACA;MACAC,gBAAA,GACA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACA;MACAC,WAAA;MACA;MACAC,WAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA,EAAAC,SAAA;QACAC,OAAA,EAAAD,SAAA;QACAE,MAAA,EAAAF;MACA;MACA;MACAG,IAAA;MACAC,YAAA;QACAC,QAAA;QACAZ,KAAA;MACA;MACA;MACAa,KAAA;QACAP,QAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,OAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,QAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,aACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAtC,OAAA;MACA,IAAAuC,cAAA,OAAAC,YAAA,MAAApB,WAAA,OAAAN,SAAA,GAAA2B,IAAA,WAAAC,QAAA;QACAJ,KAAA,CAAAhC,QAAA,GAAAoC,QAAA,CAAAC,IAAA;QACAL,KAAA,CAAAjC,KAAA,GAAAqC,QAAA,CAAArC,KAAA;QACAiC,KAAA,CAAAtC,OAAA;MACA,CACA;IACA;IACA,cACA4C,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,gBAAA,IAAAL,IAAA,WAAAC,QAAA;QACAG,MAAA,CAAA3B,WAAA,GAAAwB,QAAA,CAAA3C,IAAA;MACA;IACA;IACA;IACAgD,qBAAA,WAAAA,sBAAA;MACA;MACA,IAAAC,WAAA,QAAAC,KAAA,CAAAC,IAAA,CAAAC,cAAA;MACA;MACA,IAAAC,eAAA,QAAAH,KAAA,CAAAC,IAAA,CAAAG,kBAAA;MACAL,WAAA,CAAAM,OAAA,CAAAC,KAAA,CAAAP,WAAA,EAAAI,eAAA;MACA,OAAAJ,WAAA;IACA;IACA;IACAQ,qBAAA,WAAAA,sBAAA;MACA;MACA,IAAAR,WAAA,QAAAC,KAAA,CAAAQ,IAAA,CAAAN,cAAA;MACA;MACA,IAAAC,eAAA,QAAAH,KAAA,CAAAQ,IAAA,CAAAJ,kBAAA;MACAL,WAAA,CAAAM,OAAA,CAAAC,KAAA,CAAAP,WAAA,EAAAI,eAAA;MACA,OAAAJ,WAAA;IACA;IACA,oBACAU,qBAAA,WAAAA,sBAAAC,MAAA;MAAA,IAAAC,MAAA;MACA,WAAAC,wBAAA,EAAAF,MAAA,EAAAlB,IAAA,WAAAC,QAAA;QACAkB,MAAA,CAAA1C,WAAA,GAAAwB,QAAA,CAAAoB,KAAA;QACA,OAAApB,QAAA;MACA;IACA;IACA,oBACAqB,WAAA,WAAAA,YAAAJ,MAAA;MAAA,IAAAK,MAAA;MACA,WAAAC,oBAAA,EAAAN,MAAA,EAAAlB,IAAA,WAAAC,QAAA;QACAsB,MAAA,CAAA7C,WAAA,GAAAuB,QAAA,CAAAwB,KAAA;QACA,OAAAxB,QAAA;MACA;IACA;IACA;IACAyB,kBAAA,WAAAA,mBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,IAAA,GAAAF,GAAA,CAAA1C,MAAA;MACA,KAAA6C,MAAA,CAAAC,OAAA,UAAAF,IAAA,UAAAF,GAAA,CAAA7C,QAAA,YAAAkB,IAAA;QACA,WAAAgC,sBAAA,EAAAL,GAAA,CAAAT,MAAA,EAAAS,GAAA,CAAA1C,MAAA;MACA,GAAAe,IAAA;QACA4B,MAAA,CAAAE,MAAA,CAAAG,UAAA,CAAAJ,IAAA;MACA,GAAAK,KAAA;QACAP,GAAA,CAAA1C,MAAA,GAAA0C,GAAA,CAAA1C,MAAA;MACA;IACA;IACA;IACAkD,MAAA,WAAAA,OAAA;MACA,KAAApE,IAAA;MACA,KAAAqE,KAAA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAArE,aAAA;MACA,KAAAoE,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,SAAA5B,KAAA,CAAAC,IAAA,IAAA1B,SAAA;QACA,KAAAyB,KAAA,CAAAC,IAAA,CAAA6B,cAAA;MACA;MACA,KAAArE,UAAA,UACA,KAAAC,WAAA,UACA,KAAAC,UAAA,SACA,KAAAC,WAAA,UACA,KAAAc,IAAA;QACAgC,MAAA,EAAAnC,SAAA;QACAD,QAAA,EAAAC,SAAA;QACAC,OAAA,EAAAD,SAAA;QACAU,QAAA;QACAR,MAAA;QACAsD,OAAA;QACAC,OAAA;QACAC,iBAAA;QACAC,iBAAA;QACAC,MAAA,EAAA5D;MACA;MACA,KAAA6D,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAlE,WAAA,CAAAC,OAAA;MACA,KAAAe,OAAA;IACA;IACA,aACAmD,UAAA,WAAAA,WAAA;MACA,KAAAzE,SAAA;MACA,KAAAuE,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAxF,GAAA,GAAAwF,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAhC,MAAA;MAAA;MACA,KAAAzD,MAAA,GAAAuF,SAAA,CAAAG,MAAA;MACA,KAAAzF,QAAA,IAAAsF,SAAA,CAAAG,MAAA;IACA;IACA;IACAC,aAAA,WAAAA,cAAAC,OAAA,EAAA1B,GAAA;MACA,QAAA0B,OAAA;QACA;UACA,KAAAC,eAAA,CAAA3B,GAAA;UACA;QACA;UACA,KAAA4B,cAAA,CAAA5B,GAAA;UACA;QACA;UACA;MACA;IACA;IACA;IACA6B,uBAAA,WAAAA,wBAAAjF,KAAA,EAAAkF,IAAA;MACA,IAAAA,IAAA;QACA,IAAAC,QAAA,QAAAjF,WAAA;QACA,SAAAkF,CAAA,MAAAA,CAAA,GAAAD,QAAA,CAAAP,MAAA,EAAAQ,CAAA;UACA,KAAAnD,KAAA,CAAAC,IAAA,CAAAmD,KAAA,CAAAC,QAAA,CAAAH,QAAA,CAAAC,CAAA,EAAAG,EAAA,EAAAC,QAAA,GAAAxF,KAAA;QACA;MACA,WAAAkF,IAAA;QACA,IAAAC,SAAA,QAAAhF,WAAA;QACA,SAAAiF,EAAA,MAAAA,EAAA,GAAAD,SAAA,CAAAP,MAAA,EAAAQ,EAAA;UACA,KAAAnD,KAAA,CAAAQ,IAAA,CAAA4C,KAAA,CAAAC,QAAA,CAAAH,SAAA,CAAAC,EAAA,EAAAG,EAAA,EAAAC,QAAA,GAAAxF,KAAA;QACA;MACA;IACA;IACA;IACAyF,wBAAA,WAAAA,yBAAAzF,KAAA,EAAAkF,IAAA;MACA,IAAAA,IAAA;QACA,KAAAjD,KAAA,CAAAC,IAAA,CAAAwD,eAAA,CAAA1F,KAAA,QAAAE,WAAA;MACA,WAAAgF,IAAA;QACA,KAAAjD,KAAA,CAAAQ,IAAA,CAAAiD,eAAA,CAAA1F,KAAA,QAAAG,WAAA;MACA;IACA;IACA;IACAwF,wBAAA,WAAAA,yBAAA3F,KAAA,EAAAkF,IAAA;MACA,IAAAA,IAAA;QACA,KAAAvE,IAAA,CAAAuD,iBAAA,GAAAlE,KAAA;MACA,WAAAkF,IAAA;QACA,KAAAvE,IAAA,CAAAwD,iBAAA,GAAAnE,KAAA;MACA;IACA;IACA,aACA4F,SAAA,WAAAA,UAAA;MACA,KAAA/B,KAAA;MACA,KAAAjC,iBAAA;MACA,KAAApC,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAsG,YAAA,WAAAA,aAAAzC,GAAA;MAAA,IAAA0C,MAAA;MACA,KAAAjC,KAAA;MACA,IAAAlB,MAAA,GAAAS,GAAA,CAAAT,MAAA,SAAA1D,GAAA;MACA,IAAA8G,QAAA,QAAArD,qBAAA,CAAAC,MAAA;MACA,IAAAqD,aAAA,EAAArD,MAAA,EAAAlB,IAAA,WAAAC,QAAA;QACAoE,MAAA,CAAAnF,IAAA,GAAAe,QAAA,CAAA3C,IAAA;QACA+G,MAAA,CAAAtG,IAAA;QACAsG,MAAA,CAAAG,SAAA;UACAF,QAAA,CAAAtE,IAAA,WAAAyE,GAAA;YACA,IAAAlE,WAAA,GAAAkE,GAAA,CAAAlE,WAAA;YACAA,WAAA,CAAAmE,OAAA,WAAAC,CAAA;cACAN,MAAA,CAAAG,SAAA;gBACAH,MAAA,CAAA7D,KAAA,CAAAC,IAAA,CAAAmE,UAAA,CAAAD,CAAA;cACA;YACA;UACA;QACA;MACA;MACA,KAAA7G,KAAA;IACA;IACA,iBACA+G,qBAAA,WAAAA,sBAAAtG,KAAA;MACA,IAAAA,KAAA;QACA,KAAAiC,KAAA,CAAAQ,IAAA,CAAAsB,cAAA;MACA;IACA;IACA,eACAgB,eAAA,WAAAA,gBAAA3B,GAAA;MAAA,IAAAmD,MAAA;MACA,KAAA1C,KAAA;MACA,IAAAZ,cAAA,QAAAF,WAAA,CAAAK,GAAA,CAAAT,MAAA;MACA,IAAAqD,aAAA,EAAA5C,GAAA,CAAAT,MAAA,EAAAlB,IAAA,WAAAC,QAAA;QACA6E,MAAA,CAAA5F,IAAA,GAAAe,QAAA,CAAA3C,IAAA;QACAwH,MAAA,CAAA9G,aAAA;QACA8G,MAAA,CAAAN,SAAA;UACAhD,cAAA,CAAAxB,IAAA,WAAAyE,GAAA;YACAK,MAAA,CAAAtE,KAAA,CAAAQ,IAAA,CAAAsB,cAAA,CAAAmC,GAAA,CAAAlE,WAAA;UACA;QACA;MACA;MACA,KAAAzC,KAAA;IACA;IACA;IACAyF,cAAA,WAAAA,eAAA5B,GAAA;MACA,IAAAT,MAAA,GAAAS,GAAA,CAAAT,MAAA;MACA,KAAA6D,OAAA,CAAAC,IAAA,6BAAA9D,MAAA;IACA;IACA;IACA+D,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAA1E,KAAA,SAAA2E,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAF,MAAA,CAAAhG,IAAA,CAAAgC,MAAA,IAAAnC,SAAA;YACAmG,MAAA,CAAAhG,IAAA,CAAAqD,OAAA,GAAA2C,MAAA,CAAA5E,qBAAA;YACA,IAAA+E,gBAAA,EAAAH,MAAA,CAAAhG,IAAA,EAAAc,IAAA,WAAAC,QAAA;cACAiF,MAAA,CAAApD,MAAA,CAAAG,UAAA;cACAiD,MAAA,CAAAnH,IAAA;cACAmH,MAAA,CAAAvF,OAAA;YACA;UACA;YACAuF,MAAA,CAAAhG,IAAA,CAAAqD,OAAA,GAAA2C,MAAA,CAAA5E,qBAAA;YACA,IAAAgF,aAAA,EAAAJ,MAAA,CAAAhG,IAAA,EAAAc,IAAA,WAAAC,QAAA;cACAiF,MAAA,CAAApD,MAAA,CAAAG,UAAA;cACAiD,MAAA,CAAAnH,IAAA;cACAmH,MAAA,CAAAvF,OAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACA4F,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,SAAAtG,IAAA,CAAAgC,MAAA,IAAAnC,SAAA;QACA,KAAAG,IAAA,CAAAsD,OAAA,QAAAzB,qBAAA;QACA,IAAA0E,eAAA,OAAAvG,IAAA,EAAAc,IAAA,WAAAC,QAAA;UACAuF,MAAA,CAAA1D,MAAA,CAAAG,UAAA;UACAuD,MAAA,CAAAxH,aAAA;UACAwH,MAAA,CAAA7F,OAAA;QACA;MACA;IACA;IACA,aACA+F,YAAA,WAAAA,aAAA/D,GAAA;MAAA,IAAAgE,MAAA;MACA,IAAAC,OAAA,GAAAjE,GAAA,CAAAT,MAAA,SAAA1D,GAAA;MACA,KAAAsE,MAAA,CAAAC,OAAA,kBAAA6D,OAAA,aAAA5F,IAAA;QACA,WAAA6F,aAAA,EAAAD,OAAA;MACA,GAAA5F,IAAA;QACA2F,MAAA,CAAAhG,OAAA;QACAgG,MAAA,CAAA7D,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA;IACA;IACA,aACA4D,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,2BAAAC,cAAA,CAAAC,OAAA,MACA,KAAAtH,WAAA,WAAAuH,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}