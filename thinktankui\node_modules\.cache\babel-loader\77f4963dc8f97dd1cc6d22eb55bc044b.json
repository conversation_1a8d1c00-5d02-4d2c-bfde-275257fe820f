{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\utils\\request.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\utils\\request.js", "mtime": 1749104047634}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749104418875}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON>nu<PERSON>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"}, {"version": 3, "names": ["_axios", "_interopRequireDefault", "require", "_elementUi", "_store", "_auth", "_errorCode", "_ruoyi", "_cache", "_fileSaver", "downloadLoadingInstance", "is<PERSON><PERSON>gin", "exports", "show", "axios", "defaults", "headers", "service", "create", "baseURL", "process", "env", "VUE_APP_BASE_API", "timeout", "interceptors", "request", "use", "config", "isToken", "isRepeatSubmit", "repeatSubmit", "getToken", "method", "params", "url", "tansParams", "slice", "requestObj", "data", "_typeof2", "default", "JSON", "stringify", "time", "Date", "getTime", "requestSize", "Object", "keys", "length", "limitSize", "console", "warn", "concat", "session<PERSON>bj", "cache", "session", "getJSON", "undefined", "setJSON", "s_url", "s_data", "s_time", "interval", "message", "Promise", "reject", "Error", "error", "log", "response", "res", "code", "msg", "errorCode", "responseType", "MessageBox", "confirm", "confirmButtonText", "cancelButtonText", "type", "then", "store", "dispatch", "location", "href", "catch", "Message", "Notification", "title", "includes", "substr", "duration", "download", "filename", "Loading", "text", "spinner", "background", "post", "_objectSpread2", "transformRequest", "_ref", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "isBlob", "blob", "resText", "rspObj", "errMsg", "w", "_context", "n", "blobValidate", "Blob", "saveAs", "v", "parse", "close", "a", "_x", "apply", "arguments", "r", "_default"], "sources": ["D:/thinktank/thinktankui/src/utils/request.js"], "sourcesContent": ["import axios from 'axios'\r\nimport { Notification, MessageBox, Message, Loading } from 'element-ui'\r\nimport store from '@/store'\r\nimport { getToken } from '@/utils/auth'\r\nimport errorCode from '@/utils/errorCode'\r\nimport { tansParams, blobValidate } from \"@/utils/ruoyi\";\r\nimport cache from '@/plugins/cache'\r\nimport { saveAs } from 'file-saver'\r\n\r\nlet downloadLoadingInstance;\r\n// 是否显示重新登录\r\nexport let isRelogin = { show: false };\r\n\r\naxios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'\r\n// 创建axios实例\r\nconst service = axios.create({\r\n  // axios中请求配置有baseURL选项，表示请求URL公共部分\r\n  baseURL: process.env.VUE_APP_BASE_API,\r\n  // 超时\r\n  timeout: 10000\r\n})\r\n\r\n// request拦截器\r\nservice.interceptors.request.use(config => {\r\n  // 是否需要设置 token\r\n  const isToken = (config.headers || {}).isToken === false\r\n  // 是否需要防止数据重复提交\r\n  const isRepeatSubmit = (config.headers || {}).repeatSubmit === false\r\n  if (getToken() && !isToken) {\r\n    config.headers['Authorization'] = 'Bearer ' + getToken() // 让每个请求携带自定义token 请根据实际情况自行修改\r\n  }\r\n  // get请求映射params参数\r\n  if (config.method === 'get' && config.params) {\r\n    let url = config.url + '?' + tansParams(config.params);\r\n    url = url.slice(0, -1);\r\n    config.params = {};\r\n    config.url = url;\r\n  }\r\n  if (!isRepeatSubmit && (config.method === 'post' || config.method === 'put')) {\r\n    const requestObj = {\r\n      url: config.url,\r\n      data: typeof config.data === 'object' ? JSON.stringify(config.data) : config.data,\r\n      time: new Date().getTime()\r\n    }\r\n    const requestSize = Object.keys(JSON.stringify(requestObj)).length; // 请求数据大小\r\n    const limitSize = 5 * 1024 * 1024; // 限制存放数据5M\r\n    if (requestSize >= limitSize) {\r\n      console.warn(`[${config.url}]: ` + '请求数据大小超出允许的5M限制，无法进行防重复提交验证。')\r\n      return config;\r\n    }\r\n    const sessionObj = cache.session.getJSON('sessionObj')\r\n    if (sessionObj === undefined || sessionObj === null || sessionObj === '') {\r\n      cache.session.setJSON('sessionObj', requestObj)\r\n    } else {\r\n      const s_url = sessionObj.url;                  // 请求地址\r\n      const s_data = sessionObj.data;                // 请求数据\r\n      const s_time = sessionObj.time;                // 请求时间\r\n      const interval = 1000;                         // 间隔时间(ms)，小于此时间视为重复提交\r\n      if (s_data === requestObj.data && requestObj.time - s_time < interval && s_url === requestObj.url) {\r\n        const message = '数据正在处理，请勿重复提交';\r\n        console.warn(`[${s_url}]: ` + message)\r\n        return Promise.reject(new Error(message))\r\n      } else {\r\n        cache.session.setJSON('sessionObj', requestObj)\r\n      }\r\n    }\r\n  }\r\n  return config\r\n}, error => {\r\n    console.log(error)\r\n    Promise.reject(error)\r\n})\r\n\r\n// 响应拦截器\r\nservice.interceptors.response.use(res => {\r\n    // 未设置状态码则默认成功状态\r\n    const code = res.data.code || 200;\r\n    // 获取错误信息\r\n    const msg = errorCode[code] || res.data.msg || errorCode['default']\r\n    // 二进制数据则直接返回\r\n    if (res.request.responseType ===  'blob' || res.request.responseType ===  'arraybuffer') {\r\n      return res.data\r\n    }\r\n    if (code === 401) {\r\n      if (!isRelogin.show) {\r\n        isRelogin.show = true;\r\n        MessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', { confirmButtonText: '重新登录', cancelButtonText: '取消', type: 'warning' }).then(() => {\r\n          isRelogin.show = false;\r\n          store.dispatch('LogOut').then(() => {\r\n            location.href = '/index';\r\n          })\r\n      }).catch(() => {\r\n        isRelogin.show = false;\r\n      });\r\n    }\r\n      return Promise.reject('无效的会话，或者会话已过期，请重新登录。')\r\n    } else if (code === 500) {\r\n      Message({ message: msg, type: 'error' })\r\n      return Promise.reject(new Error(msg))\r\n    } else if (code === 601) {\r\n      Message({ message: msg, type: 'warning' })\r\n      return Promise.reject('error')\r\n    } else if (code !== 200) {\r\n      Notification.error({ title: msg })\r\n      return Promise.reject('error')\r\n    } else {\r\n      return res.data\r\n    }\r\n  },\r\n  error => {\r\n    console.log('err' + error)\r\n    let { message } = error;\r\n    if (message == \"Network Error\") {\r\n      message = \"后端接口连接异常\";\r\n    } else if (message.includes(\"timeout\")) {\r\n      message = \"系统接口请求超时\";\r\n    } else if (message.includes(\"Request failed with status code\")) {\r\n      message = \"系统接口\" + message.substr(message.length - 3) + \"异常\";\r\n    }\r\n    Message({ message: message, type: 'error', duration: 5 * 1000 })\r\n    return Promise.reject(error)\r\n  }\r\n)\r\n\r\n// 通用下载方法\r\nexport function download(url, params, filename, config) {\r\n  downloadLoadingInstance = Loading.service({ text: \"正在下载数据，请稍候\", spinner: \"el-icon-loading\", background: \"rgba(0, 0, 0, 0.7)\", })\r\n  return service.post(url, params, {\r\n    transformRequest: [(params) => { return tansParams(params) }],\r\n    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },\r\n    responseType: 'blob',\r\n    ...config\r\n  }).then(async (data) => {\r\n    const isBlob = blobValidate(data);\r\n    if (isBlob) {\r\n      const blob = new Blob([data])\r\n      saveAs(blob, filename)\r\n    } else {\r\n      const resText = await data.text();\r\n      const rspObj = JSON.parse(resText);\r\n      const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']\r\n      Message.error(errMsg);\r\n    }\r\n    downloadLoadingInstance.close();\r\n  }).catch((r) => {\r\n    console.error(r)\r\n    Message.error('下载文件出现错误，请联系管理员！')\r\n    downloadLoadingInstance.close();\r\n  })\r\n}\r\n\r\nexport default service\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,UAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AACA,IAAAM,MAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,UAAA,GAAAP,OAAA;AAEA,IAAIQ,uBAAuB;AAC3B;AACO,IAAIC,SAAS,GAAAC,OAAA,CAAAD,SAAA,GAAG;EAAEE,IAAI,EAAE;AAAM,CAAC;AAEtCC,cAAK,CAACC,QAAQ,CAACC,OAAO,CAAC,cAAc,CAAC,GAAG,gCAAgC;AACzE;AACA,IAAMC,OAAO,GAAGH,cAAK,CAACI,MAAM,CAAC;EAC3B;EACAC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAgB;EACrC;EACAC,OAAO,EAAE;AACX,CAAC,CAAC;;AAEF;AACAN,OAAO,CAACO,YAAY,CAACC,OAAO,CAACC,GAAG,CAAC,UAAAC,MAAM,EAAI;EACzC;EACA,IAAMC,OAAO,GAAG,CAACD,MAAM,CAACX,OAAO,IAAI,CAAC,CAAC,EAAEY,OAAO,KAAK,KAAK;EACxD;EACA,IAAMC,cAAc,GAAG,CAACF,MAAM,CAACX,OAAO,IAAI,CAAC,CAAC,EAAEc,YAAY,KAAK,KAAK;EACpE,IAAI,IAAAC,cAAQ,EAAC,CAAC,IAAI,CAACH,OAAO,EAAE;IAC1BD,MAAM,CAACX,OAAO,CAAC,eAAe,CAAC,GAAG,SAAS,GAAG,IAAAe,cAAQ,EAAC,CAAC,EAAC;EAC3D;EACA;EACA,IAAIJ,MAAM,CAACK,MAAM,KAAK,KAAK,IAAIL,MAAM,CAACM,MAAM,EAAE;IAC5C,IAAIC,GAAG,GAAGP,MAAM,CAACO,GAAG,GAAG,GAAG,GAAG,IAAAC,iBAAU,EAACR,MAAM,CAACM,MAAM,CAAC;IACtDC,GAAG,GAAGA,GAAG,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtBT,MAAM,CAACM,MAAM,GAAG,CAAC,CAAC;IAClBN,MAAM,CAACO,GAAG,GAAGA,GAAG;EAClB;EACA,IAAI,CAACL,cAAc,KAAKF,MAAM,CAACK,MAAM,KAAK,MAAM,IAAIL,MAAM,CAACK,MAAM,KAAK,KAAK,CAAC,EAAE;IAC5E,IAAMK,UAAU,GAAG;MACjBH,GAAG,EAAEP,MAAM,CAACO,GAAG;MACfI,IAAI,EAAE,IAAAC,QAAA,CAAAC,OAAA,EAAOb,MAAM,CAACW,IAAI,MAAK,QAAQ,GAAGG,IAAI,CAACC,SAAS,CAACf,MAAM,CAACW,IAAI,CAAC,GAAGX,MAAM,CAACW,IAAI;MACjFK,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC;IAC3B,CAAC;IACD,IAAMC,WAAW,GAAGC,MAAM,CAACC,IAAI,CAACP,IAAI,CAACC,SAAS,CAACL,UAAU,CAAC,CAAC,CAACY,MAAM,CAAC,CAAC;IACpE,IAAMC,SAAS,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IACnC,IAAIJ,WAAW,IAAII,SAAS,EAAE;MAC5BC,OAAO,CAACC,IAAI,CAAC,IAAAC,MAAA,CAAI1B,MAAM,CAACO,GAAG,WAAQ,8BAA8B,CAAC;MAClE,OAAOP,MAAM;IACf;IACA,IAAM2B,UAAU,GAAGC,cAAK,CAACC,OAAO,CAACC,OAAO,CAAC,YAAY,CAAC;IACtD,IAAIH,UAAU,KAAKI,SAAS,IAAIJ,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,EAAE,EAAE;MACxEC,cAAK,CAACC,OAAO,CAACG,OAAO,CAAC,YAAY,EAAEtB,UAAU,CAAC;IACjD,CAAC,MAAM;MACL,IAAMuB,KAAK,GAAGN,UAAU,CAACpB,GAAG,CAAC,CAAkB;MAC/C,IAAM2B,MAAM,GAAGP,UAAU,CAAChB,IAAI,CAAC,CAAgB;MAC/C,IAAMwB,MAAM,GAAGR,UAAU,CAACX,IAAI,CAAC,CAAgB;MAC/C,IAAMoB,QAAQ,GAAG,IAAI,CAAC,CAAyB;MAC/C,IAAIF,MAAM,KAAKxB,UAAU,CAACC,IAAI,IAAID,UAAU,CAACM,IAAI,GAAGmB,MAAM,GAAGC,QAAQ,IAAIH,KAAK,KAAKvB,UAAU,CAACH,GAAG,EAAE;QACjG,IAAM8B,OAAO,GAAG,eAAe;QAC/Bb,OAAO,CAACC,IAAI,CAAC,IAAAC,MAAA,CAAIO,KAAK,WAAQI,OAAO,CAAC;QACtC,OAAOC,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAACH,OAAO,CAAC,CAAC;MAC3C,CAAC,MAAM;QACLT,cAAK,CAACC,OAAO,CAACG,OAAO,CAAC,YAAY,EAAEtB,UAAU,CAAC;MACjD;IACF;EACF;EACA,OAAOV,MAAM;AACf,CAAC,EAAE,UAAAyC,KAAK,EAAI;EACRjB,OAAO,CAACkB,GAAG,CAACD,KAAK,CAAC;EAClBH,OAAO,CAACC,MAAM,CAACE,KAAK,CAAC;AACzB,CAAC,CAAC;;AAEF;AACAnD,OAAO,CAACO,YAAY,CAAC8C,QAAQ,CAAC5C,GAAG,CAAC,UAAA6C,GAAG,EAAI;EACrC;EACA,IAAMC,IAAI,GAAGD,GAAG,CAACjC,IAAI,CAACkC,IAAI,IAAI,GAAG;EACjC;EACA,IAAMC,GAAG,GAAGC,kBAAS,CAACF,IAAI,CAAC,IAAID,GAAG,CAACjC,IAAI,CAACmC,GAAG,IAAIC,kBAAS,CAAC,SAAS,CAAC;EACnE;EACA,IAAIH,GAAG,CAAC9C,OAAO,CAACkD,YAAY,KAAM,MAAM,IAAIJ,GAAG,CAAC9C,OAAO,CAACkD,YAAY,KAAM,aAAa,EAAE;IACvF,OAAOJ,GAAG,CAACjC,IAAI;EACjB;EACA,IAAIkC,IAAI,KAAK,GAAG,EAAE;IAChB,IAAI,CAAC7D,SAAS,CAACE,IAAI,EAAE;MACnBF,SAAS,CAACE,IAAI,GAAG,IAAI;MACrB+D,qBAAU,CAACC,OAAO,CAAC,2BAA2B,EAAE,MAAM,EAAE;QAAEC,iBAAiB,EAAE,MAAM;QAAEC,gBAAgB,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAU,CAAC,CAAC,CAACC,IAAI,CAAC,YAAM;QACzItE,SAAS,CAACE,IAAI,GAAG,KAAK;QACtBqE,cAAK,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAACF,IAAI,CAAC,YAAM;UAClCG,QAAQ,CAACC,IAAI,GAAG,QAAQ;QAC1B,CAAC,CAAC;MACN,CAAC,CAAC,CAACC,KAAK,CAAC,YAAM;QACb3E,SAAS,CAACE,IAAI,GAAG,KAAK;MACxB,CAAC,CAAC;IACJ;IACE,OAAOoD,OAAO,CAACC,MAAM,CAAC,sBAAsB,CAAC;EAC/C,CAAC,MAAM,IAAIM,IAAI,KAAK,GAAG,EAAE;IACvB,IAAAe,kBAAO,EAAC;MAAEvB,OAAO,EAAES,GAAG;MAAEO,IAAI,EAAE;IAAQ,CAAC,CAAC;IACxC,OAAOf,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAACM,GAAG,CAAC,CAAC;EACvC,CAAC,MAAM,IAAID,IAAI,KAAK,GAAG,EAAE;IACvB,IAAAe,kBAAO,EAAC;MAAEvB,OAAO,EAAES,GAAG;MAAEO,IAAI,EAAE;IAAU,CAAC,CAAC;IAC1C,OAAOf,OAAO,CAACC,MAAM,CAAC,OAAO,CAAC;EAChC,CAAC,MAAM,IAAIM,IAAI,KAAK,GAAG,EAAE;IACvBgB,uBAAY,CAACpB,KAAK,CAAC;MAAEqB,KAAK,EAAEhB;IAAI,CAAC,CAAC;IAClC,OAAOR,OAAO,CAACC,MAAM,CAAC,OAAO,CAAC;EAChC,CAAC,MAAM;IACL,OAAOK,GAAG,CAACjC,IAAI;EACjB;AACF,CAAC,EACD,UAAA8B,KAAK,EAAI;EACPjB,OAAO,CAACkB,GAAG,CAAC,KAAK,GAAGD,KAAK,CAAC;EAC1B,IAAMJ,OAAO,GAAKI,KAAK,CAAjBJ,OAAO;EACb,IAAIA,OAAO,IAAI,eAAe,EAAE;IAC9BA,OAAO,GAAG,UAAU;EACtB,CAAC,MAAM,IAAIA,OAAO,CAAC0B,QAAQ,CAAC,SAAS,CAAC,EAAE;IACtC1B,OAAO,GAAG,UAAU;EACtB,CAAC,MAAM,IAAIA,OAAO,CAAC0B,QAAQ,CAAC,iCAAiC,CAAC,EAAE;IAC9D1B,OAAO,GAAG,MAAM,GAAGA,OAAO,CAAC2B,MAAM,CAAC3B,OAAO,CAACf,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;EAC9D;EACA,IAAAsC,kBAAO,EAAC;IAAEvB,OAAO,EAAEA,OAAO;IAAEgB,IAAI,EAAE,OAAO;IAAEY,QAAQ,EAAE,CAAC,GAAG;EAAK,CAAC,CAAC;EAChE,OAAO3B,OAAO,CAACC,MAAM,CAACE,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACO,SAASyB,QAAQA,CAAC3D,GAAG,EAAED,MAAM,EAAE6D,QAAQ,EAAEnE,MAAM,EAAE;EACtDjB,uBAAuB,GAAGqF,kBAAO,CAAC9E,OAAO,CAAC;IAAE+E,IAAI,EAAE,YAAY;IAAEC,OAAO,EAAE,iBAAiB;IAAEC,UAAU,EAAE;EAAsB,CAAC,CAAC;EAChI,OAAOjF,OAAO,CAACkF,IAAI,CAACjE,GAAG,EAAED,MAAM,MAAAmE,cAAA,CAAA5D,OAAA;IAC7B6D,gBAAgB,EAAE,CAAC,UAACpE,MAAM,EAAK;MAAE,OAAO,IAAAE,iBAAU,EAACF,MAAM,CAAC;IAAC,CAAC,CAAC;IAC7DjB,OAAO,EAAE;MAAE,cAAc,EAAE;IAAoC,CAAC;IAChE2D,YAAY,EAAE;EAAM,GACjBhD,MAAM,CACV,CAAC,CAACsD,IAAI;IAAA,IAAAqB,IAAA,OAAAC,kBAAA,CAAA/D,OAAA,mBAAAgE,aAAA,CAAAhE,OAAA,IAAAiE,CAAA,CAAC,SAAAC,QAAOpE,IAAI;MAAA,IAAAqE,MAAA,EAAAC,IAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,MAAA;MAAA,WAAAP,aAAA,CAAAhE,OAAA,IAAAwE,CAAA,WAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,CAAA;UAAA;YACXP,MAAM,GAAG,IAAAQ,mBAAY,EAAC7E,IAAI,CAAC;YAAA,KAC7BqE,MAAM;cAAAM,QAAA,CAAAC,CAAA;cAAA;YAAA;YACFN,IAAI,GAAG,IAAIQ,IAAI,CAAC,CAAC9E,IAAI,CAAC,CAAC;YAC7B,IAAA+E,iBAAM,EAACT,IAAI,EAAEd,QAAQ,CAAC;YAAAmB,QAAA,CAAAC,CAAA;YAAA;UAAA;YAAAD,QAAA,CAAAC,CAAA;YAAA,OAEA5E,IAAI,CAAC0D,IAAI,CAAC,CAAC;UAAA;YAA3Ba,OAAO,GAAAI,QAAA,CAAAK,CAAA;YACPR,MAAM,GAAGrE,IAAI,CAAC8E,KAAK,CAACV,OAAO,CAAC;YAC5BE,MAAM,GAAGrC,kBAAS,CAACoC,MAAM,CAACtC,IAAI,CAAC,IAAIsC,MAAM,CAACrC,GAAG,IAAIC,kBAAS,CAAC,SAAS,CAAC;YAC3Ea,kBAAO,CAACnB,KAAK,CAAC2C,MAAM,CAAC;UAAC;YAExBrG,uBAAuB,CAAC8G,KAAK,CAAC,CAAC;UAAC;YAAA,OAAAP,QAAA,CAAAQ,CAAA;QAAA;MAAA,GAAAf,OAAA;IAAA,CACjC;IAAA,iBAAAgB,EAAA;MAAA,OAAApB,IAAA,CAAAqB,KAAA,OAAAC,SAAA;IAAA;EAAA,IAAC,CAACtC,KAAK,CAAC,UAACuC,CAAC,EAAK;IACd1E,OAAO,CAACiB,KAAK,CAACyD,CAAC,CAAC;IAChBtC,kBAAO,CAACnB,KAAK,CAAC,kBAAkB,CAAC;IACjC1D,uBAAuB,CAAC8G,KAAK,CAAC,CAAC;EACjC,CAAC,CAAC;AACJ;AAAC,IAAAM,QAAA,GAAAlH,OAAA,CAAA4B,OAAA,GAEcvB,OAAO", "ignoreList": []}]}