{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\api\\monitor\\job.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\api\\monitor\\job.js", "mtime": 1749104047591}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749104418875}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listJob", "query", "request", "url", "method", "params", "get<PERSON>ob", "jobId", "addJob", "data", "updateJob", "<PERSON><PERSON><PERSON>", "changeJobStatus", "status", "runJob", "jobGroup"], "sources": ["D:/thinktank/thinktankui/src/api/monitor/job.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询定时任务调度列表\r\nexport function listJob(query) {\r\n  return request({\r\n    url: '/monitor/job/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询定时任务调度详细\r\nexport function getJob(jobId) {\r\n  return request({\r\n    url: '/monitor/job/' + jobId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增定时任务调度\r\nexport function addJob(data) {\r\n  return request({\r\n    url: '/monitor/job',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改定时任务调度\r\nexport function updateJob(data) {\r\n  return request({\r\n    url: '/monitor/job',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除定时任务调度\r\nexport function delJob(jobId) {\r\n  return request({\r\n    url: '/monitor/job/' + jobId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 任务状态修改\r\nexport function changeJobStatus(jobId, status) {\r\n  const data = {\r\n    jobId,\r\n    status\r\n  }\r\n  return request({\r\n    url: '/monitor/job/changeStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n\r\n// 定时任务立即执行一次\r\nexport function runJob(jobId, jobGroup) {\r\n  const data = {\r\n    jobId,\r\n    jobGroup\r\n  }\r\n  return request({\r\n    url: '/monitor/job/run',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,OAAOA,CAACC,KAAK,EAAE;EAC7B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,MAAMA,CAACC,KAAK,EAAE;EAC5B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,KAAK;IAC5BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,MAAMA,CAACC,IAAI,EAAE;EAC3B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,SAASA,CAACD,IAAI,EAAE;EAC9B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,MAAMA,CAACJ,KAAK,EAAE;EAC5B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,KAAK;IAC5BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,eAAeA,CAACL,KAAK,EAAEM,MAAM,EAAE;EAC7C,IAAMJ,IAAI,GAAG;IACXF,KAAK,EAALA,KAAK;IACLM,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAGA;AACO,SAASK,MAAMA,CAACP,KAAK,EAAEQ,QAAQ,EAAE;EACtC,IAAMN,IAAI,GAAG;IACXF,KAAK,EAALA,KAAK;IACLQ,QAAQ,EAARA;EACF,CAAC;EACD,OAAO,IAAAb,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}