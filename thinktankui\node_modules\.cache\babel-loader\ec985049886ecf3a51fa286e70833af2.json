{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\store\\modules\\user.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\store\\modules\\user.js", "mtime": 1749104047629}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749104418875}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_login", "require", "_auth", "_validate", "_profile", "_interopRequireDefault", "user", "state", "token", "getToken", "id", "name", "avatar", "roles", "permissions", "mutations", "SET_TOKEN", "SET_ID", "SET_NAME", "SET_AVATAR", "SET_ROLES", "SET_PERMISSIONS", "actions", "<PERSON><PERSON>", "_ref", "userInfo", "commit", "username", "trim", "password", "code", "uuid", "Promise", "resolve", "reject", "login", "then", "res", "setToken", "catch", "error", "GetInfo", "_ref2", "getInfo", "isHttp", "isEmpty", "defAva", "process", "env", "VUE_APP_BASE_API", "length", "userId", "userName", "LogOut", "_ref3", "logout", "removeToken", "FedLogOut", "_ref4", "_default", "exports", "default"], "sources": ["D:/thinktank/thinktankui/src/store/modules/user.js"], "sourcesContent": ["import { login, logout, getInfo } from '@/api/login'\r\nimport { getToken, setToken, removeToken } from '@/utils/auth'\r\nimport { isHttp, isEmpty } from \"@/utils/validate\"\r\nimport defAva from '@/assets/images/profile.jpg'\r\n\r\nconst user = {\r\n  state: {\r\n    token: getToken(),\r\n    id: '',\r\n    name: '',\r\n    avatar: '',\r\n    roles: [],\r\n    permissions: []\r\n  },\r\n\r\n  mutations: {\r\n    SET_TOKEN: (state, token) => {\r\n      state.token = token\r\n    },\r\n    SET_ID: (state, id) => {\r\n      state.id = id\r\n    },\r\n    SET_NAME: (state, name) => {\r\n      state.name = name\r\n    },\r\n    SET_AVATAR: (state, avatar) => {\r\n      state.avatar = avatar\r\n    },\r\n    SET_ROLES: (state, roles) => {\r\n      state.roles = roles\r\n    },\r\n    SET_PERMISSIONS: (state, permissions) => {\r\n      state.permissions = permissions\r\n    }\r\n  },\r\n\r\n  actions: {\r\n    // 登录\r\n    Login({ commit }, userInfo) {\r\n      const username = userInfo.username.trim()\r\n      const password = userInfo.password\r\n      const code = userInfo.code\r\n      const uuid = userInfo.uuid\r\n      return new Promise((resolve, reject) => {\r\n        login(username, password, code, uuid).then(res => {\r\n          setToken(res.token)\r\n          commit('SET_TOKEN', res.token)\r\n          resolve()\r\n        }).catch(error => {\r\n          reject(error)\r\n        })\r\n      })\r\n    },\r\n\r\n    // 获取用户信息\r\n    GetInfo({ commit, state }) {\r\n      return new Promise((resolve, reject) => {\r\n        getInfo().then(res => {\r\n          const user = res.user\r\n          let avatar = user.avatar || \"\"\r\n          if (!isHttp(avatar)) {\r\n            avatar = (isEmpty(avatar)) ? defAva : process.env.VUE_APP_BASE_API + avatar\r\n          }\r\n          if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组\r\n            commit('SET_ROLES', res.roles)\r\n            commit('SET_PERMISSIONS', res.permissions)\r\n          } else {\r\n            commit('SET_ROLES', ['ROLE_DEFAULT'])\r\n          }\r\n          commit('SET_ID', user.userId)\r\n          commit('SET_NAME', user.userName)\r\n          commit('SET_AVATAR', avatar)\r\n          resolve(res)\r\n        }).catch(error => {\r\n          reject(error)\r\n        })\r\n      })\r\n    },\r\n\r\n    // 退出系统\r\n    LogOut({ commit, state }) {\r\n      return new Promise((resolve, reject) => {\r\n        logout(state.token).then(() => {\r\n          commit('SET_TOKEN', '')\r\n          commit('SET_ROLES', [])\r\n          commit('SET_PERMISSIONS', [])\r\n          removeToken()\r\n          resolve()\r\n        }).catch(error => {\r\n          reject(error)\r\n        })\r\n      })\r\n    },\r\n\r\n    // 前端 登出\r\n    FedLogOut({ commit }) {\r\n      return new Promise(resolve => {\r\n        commit('SET_TOKEN', '')\r\n        removeToken()\r\n        resolve()\r\n      })\r\n    }\r\n  }\r\n}\r\n\r\nexport default user\r\n"], "mappings": ";;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAC,sBAAA,CAAAJ,OAAA;AAEA,IAAMK,IAAI,GAAG;EACXC,KAAK,EAAE;IACLC,KAAK,EAAE,IAAAC,cAAQ,EAAC,CAAC;IACjBC,EAAE,EAAE,EAAE;IACNC,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE;EACf,CAAC;EAEDC,SAAS,EAAE;IACTC,SAAS,EAAE,SAAXA,SAASA,CAAGT,KAAK,EAAEC,KAAK,EAAK;MAC3BD,KAAK,CAACC,KAAK,GAAGA,KAAK;IACrB,CAAC;IACDS,MAAM,EAAE,SAARA,MAAMA,CAAGV,KAAK,EAAEG,EAAE,EAAK;MACrBH,KAAK,CAACG,EAAE,GAAGA,EAAE;IACf,CAAC;IACDQ,QAAQ,EAAE,SAAVA,QAAQA,CAAGX,KAAK,EAAEI,IAAI,EAAK;MACzBJ,KAAK,CAACI,IAAI,GAAGA,IAAI;IACnB,CAAC;IACDQ,UAAU,EAAE,SAAZA,UAAUA,CAAGZ,KAAK,EAAEK,MAAM,EAAK;MAC7BL,KAAK,CAACK,MAAM,GAAGA,MAAM;IACvB,CAAC;IACDQ,SAAS,EAAE,SAAXA,SAASA,CAAGb,KAAK,EAAEM,KAAK,EAAK;MAC3BN,KAAK,CAACM,KAAK,GAAGA,KAAK;IACrB,CAAC;IACDQ,eAAe,EAAE,SAAjBA,eAAeA,CAAGd,KAAK,EAAEO,WAAW,EAAK;MACvCP,KAAK,CAACO,WAAW,GAAGA,WAAW;IACjC;EACF,CAAC;EAEDQ,OAAO,EAAE;IACP;IACAC,KAAK,WAALA,KAAKA,CAAAC,IAAA,EAAaC,QAAQ,EAAE;MAAA,IAApBC,MAAM,GAAAF,IAAA,CAANE,MAAM;MACZ,IAAMC,QAAQ,GAAGF,QAAQ,CAACE,QAAQ,CAACC,IAAI,CAAC,CAAC;MACzC,IAAMC,QAAQ,GAAGJ,QAAQ,CAACI,QAAQ;MAClC,IAAMC,IAAI,GAAGL,QAAQ,CAACK,IAAI;MAC1B,IAAMC,IAAI,GAAGN,QAAQ,CAACM,IAAI;MAC1B,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;QACtC,IAAAC,YAAK,EAACR,QAAQ,EAAEE,QAAQ,EAAEC,IAAI,EAAEC,IAAI,CAAC,CAACK,IAAI,CAAC,UAAAC,GAAG,EAAI;UAChD,IAAAC,cAAQ,EAACD,GAAG,CAAC7B,KAAK,CAAC;UACnBkB,MAAM,CAAC,WAAW,EAAEW,GAAG,CAAC7B,KAAK,CAAC;UAC9ByB,OAAO,CAAC,CAAC;QACX,CAAC,CAAC,CAACM,KAAK,CAAC,UAAAC,KAAK,EAAI;UAChBN,MAAM,CAACM,KAAK,CAAC;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,OAAO,WAAPA,OAAOA,CAAAC,KAAA,EAAoB;MAAA,IAAjBhB,MAAM,GAAAgB,KAAA,CAANhB,MAAM;QAAEnB,KAAK,GAAAmC,KAAA,CAALnC,KAAK;MACrB,OAAO,IAAIyB,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;QACtC,IAAAS,cAAO,EAAC,CAAC,CAACP,IAAI,CAAC,UAAAC,GAAG,EAAI;UACpB,IAAM/B,IAAI,GAAG+B,GAAG,CAAC/B,IAAI;UACrB,IAAIM,MAAM,GAAGN,IAAI,CAACM,MAAM,IAAI,EAAE;UAC9B,IAAI,CAAC,IAAAgC,gBAAM,EAAChC,MAAM,CAAC,EAAE;YACnBA,MAAM,GAAI,IAAAiC,iBAAO,EAACjC,MAAM,CAAC,GAAIkC,gBAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,gBAAgB,GAAGrC,MAAM;UAC7E;UACA,IAAIyB,GAAG,CAACxB,KAAK,IAAIwB,GAAG,CAACxB,KAAK,CAACqC,MAAM,GAAG,CAAC,EAAE;YAAE;YACvCxB,MAAM,CAAC,WAAW,EAAEW,GAAG,CAACxB,KAAK,CAAC;YAC9Ba,MAAM,CAAC,iBAAiB,EAAEW,GAAG,CAACvB,WAAW,CAAC;UAC5C,CAAC,MAAM;YACLY,MAAM,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,CAAC;UACvC;UACAA,MAAM,CAAC,QAAQ,EAAEpB,IAAI,CAAC6C,MAAM,CAAC;UAC7BzB,MAAM,CAAC,UAAU,EAAEpB,IAAI,CAAC8C,QAAQ,CAAC;UACjC1B,MAAM,CAAC,YAAY,EAAEd,MAAM,CAAC;UAC5BqB,OAAO,CAACI,GAAG,CAAC;QACd,CAAC,CAAC,CAACE,KAAK,CAAC,UAAAC,KAAK,EAAI;UAChBN,MAAM,CAACM,KAAK,CAAC;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACAa,MAAM,WAANA,MAAMA,CAAAC,KAAA,EAAoB;MAAA,IAAjB5B,MAAM,GAAA4B,KAAA,CAAN5B,MAAM;QAAEnB,KAAK,GAAA+C,KAAA,CAAL/C,KAAK;MACpB,OAAO,IAAIyB,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;QACtC,IAAAqB,aAAM,EAAChD,KAAK,CAACC,KAAK,CAAC,CAAC4B,IAAI,CAAC,YAAM;UAC7BV,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;UACvBA,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;UACvBA,MAAM,CAAC,iBAAiB,EAAE,EAAE,CAAC;UAC7B,IAAA8B,iBAAW,EAAC,CAAC;UACbvB,OAAO,CAAC,CAAC;QACX,CAAC,CAAC,CAACM,KAAK,CAAC,UAAAC,KAAK,EAAI;UAChBN,MAAM,CAACM,KAAK,CAAC;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACAiB,SAAS,WAATA,SAASA,CAAAC,KAAA,EAAa;MAAA,IAAVhC,MAAM,GAAAgC,KAAA,CAANhC,MAAM;MAChB,OAAO,IAAIM,OAAO,CAAC,UAAAC,OAAO,EAAI;QAC5BP,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;QACvB,IAAA8B,iBAAW,EAAC,CAAC;QACbvB,OAAO,CAAC,CAAC;MACX,CAAC,CAAC;IACJ;EACF;AACF,CAAC;AAAA,IAAA0B,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEcvD,IAAI", "ignoreList": []}]}