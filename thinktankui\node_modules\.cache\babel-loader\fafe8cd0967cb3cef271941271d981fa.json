{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\color.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\color.js", "mtime": 1749104421190}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_parchment", "require", "ColorAttributor", "exports", "_StyleAttributor", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "key", "value", "domNode", "_superPropGet2", "startsWith", "replace", "hex", "split", "map", "component", "concat", "parseInt", "toString", "slice", "join", "StyleAttributor", "ColorClass", "ClassAttributor", "scope", "<PERSON><PERSON>", "INLINE", "ColorStyle"], "sources": ["../../src/formats/color.ts"], "sourcesContent": ["import { ClassAttributor, Scope, StyleAttributor } from 'parchment';\n\nclass ColorAttributor extends StyleAttributor {\n  value(domNode: HTMLElement) {\n    let value = super.value(domNode) as string;\n    if (!value.startsWith('rgb(')) return value;\n    value = value.replace(/^[^\\d]+/, '').replace(/[^\\d]+$/, '');\n    const hex = value\n      .split(',')\n      .map((component) => `00${parseInt(component, 10).toString(16)}`.slice(-2))\n      .join('');\n    return `#${hex}`;\n  }\n}\n\nconst ColorClass = new ClassAttributor('color', 'ql-color', {\n  scope: Scope.INLINE,\n});\nconst ColorStyle = new ColorAttributor('color', 'color', {\n  scope: Scope.INLINE,\n});\n\nexport { ColorAttributor, ColorClass, ColorStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAAmE,IAE7DC,eAAe,GAAAC,OAAA,CAAAD,eAAA,0BAAAE,gBAAA;EAAA,SAAAF,gBAAA;IAAA,IAAAG,gBAAA,CAAAC,OAAA,QAAAJ,eAAA;IAAA,WAAAK,WAAA,CAAAD,OAAA,QAAAJ,eAAA,EAAAM,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAJ,eAAA,EAAAE,gBAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAJ,eAAA;IAAAS,GAAA;IAAAC,KAAA,EACnB,SAAAA,KAAKA,CAACC,OAAoB,EAAE;MAC1B,IAAID,KAAK,OAAAE,cAAA,CAAAR,OAAA,EAAAJ,eAAA,qBAAeW,OAAO,EAAW;MAC1C,IAAI,CAACD,KAAK,CAACG,UAAU,CAAC,MAAM,CAAC,EAAE,OAAOH,KAAK;MAC3CA,KAAK,GAAGA,KAAK,CAACI,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;MAC3D,IAAMC,GAAG,GAAGL,KAAK,CACdM,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAAE,UAAAC,SAAS;QAAA,OAAM,KAAAC,MAAA,CAAIC,QAAQ,CAACF,SAAS,EAAE,EAAE,CAAC,CAACG,QAAQ,CAAC,EAAE,CAAE,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC;MAAA,EAAC,CACzEC,IAAI,CAAC,EAAE,CAAC;MACX,WAAAJ,MAAA,CAAWJ,GAAI;IACjB;EAAA;AAAA,EAV4BS,0BAAe;AAa7C,IAAMC,UAAU,GAAAxB,OAAA,CAAAwB,UAAA,GAAG,IAAIC,0BAAe,CAAC,OAAO,EAAE,UAAU,EAAE;EAC1DC,KAAK,EAAEC,gBAAK,CAACC;AACf,CAAC,CAAC;AACF,IAAMC,UAAU,GAAA7B,OAAA,CAAA6B,UAAA,GAAG,IAAI9B,eAAe,CAAC,OAAO,EAAE,OAAO,EAAE;EACvD2B,KAAK,EAAEC,gBAAK,CAACC;AACf,CAAC,CAAC", "ignoreList": []}]}