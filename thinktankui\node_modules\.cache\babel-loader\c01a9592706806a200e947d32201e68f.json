{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\router\\index.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\router\\index.js", "mtime": 1749104047629}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749104418875}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_vue", "_interopRequireDefault", "require", "_vueR<PERSON>er", "_layout", "<PERSON><PERSON>", "use", "Router", "constantRoutes", "exports", "path", "component", "Layout", "hidden", "children", "Promise", "resolve", "then", "_interopRequireWildcard2", "default", "redirect", "name", "meta", "title", "icon", "affix", "dynamicRoutes", "permissions", "activeMenu", "routerPush", "prototype", "push", "routerReplace", "replace", "location", "call", "catch", "err", "_default", "mode", "scroll<PERSON>eh<PERSON>or", "y", "routes"], "sources": ["D:/thinktank/thinktankui/src/router/index.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport Router from 'vue-router'\r\n\r\nVue.use(Router)\r\n\r\n/* Layout */\r\nimport Layout from '@/layout'\r\n\r\n/**\r\n * Note: 路由配置项\r\n *\r\n * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1\r\n * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面\r\n *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面\r\n *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由\r\n *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由\r\n * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击\r\n * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题\r\n * query: '{\"id\": 1, \"name\": \"ry\"}' // 访问路由的默认传递参数\r\n * roles: ['admin', 'common']       // 访问路由的角色权限\r\n * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限\r\n * meta : {\r\n    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)\r\n    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字\r\n    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg\r\n    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示\r\n    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。\r\n  }\r\n */\r\n\r\n// 公共路由\r\nexport const constantRoutes = [\r\n  // 系统路由\r\n  {\r\n    path: '/redirect',\r\n    component: Layout,\r\n    hidden: true,\r\n    children: [\r\n      {\r\n        path: '/redirect/:path(.*)',\r\n        component: () => import('@/views/redirect')\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/login',\r\n    component: () => import('@/views/login'),\r\n    hidden: true\r\n  },\r\n  {\r\n    path: '/register',\r\n    component: () => import('@/views/register'),\r\n    hidden: true\r\n  },\r\n  {\r\n    path: '/404',\r\n    component: () => import('@/views/error/404'),\r\n    hidden: true\r\n  },\r\n  {\r\n    path: '/401',\r\n    component: () => import('@/views/error/401'),\r\n    hidden: true\r\n  },\r\n  {\r\n    path: '/user',\r\n    component: Layout,\r\n    hidden: true,\r\n    redirect: 'noredirect',\r\n    children: [\r\n      {\r\n        path: 'profile',\r\n        component: () => import('@/views/system/user/profile/index'),\r\n        name: 'Profile',\r\n        meta: { title: '个人中心', icon: 'user' }\r\n      }\r\n    ]\r\n  },\r\n\r\n  // 主要功能页面（按顺序排列）\r\n  {\r\n    path: '',\r\n    component: Layout,\r\n    redirect: 'index',\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/dashboard/index'),\r\n        name: 'Index',\r\n        meta: { title: '首页', icon: 'dashboard', affix: true }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/search-results',\r\n    component: Layout,\r\n    redirect: '/meta-search/index',\r\n    hidden: true\r\n  },\r\n  {\r\n    path: '/info-summary',\r\n    component: Layout,\r\n    redirect: '/info-summary/index',\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/info-summary/index'),\r\n        name: 'InfoSummary',\r\n        meta: { title: '信息汇总', icon: 'form' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/spread-analysis',\r\n    component: Layout,\r\n    redirect: '/spread-analysis/index',\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/spread-analysis/index'),\r\n        name: 'SpreadAnalysis',\r\n        meta: { title: '传播分析', icon: 'chart' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/warning-center',\r\n    component: Layout,\r\n    redirect: '/warning-center/index',\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/warning-center/index'),\r\n        name: 'WarningCenter',\r\n        meta: { title: '预警中心', icon: 'alert' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/report-center',\r\n    component: Layout,\r\n    redirect: '/report-center/index',\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/report-center/index'),\r\n        name: 'ReportCenter',\r\n        meta: { title: '报告中心', icon: 'report' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/opinion-overview',\r\n    component: Layout,\r\n    redirect: '/opinion-overview/index',\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/opinion-overview/index'),\r\n        name: 'OpinionOverview',\r\n        meta: { title: '舆情总览', icon: 'monitor' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/my-issues',\r\n    component: Layout,\r\n    redirect: '/my-issues/index',\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/my-issues/index'),\r\n        name: 'MyIssues',\r\n        meta: { title: '我的问题', icon: 'question' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/event-analysis',\r\n    component: Layout,\r\n    redirect: '/event-analysis/index',\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/event-analysis/index'),\r\n        name: 'EventAnalysis',\r\n        meta: { title: '事件分析', icon: 'international' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/source-monitoring',\r\n    component: Layout,\r\n    redirect: '/source-monitoring/index',\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/source-monitoring/index'),\r\n        name: 'SourceMonitoring',\r\n        meta: { title: '信源监测', icon: 'monitor' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/hot-events',\r\n    component: Layout,\r\n    redirect: '/hot-events/index',\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/hot-events/index'),\r\n        name: 'HotEvents',\r\n        meta: { title: '热点事件', icon: 'hot' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/meta-search',\r\n    component: Layout,\r\n    redirect: '/meta-search/index',\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/meta-search/index'),\r\n        name: 'MetaSearch',\r\n        meta: { title: '搜索中心', icon: 'search' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/account',\r\n    component: Layout,\r\n    redirect: '/account/index',\r\n    meta: { title: '账号相关', icon: 'user' },\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/account/index'),\r\n        name: 'Account',\r\n        meta: { title: '我的账号', icon: 'user' }\r\n      },\r\n      {\r\n        path: 'user-management',\r\n        component: () => import('@/views/account/user-management'),\r\n        name: 'UserManagement',\r\n        meta: { title: '用户管理', icon: 'peoples' }\r\n      }\r\n    ]\r\n  }\r\n]\r\n\r\n// 动态路由，基于用户权限动态去加载\r\nexport const dynamicRoutes = [\r\n  {\r\n    path: '/system/user-auth',\r\n    component: Layout,\r\n    hidden: true,\r\n    permissions: ['system:user:edit'],\r\n    children: [\r\n      {\r\n        path: 'role/:userId(\\\\d+)',\r\n        component: () => import('@/views/system/user/authRole'),\r\n        name: 'AuthRole',\r\n        meta: { title: '分配角色', activeMenu: '/system/user' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/system/role-auth',\r\n    component: Layout,\r\n    hidden: true,\r\n    permissions: ['system:role:edit'],\r\n    children: [\r\n      {\r\n        path: 'user/:roleId(\\\\d+)',\r\n        component: () => import('@/views/system/role/authUser'),\r\n        name: 'AuthUser',\r\n        meta: { title: '分配用户', activeMenu: '/system/role' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/system/dict-data',\r\n    component: Layout,\r\n    hidden: true,\r\n    permissions: ['system:dict:list'],\r\n    children: [\r\n      {\r\n        path: 'index/:dictId(\\\\d+)',\r\n        component: () => import('@/views/system/dict/data'),\r\n        name: 'Data',\r\n        meta: { title: '字典数据', activeMenu: '/system/dict' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/monitor/job-log',\r\n    component: Layout,\r\n    hidden: true,\r\n    permissions: ['monitor:job:list'],\r\n    children: [\r\n      {\r\n        path: 'index/:jobId(\\\\d+)',\r\n        component: () => import('@/views/monitor/job/log'),\r\n        name: 'JobLog',\r\n        meta: { title: '调度日志', activeMenu: '/monitor/job' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/tool/gen-edit',\r\n    component: Layout,\r\n    hidden: true,\r\n    permissions: ['tool:gen:edit'],\r\n    children: [\r\n      {\r\n        path: 'index/:tableId(\\\\d+)',\r\n        component: () => import('@/views/tool/gen/editTable'),\r\n        name: 'GenEdit',\r\n        meta: { title: '修改生成配置', activeMenu: '/tool/gen' }\r\n      }\r\n    ]\r\n  }\r\n]\r\n\r\n// 防止连续点击多次路由报错\r\nlet routerPush = Router.prototype.push;\r\nlet routerReplace = Router.prototype.replace;\r\n// push\r\nRouter.prototype.push = function push(location) {\r\n  return routerPush.call(this, location).catch(err => err)\r\n}\r\n// replace\r\nRouter.prototype.replace = function push(location) {\r\n  return routerReplace.call(this, location).catch(err => err)\r\n}\r\n\r\nexport default new Router({\r\n  mode: 'history', // 去掉url中的#\r\n  scrollBehavior: () => ({ y: 0 }),\r\n  routes: constantRoutes\r\n})\r\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAF,sBAAA,CAAAC,OAAA;AAKA,IAAAE,OAAA,GAAAH,sBAAA,CAAAC,OAAA;AAHAG,YAAG,CAACC,GAAG,CAACC,kBAAM,CAAC;;AAEf;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACO,IAAMC,cAAc,GAAAC,OAAA,CAAAD,cAAA,GAAG;AAC5B;AACA;EACEE,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZC,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,qBAAqB;IAC3BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,kBAAkB;MAAA;IAAA;EAC5C,CAAC;AAEL,CAAC,EACD;EACEQ,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,eAAe;IAAA;EAAA,CAAC;EACxCW,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,kBAAkB;IAAA;EAAA,CAAC;EAC3CW,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,mBAAmB;IAAA;EAAA,CAAC;EAC5CW,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,mBAAmB;IAAA;EAAA,CAAC;EAC5CW,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZO,QAAQ,EAAE,YAAY;EACtBN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,mCAAmC;MAAA;IAAA,CAAC;IAC5DmB,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAO;EACtC,CAAC;AAEL,CAAC;AAED;AACA;EACEd,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,OAAO;EACjBN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,yBAAyB;MAAA;IAAA,CAAC;IAClDmB,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAK;EACtD,CAAC;AAEL,CAAC,EACD;EACEf,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,oBAAoB;EAC9BP,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,eAAe;EACrBC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,qBAAqB;EAC/BN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,4BAA4B;MAAA;IAAA,CAAC;IACrDmB,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAO;EACtC,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,kBAAkB;EACxBC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,wBAAwB;EAClCN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,+BAA+B;MAAA;IAAA,CAAC;IACxDmB,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAQ;EACvC,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,uBAAuB;EACjCN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,8BAA8B;MAAA;IAAA,CAAC;IACvDmB,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAQ;EACvC,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,sBAAsB;EAChCN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,6BAA6B;MAAA;IAAA,CAAC;IACtDmB,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAS;EACxC,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,yBAAyB;EACnCN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,gCAAgC;MAAA;IAAA,CAAC;IACzDmB,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAU;EACzC,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,kBAAkB;EAC5BN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,yBAAyB;MAAA;IAAA,CAAC;IAClDmB,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAW;EAC1C,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,uBAAuB;EACjCN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,8BAA8B;MAAA;IAAA,CAAC;IACvDmB,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAgB;EAC/C,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,oBAAoB;EAC1BC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,0BAA0B;EACpCN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,iCAAiC;MAAA;IAAA,CAAC;IAC1DmB,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAU;EACzC,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,mBAAmB;EAC7BN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,0BAA0B;MAAA;IAAA,CAAC;IACnDmB,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAM;EACrC,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,cAAc;EACpBC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,oBAAoB;EAC9BN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,2BAA2B;MAAA;IAAA,CAAC;IACpDmB,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAS;EACxC,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,gBAAgB;EAC1BE,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE;EAAO,CAAC;EACrCV,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,uBAAuB;MAAA;IAAA,CAAC;IAChDmB,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAO;EACtC,CAAC,EACD;IACEd,IAAI,EAAE,iBAAiB;IACvBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,iCAAiC;MAAA;IAAA,CAAC;IAC1DmB,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAU;EACzC,CAAC;AAEL,CAAC,CACF;;AAED;AACO,IAAME,aAAa,GAAAjB,OAAA,CAAAiB,aAAA,GAAG,CAC3B;EACEhB,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZc,WAAW,EAAE,CAAC,kBAAkB,CAAC;EACjCb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,oBAAoB;IAC1BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,8BAA8B;MAAA;IAAA,CAAC;IACvDmB,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEK,UAAU,EAAE;IAAe;EACpD,CAAC;AAEL,CAAC,EACD;EACElB,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZc,WAAW,EAAE,CAAC,kBAAkB,CAAC;EACjCb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,oBAAoB;IAC1BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,8BAA8B;MAAA;IAAA,CAAC;IACvDmB,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEK,UAAU,EAAE;IAAe;EACpD,CAAC;AAEL,CAAC,EACD;EACElB,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZc,WAAW,EAAE,CAAC,kBAAkB,CAAC;EACjCb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,qBAAqB;IAC3BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,0BAA0B;MAAA;IAAA,CAAC;IACnDmB,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEK,UAAU,EAAE;IAAe;EACpD,CAAC;AAEL,CAAC,EACD;EACElB,IAAI,EAAE,kBAAkB;EACxBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZc,WAAW,EAAE,CAAC,kBAAkB,CAAC;EACjCb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,oBAAoB;IAC1BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,yBAAyB;MAAA;IAAA,CAAC;IAClDmB,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEK,UAAU,EAAE;IAAe;EACpD,CAAC;AAEL,CAAC,EACD;EACElB,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZc,WAAW,EAAE,CAAC,eAAe,CAAC;EAC9Bb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,sBAAsB;IAC5BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,4BAA4B;MAAA;IAAA,CAAC;IACrDmB,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE;MAAEC,KAAK,EAAE,QAAQ;MAAEK,UAAU,EAAE;IAAY;EACnD,CAAC;AAEL,CAAC,CACF;;AAED;AACA,IAAIC,UAAU,GAAGtB,kBAAM,CAACuB,SAAS,CAACC,IAAI;AACtC,IAAIC,aAAa,GAAGzB,kBAAM,CAACuB,SAAS,CAACG,OAAO;AAC5C;AACA1B,kBAAM,CAACuB,SAAS,CAACC,IAAI,GAAG,SAASA,IAAIA,CAACG,QAAQ,EAAE;EAC9C,OAAOL,UAAU,CAACM,IAAI,CAAC,IAAI,EAAED,QAAQ,CAAC,CAACE,KAAK,CAAC,UAAAC,GAAG;IAAA,OAAIA,GAAG;EAAA,EAAC;AAC1D,CAAC;AACD;AACA9B,kBAAM,CAACuB,SAAS,CAACG,OAAO,GAAG,SAASF,IAAIA,CAACG,QAAQ,EAAE;EACjD,OAAOF,aAAa,CAACG,IAAI,CAAC,IAAI,EAAED,QAAQ,CAAC,CAACE,KAAK,CAAC,UAAAC,GAAG;IAAA,OAAIA,GAAG;EAAA,EAAC;AAC7D,CAAC;AAAA,IAAAC,QAAA,GAAA7B,OAAA,CAAAU,OAAA,GAEc,IAAIZ,kBAAM,CAAC;EACxBgC,IAAI,EAAE,SAAS;EAAE;EACjBC,cAAc,EAAE,SAAhBA,cAAcA,CAAA;IAAA,OAAS;MAAEC,CAAC,EAAE;IAAE,CAAC;EAAA,CAAC;EAChCC,MAAM,EAAElC;AACV,CAAC,CAAC", "ignoreList": []}]}