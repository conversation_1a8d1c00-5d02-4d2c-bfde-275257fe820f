{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\layout\\components\\IframeToggle\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\layout\\components\\IframeToggle\\index.vue", "mtime": 1749104047626}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5qb2luLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC5rZXlzLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5tYXAuanMiKTsKdmFyIF9pbmRleCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi4vSW5uZXJMaW5rL2luZGV4IikpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSB7CiAgY29tcG9uZW50czogewogICAgSW5uZXJMaW5rOiBfaW5kZXguZGVmYXVsdAogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGlmcmFtZVZpZXdzOiBmdW5jdGlvbiBpZnJhbWVWaWV3cygpIHsKICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLnRhZ3NWaWV3LmlmcmFtZVZpZXdzOwogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgaWZyYW1lVXJsOiBmdW5jdGlvbiBpZnJhbWVVcmwodXJsLCBxdWVyeSkgewogICAgICBpZiAoT2JqZWN0LmtleXMocXVlcnkpLmxlbmd0aCA+IDApIHsKICAgICAgICB2YXIgcGFyYW1zID0gT2JqZWN0LmtleXMocXVlcnkpLm1hcChmdW5jdGlvbiAoa2V5KSB7CiAgICAgICAgICByZXR1cm4ga2V5ICsgIj0iICsgcXVlcnlba2V5XTsKICAgICAgICB9KS5qb2luKCImIik7CiAgICAgICAgcmV0dXJuIHVybCArICI/IiArIHBhcmFtczsKICAgICAgfQogICAgICByZXR1cm4gdXJsOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_index", "_interopRequireDefault", "require", "components", "InnerLink", "computed", "iframeViews", "$store", "state", "tagsView", "methods", "iframeUrl", "url", "query", "Object", "keys", "length", "params", "map", "key", "join"], "sources": ["src/layout/components/IframeToggle/index.vue"], "sourcesContent": ["<template>\r\n  <transition-group name=\"fade-transform\" mode=\"out-in\">\r\n    <inner-link\r\n      v-for=\"(item, index) in iframeViews\"\r\n      :key=\"item.path\"\r\n      :iframeId=\"'iframe' + index\"\r\n      v-show=\"$route.path === item.path\"\r\n      :src=\"iframeUrl(item.meta.link, item.query)\"\r\n    ></inner-link>\r\n  </transition-group>\r\n</template>\r\n\r\n<script>\r\nimport InnerLink from \"../InnerLink/index\";\r\n\r\nexport default {\r\n  components: { InnerLink },\r\n  computed: {\r\n    iframeViews() {\r\n      return this.$store.state.tagsView.iframeViews;\r\n    }\r\n  },\r\n  methods: {\r\n    iframeUrl(url, query) {\r\n      if (Object.keys(query).length > 0) {\r\n        let params = Object.keys(query).map((key) => key + \"=\" + query[key]).join(\"&\");\r\n        return url + \"?\" + params;\r\n      }\r\n      return url;\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;AAaA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;iCAEA;EACAC,UAAA;IAAAC,SAAA,EAAAA;EAAA;EACAC,QAAA;IACAC,WAAA,WAAAA,YAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAH,WAAA;IACA;EACA;EACAI,OAAA;IACAC,SAAA,WAAAA,UAAAC,GAAA,EAAAC,KAAA;MACA,IAAAC,MAAA,CAAAC,IAAA,CAAAF,KAAA,EAAAG,MAAA;QACA,IAAAC,MAAA,GAAAH,MAAA,CAAAC,IAAA,CAAAF,KAAA,EAAAK,GAAA,WAAAC,GAAA;UAAA,OAAAA,GAAA,SAAAN,KAAA,CAAAM,GAAA;QAAA,GAAAC,IAAA;QACA,OAAAR,GAAA,SAAAK,MAAA;MACA;MACA,OAAAL,GAAA;IACA;EACA;AACA", "ignoreList": []}]}