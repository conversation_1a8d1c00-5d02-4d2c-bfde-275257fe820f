{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\tool\\gen\\importTable.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\tool\\gen\\importTable.vue", "mtime": 1749104047651}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["importTable.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "importTable.vue", "sourceRoot": "src/views/tool/gen", "sourcesContent": ["<template>\r\n  <!-- 导入表 -->\r\n  <el-dialog title=\"导入表\" :visible.sync=\"visible\" width=\"800px\" top=\"5vh\" append-to-body>\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\">\r\n      <el-form-item label=\"表名称\" prop=\"tableName\">\r\n        <el-input\r\n          v-model=\"queryParams.tableName\"\r\n          placeholder=\"请输入表名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"表描述\" prop=\"tableComment\">\r\n        <el-input\r\n          v-model=\"queryParams.tableComment\"\r\n          placeholder=\"请输入表描述\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n    <el-row>\r\n      <el-table @row-click=\"clickRow\" ref=\"table\" :data=\"dbTableList\" @selection-change=\"handleSelectionChange\" height=\"260px\">\r\n        <el-table-column type=\"selection\" width=\"55\"></el-table-column>\r\n        <el-table-column prop=\"tableName\" label=\"表名称\" :show-overflow-tooltip=\"true\"></el-table-column>\r\n        <el-table-column prop=\"tableComment\" label=\"表描述\" :show-overflow-tooltip=\"true\"></el-table-column>\r\n        <el-table-column prop=\"createTime\" label=\"创建时间\"></el-table-column>\r\n        <el-table-column prop=\"updateTime\" label=\"更新时间\"></el-table-column>\r\n      </el-table>\r\n      <pagination\r\n        v-show=\"total>0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\"\r\n      />\r\n    </el-row>\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button type=\"primary\" @click=\"handleImportTable\">确 定</el-button>\r\n      <el-button @click=\"visible = false\">取 消</el-button>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport { listDbTable, importTable } from \"@/api/tool/gen\";\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      visible: false,\r\n      // 选中数组值\r\n      tables: [],\r\n      // 总条数\r\n      total: 0,\r\n      // 表数据\r\n      dbTableList: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        tableName: undefined,\r\n        tableComment: undefined\r\n      }\r\n    };\r\n  },\r\n  methods: {\r\n    // 显示弹框\r\n    show() {\r\n      this.getList();\r\n      this.visible = true;\r\n    },\r\n    clickRow(row) {\r\n      this.$refs.table.toggleRowSelection(row);\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.tables = selection.map(item => item.tableName);\r\n    },\r\n    // 查询表数据\r\n    getList() {\r\n      listDbTable(this.queryParams).then(res => {\r\n        if (res.code === 200) {\r\n          this.dbTableList = res.rows;\r\n          this.total = res.total;\r\n        }\r\n      });\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 导入按钮操作 */\r\n    handleImportTable() {\r\n      const tableNames = this.tables.join(\",\");\r\n      if (tableNames == \"\") {\r\n        this.$modal.msgError(\"请选择要导入的表\");\r\n        return;\r\n      }\r\n      importTable({ tables: tableNames }).then(res => {\r\n        this.$modal.msgSuccess(res.msg);\r\n        if (res.code === 200) {\r\n          this.visible = false;\r\n          this.$emit(\"ok\");\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}