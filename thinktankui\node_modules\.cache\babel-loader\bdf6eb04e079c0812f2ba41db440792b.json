{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\modules\\syntax.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\modules\\syntax.js", "mtime": 1749104422688}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_quill<PERSON><PERSON><PERSON>", "_interopRequireDefault", "require", "_parchment", "_inline", "_quill", "_module", "_block", "_break", "_cursor", "_text", "_interopRequireWildcard", "_code", "_clipboard", "TokenAttributor", "ClassAttributor", "scope", "<PERSON><PERSON>", "INLINE", "CodeToken", "exports", "_Inline", "scroll", "domNode", "value", "_this", "_classCallCheck2", "default", "_callSuper2", "add", "_inherits2", "_createClass2", "key", "format", "blotName", "_superPropGet2", "remove", "classList", "statics", "className", "optimize", "arguments", "unwrap", "formats", "node", "contains", "CodeBlock", "parentNode", "undefined", "Inline", "SyntaxCodeBlock", "_CodeBlock", "name", "setAttribute", "replaceWith", "formatAt", "length", "create", "getAttribute", "register", "SyntaxCodeBlockContainer", "_CodeBlockContainer", "attach", "forceNext", "emitMount", "children", "for<PERSON>ach", "child", "index", "highlight", "_this2", "forced", "head", "nodes", "Array", "from", "childNodes", "filter", "uiNode", "text", "concat", "map", "textContent", "join", "language", "cachedText", "trim", "<PERSON><PERSON><PERSON><PERSON>", "reduce", "delta", "blockDelta", "Delta", "diff", "_ref", "retain", "attributes", "Object", "keys", "includes", "html", "_this$children$find", "find", "_this$children$find2", "_slicedToArray2", "codeBlock", "escapeText", "code", "context", "parent", "CodeBlockContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requiredC<PERSON><PERSON>", "CursorBlot", "TextBlot", "BreakBlot", "lib", "versionString", "majorVersion", "split", "parseInt", "Syntax", "_Module", "quill", "options", "_this3", "hljs", "Error", "languages", "memo", "_ref2", "highlightBlot", "bind", "initListener", "initTimer", "_this4", "on", "<PERSON><PERSON><PERSON>", "events", "SCROLL_BLOT_MOUNT", "blot", "select", "root", "ownerDocument", "createElement", "_ref3", "label", "option", "append<PERSON><PERSON><PERSON>", "addEventListener", "focus", "attachUI", "_this5", "timer", "SCROLL_OPTIMIZE", "clearTimeout", "setTimeout", "interval", "_this6", "force", "selection", "composing", "update", "sources", "USER", "range", "getSelection", "blots", "descendants", "container", "SILENT", "setSelection", "line", "i", "insert", "_defineProperty2", "innerHTML", "traverse", "compose", "data", "nodeText", "WeakMap", "<PERSON><PERSON><PERSON>", "DEFAULTS", "window"], "sources": ["../../src/modules/syntax.ts"], "sourcesContent": ["import Delta from 'quill-delta';\nimport { ClassAttributor, Scope } from 'parchment';\nimport type { Blot, ScrollBlot } from 'parchment';\nimport Inline from '../blots/inline.js';\nimport Quill from '../core/quill.js';\nimport Module from '../core/module.js';\nimport { blockD<PERSON><PERSON> } from '../blots/block.js';\nimport BreakBlot from '../blots/break.js';\nimport CursorBlot from '../blots/cursor.js';\nimport TextBlot, { escapeText } from '../blots/text.js';\nimport CodeBlock, { CodeBlockContainer } from '../formats/code.js';\nimport { traverse } from './clipboard.js';\n\nconst TokenAttributor = new ClassAttributor('code-token', 'hljs', {\n  scope: Scope.INLINE,\n});\nclass CodeToken extends Inline {\n  static formats(node: Element, scroll: ScrollBlot) {\n    while (node != null && node !== scroll.domNode) {\n      if (node.classList && node.classList.contains(CodeBlock.className)) {\n        // @ts-expect-error\n        return super.formats(node, scroll);\n      }\n      // @ts-expect-error\n      node = node.parentNode;\n    }\n    return undefined;\n  }\n\n  constructor(scroll: ScrollBlot, domNode: Node, value: unknown) {\n    // @ts-expect-error\n    super(scroll, domNode, value);\n    TokenAttributor.add(this.domNode, value);\n  }\n\n  format(format: string, value: unknown) {\n    if (format !== CodeToken.blotName) {\n      super.format(format, value);\n    } else if (value) {\n      TokenAttributor.add(this.domNode, value);\n    } else {\n      TokenAttributor.remove(this.domNode);\n      this.domNode.classList.remove(this.statics.className);\n    }\n  }\n\n  optimize(...args: unknown[]) {\n    // @ts-expect-error\n    super.optimize(...args);\n    if (!TokenAttributor.value(this.domNode)) {\n      this.unwrap();\n    }\n  }\n}\nCodeToken.blotName = 'code-token';\nCodeToken.className = 'ql-token';\n\nclass SyntaxCodeBlock extends CodeBlock {\n  static create(value: unknown) {\n    const domNode = super.create(value);\n    if (typeof value === 'string') {\n      domNode.setAttribute('data-language', value);\n    }\n    return domNode;\n  }\n\n  static formats(domNode: Node) {\n    // @ts-expect-error\n    return domNode.getAttribute('data-language') || 'plain';\n  }\n\n  static register() {} // Syntax module will register\n\n  format(name: string, value: unknown) {\n    if (name === this.statics.blotName && value) {\n      // @ts-expect-error\n      this.domNode.setAttribute('data-language', value);\n    } else {\n      super.format(name, value);\n    }\n  }\n\n  replaceWith(name: string | Blot, value?: any) {\n    this.formatAt(0, this.length(), CodeToken.blotName, false);\n    return super.replaceWith(name, value);\n  }\n}\n\nclass SyntaxCodeBlockContainer extends CodeBlockContainer {\n  forceNext?: boolean;\n  cachedText?: string | null;\n\n  attach() {\n    super.attach();\n    this.forceNext = false;\n    // @ts-expect-error\n    this.scroll.emitMount(this);\n  }\n\n  format(name: string, value: unknown) {\n    if (name === SyntaxCodeBlock.blotName) {\n      this.forceNext = true;\n      this.children.forEach((child) => {\n        // @ts-expect-error\n        child.format(name, value);\n      });\n    }\n  }\n\n  formatAt(index: number, length: number, name: string, value: unknown) {\n    if (name === SyntaxCodeBlock.blotName) {\n      this.forceNext = true;\n    }\n    super.formatAt(index, length, name, value);\n  }\n\n  highlight(\n    highlight: (text: string, language: string) => Delta,\n    forced = false,\n  ) {\n    if (this.children.head == null) return;\n    const nodes = Array.from(this.domNode.childNodes).filter(\n      (node) => node !== this.uiNode,\n    );\n    const text = `${nodes.map((node) => node.textContent).join('\\n')}\\n`;\n    const language = SyntaxCodeBlock.formats(this.children.head.domNode);\n    if (forced || this.forceNext || this.cachedText !== text) {\n      if (text.trim().length > 0 || this.cachedText == null) {\n        const oldDelta = this.children.reduce((delta, child) => {\n          // @ts-expect-error\n          return delta.concat(blockDelta(child, false));\n        }, new Delta());\n        const delta = highlight(text, language);\n        oldDelta.diff(delta).reduce((index, { retain, attributes }) => {\n          // Should be all retains\n          if (!retain) return index;\n          if (attributes) {\n            Object.keys(attributes).forEach((format) => {\n              if (\n                [SyntaxCodeBlock.blotName, CodeToken.blotName].includes(format)\n              ) {\n                // @ts-expect-error\n                this.formatAt(index, retain, format, attributes[format]);\n              }\n            });\n          }\n          // @ts-expect-error\n          return index + retain;\n        }, 0);\n      }\n      this.cachedText = text;\n      this.forceNext = false;\n    }\n  }\n\n  html(index: number, length: number) {\n    const [codeBlock] = this.children.find(index);\n    const language = codeBlock\n      ? SyntaxCodeBlock.formats(codeBlock.domNode)\n      : 'plain';\n\n    return `<pre data-language=\"${language}\">\\n${escapeText(\n      this.code(index, length),\n    )}\\n</pre>`;\n  }\n\n  optimize(context: Record<string, any>) {\n    super.optimize(context);\n    if (\n      this.parent != null &&\n      this.children.head != null &&\n      this.uiNode != null\n    ) {\n      const language = SyntaxCodeBlock.formats(this.children.head.domNode);\n      // @ts-expect-error\n      if (language !== this.uiNode.value) {\n        // @ts-expect-error\n        this.uiNode.value = language;\n      }\n    }\n  }\n}\n\nSyntaxCodeBlockContainer.allowedChildren = [SyntaxCodeBlock];\nSyntaxCodeBlock.requiredContainer = SyntaxCodeBlockContainer;\nSyntaxCodeBlock.allowedChildren = [CodeToken, CursorBlot, TextBlot, BreakBlot];\n\ninterface SyntaxOptions {\n  interval: number;\n  languages: { key: string; label: string }[];\n  hljs: any;\n}\n\nconst highlight = (lib: any, language: string, text: string) => {\n  if (typeof lib.versionString === 'string') {\n    const majorVersion = lib.versionString.split('.')[0];\n    if (parseInt(majorVersion, 10) >= 11) {\n      return lib.highlight(text, { language }).value;\n    }\n  }\n  return lib.highlight(language, text).value;\n};\n\nclass Syntax extends Module<SyntaxOptions> {\n  static DEFAULTS: SyntaxOptions & { hljs: any };\n\n  static register() {\n    Quill.register(CodeToken, true);\n    Quill.register(SyntaxCodeBlock, true);\n    Quill.register(SyntaxCodeBlockContainer, true);\n  }\n\n  languages: Record<string, true>;\n\n  constructor(quill: Quill, options: Partial<SyntaxOptions>) {\n    super(quill, options);\n    if (this.options.hljs == null) {\n      throw new Error(\n        'Syntax module requires highlight.js. Please include the library on the page before Quill.',\n      );\n    }\n    // @ts-expect-error Fix me later\n    this.languages = this.options.languages.reduce(\n      (memo: Record<string, unknown>, { key }) => {\n        memo[key] = true;\n        return memo;\n      },\n      {},\n    );\n    this.highlightBlot = this.highlightBlot.bind(this);\n    this.initListener();\n    this.initTimer();\n  }\n\n  initListener() {\n    this.quill.on(Quill.events.SCROLL_BLOT_MOUNT, (blot: Blot) => {\n      if (!(blot instanceof SyntaxCodeBlockContainer)) return;\n      const select = this.quill.root.ownerDocument.createElement('select');\n      // @ts-expect-error Fix me later\n      this.options.languages.forEach(({ key, label }) => {\n        const option = select.ownerDocument.createElement('option');\n        option.textContent = label;\n        option.setAttribute('value', key);\n        select.appendChild(option);\n      });\n      select.addEventListener('change', () => {\n        blot.format(SyntaxCodeBlock.blotName, select.value);\n        this.quill.root.focus(); // Prevent scrolling\n        this.highlight(blot, true);\n      });\n      if (blot.uiNode == null) {\n        blot.attachUI(select);\n        if (blot.children.head) {\n          select.value = SyntaxCodeBlock.formats(blot.children.head.domNode);\n        }\n      }\n    });\n  }\n\n  initTimer() {\n    let timer: ReturnType<typeof setTimeout> | null = null;\n    this.quill.on(Quill.events.SCROLL_OPTIMIZE, () => {\n      if (timer) {\n        clearTimeout(timer);\n      }\n      timer = setTimeout(() => {\n        this.highlight();\n        timer = null;\n      }, this.options.interval);\n    });\n  }\n\n  highlight(blot: SyntaxCodeBlockContainer | null = null, force = false) {\n    if (this.quill.selection.composing) return;\n    this.quill.update(Quill.sources.USER);\n    const range = this.quill.getSelection();\n    const blots =\n      blot == null\n        ? this.quill.scroll.descendants(SyntaxCodeBlockContainer)\n        : [blot];\n    blots.forEach((container) => {\n      container.highlight(this.highlightBlot, force);\n    });\n    this.quill.update(Quill.sources.SILENT);\n    if (range != null) {\n      this.quill.setSelection(range, Quill.sources.SILENT);\n    }\n  }\n\n  highlightBlot(text: string, language = 'plain') {\n    language = this.languages[language] ? language : 'plain';\n    if (language === 'plain') {\n      return escapeText(text)\n        .split('\\n')\n        .reduce((delta, line, i) => {\n          if (i !== 0) {\n            delta.insert('\\n', { [CodeBlock.blotName]: language });\n          }\n          return delta.insert(line);\n        }, new Delta());\n    }\n    const container = this.quill.root.ownerDocument.createElement('div');\n    container.classList.add(CodeBlock.className);\n    container.innerHTML = highlight(this.options.hljs, language, text);\n    return traverse(\n      this.quill.scroll,\n      container,\n      [\n        (node, delta) => {\n          // @ts-expect-error\n          const value = TokenAttributor.value(node);\n          if (value) {\n            return delta.compose(\n              new Delta().retain(delta.length(), {\n                [CodeToken.blotName]: value,\n              }),\n            );\n          }\n          return delta;\n        },\n      ],\n      [\n        (node, delta) => {\n          // @ts-expect-error\n          return node.data.split('\\n').reduce((memo, nodeText, i) => {\n            if (i !== 0) memo.insert('\\n', { [CodeBlock.blotName]: language });\n            return memo.insert(nodeText);\n          }, delta);\n        },\n      ],\n      new WeakMap(),\n    );\n  }\n}\nSyntax.DEFAULTS = {\n  hljs: (() => {\n    return window.hljs;\n  })(),\n  interval: 1000,\n  languages: [\n    { key: 'plain', label: 'Plain' },\n    { key: 'bash', label: 'Bash' },\n    { key: 'cpp', label: 'C++' },\n    { key: 'cs', label: 'C#' },\n    { key: 'css', label: 'CSS' },\n    { key: 'diff', label: 'Diff' },\n    { key: 'xml', label: 'HTML/XML' },\n    { key: 'java', label: 'Java' },\n    { key: 'javascript', label: 'JavaScript' },\n    { key: 'markdown', label: 'Markdown' },\n    { key: 'php', label: 'PHP' },\n    { key: 'python', label: 'Python' },\n    { key: 'ruby', label: 'Ruby' },\n    { key: 'sql', label: 'SQL' },\n  ],\n};\n\nexport { SyntaxCodeBlock as CodeBlock, CodeToken, Syntax as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;AAEA,IAAAE,OAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,MAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,OAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AACA,IAAAM,MAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,OAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,KAAA,GAAAC,uBAAA,CAAAT,OAAA;AACA,IAAAU,KAAA,GAAAD,uBAAA,CAAAT,OAAA;AACA,IAAAW,UAAA,GAAAX,OAAA;AAEA,IAAMY,eAAe,GAAG,IAAIC,0BAAe,CAAC,YAAY,EAAE,MAAM,EAAE;EAChEC,KAAK,EAAEC,gBAAK,CAACC;AACf,CAAC,CAAC;AAAA,IACIC,SAAS,GAAAC,OAAA,CAAAD,SAAA,0BAAAE,OAAA;EAab,SAAAF,UAAYG,MAAkB,EAAEC,OAAa,EAAEC,KAAc,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAR,SAAA;IAC7D;IACAM,KAAA,OAAAG,WAAA,CAAAD,OAAA,QAAAR,SAAA,GAAMG,MAAM,EAAEC,OAAO,EAAEC,KAAK;IAC5BV,eAAe,CAACe,GAAG,CAACJ,KAAA,CAAKF,OAAO,EAAEC,KAAK,CAAC;IAAA,OAAAC,KAAA;EAC1C;EAAA,IAAAK,UAAA,CAAAH,OAAA,EAAAR,SAAA,EAAAE,OAAA;EAAA,WAAAU,aAAA,CAAAJ,OAAA,EAAAR,SAAA;IAAAa,GAAA;IAAAR,KAAA,EAEA,SAAAS,MAAMA,CAACA,OAAc,EAAET,KAAc,EAAE;MACrC,IAAIS,OAAM,KAAKd,SAAS,CAACe,QAAQ,EAAE;QACjC,IAAAC,cAAA,CAAAR,OAAA,EAAAR,SAAA,sBAAac,OAAM,EAAET,KAAK;MAC5B,CAAC,MAAM,IAAIA,KAAK,EAAE;QAChBV,eAAe,CAACe,GAAG,CAAC,IAAI,CAACN,OAAO,EAAEC,KAAK,CAAC;MAC1C,CAAC,MAAM;QACLV,eAAe,CAACsB,MAAM,CAAC,IAAI,CAACb,OAAO,CAAC;QACpC,IAAI,CAACA,OAAO,CAACc,SAAS,CAACD,MAAM,CAAC,IAAI,CAACE,OAAO,CAACC,SAAS,CAAC;MACvD;IACF;EAAA;IAAAP,GAAA;IAAAR,KAAA,EAEA,SAAAgB,QAAQA,CAAA,EAAqB;MAC3B;MACA,IAAAL,cAAA,CAAAR,OAAA,EAAAR,SAAA,uBAAesB,SAAO;MACtB,IAAI,CAAC3B,eAAe,CAACU,KAAK,CAAC,IAAI,CAACD,OAAO,CAAC,EAAE;QACxC,IAAI,CAACmB,MAAM,CAAC,CAAC;MACf;IACF;EAAA;IAAAV,GAAA;IAAAR,KAAA,EAnCA,SAAOmB,OAAOA,CAACC,IAAa,EAAEtB,MAAkB,EAAE;MAChD,OAAOsB,IAAI,IAAI,IAAI,IAAIA,IAAI,KAAKtB,MAAM,CAACC,OAAO,EAAE;QAC9C,IAAIqB,IAAI,CAACP,SAAS,IAAIO,IAAI,CAACP,SAAS,CAACQ,QAAQ,CAACC,aAAS,CAACP,SAAS,CAAC,EAAE;UAClE;UACA,WAAAJ,cAAA,CAAAR,OAAA,EAAAR,SAAA,uBAAqByB,IAAI,EAAEtB,MAAM;QACnC;QACA;QACAsB,IAAI,GAAGA,IAAI,CAACG,UAAU;MACxB;MACA,OAAOC,SAAS;IAClB;EAAA;AAAA,EAXsBC,eAAM;AAsC9B9B,SAAS,CAACe,QAAQ,GAAG,YAAY;AACjCf,SAAS,CAACoB,SAAS,GAAG,UAAU;AAAA,IAE1BW,eAAe,GAAA9B,OAAA,CAAA0B,SAAA,0BAAAK,UAAA;EAAA,SAAAD,gBAAA;IAAA,IAAAxB,gBAAA,CAAAC,OAAA,QAAAuB,eAAA;IAAA,WAAAtB,WAAA,CAAAD,OAAA,QAAAuB,eAAA,EAAAT,SAAA;EAAA;EAAA,IAAAX,UAAA,CAAAH,OAAA,EAAAuB,eAAA,EAAAC,UAAA;EAAA,WAAApB,aAAA,CAAAJ,OAAA,EAAAuB,eAAA;IAAAlB,GAAA;IAAAR,KAAA;IAcE;;IAErB,SAAAS,MAAMA,CAACmB,IAAY,EAAE5B,KAAc,EAAE;MACnC,IAAI4B,IAAI,KAAK,IAAI,CAACd,OAAO,CAACJ,QAAQ,IAAIV,KAAK,EAAE;QAC3C;QACA,IAAI,CAACD,OAAO,CAAC8B,YAAY,CAAC,eAAe,EAAE7B,KAAK,CAAC;MACnD,CAAC,MAAM;QACL,IAAAW,cAAA,CAAAR,OAAA,EAAAuB,eAAA,sBAAaE,IAAI,EAAE5B,KAAK;MAC1B;IACF;EAAA;IAAAQ,GAAA;IAAAR,KAAA,EAEA,SAAA8B,WAAWA,CAACF,IAAmB,EAAE5B,KAAW,EAAE;MAC5C,IAAI,CAAC+B,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACC,MAAM,CAAC,CAAC,EAAErC,SAAS,CAACe,QAAQ,EAAE,KAAK,CAAC;MAC1D,WAAAC,cAAA,CAAAR,OAAA,EAAAuB,eAAA,2BAAyBE,IAAI,EAAE5B,KAAK;IACtC;EAAA;IAAAQ,GAAA;IAAAR,KAAA,EA3BA,SAAOiC,MAAMA,CAACjC,KAAc,EAAE;MAC5B,IAAMD,OAAO,OAAAY,cAAA,CAAAR,OAAA,EAAAuB,eAAA,sBAAgB1B,KAAK,EAAC;MACnC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7BD,OAAO,CAAC8B,YAAY,CAAC,eAAe,EAAE7B,KAAK,CAAC;MAC9C;MACA,OAAOD,OAAO;IAChB;EAAA;IAAAS,GAAA;IAAAR,KAAA,EAEA,SAAOmB,OAAOA,CAACpB,OAAa,EAAE;MAC5B;MACA,OAAOA,OAAO,CAACmC,YAAY,CAAC,eAAe,CAAC,IAAI,OAAO;IACzD;EAAA;IAAA1B,GAAA;IAAAR,KAAA,EAEA,SAAOmC,QAAQA,CAAA,EAAG,CAAC;EAAC;AAAA,EAdQb,aAAS;AAAA,IA+BjCc,wBAAwB,0BAAAC,mBAAA;EAAA,SAAAD,yBAAA;IAAA,IAAAlC,gBAAA,CAAAC,OAAA,QAAAiC,wBAAA;IAAA,WAAAhC,WAAA,CAAAD,OAAA,QAAAiC,wBAAA,EAAAnB,SAAA;EAAA;EAAA,IAAAX,UAAA,CAAAH,OAAA,EAAAiC,wBAAA,EAAAC,mBAAA;EAAA,WAAA9B,aAAA,CAAAJ,OAAA,EAAAiC,wBAAA;IAAA5B,GAAA;IAAAR,KAAA,EAI5B,SAAAsC,MAAMA,CAAA,EAAG;MACP,IAAA3B,cAAA,CAAAR,OAAA,EAAAiC,wBAAA;MACA,IAAI,CAACG,SAAS,GAAG,KAAK;MACtB;MACA,IAAI,CAACzC,MAAM,CAAC0C,SAAS,CAAC,IAAI,CAAC;IAC7B;EAAA;IAAAhC,GAAA;IAAAR,KAAA,EAEA,SAAAS,MAAMA,CAACmB,IAAY,EAAE5B,KAAc,EAAE;MACnC,IAAI4B,IAAI,KAAKF,eAAe,CAAChB,QAAQ,EAAE;QACrC,IAAI,CAAC6B,SAAS,GAAG,IAAI;QACrB,IAAI,CAACE,QAAQ,CAACC,OAAO,CAAE,UAAAC,KAAK,EAAK;UAC/B;UACAA,KAAK,CAAClC,MAAM,CAACmB,IAAI,EAAE5B,KAAK,CAAC;QAC3B,CAAC,CAAC;MACJ;IACF;EAAA;IAAAQ,GAAA;IAAAR,KAAA,EAEA,SAAA+B,QAAQA,CAACa,KAAa,EAAEZ,MAAc,EAAEJ,IAAY,EAAE5B,KAAc,EAAE;MACpE,IAAI4B,IAAI,KAAKF,eAAe,CAAChB,QAAQ,EAAE;QACrC,IAAI,CAAC6B,SAAS,GAAG,IAAI;MACvB;MACA,IAAA5B,cAAA,CAAAR,OAAA,EAAAiC,wBAAA,wBAAeQ,KAAK,EAAEZ,MAAM,EAAEJ,IAAI,EAAE5B,KAAK;IAC3C;EAAA;IAAAQ,GAAA;IAAAR,KAAA,EAEA,SAAA6C,SAASA,CACPA,UAAoD,EAEpD;MAAA,IAAAC,MAAA;MAAA,IADAC,MAAM,GAAA9B,SAAA,CAAAe,MAAA,QAAAf,SAAA,QAAAO,SAAA,GAAAP,SAAA,MAAG,KAAK;MAEd,IAAI,IAAI,CAACwB,QAAQ,CAACO,IAAI,IAAI,IAAI,EAAE;MAChC,IAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACpD,OAAO,CAACqD,UAAU,CAAC,CAACC,MAAM,CACrD,UAAAjC,IAAI;QAAA,OAAKA,IAAI,KAAK0B,MAAI,CAACQ,MAC1B;MAAA,EAAC;MACD,IAAMC,IAAI,MAAAC,MAAA,CAAMP,KAAK,CAACQ,GAAG,CAAE,UAAArC,IAAI;QAAA,OAAKA,IAAI,CAACsC,WAAW;MAAA,EAAC,CAACC,IAAI,CAAC,IAAI,CAAE,OAAG;MACpE,IAAMC,QAAQ,GAAGlC,eAAe,CAACP,OAAO,CAAC,IAAI,CAACsB,QAAQ,CAACO,IAAI,CAACjD,OAAO,CAAC;MACpE,IAAIgD,MAAM,IAAI,IAAI,CAACR,SAAS,IAAI,IAAI,CAACsB,UAAU,KAAKN,IAAI,EAAE;QACxD,IAAIA,IAAI,CAACO,IAAI,CAAC,CAAC,CAAC9B,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC6B,UAAU,IAAI,IAAI,EAAE;UACrD,IAAME,QAAQ,GAAG,IAAI,CAACtB,QAAQ,CAACuB,MAAM,CAAC,UAACC,KAAK,EAAEtB,KAAK,EAAK;YACtD;YACA,OAAOsB,KAAK,CAACT,MAAM,CAAC,IAAAU,iBAAU,EAACvB,KAAK,EAAE,KAAK,CAAC,CAAC;UAC/C,CAAC,EAAE,IAAIwB,mBAAK,CAAC,CAAC,CAAC;UACf,IAAMF,KAAK,GAAGpB,UAAS,CAACU,IAAI,EAAEK,QAAQ,CAAC;UACvCG,QAAQ,CAACK,IAAI,CAACH,KAAK,CAAC,CAACD,MAAM,CAAC,UAACpB,KAAK,EAAAyB,IAAA,EAA6B;YAAA,IAAzBC,MAAM,GAAcD,IAAA,CAApBC,MAAM;cAAEC,UAAA,GAAYF,IAAA,CAAZE,UAAA;YAC5C;YACA,IAAI,CAACD,MAAM,EAAE,OAAO1B,KAAK;YACzB,IAAI2B,UAAU,EAAE;cACdC,MAAM,CAACC,IAAI,CAACF,UAAU,CAAC,CAAC7B,OAAO,CAAE,UAAAjC,MAAM,EAAK;gBAC1C,IACE,CAACiB,eAAe,CAAChB,QAAQ,EAAEf,SAAS,CAACe,QAAQ,CAAC,CAACgE,QAAQ,CAACjE,MAAM,CAAC,EAC/D;kBACA;kBACAqC,MAAI,CAACf,QAAQ,CAACa,KAAK,EAAE0B,MAAM,EAAE7D,MAAM,EAAE8D,UAAU,CAAC9D,MAAM,CAAC,CAAC;gBAC1D;cACF,CAAC,CAAC;YACJ;YACA;YACA,OAAOmC,KAAK,GAAG0B,MAAM;UACvB,CAAC,EAAE,CAAC,CAAC;QACP;QACA,IAAI,CAACT,UAAU,GAAGN,IAAI;QACtB,IAAI,CAAChB,SAAS,GAAG,KAAK;MACxB;IACF;EAAA;IAAA/B,GAAA;IAAAR,KAAA,EAEA,SAAA2E,IAAIA,CAAC/B,KAAa,EAAEZ,MAAc,EAAE;MAClC,IAAA4C,mBAAA,GAAoB,IAAI,CAACnC,QAAQ,CAACoC,IAAI,CAACjC,KAAK,CAAC;QAAAkC,oBAAA,OAAAC,eAAA,CAAA5E,OAAA,EAAAyE,mBAAA;QAAtCI,SAAS,GAAAF,oBAAA;MAChB,IAAMlB,QAAQ,GAAGoB,SAAS,GACtBtD,eAAe,CAACP,OAAO,CAAC6D,SAAS,CAACjF,OAAO,CAAC,GAC1C,OAAO;MAEX,+BAAAyD,MAAA,CAA8BI,QAAS,WAAAJ,MAAA,CAAM,IAAAyB,gBAAU,EACrD,IAAI,CAACC,IAAI,CAACtC,KAAK,EAAEZ,MAAM,CACzB,CAAE;IACJ;EAAA;IAAAxB,GAAA;IAAAR,KAAA,EAEA,SAAAgB,QAAQA,CAACmE,OAA4B,EAAE;MACrC,IAAAxE,cAAA,CAAAR,OAAA,EAAAiC,wBAAA,wBAAe+C,OAAO;MACtB,IACE,IAAI,CAACC,MAAM,IAAI,IAAI,IACnB,IAAI,CAAC3C,QAAQ,CAACO,IAAI,IAAI,IAAI,IAC1B,IAAI,CAACM,MAAM,IAAI,IAAI,EACnB;QACA,IAAMM,QAAQ,GAAGlC,eAAe,CAACP,OAAO,CAAC,IAAI,CAACsB,QAAQ,CAACO,IAAI,CAACjD,OAAO,CAAC;QACpE;QACA,IAAI6D,QAAQ,KAAK,IAAI,CAACN,MAAM,CAACtD,KAAK,EAAE;UAClC;UACA,IAAI,CAACsD,MAAM,CAACtD,KAAK,GAAG4D,QAAQ;QAC9B;MACF;IACF;EAAA;AAAA,EA5FqCyB,wBAAkB;AA+FzDjD,wBAAwB,CAACkD,eAAe,GAAG,CAAC5D,eAAe,CAAC;AAC5DA,eAAe,CAAC6D,iBAAiB,GAAGnD,wBAAwB;AAC5DV,eAAe,CAAC4D,eAAe,GAAG,CAAC3F,SAAS,EAAE6F,eAAU,EAAEC,aAAQ,EAAEC,cAAS,CAAC;AAQ9E,IAAM7C,SAAS,GAAG,SAAZA,SAASA,CAAI8C,GAAQ,EAAE/B,QAAgB,EAAEL,IAAY,EAAK;EAC9D,IAAI,OAAOoC,GAAG,CAACC,aAAa,KAAK,QAAQ,EAAE;IACzC,IAAMC,YAAY,GAAGF,GAAG,CAACC,aAAa,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACpD,IAAIC,QAAQ,CAACF,YAAY,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE;MACpC,OAAOF,GAAG,CAAC9C,SAAS,CAACU,IAAI,EAAE;QAAEK,QAAA,EAAAA;MAAS,CAAC,CAAC,CAAC5D,KAAK;IAChD;EACF;EACA,OAAO2F,GAAG,CAAC9C,SAAS,CAACe,QAAQ,EAAEL,IAAI,CAAC,CAACvD,KAAK;AAC5C,CAAC;AAAA,IAEKgG,MAAM,GAAApG,OAAA,CAAAO,OAAA,0BAAA8F,OAAA;EAWV,SAAAD,OAAYE,KAAY,EAAEC,OAA+B,EAAE;IAAA,IAAAC,MAAA;IAAA,IAAAlG,gBAAA,CAAAC,OAAA,QAAA6F,MAAA;IACzDI,MAAA,OAAAhG,WAAA,CAAAD,OAAA,QAAA6F,MAAA,GAAME,KAAK,EAAEC,OAAO;IACpB,IAAIC,MAAA,CAAKD,OAAO,CAACE,IAAI,IAAI,IAAI,EAAE;MAC7B,MAAM,IAAIC,KAAK,CACb,2FACF,CAAC;IACH;IACA;IACAF,MAAA,CAAKG,SAAS,GAAGH,MAAA,CAAKD,OAAO,CAACI,SAAS,CAACvC,MAAM,CAC5C,UAACwC,IAA6B,EAAAC,KAAA,EAAc;MAAA,IAAVjG,GAAA,GAAKiG,KAAA,CAALjG,GAAA;MAChCgG,IAAI,CAAChG,GAAG,CAAC,GAAG,IAAI;MAChB,OAAOgG,IAAI;IACb,CAAC,EACD,CAAC,CACH,CAAC;IACDJ,MAAA,CAAKM,aAAa,GAAGN,MAAA,CAAKM,aAAa,CAACC,IAAI,CAAAP,MAAK,CAAC;IAClDA,MAAA,CAAKQ,YAAY,CAAC,CAAC;IACnBR,MAAA,CAAKS,SAAS,CAAC,CAAC;IAAA,OAAAT,MAAA;EAClB;EAAA,IAAA9F,UAAA,CAAAH,OAAA,EAAA6F,MAAA,EAAAC,OAAA;EAAA,WAAA1F,aAAA,CAAAJ,OAAA,EAAA6F,MAAA;IAAAxF,GAAA;IAAAR,KAAA,EAEA,SAAA4G,YAAYA,CAAA,EAAG;MAAA,IAAAE,MAAA;MACb,IAAI,CAACZ,KAAK,CAACa,EAAE,CAACC,cAAK,CAACC,MAAM,CAACC,iBAAiB,EAAG,UAAAC,IAAU,EAAK;QAC5D,IAAI,EAAEA,IAAI,YAAY/E,wBAAwB,CAAC,EAAE;QACjD,IAAMgF,MAAM,GAAGN,MAAI,CAACZ,KAAK,CAACmB,IAAI,CAACC,aAAa,CAACC,aAAa,CAAC,QAAQ,CAAC;QACpE;QACAT,MAAI,CAACX,OAAO,CAACI,SAAS,CAAC7D,OAAO,CAAC,UAAA8E,KAAA,EAAoB;UAAA,IAAjBhH,GAAG,GAASgH,KAAA,CAAZhH,GAAG;YAAEiH,KAAA,GAAOD,KAAA,CAAPC,KAAA;UACrC,IAAMC,MAAM,GAAGN,MAAM,CAACE,aAAa,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC3DG,MAAM,CAAChE,WAAW,GAAG+D,KAAK;UAC1BC,MAAM,CAAC7F,YAAY,CAAC,OAAO,EAAErB,GAAG,CAAC;UACjC4G,MAAM,CAACO,WAAW,CAACD,MAAM,CAAC;QAC5B,CAAC,CAAC;QACFN,MAAM,CAACQ,gBAAgB,CAAC,QAAQ,EAAE,YAAM;UACtCT,IAAI,CAAC1G,MAAM,CAACiB,eAAe,CAAChB,QAAQ,EAAE0G,MAAM,CAACpH,KAAK,CAAC;UACnD8G,MAAI,CAACZ,KAAK,CAACmB,IAAI,CAACQ,KAAK,CAAC,CAAC,CAAC,CAAC;UACzBf,MAAI,CAACjE,SAAS,CAACsE,IAAI,EAAE,IAAI,CAAC;QAC5B,CAAC,CAAC;QACF,IAAIA,IAAI,CAAC7D,MAAM,IAAI,IAAI,EAAE;UACvB6D,IAAI,CAACW,QAAQ,CAACV,MAAM,CAAC;UACrB,IAAID,IAAI,CAAC1E,QAAQ,CAACO,IAAI,EAAE;YACtBoE,MAAM,CAACpH,KAAK,GAAG0B,eAAe,CAACP,OAAO,CAACgG,IAAI,CAAC1E,QAAQ,CAACO,IAAI,CAACjD,OAAO,CAAC;UACpE;QACF;MACF,CAAC,CAAC;IACJ;EAAA;IAAAS,GAAA;IAAAR,KAAA,EAEA,SAAA6G,SAASA,CAAA,EAAG;MAAA,IAAAkB,MAAA;MACV,IAAIC,KAA2C,GAAG,IAAI;MACtD,IAAI,CAAC9B,KAAK,CAACa,EAAE,CAACC,cAAK,CAACC,MAAM,CAACgB,eAAe,EAAE,YAAM;QAChD,IAAID,KAAK,EAAE;UACTE,YAAY,CAACF,KAAK,CAAC;QACrB;QACAA,KAAK,GAAGG,UAAU,CAAC,YAAM;UACvBJ,MAAI,CAAClF,SAAS,CAAC,CAAC;UAChBmF,KAAK,GAAG,IAAI;QACd,CAAC,EAAED,MAAI,CAAC5B,OAAO,CAACiC,QAAQ,CAAC;MAC3B,CAAC,CAAC;IACJ;EAAA;IAAA5H,GAAA;IAAAR,KAAA,EAEA,SAAA6C,SAASA,CAAA,EAA8D;MAAA,IAAAwF,MAAA;MAAA,IAA7DlB,IAAqC,GAAAlG,SAAA,CAAAe,MAAA,QAAAf,SAAA,QAAAO,SAAA,GAAAP,SAAA,MAAG,IAAI;MAAA,IAAEqH,KAAK,GAAArH,SAAA,CAAAe,MAAA,QAAAf,SAAA,QAAAO,SAAA,GAAAP,SAAA,MAAG,KAAK;MACnE,IAAI,IAAI,CAACiF,KAAK,CAACqC,SAAS,CAACC,SAAS,EAAE;MACpC,IAAI,CAACtC,KAAK,CAACuC,MAAM,CAACzB,cAAK,CAAC0B,OAAO,CAACC,IAAI,CAAC;MACrC,IAAMC,KAAK,GAAG,IAAI,CAAC1C,KAAK,CAAC2C,YAAY,CAAC,CAAC;MACvC,IAAMC,KAAK,GACT3B,IAAI,IAAI,IAAI,GACR,IAAI,CAACjB,KAAK,CAACpG,MAAM,CAACiJ,WAAW,CAAC3G,wBAAwB,CAAC,GACvD,CAAC+E,IAAI,CAAC;MACZ2B,KAAK,CAACpG,OAAO,CAAE,UAAAsG,SAAS,EAAK;QAC3BA,SAAS,CAACnG,SAAS,CAACwF,MAAI,CAAC3B,aAAa,EAAE4B,KAAK,CAAC;MAChD,CAAC,CAAC;MACF,IAAI,CAACpC,KAAK,CAACuC,MAAM,CAACzB,cAAK,CAAC0B,OAAO,CAACO,MAAM,CAAC;MACvC,IAAIL,KAAK,IAAI,IAAI,EAAE;QACjB,IAAI,CAAC1C,KAAK,CAACgD,YAAY,CAACN,KAAK,EAAE5B,cAAK,CAAC0B,OAAO,CAACO,MAAM,CAAC;MACtD;IACF;EAAA;IAAAzI,GAAA;IAAAR,KAAA,EAEA,SAAA0G,aAAaA,CAACnD,IAAY,EAAsB;MAAA,IAApBK,QAAQ,GAAA3C,SAAA,CAAAe,MAAA,QAAAf,SAAA,QAAAO,SAAA,GAAAP,SAAA,MAAG,OAAO;MAC5C2C,QAAQ,GAAG,IAAI,CAAC2C,SAAS,CAAC3C,QAAQ,CAAC,GAAGA,QAAQ,GAAG,OAAO;MACxD,IAAIA,QAAQ,KAAK,OAAO,EAAE;QACxB,OAAO,IAAAqB,gBAAU,EAAC1B,IAAI,CAAC,CACpBuC,KAAK,CAAC,IAAI,CAAC,CACX9B,MAAM,CAAC,UAACC,KAAK,EAAEkF,IAAI,EAAEC,CAAC,EAAK;UAC1B,IAAIA,CAAC,KAAK,CAAC,EAAE;YACXnF,KAAK,CAACoF,MAAM,CAAC,IAAI,MAAAC,gBAAA,CAAAnJ,OAAA,MAAKmB,aAAS,CAACZ,QAAQ,EAAGkD,QAAA,CAAU,CAAC;UACxD;UACA,OAAOK,KAAK,CAACoF,MAAM,CAACF,IAAI,CAAC;QAC3B,CAAC,EAAE,IAAIhF,mBAAK,CAAC,CAAC,CAAC;MACnB;MACA,IAAM6E,SAAS,GAAG,IAAI,CAAC9C,KAAK,CAACmB,IAAI,CAACC,aAAa,CAACC,aAAa,CAAC,KAAK,CAAC;MACpEyB,SAAS,CAACnI,SAAS,CAACR,GAAG,CAACiB,aAAS,CAACP,SAAS,CAAC;MAC5CiI,SAAS,CAACO,SAAS,GAAG1G,SAAS,CAAC,IAAI,CAACsD,OAAO,CAACE,IAAI,EAAEzC,QAAQ,EAAEL,IAAI,CAAC;MAClE,OAAO,IAAAiG,mBAAQ,EACb,IAAI,CAACtD,KAAK,CAACpG,MAAM,EACjBkJ,SAAS,EACT,CACE,UAAC5H,IAAI,EAAE6C,KAAK,EAAK;QACf;QACA,IAAMjE,KAAK,GAAGV,eAAe,CAACU,KAAK,CAACoB,IAAI,CAAC;QACzC,IAAIpB,KAAK,EAAE;UACT,OAAOiE,KAAK,CAACwF,OAAO,CAClB,IAAItF,mBAAK,CAAC,CAAC,CAACG,MAAM,CAACL,KAAK,CAACjC,MAAM,CAAC,CAAC,MAAAsH,gBAAA,CAAAnJ,OAAA,MAC9BR,SAAS,CAACe,QAAQ,EAAGV,KAAA,CACvB,CACH,CAAC;QACH;QACA,OAAOiE,KAAK;MACd,CAAC,CACF,EACD,CACE,UAAC7C,IAAI,EAAE6C,KAAK,EAAK;QACf;QACA,OAAO7C,IAAI,CAACsI,IAAI,CAAC5D,KAAK,CAAC,IAAI,CAAC,CAAC9B,MAAM,CAAC,UAACwC,IAAI,EAAEmD,QAAQ,EAAEP,CAAC,EAAK;UACzD,IAAIA,CAAC,KAAK,CAAC,EAAE5C,IAAI,CAAC6C,MAAM,CAAC,IAAI,MAAAC,gBAAA,CAAAnJ,OAAA,MAAKmB,aAAS,CAACZ,QAAQ,EAAGkD,QAAA,CAAU,CAAC;UAClE,OAAO4C,IAAI,CAAC6C,MAAM,CAACM,QAAQ,CAAC;QAC9B,CAAC,EAAE1F,KAAK,CAAC;MACX,CAAC,CACF,EACD,IAAI2F,OAAO,CAAC,CACd,CAAC;IACH;EAAA;IAAApJ,GAAA;IAAAR,KAAA,EA9HA,SAAOmC,QAAQA,CAAA,EAAG;MAChB6E,cAAK,CAAC7E,QAAQ,CAACxC,SAAS,EAAE,IAAI,CAAC;MAC/BqH,cAAK,CAAC7E,QAAQ,CAACT,eAAe,EAAE,IAAI,CAAC;MACrCsF,cAAK,CAAC7E,QAAQ,CAACC,wBAAwB,EAAE,IAAI,CAAC;IAChD;EAAA;AAAA,EAPmByH,eAAM;AAmI3B7D,MAAM,CAAC8D,QAAQ,GAAG;EAChBzD,IAAI,EAAG,YAAM;IACX,OAAO0D,MAAM,CAAC1D,IAAI;EACpB,CAAC,CAAE,CAAC;EACJ+B,QAAQ,EAAE,IAAI;EACd7B,SAAS,EAAE,CACT;IAAE/F,GAAG,EAAE,OAAO;IAAEiH,KAAK,EAAE;EAAQ,CAAC,EAChC;IAAEjH,GAAG,EAAE,MAAM;IAAEiH,KAAK,EAAE;EAAO,CAAC,EAC9B;IAAEjH,GAAG,EAAE,KAAK;IAAEiH,KAAK,EAAE;EAAM,CAAC,EAC5B;IAAEjH,GAAG,EAAE,IAAI;IAAEiH,KAAK,EAAE;EAAK,CAAC,EAC1B;IAAEjH,GAAG,EAAE,KAAK;IAAEiH,KAAK,EAAE;EAAM,CAAC,EAC5B;IAAEjH,GAAG,EAAE,MAAM;IAAEiH,KAAK,EAAE;EAAO,CAAC,EAC9B;IAAEjH,GAAG,EAAE,KAAK;IAAEiH,KAAK,EAAE;EAAW,CAAC,EACjC;IAAEjH,GAAG,EAAE,MAAM;IAAEiH,KAAK,EAAE;EAAO,CAAC,EAC9B;IAAEjH,GAAG,EAAE,YAAY;IAAEiH,KAAK,EAAE;EAAa,CAAC,EAC1C;IAAEjH,GAAG,EAAE,UAAU;IAAEiH,KAAK,EAAE;EAAW,CAAC,EACtC;IAAEjH,GAAG,EAAE,KAAK;IAAEiH,KAAK,EAAE;EAAM,CAAC,EAC5B;IAAEjH,GAAG,EAAE,QAAQ;IAAEiH,KAAK,EAAE;EAAS,CAAC,EAClC;IAAEjH,GAAG,EAAE,MAAM;IAAEiH,KAAK,EAAE;EAAO,CAAC,EAC9B;IAAEjH,GAAG,EAAE,KAAK;IAAEiH,KAAK,EAAE;EAAM,CAAC;AAEhC,CAAC", "ignoreList": []}]}