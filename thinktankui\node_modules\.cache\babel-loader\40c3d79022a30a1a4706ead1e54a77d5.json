{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\background.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\background.js", "mtime": 1749104419956}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLkJhY2tncm91bmRTdHlsZSA9IGV4cG9ydHMuQmFja2dyb3VuZENsYXNzID0gdm9pZCAwOwp2YXIgX3BhcmNobWVudCA9IHJlcXVpcmUoInBhcmNobWVudCIpOwp2YXIgX2NvbG9yID0gcmVxdWlyZSgiLi9jb2xvci5qcyIpOwp2YXIgQmFja2dyb3VuZENsYXNzID0gZXhwb3J0cy5CYWNrZ3JvdW5kQ2xhc3MgPSBuZXcgX3BhcmNobWVudC5DbGFzc0F0dHJpYnV0b3IoJ2JhY2tncm91bmQnLCAncWwtYmcnLCB7CiAgc2NvcGU6IF9wYXJjaG1lbnQuU2NvcGUuSU5MSU5FCn0pOwp2YXIgQmFja2dyb3VuZFN0eWxlID0gZXhwb3J0cy5CYWNrZ3JvdW5kU3R5bGUgPSBuZXcgX2NvbG9yLkNvbG9yQXR0cmlidXRvcignYmFja2dyb3VuZCcsICdiYWNrZ3JvdW5kLWNvbG9yJywgewogIHNjb3BlOiBfcGFyY2htZW50LlNjb3BlLklOTElORQp9KTs="}, {"version": 3, "names": ["_parchment", "require", "_color", "BackgroundClass", "exports", "ClassAttributor", "scope", "<PERSON><PERSON>", "INLINE", "BackgroundStyle", "ColorAttributor"], "sources": ["../../src/formats/background.ts"], "sourcesContent": ["import { ClassAttributor, Scope } from 'parchment';\nimport { ColorAttributor } from './color.js';\n\nconst BackgroundClass = new ClassAttributor('background', 'ql-bg', {\n  scope: Scope.INLINE,\n});\nconst BackgroundStyle = new ColorAttributor('background', 'background-color', {\n  scope: Scope.INLINE,\n});\n\nexport { BackgroundClass, BackgroundStyle };\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAEA,IAAME,eAAe,GAAAC,OAAA,CAAAD,eAAA,GAAG,IAAIE,0BAAe,CAAC,YAAY,EAAE,OAAO,EAAE;EACjEC,KAAK,EAAEC,gBAAK,CAACC;AACf,CAAC,CAAC;AACF,IAAMC,eAAe,GAAAL,OAAA,CAAAK,eAAA,GAAG,IAAIC,sBAAe,CAAC,YAAY,EAAE,kBAAkB,EAAE;EAC5EJ,KAAK,EAAEC,gBAAK,CAACC;AACf,CAAC,CAAC", "ignoreList": []}]}