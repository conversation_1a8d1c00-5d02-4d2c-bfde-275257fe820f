{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\system\\user\\profile\\userAvatar.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\system\\user\\profile\\userAvatar.vue", "mtime": 1749104047647}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["userAvatar.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwDA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "userAvatar.vue", "sourceRoot": "src/views/system/user/profile", "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"user-info-head\" @click=\"editCropper()\"><img v-bind:src=\"options.img\" title=\"点击上传头像\" class=\"img-circle img-lg\" /></div>\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body @opened=\"modalOpened\"  @close=\"closeDialog\">\r\n      <el-row>\r\n        <el-col :xs=\"24\" :md=\"12\" :style=\"{height: '350px'}\">\r\n          <vue-cropper\r\n            ref=\"cropper\"\r\n            :img=\"options.img\"\r\n            :info=\"true\"\r\n            :autoCrop=\"options.autoCrop\"\r\n            :autoCropWidth=\"options.autoCropWidth\"\r\n            :autoCropHeight=\"options.autoCropHeight\"\r\n            :fixedBox=\"options.fixedBox\"\r\n            :outputType=\"options.outputType\"\r\n            @realTime=\"realTime\"\r\n            v-if=\"visible\"\r\n          />\r\n        </el-col>\r\n        <el-col :xs=\"24\" :md=\"12\" :style=\"{height: '350px'}\">\r\n          <div class=\"avatar-upload-preview\">\r\n            <img :src=\"previews.url\" :style=\"previews.img\" />\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <br />\r\n      <el-row>\r\n        <el-col :lg=\"2\" :sm=\"3\" :xs=\"3\">\r\n          <el-upload action=\"#\" :http-request=\"requestUpload\" :show-file-list=\"false\" :before-upload=\"beforeUpload\">\r\n            <el-button size=\"small\">\r\n              选择\r\n              <i class=\"el-icon-upload el-icon--right\"></i>\r\n            </el-button>\r\n          </el-upload>\r\n        </el-col>\r\n        <el-col :lg=\"{span: 1, offset: 2}\" :sm=\"2\" :xs=\"2\">\r\n          <el-button icon=\"el-icon-plus\" size=\"small\" @click=\"changeScale(1)\"></el-button>\r\n        </el-col>\r\n        <el-col :lg=\"{span: 1, offset: 1}\" :sm=\"2\" :xs=\"2\">\r\n          <el-button icon=\"el-icon-minus\" size=\"small\" @click=\"changeScale(-1)\"></el-button>\r\n        </el-col>\r\n        <el-col :lg=\"{span: 1, offset: 1}\" :sm=\"2\" :xs=\"2\">\r\n          <el-button icon=\"el-icon-refresh-left\" size=\"small\" @click=\"rotateLeft()\"></el-button>\r\n        </el-col>\r\n        <el-col :lg=\"{span: 1, offset: 1}\" :sm=\"2\" :xs=\"2\">\r\n          <el-button icon=\"el-icon-refresh-right\" size=\"small\" @click=\"rotateRight()\"></el-button>\r\n        </el-col>\r\n        <el-col :lg=\"{span: 2, offset: 6}\" :sm=\"2\" :xs=\"2\">\r\n          <el-button type=\"primary\" size=\"small\" @click=\"uploadImg()\">提 交</el-button>\r\n        </el-col>\r\n      </el-row>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport store from \"@/store\";\r\nimport { VueCropper } from \"vue-cropper\";\r\nimport { uploadAvatar } from \"@/api/system/user\";\r\nimport { debounce } from '@/utils'\r\n\r\nexport default {\r\n  components: { VueCropper },\r\n  data() {\r\n    return {\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否显示cropper\r\n      visible: false,\r\n      // 弹出层标题\r\n      title: \"修改头像\",\r\n      options: {\r\n        img: store.getters.avatar,  //裁剪图片的地址\r\n        autoCrop: true,             // 是否默认生成截图框\r\n        autoCropWidth: 200,         // 默认生成截图框宽度\r\n        autoCropHeight: 200,        // 默认生成截图框高度\r\n        fixedBox: true,             // 固定截图框大小 不允许改变\r\n        outputType:\"png\",           // 默认生成截图为PNG格式\r\n        filename: 'avatar'          // 文件名称\r\n      },\r\n      previews: {},\r\n      resizeHandler: null\r\n    };\r\n  },\r\n  methods: {\r\n    // 编辑头像\r\n    editCropper() {\r\n      this.open = true;\r\n    },\r\n    // 打开弹出层结束时的回调\r\n    modalOpened() {\r\n      this.visible = true;\r\n      if (!this.resizeHandler) {\r\n        this.resizeHandler = debounce(() => {\r\n          this.refresh()\r\n        }, 100)\r\n      }\r\n      window.addEventListener(\"resize\", this.resizeHandler)\r\n    },\r\n    // 刷新组件\r\n    refresh() {\r\n      this.$refs.cropper.refresh();\r\n    },\r\n    // 覆盖默认的上传行为\r\n    requestUpload() {\r\n    },\r\n    // 向左旋转\r\n    rotateLeft() {\r\n      this.$refs.cropper.rotateLeft();\r\n    },\r\n    // 向右旋转\r\n    rotateRight() {\r\n      this.$refs.cropper.rotateRight();\r\n    },\r\n    // 图片缩放\r\n    changeScale(num) {\r\n      num = num || 1;\r\n      this.$refs.cropper.changeScale(num);\r\n    },\r\n    // 上传预处理\r\n    beforeUpload(file) {\r\n      if (file.type.indexOf(\"image/\") == -1) {\r\n        this.$modal.msgError(\"文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。\");\r\n      } else {\r\n        const reader = new FileReader();\r\n        reader.readAsDataURL(file);\r\n        reader.onload = () => {\r\n          this.options.img = reader.result;\r\n          this.options.filename = file.name;\r\n        };\r\n      }\r\n    },\r\n    // 上传图片\r\n    uploadImg() {\r\n      this.$refs.cropper.getCropBlob(data => {\r\n        let formData = new FormData();\r\n        formData.append(\"avatarfile\", data, this.options.filename);\r\n        uploadAvatar(formData).then(response => {\r\n          this.open = false;\r\n          this.options.img = process.env.VUE_APP_BASE_API + response.imgUrl;\r\n          store.commit('SET_AVATAR', this.options.img);\r\n          this.$modal.msgSuccess(\"修改成功\");\r\n          this.visible = false;\r\n        });\r\n      });\r\n    },\r\n    // 实时预览\r\n    realTime(data) {\r\n      this.previews = data;\r\n    },\r\n    // 关闭窗口\r\n    closeDialog() {\r\n      this.options.img = store.getters.avatar\r\n      this.visible = false;\r\n      window.removeEventListener(\"resize\", this.resizeHandler)\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.user-info-head {\r\n  position: relative;\r\n  display: inline-block;\r\n  height: 120px;\r\n}\r\n\r\n.user-info-head:hover:after {\r\n  content: '+';\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  top: 0;\r\n  bottom: 0;\r\n  color: #eee;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  font-size: 24px;\r\n  font-style: normal;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  cursor: pointer;\r\n  line-height: 110px;\r\n  border-radius: 50%;\r\n}\r\n</style>\r\n"]}]}