{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\account\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\account\\index.vue", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgVXNlck1hbmFnZW1lbnQgZnJvbSAnLi91c2VyLW1hbmFnZW1lbnQudnVlJzsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiQWNjb3VudE1hbmFnZW1lbnQiLA0KICBjb21wb25lbnRzOiB7DQogICAgVXNlck1hbmFnZW1lbnQNCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICBzZWxlY3RBbGxGYXZvcml0ZXModmFsKSB7DQogICAgICB0aGlzLndhdGNoU2VsZWN0QWxsRmF2b3JpdGVzKHZhbCk7DQogICAgfQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBhY3RpdmVNZW51SXRlbTogJ2FjY291bnQnLCAvLyDpu5jorqTpgInkuK3miJHnmoTotKblj7cNCiAgICAgIGN1cnJlbnRTZXR0aW5nc1RhYjogJ2Jhc2ljJywgLy8g6buY6K6k6YCJ5Lit5Z+656GA6K6+572uDQogICAgICAvLyDnlKjmiLfkv6Hmga8NCiAgICAgIHVzZXJJbmZvOiB7DQogICAgICAgIHVzZXJJZDogJzE3NjEwOTcyMjExJywNCiAgICAgICAgcGhvbmVOdW1iZXI6ICcxNzYxMDk3MjIxMScsDQogICAgICAgIHJlZ2lzdGVyRGF0ZTogJzIwMjMtMDQtMjgnLA0KICAgICAgICBjYXRlZ29yeTogJ+S6kuiBlOe9kSsnLA0KICAgICAgICBwbGFuOiAn44CQ5YWN6LS544CRK+OAkOmZkOaXtuOAkScsDQogICAgICAgIHRvdGFsUG9pbnRzOiAnMjAwMCcsDQogICAgICAgIHBvaW50c0xldmVsOiAn5Yid57qnVklQJywNCiAgICAgICAgYXZhaWxhYmxlUG9pbnRzOiAn44CQMjUw56ev5YiGID0gNjAw5qyh44CRJywNCiAgICAgICAgZXN0aW1hdGVkRGF5czogJzM2NScsDQogICAgICAgIGV4cGlyYXRpb25EYXRlOiAnMzY1JywNCiAgICAgICAgcmVtYWluaW5nQ291bnQ6ICcxMDAwMOasoScNCiAgICAgIH0sDQogICAgICAvLyDns7vnu5/orr7nva4NCiAgICAgIHNldHRpbmdzOiB7DQogICAgICAgIGF1dG86IHRydWUsDQogICAgICAgIG1hbnVhbDogdHJ1ZSwNCiAgICAgICAgZG93bmxvYWRJbWFnZXM6IHRydWUsDQogICAgICAgIGJhY2t1cDogdHJ1ZSwNCiAgICAgICAgcmVhbFRpbWVTeW5jOiBmYWxzZSwNCiAgICAgICAgaHVtYW5SZXZpZXc6IHRydWUsDQogICAgICAgIGF1dG9QdWJsaXNoOiBmYWxzZSwNCiAgICAgICAga2V5d29yZHM6IHRydWUsDQogICAgICAgIGhpZ2hRdWFsaXR5OiBmYWxzZSwNCiAgICAgICAgbXVsdGlQbGF0Zm9ybTogdHJ1ZSwNCiAgICAgICAgYXV0b1NhdmU6IHRydWUsDQogICAgICAgIGJhdGNoUHJvY2VzczogdHJ1ZSwNCiAgICAgICAgaW1hZ2VQcm9jZXNzOiB0cnVlLA0KICAgICAgICBhdXRvT246IHRydWUsDQogICAgICAgIGF1dG9PZmY6IGZhbHNlDQogICAgICB9LA0KICAgICAgLy8g5LiL6L295YiX6KGoDQogICAgICBkb3dubG9hZExpc3Q6IFsNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAxLA0KICAgICAgICAgIG5hbWU6ICfmlrnmoYhfMjAyNDA0MjgxNjM3NDNfMScsDQogICAgICAgICAgZGF0YVNpemU6ICcxMzIzJywNCiAgICAgICAgICBjcmVhdGVUaW1lOiAnMjAyNC0wNC0yOCAxNjozNzo1NycsDQogICAgICAgICAgc3RhdHVzOiAn5bey5a6M5oiQJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDIsDQogICAgICAgICAgbmFtZTogJ+aWueahiF8yMDI0MDQyODE2Mzc0M18xJywNCiAgICAgICAgICBkYXRhU2l6ZTogJzIwMDAnLA0KICAgICAgICAgIGNyZWF0ZVRpbWU6ICcyMDI0LTA0LTI4IDE2OjM3OjU3JywNCiAgICAgICAgICBzdGF0dXM6ICflt7LlrozmiJAnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpZDogMywNCiAgICAgICAgICBuYW1lOiAn5pa55qGIXzIwMjQwNDI3MTczNzQyXzQnLA0KICAgICAgICAgIGRhdGFTaXplOiAnMTg5MycsDQogICAgICAgICAgY3JlYXRlVGltZTogJzIwMjQtMDQtMjcgMTc6Mzc6NDInLA0KICAgICAgICAgIHN0YXR1czogJ+W3suWujOaIkCcNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiA0LA0KICAgICAgICAgIG5hbWU6ICfmlrnmoYhfMjAyNDA0MjcxNzM3NDJfMycsDQogICAgICAgICAgZGF0YVNpemU6ICcyMDAwJywNCiAgICAgICAgICBjcmVhdGVUaW1lOiAnMjAyNC0wNC0yNyAxNzozNzo0MicsDQogICAgICAgICAgc3RhdHVzOiAn5bey5a6M5oiQJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDUsDQogICAgICAgICAgbmFtZTogJ+aWueahiF8yMDI0MDQyNzE3Mzc0Ml8yJywNCiAgICAgICAgICBkYXRhU2l6ZTogJzIwMDAnLA0KICAgICAgICAgIGNyZWF0ZVRpbWU6ICcyMDI0LTA0LTI3IDE3OjM3OjQyJywNCiAgICAgICAgICBzdGF0dXM6ICflt7LlrozmiJAnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpZDogNiwNCiAgICAgICAgICBuYW1lOiAn5pa55qGIXzIwMjQwNDI3MTczNzQyXzInLA0KICAgICAgICAgIGRhdGFTaXplOiAnMjAwMCcsDQogICAgICAgICAgY3JlYXRlVGltZTogJzIwMjQtMDQtMjcgMTc6Mzc6NDInLA0KICAgICAgICAgIHN0YXR1czogJ+W3suWujOaIkCcNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiA3LA0KICAgICAgICAgIG5hbWU6ICfmlrnmoYhfMjAyNDA0MjcxNzM3NDJfMScsDQogICAgICAgICAgZGF0YVNpemU6ICcyMDAwJywNCiAgICAgICAgICBjcmVhdGVUaW1lOiAnMjAyNC0wNC0yNyAxNzozNzo0MicsDQogICAgICAgICAgc3RhdHVzOiAn5bey5a6M5oiQJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDgsDQogICAgICAgICAgbmFtZTogJ+WPsOi0pl8yMDI0MDQyNzE3MzEyOV81JywNCiAgICAgICAgICBkYXRhU2l6ZTogJzEyODEnLA0KICAgICAgICAgIGNyZWF0ZVRpbWU6ICcyMDI0LTA0LTI3IDE3OjMxOjI5JywNCiAgICAgICAgICBzdGF0dXM6ICflt7LlrozmiJAnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpZDogOSwNCiAgICAgICAgICBuYW1lOiAn5Y+w6LSmXzIwMjQwNDI3MTczMTI5XzQnLA0KICAgICAgICAgIGRhdGFTaXplOiAnMjAwMCcsDQogICAgICAgICAgY3JlYXRlVGltZTogJzIwMjQtMDQtMjcgMTc6MzE6MjknLA0KICAgICAgICAgIHN0YXR1czogJ+W3suWujOaIkCcNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAxMCwNCiAgICAgICAgICBuYW1lOiAn5Y+w6LSmXzIwMjQwNDI3MTczMTI5XzMnLA0KICAgICAgICAgIGRhdGFTaXplOiAnMjAwMCcsDQogICAgICAgICAgY3JlYXRlVGltZTogJzIwMjQtMDQtMjcgMTc6MzE6MjknLA0KICAgICAgICAgIHN0YXR1czogJ+W3suWujOaIkCcNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAxMSwNCiAgICAgICAgICBuYW1lOiAn5Y+w6LSmXzIwMjQwNDI3MTczMTI5XzInLA0KICAgICAgICAgIGRhdGFTaXplOiAnMjAwMCcsDQogICAgICAgICAgY3JlYXRlVGltZTogJzIwMjQtMDQtMjcgMTc6MzE6MjknLA0KICAgICAgICAgIHN0YXR1czogJ+W3suWujOaIkCcNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAxMiwNCiAgICAgICAgICBuYW1lOiAn5Y+w6LSmXzIwMjQwNDI3MTczMTI5XzEnLA0KICAgICAgICAgIGRhdGFTaXplOiAnMjAwMCcsDQogICAgICAgICAgY3JlYXRlVGltZTogJzIwMjQtMDQtMjcgMTc6MzE6MjknLA0KICAgICAgICAgIHN0YXR1czogJ+W3suWujOaIkCcNCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIC8vIOWIhumhteebuOWFsw0KICAgICAgdG90YWw6IDEyLA0KICAgICAgY3VycmVudFBhZ2U6IDEsDQogICAgICBwYWdlU2l6ZTogMTAsDQogICAgICBqdW1wUGFnZTogJycsDQogICAgICAvLyDpgInkuK3nmoTkuIvovb3pobkNCiAgICAgIHNlbGVjdGVkRG93bmxvYWRzOiBbXSwNCiAgICAgIC8vIOeJiOacrOWIl+ihqA0KICAgICAgdmVyc2lvbkxpc3Q6IFsNCiAgICAgICAgeyB2ZXJzaW9uOiAnNi4yLjknLCBkYXRlOiAnMjAyNC4wMS4xOScgfSwNCiAgICAgICAgeyB2ZXJzaW9uOiAnNi4yLjgnLCBkYXRlOiAnMjAyMy4xMi4xNScgfSwNCiAgICAgICAgeyB2ZXJzaW9uOiAnNi4yLjcnLCBkYXRlOiAnMjAyMy4xMS4yMCcgfSwNCiAgICAgICAgeyB2ZXJzaW9uOiAnNi4yLjYnLCBkYXRlOiAnMjAyMy4xMC4xOCcgfSwNCiAgICAgICAgeyB2ZXJzaW9uOiAnNi4yLjUnLCBkYXRlOiAnMjAyMy4wOS4yNScgfSwNCiAgICAgICAgeyB2ZXJzaW9uOiAnNi4yLjQnLCBkYXRlOiAnMjAyMy4wOC4zMCcgfSwNCiAgICAgICAgeyB2ZXJzaW9uOiAnNi4yLjMnLCBkYXRlOiAnMjAyMy4wNy4yOCcgfSwNCiAgICAgICAgeyB2ZXJzaW9uOiAnNi4yLjInLCBkYXRlOiAnMjAyMy4wNi4yMicgfSwNCiAgICAgICAgeyB2ZXJzaW9uOiAnNi4yLjEnLCBkYXRlOiAnMjAyMy4wNS4xNScgfSwNCiAgICAgICAgeyB2ZXJzaW9uOiAnNi4yLjAnLCBkYXRlOiAnMjAyMy4wNC4xMCcgfQ0KICAgICAgXSwNCiAgICAgIC8vIOW9k+WJjemAieS4reeahOeJiOacrA0KICAgICAgc2VsZWN0ZWRWZXJzaW9uOiB7DQogICAgICAgIHZlcnNpb246ICc2LjIuOScsDQogICAgICAgIGRhdGU6ICcyMDI0LjAxLjE5JywNCiAgICAgICAgbm90ZXM6IFsNCiAgICAgICAgICAnMS4g44CQ5paw5aKe5Yqf6IO944CR5paw5aKePGI+5Liq5Lq65Lit5b+DPC9iPuWKn+iDve+8jOeUqOaIt+WPr+S7peafpeeci+WSjOeuoeeQhuS4quS6uuS/oeaBr+OAgicsDQogICAgICAgICAgJzIuIOOAkOWKn+iDveS8mOWMluOAkeS8mOWMluS6hjxiPuaQnOe0ouW8leaTjjwvYj7nmoTmgKfog73vvIzmj5Dpq5jkuobmkJzntKLpgJ/luqblkozlh4bnoa7mgKfjgIInLA0KICAgICAgICAgICczLiDjgJDnlYzpnaLosIPmlbTjgJHosIPmlbTkuoY8Yj7pppbpobXluIPlsYA8L2I+77yM5L2/55WM6Z2i5pu05Yqg566A5rSB576O6KeC44CCJywNCiAgICAgICAgICAnNC4g44CQ6Zeu6aKY5L+u5aSN44CR5L+u5aSN5LqG5Zyo5p+Q5Lqb5oOF5Ya15LiLPGI+IuaVsOaNruWvvOWHuiI8L2I+5Yqf6IO95aSx5pWI55qE6Zeu6aKY44CCJywNCiAgICAgICAgICAnNS4g44CQ5a6J5YWo5aKe5by644CR5aKe5by65LqG57O757uf55qEPGI+5a6J5YWo5oCnPC9iPu+8jOaPkOmrmOS6huaVsOaNruS/neaKpOiDveWKm+OAgicNCiAgICAgICAgXQ0KICAgICAgfSwNCiAgICAgIC8vIOe0oOadkOW6k+ebuOWFsw0KICAgICAgYWRkTWF0ZXJpYWxEaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIG5ld01hdGVyaWFsTmFtZTogJycsDQogICAgICBtYXRlcmlhbExpc3Q6IFtdLA0KICAgICAgLy8g5L+u5pS55a+G56CB55u45YWzDQogICAgICBzaG93UGFzc3dvcmRDaGFuZ2U6IGZhbHNlLA0KICAgICAgcGFzc3dvcmRGb3JtOiB7DQogICAgICAgIG9sZFBhc3N3b3JkOiAnJywNCiAgICAgICAgbmV3UGFzc3dvcmQ6ICcnLA0KICAgICAgICBjb25maXJtUGFzc3dvcmQ6ICcnDQogICAgICB9LA0KICAgICAgLy8g5oiR55qE5pS26JeP55u45YWzDQogICAgICBzZWxlY3RBbGxGYXZvcml0ZXM6IGZhbHNlLA0KICAgICAgc2VsZWN0ZWRGYXZvcml0ZXM6IFtdLA0KICAgICAgZmF2b3JpdGVTdGFydERhdGU6ICcnLA0KICAgICAgZmF2b3JpdGVFbmREYXRlOiAnJywNCiAgICAgIGZhdm9yaXRlU2VhcmNoS2V5d29yZDogJycsDQogICAgICBmYXZvcml0ZUxpc3Q6IFsNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAxLA0KICAgICAgICAgIHNlbGVjdGVkOiBmYWxzZSwNCiAgICAgICAgICB0YWc6ICdIT1QnLA0KICAgICAgICAgIHRpdGxlOiAn5paw5Lqn5ZOB5biC5Zy65YiG5p6Q5oql5ZGKJywNCiAgICAgICAgICB0aW1lOiAnMjAyMy0wNC0yOSAyMDozNzo1MScsDQogICAgICAgICAgc291cmNlOiAn5biC5Zy66LCD5p+l6YOo6ZeoIEEg5pWw5o2u5bqTJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDIsDQogICAgICAgICAgc2VsZWN0ZWQ6IGZhbHNlLA0KICAgICAgICAgIHRhZzogJ05FVycsDQogICAgICAgICAgdGl0bGU6ICcyMDI05bm056ys5LiA5a2j5bqm6KGM5Lia6LaL5Yq/5YiG5p6QJywNCiAgICAgICAgICB0aW1lOiAnMjAyNC0wNC0yOCAxNToyMjozNicsDQogICAgICAgICAgc291cmNlOiAn6KGM5Lia56CU56m25Lit5b+DJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDMsDQogICAgICAgICAgc2VsZWN0ZWQ6IGZhbHNlLA0KICAgICAgICAgIHRhZzogJ0hPVCcsDQogICAgICAgICAgdGl0bGU6ICfnq57lk4HliIbmnpDkuI7luILlnLrlrprkvY3nrZbnlaUnLA0KICAgICAgICAgIHRpbWU6ICcyMDI0LTA0LTI3IDA5OjE1OjQyJywNCiAgICAgICAgICBzb3VyY2U6ICfmiJjnlaXop4TliJLpg6gnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpZDogNCwNCiAgICAgICAgICBzZWxlY3RlZDogZmFsc2UsDQogICAgICAgICAgdGFnOiAnVE9QJywNCiAgICAgICAgICB0aXRsZTogJ+eUqOaIt+ihjOS4uuaVsOaNruWIhuaekOaKpeWRiicsDQogICAgICAgICAgdGltZTogJzIwMjQtMDQtMjYgMTQ6MzA6MTgnLA0KICAgICAgICAgIHNvdXJjZTogJ+aVsOaNruWIhuaekOmDqOmXqCcNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiA1LA0KICAgICAgICAgIHNlbGVjdGVkOiBmYWxzZSwNCiAgICAgICAgICB0YWc6ICdORVcnLA0KICAgICAgICAgIHRpdGxlOiAn5paw5aqS5L2T6JCl6ZSA562W55Wl55m955qu5LmmJywNCiAgICAgICAgICB0aW1lOiAnMjAyNC0wNC0yNSAxMTo0NToyMycsDQogICAgICAgICAgc291cmNlOiAn6JCl6ZSA6YOo6ZeoJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDYsDQogICAgICAgICAgc2VsZWN0ZWQ6IGZhbHNlLA0KICAgICAgICAgIHRhZzogJ0hPVCcsDQogICAgICAgICAgdGl0bGU6ICfkuqflk4Hov63ku6PorqHliJLkuI7ot6/nur/lm74nLA0KICAgICAgICAgIHRpbWU6ICcyMDI0LTA0LTI0IDE2OjIwOjM3JywNCiAgICAgICAgICBzb3VyY2U6ICfkuqflk4Hpg6jpl6gnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpZDogNywNCiAgICAgICAgICBzZWxlY3RlZDogZmFsc2UsDQogICAgICAgICAgdGFnOiAnVE9QJywNCiAgICAgICAgICB0aXRsZTogJ+ihjOS4muaUv+etluino+ivu+S4juW9seWTjeWIhuaekCcsDQogICAgICAgICAgdGltZTogJzIwMjQtMDQtMjMgMTA6MDU6MTInLA0KICAgICAgICAgIHNvdXJjZTogJ+aUv+etlueglOeptuS4reW/gycNCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIGZhdm9yaXRlc1RvdGFsOiA3LA0KICAgICAgZmF2b3JpdGVzQ3VycmVudFBhZ2U6IDEsDQogICAgICBmYXZvcml0ZXNQYWdlU2l6ZTogMTAsDQogICAgICBmYXZvcml0ZXNKdW1wUGFnZTogJycsDQoNCiAgICAgIC8vIOaIkeeahOiBlOezu+S6uuebuOWFsw0KICAgICAgY29udGFjdEFjdGl2ZVRhYjogJ3dlY2hhdCcsIC8vIOm7mOiupOmAieS4reW+ruS/oeagh+etvg0KICAgICAgY29udGFjdExpc3Q6IFsNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAxLA0KICAgICAgICAgIG5hbWU6ICflvKDkuIknLA0KICAgICAgICAgIHBob25lOiAnMTM4MDAxMzgwMDEnLA0KICAgICAgICAgIGVtYWlsOiAnemhhbmdzYW5AZXhhbXBsZS5jb20nLA0KICAgICAgICAgIGxvY2F0aW9uOiAn5YyX5Lqs5biC5pyd6Ziz5Yy6JywNCiAgICAgICAgICBjcmVhdGVUaW1lOiAnMjAyNC0wNC0yOCAxMDozMDo0NScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAyLA0KICAgICAgICAgIG5hbWU6ICfmnY7lm5snLA0KICAgICAgICAgIHBob25lOiAnMTM4MDAxMzgwMDInLA0KICAgICAgICAgIGVtYWlsOiAnbGlzaUBleGFtcGxlLmNvbScsDQogICAgICAgICAgbG9jYXRpb246ICfkuIrmtbfluILmtabkuJzmlrDljLonLA0KICAgICAgICAgIGNyZWF0ZVRpbWU6ICcyMDI0LTA0LTI3IDE1OjIwOjM2Jw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDMsDQogICAgICAgICAgbmFtZTogJ+eOi+S6lCcsDQogICAgICAgICAgcGhvbmU6ICcxMzgwMDEzODAwMycsDQogICAgICAgICAgZW1haWw6ICd3YW5nd3VAZXhhbXBsZS5jb20nLA0KICAgICAgICAgIGxvY2F0aW9uOiAn5bm/5bee5biC5aSp5rKz5Yy6JywNCiAgICAgICAgICBjcmVhdGVUaW1lOiAnMjAyNC0wNC0yNiAwOToxNToyMicNCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIGFkZENvbnRhY3REaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGNvbnRhY3RGb3JtOiB7DQogICAgICAgIGlkOiBudWxsLA0KICAgICAgICBuYW1lOiAnJywNCiAgICAgICAgcGhvbmU6ICcnLA0KICAgICAgICBlbWFpbDogJycsDQogICAgICAgIGxvY2F0aW9uOiAnJw0KICAgICAgfSwNCiAgICAgIGNvbnRhY3RGb3JtUnVsZXM6IHsNCiAgICAgICAgbmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXlp5PlkI0nLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KICAgICAgICBwaG9uZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXmiYvmnLrlj7fnoIEnLCB0cmlnZ2VyOiAnYmx1cicgfSwNCiAgICAgICAgICB7IHBhdHRlcm46IC9eMVszLTldXGR7OX0kLywgbWVzc2FnZTogJ+ivt+i+k+WFpeato+ehrueahOaJi+acuuWPt+eggScsIHRyaWdnZXI6ICdibHVyJyB9DQogICAgICAgIF0sDQogICAgICAgIGVtYWlsOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpemCrueusScsIHRyaWdnZXI6ICdibHVyJyB9LA0KICAgICAgICAgIHsgdHlwZTogJ2VtYWlsJywgbWVzc2FnZTogJ+ivt+i+k+WFpeato+ehrueahOmCrueuseWcsOWdgCcsIHRyaWdnZXI6ICdibHVyJyB9DQogICAgICAgIF0NCiAgICAgIH0NCiAgICB9Ow0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLy8g5aSE55CG6I+c5Y2V6aG56YCJ5oupDQogICAgaGFuZGxlTWVudVNlbGVjdChpbmRleCkgew0KICAgICAgdGhpcy5hY3RpdmVNZW51SXRlbSA9IGluZGV4Ow0KICAgIH0sDQogICAgLy8g5pi+56S66LSm5oi35L+h5oGvDQogICAgc2hvd0FjY291bnRJbmZvKCkgew0KICAgICAgdGhpcy5zaG93UGFzc3dvcmRDaGFuZ2UgPSBmYWxzZTsNCiAgICB9LA0KICAgIC8vIOaYvuekuuS/ruaUueWvhueggeihqOWNlQ0KICAgIHNob3dQYXNzd29yZEZvcm0oKSB7DQogICAgICB0aGlzLnNob3dQYXNzd29yZENoYW5nZSA9IHRydWU7DQogICAgICB0aGlzLnBhc3N3b3JkRm9ybSA9IHsNCiAgICAgICAgb2xkUGFzc3dvcmQ6ICcnLA0KICAgICAgICBuZXdQYXNzd29yZDogJycsDQogICAgICAgIGNvbmZpcm1QYXNzd29yZDogJycNCiAgICAgIH07DQogICAgfSwNCiAgICAvLyDkv67mlLnlr4bnoIENCiAgICBjaGFuZ2VQYXNzd29yZCgpIHsNCiAgICAgIC8vIOihqOWNlemqjOivgQ0KICAgICAgaWYgKCF0aGlzLnBhc3N3b3JkRm9ybS5vbGRQYXNzd29yZCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+i+k+WFpeaXp+WvhueggScpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICBpZiAoIXRoaXMucGFzc3dvcmRGb3JtLm5ld1Bhc3N3b3JkKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36L6T5YWl5paw5a+G56CBJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIGlmICghdGhpcy5wYXNzd29yZEZvcm0uY29uZmlybVBhc3N3b3JkKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+356Gu6K6k5paw5a+G56CBJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLnBhc3N3b3JkRm9ybS5uZXdQYXNzd29yZCAhPT0gdGhpcy5wYXNzd29yZEZvcm0uY29uZmlybVBhc3N3b3JkKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5Lik5qyh6L6T5YWl55qE5paw5a+G56CB5LiN5LiA6Ie0Jyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgLy8g5o+Q5Lqk5L+u5pS55a+G56CB6K+35rGCDQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WvhueggeS/ruaUueaIkOWKnycpOw0KICAgICAgdGhpcy5zaG93UGFzc3dvcmRDaGFuZ2UgPSBmYWxzZTsNCiAgICB9LA0KICAgIC8vIOWIh+aNouiuvue9rumAiemhueWNoQ0KICAgIHN3aXRjaFNldHRpbmdzVGFiKHRhYikgew0KICAgICAgdGhpcy5jdXJyZW50U2V0dGluZ3NUYWIgPSB0YWI7DQogICAgfSwNCiAgICAvLyDkv53lrZjorr7nva4NCiAgICBzYXZlU2V0dGluZ3MoKSB7DQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+iuvue9ruW3suS/neWtmCcpOw0KICAgIH0sDQogICAgLy8g5aSE55CG6KGo5qC86YCJ5oup5Y+Y5YyWDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5zZWxlY3RlZERvd25sb2FkcyA9IHNlbGVjdGlvbjsNCiAgICB9LA0KICAgIC8vIOaJuemHj+S4i+i9vQ0KICAgIGJhdGNoRG93bmxvYWQoKSB7DQogICAgICBpZiAodGhpcy5zZWxlY3RlZERvd25sb2Fkcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fpgInmi6nopoHkuIvovb3nmoTmlofku7YnKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDlt7LlvIDlp4vkuIvovb0ke3RoaXMuc2VsZWN0ZWREb3dubG9hZHMubGVuZ3RofeS4quaWh+S7tmApOw0KICAgIH0sDQogICAgLy8g5om56YeP5Yig6ZmkDQogICAgYmF0Y2hEZWxldGUoKSB7DQogICAgICBpZiAodGhpcy5zZWxlY3RlZERvd25sb2Fkcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fpgInmi6nopoHliKDpmaTnmoTmlofku7YnKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgdGhpcy4kY29uZmlybSgn56Gu5a6a6KaB5Yig6Zmk6YCJ5Lit55qE5paH5Lu25ZCX77yfJywgJ+aPkOekuicsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDlt7LliKDpmaQke3RoaXMuc2VsZWN0ZWREb3dubG9hZHMubGVuZ3RofeS4quaWh+S7tmApOw0KICAgICAgICAvLyDlrp7pmYXlupTnlKjkuK3ov5nph4zpnIDopoHosIPnlKjmjqXlj6PliKDpmaTmlofku7YNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCflt7Llj5bmtojliKDpmaQnKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5LiL6L295Y2V5Liq5paH5Lu2DQogICAgaGFuZGxlRG93bmxvYWQocm93KSB7DQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOW8gOWni+S4i+i9vTogJHtyb3cubmFtZX1gKTsNCiAgICB9LA0KICAgIC8vIOWIoOmZpOWNleS4quaWh+S7tg0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruWumuimgeWIoOmZpOivpeaWh+S7tuWQl++8nycsICfmj5DnpLonLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5bey5Yig6ZmkOiAke3Jvdy5uYW1lfWApOw0KICAgICAgICAvLyDlrp7pmYXlupTnlKjkuK3ov5nph4zpnIDopoHosIPnlKjmjqXlj6PliKDpmaTmlofku7YNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCflt7Llj5bmtojliKDpmaQnKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5aSE55CG6aG156CB5Y+Y5YyWDQogICAgaGFuZGxlQ3VycmVudENoYW5nZSh2YWwpIHsNCiAgICAgIHRoaXMuY3VycmVudFBhZ2UgPSB2YWw7DQogICAgICAvLyDlrp7pmYXlupTnlKjkuK3ov5nph4zpnIDopoHosIPnlKjmjqXlj6Pojrflj5blr7nlupTpobXnmoTmlbDmja4NCiAgICB9LA0KICAgIC8vIOWkhOeQhui3s+i9rOmhtemdog0KICAgIGhhbmRsZUp1bXBQYWdlKCkgew0KICAgICAgaWYgKCF0aGlzLmp1bXBQYWdlKSB7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIGNvbnN0IHBhZ2UgPSBwYXJzZUludCh0aGlzLmp1bXBQYWdlKTsNCiAgICAgIGlmIChpc05hTihwYWdlKSB8fCBwYWdlIDwgMSB8fCBwYWdlID4gTWF0aC5jZWlsKHRoaXMudG90YWwgLyB0aGlzLnBhZ2VTaXplKSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+i+k+WFpeacieaViOeahOmhteeggScpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICB0aGlzLmN1cnJlbnRQYWdlID0gcGFnZTsNCiAgICAgIC8vIOWunumZheW6lOeUqOS4rei/memHjOmcgOimgeiwg+eUqOaOpeWPo+iOt+WPluWvueW6lOmhteeahOaVsOaNrg0KICAgIH0sDQogICAgLy8g6YCJ5oup54mI5pysDQogICAgc2VsZWN0VmVyc2lvbih2ZXJzaW9uKSB7DQogICAgICBjb25zdCB2ZXJzaW9uRGF0YSA9IHRoaXMudmVyc2lvbkxpc3QuZmluZCh2ID0+IHYudmVyc2lvbiA9PT0gdmVyc2lvbik7DQogICAgICBpZiAodmVyc2lvbkRhdGEpIHsNCiAgICAgICAgLy8g5qC55o2u54mI5pys5Y+36I635Y+W5a+55bqU55qE5pu05paw6K+05piODQogICAgICAgIGxldCBub3RlcyA9IFtdOw0KICAgICAgICBpZiAodmVyc2lvbiA9PT0gJzYuMi45Jykgew0KICAgICAgICAgIG5vdGVzID0gWw0KICAgICAgICAgICAgJzEuIOOAkOaWsOWinuWKn+iDveOAkeaWsOWinjxiPuS4quS6uuS4reW/gzwvYj7lip/og73vvIznlKjmiLflj6/ku6Xmn6XnnIvlkoznrqHnkIbkuKrkurrkv6Hmga/jgIInLA0KICAgICAgICAgICAgJzIuIOOAkOWKn+iDveS8mOWMluOAkeS8mOWMluS6hjxiPuaQnOe0ouW8leaTjjwvYj7nmoTmgKfog73vvIzmj5Dpq5jkuobmkJzntKLpgJ/luqblkozlh4bnoa7mgKfjgIInLA0KICAgICAgICAgICAgJzMuIOOAkOeVjOmdouiwg+aVtOOAkeiwg+aVtOS6hjxiPummlumhteW4g+WxgDwvYj7vvIzkvb/nlYzpnaLmm7TliqDnroDmtIHnvo7op4LjgIInLA0KICAgICAgICAgICAgJzQuIOOAkOmXrumimOS/ruWkjeOAkeS/ruWkjeS6huWcqOafkOS6m+aDheWGteS4izxiPiLmlbDmja7lr7zlh7oiPC9iPuWKn+iDveWkseaViOeahOmXrumimOOAgicsDQogICAgICAgICAgICAnNS4g44CQ5a6J5YWo5aKe5by644CR5aKe5by65LqG57O757uf55qEPGI+5a6J5YWo5oCnPC9iPu+8jOaPkOmrmOS6huaVsOaNruS/neaKpOiDveWKm+OAgicNCiAgICAgICAgICBdOw0KICAgICAgICB9IGVsc2UgaWYgKHZlcnNpb24gPT09ICc2LjIuOCcpIHsNCiAgICAgICAgICBub3RlcyA9IFsNCiAgICAgICAgICAgICcxLiDjgJDmlrDlop7lip/og73jgJHmlrDlop48Yj7mlbDmja7liIbmnpA8L2I+5qih5Z2X77yM5o+Q5L6b5pu05YWo6Z2i55qE5pWw5o2u57uf6K6h44CCJywNCiAgICAgICAgICAgICcyLiDjgJDlip/og73kvJjljJbjgJHkvJjljJbkuoY8Yj7mlofku7bkuIrkvKA8L2I+5Yqf6IO977yM5pSv5oyB5pu05aSa5paH5Lu25qC85byP44CCJywNCiAgICAgICAgICAgICczLiDjgJDpl67popjkv67lpI3jgJHkv67lpI3kuobpg6jliIbnlKjmiLc8Yj7ml6Dms5XnmbvlvZU8L2I+55qE6Zeu6aKY44CCJw0KICAgICAgICAgIF07DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgbm90ZXMgPSBbDQogICAgICAgICAgICAnMS4g44CQ5Yqf6IO95LyY5YyW44CR5LyY5YyW57O757uf5oCn6IO977yM5o+Q5Y2H55So5oi35L2T6aqM44CCJywNCiAgICAgICAgICAgICcyLiDjgJDpl67popjkv67lpI3jgJHkv67lpI3lt7Lnn6Xpl67popjvvIzmj5Dpq5jns7vnu5/nqLPlrprmgKfjgIInDQogICAgICAgICAgXTsNCiAgICAgICAgfQ0KDQogICAgICAgIHRoaXMuc2VsZWN0ZWRWZXJzaW9uID0gew0KICAgICAgICAgIHZlcnNpb246IHZlcnNpb25EYXRhLnZlcnNpb24sDQogICAgICAgICAgZGF0ZTogdmVyc2lvbkRhdGEuZGF0ZSwNCiAgICAgICAgICBub3Rlczogbm90ZXMNCiAgICAgICAgfTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOaYvuekuua3u+WKoOe0oOadkOWvueivneahhg0KICAgIHNob3dBZGRNYXRlcmlhbERpYWxvZygpIHsNCiAgICAgIHRoaXMubmV3TWF0ZXJpYWxOYW1lID0gJyc7DQogICAgICB0aGlzLmFkZE1hdGVyaWFsRGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgfSwNCiAgICAvLyDmt7vliqDntKDmnZANCiAgICBhZGRNYXRlcmlhbCgpIHsNCiAgICAgIGlmICghdGhpcy5uZXdNYXRlcmlhbE5hbWUudHJpbSgpKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36L6T5YWl57Sg5p2Q5ZCN56ewJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgLy8g5re75Yqg5paw57Sg5p2QDQogICAgICB0aGlzLm1hdGVyaWFsTGlzdC5wdXNoKHsNCiAgICAgICAgaWQ6IERhdGUubm93KCksDQogICAgICAgIG5hbWU6IHRoaXMubmV3TWF0ZXJpYWxOYW1lLA0KICAgICAgICBjcmVhdGVUaW1lOiBuZXcgRGF0ZSgpLnRvTG9jYWxlU3RyaW5nKCkNCiAgICAgIH0pOw0KDQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOe0oOadkCIke3RoaXMubmV3TWF0ZXJpYWxOYW1lfSLliJvlu7rmiJDlip9gKTsNCiAgICAgIHRoaXMuYWRkTWF0ZXJpYWxEaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhuaUtuiXj+mhuemAieaLqQ0KICAgIGhhbmRsZUZhdm9yaXRlU2VsZWN0KCkgew0KICAgICAgdGhpcy5zZWxlY3RlZEZhdm9yaXRlcyA9IHRoaXMuZmF2b3JpdGVMaXN0LmZpbHRlcihpdGVtID0+IGl0ZW0uc2VsZWN0ZWQpOw0KICAgICAgLy8g5qOA5p+l5piv5ZCm5YWo6YCJDQogICAgICB0aGlzLnNlbGVjdEFsbEZhdm9yaXRlcyA9IHRoaXMuc2VsZWN0ZWRGYXZvcml0ZXMubGVuZ3RoID09PSB0aGlzLmZhdm9yaXRlTGlzdC5sZW5ndGg7DQogICAgfSwNCg0KICAgIC8vIOWFqOmAiS/lj5bmtojlhajpgInmlLbol4/pobkNCiAgICB3YXRjaFNlbGVjdEFsbEZhdm9yaXRlcyh2YWwpIHsNCiAgICAgIHRoaXMuZmF2b3JpdGVMaXN0LmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgIGl0ZW0uc2VsZWN0ZWQgPSB2YWw7DQogICAgICB9KTsNCiAgICAgIHRoaXMuc2VsZWN0ZWRGYXZvcml0ZXMgPSB2YWwgPyBbLi4udGhpcy5mYXZvcml0ZUxpc3RdIDogW107DQogICAgfSwNCg0KICAgIC8vIOaJuemHj+WPlua2iOaUtuiXjw0KICAgIGJhdGNoQ2FuY2VsRmF2b3JpdGUoKSB7DQogICAgICBpZiAodGhpcy5zZWxlY3RlZEZhdm9yaXRlcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fpgInmi6nopoHlj5bmtojmlLbol4/nmoTpobknKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrpropoHlj5bmtojpgInkuK3nmoTmlLbol4/lkJfvvJ8nLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICAvLyDlrp7pmYXlupTnlKjkuK3ov5nph4zpnIDopoHosIPnlKjmjqXlj6Plj5bmtojmlLbol48NCiAgICAgICAgY29uc3Qgc2VsZWN0ZWRJZHMgPSB0aGlzLnNlbGVjdGVkRmF2b3JpdGVzLm1hcChpdGVtID0+IGl0ZW0uaWQpOw0KICAgICAgICB0aGlzLmZhdm9yaXRlTGlzdCA9IHRoaXMuZmF2b3JpdGVMaXN0LmZpbHRlcihpdGVtID0+ICFzZWxlY3RlZElkcy5pbmNsdWRlcyhpdGVtLmlkKSk7DQogICAgICAgIHRoaXMuc2VsZWN0ZWRGYXZvcml0ZXMgPSBbXTsNCiAgICAgICAgdGhpcy5zZWxlY3RBbGxGYXZvcml0ZXMgPSBmYWxzZTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflt7Llj5bmtojmlLbol48nKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCflt7Llj5bmtojmk43kvZwnKTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvLyDlj5bmtojljZXkuKrmlLbol48NCiAgICBjYW5jZWxGYXZvcml0ZShpdGVtKSB7DQogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrpropoHlj5bmtojmlLbol48iJyArIGl0ZW0udGl0bGUgKyAnIuWQl++8nycsICfmj5DnpLonLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIC8vIOWunumZheW6lOeUqOS4rei/memHjOmcgOimgeiwg+eUqOaOpeWPo+WPlua2iOaUtuiXjw0KICAgICAgICB0aGlzLmZhdm9yaXRlTGlzdCA9IHRoaXMuZmF2b3JpdGVMaXN0LmZpbHRlcihpID0+IGkuaWQgIT09IGl0ZW0uaWQpOw0KICAgICAgICAvLyDmm7TmlrDpgInkuK3nmoTmlLbol4/pobkNCiAgICAgICAgdGhpcy5zZWxlY3RlZEZhdm9yaXRlcyA9IHRoaXMuc2VsZWN0ZWRGYXZvcml0ZXMuZmlsdGVyKGkgPT4gaS5pZCAhPT0gaXRlbS5pZCk7DQogICAgICAgIC8vIOabtOaWsOaAu+aVsA0KICAgICAgICB0aGlzLmZhdm9yaXRlc1RvdGFsID0gdGhpcy5mYXZvcml0ZUxpc3QubGVuZ3RoOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+W3suWPlua2iOaUtuiXjycpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmluZm8oJ+W3suWPlua2iOaTjeS9nCcpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8vIOaYvuekuua3u+WKoOiBlOezu+S6uuWvueivneahhg0KICAgIHNob3dBZGRDb250YWN0RGlhbG9nKCkgew0KICAgICAgdGhpcy5jb250YWN0Rm9ybSA9IHsNCiAgICAgICAgaWQ6IG51bGwsDQogICAgICAgIG5hbWU6ICcnLA0KICAgICAgICBwaG9uZTogJycsDQogICAgICAgIGVtYWlsOiAnJywNCiAgICAgICAgbG9jYXRpb246ICcnDQogICAgICB9Ow0KICAgICAgdGhpcy5hZGRDb250YWN0RGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgfSwNCg0KICAgIC8vIOaPkOS6pOiBlOezu+S6uuihqOWNlQ0KICAgIHN1Ym1pdENvbnRhY3RGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmcy5jb250YWN0Rm9ybS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLmNvbnRhY3RGb3JtLmlkKSB7DQogICAgICAgICAgICAvLyDnvJbovpHogZTns7vkuroNCiAgICAgICAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy5jb250YWN0TGlzdC5maW5kSW5kZXgoaXRlbSA9PiBpdGVtLmlkID09PSB0aGlzLmNvbnRhY3RGb3JtLmlkKTsNCiAgICAgICAgICAgIGlmIChpbmRleCAhPT0gLTEpIHsNCiAgICAgICAgICAgICAgLy8g5pu05paw5Yib5bu65pe26Ze0DQogICAgICAgICAgICAgIHRoaXMuY29udGFjdEZvcm0uY3JlYXRlVGltZSA9IG5ldyBEYXRlKCkudG9Mb2NhbGVTdHJpbmcoJ3poLUNOJywgew0KICAgICAgICAgICAgICAgIHllYXI6ICdudW1lcmljJywNCiAgICAgICAgICAgICAgICBtb250aDogJzItZGlnaXQnLA0KICAgICAgICAgICAgICAgIGRheTogJzItZGlnaXQnLA0KICAgICAgICAgICAgICAgIGhvdXI6ICcyLWRpZ2l0JywNCiAgICAgICAgICAgICAgICBtaW51dGU6ICcyLWRpZ2l0JywNCiAgICAgICAgICAgICAgICBzZWNvbmQ6ICcyLWRpZ2l0JywNCiAgICAgICAgICAgICAgICBob3VyMTI6IGZhbHNlDQogICAgICAgICAgICAgIH0pLnJlcGxhY2UoL1wvL2csICctJyk7DQogICAgICAgICAgICAgIHRoaXMuY29udGFjdExpc3Quc3BsaWNlKGluZGV4LCAxLCB0aGlzLmNvbnRhY3RGb3JtKTsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfogZTns7vkurrkv67mlLnmiJDlip8nKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgLy8g5re75Yqg6IGU57O75Lq6DQogICAgICAgICAgICBjb25zdCBuZXdDb250YWN0ID0gew0KICAgICAgICAgICAgICAuLi50aGlzLmNvbnRhY3RGb3JtLA0KICAgICAgICAgICAgICBpZDogdGhpcy5jb250YWN0TGlzdC5sZW5ndGggPiAwID8gTWF0aC5tYXgoLi4udGhpcy5jb250YWN0TGlzdC5tYXAoaXRlbSA9PiBpdGVtLmlkKSkgKyAxIDogMSwNCiAgICAgICAgICAgICAgY3JlYXRlVGltZTogbmV3IERhdGUoKS50b0xvY2FsZVN0cmluZygnemgtQ04nLCB7DQogICAgICAgICAgICAgICAgeWVhcjogJ251bWVyaWMnLA0KICAgICAgICAgICAgICAgIG1vbnRoOiAnMi1kaWdpdCcsDQogICAgICAgICAgICAgICAgZGF5OiAnMi1kaWdpdCcsDQogICAgICAgICAgICAgICAgaG91cjogJzItZGlnaXQnLA0KICAgICAgICAgICAgICAgIG1pbnV0ZTogJzItZGlnaXQnLA0KICAgICAgICAgICAgICAgIHNlY29uZDogJzItZGlnaXQnLA0KICAgICAgICAgICAgICAgIGhvdXIxMjogZmFsc2UNCiAgICAgICAgICAgICAgfSkucmVwbGFjZSgvXC8vZywgJy0nKQ0KICAgICAgICAgICAgfTsNCiAgICAgICAgICAgIHRoaXMuY29udGFjdExpc3QucHVzaChuZXdDb250YWN0KTsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn6IGU57O75Lq65re75Yqg5oiQ5YqfJyk7DQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMuYWRkQ29udGFjdERpYWxvZ1Zpc2libGUgPSBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8vIOe8lui+keiBlOezu+S6ug0KICAgIGhhbmRsZUVkaXRDb250YWN0KHJvdykgew0KICAgICAgdGhpcy5jb250YWN0Rm9ybSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkocm93KSk7DQogICAgICB0aGlzLmFkZENvbnRhY3REaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICB9LA0KDQogICAgLy8g5Yig6Zmk6IGU57O75Lq6DQogICAgaGFuZGxlRGVsZXRlQ29udGFjdChyb3cpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oYOehruWumuimgeWIoOmZpOiBlOezu+S6uiIke3Jvdy5uYW1lfSLlkJfvvJ9gLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICAvLyDlrp7pmYXlupTnlKjkuK3ov5nph4zpnIDopoHosIPnlKjmjqXlj6PliKDpmaTogZTns7vkuroNCiAgICAgICAgdGhpcy5jb250YWN0TGlzdCA9IHRoaXMuY29udGFjdExpc3QuZmlsdGVyKGl0ZW0gPT4gaXRlbS5pZCAhPT0gcm93LmlkKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfogZTns7vkurrliKDpmaTmiJDlip8nKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCflt7Llj5bmtojliKDpmaQnKTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvLyDlr7zlh7rmlLbol48NCiAgICBleHBvcnRGYXZvcml0ZXMoKSB7DQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+W8gOWni+WvvOWHuuaUtuiXjycpOw0KICAgICAgLy8g5a6e6ZmF5bqU55So5Lit6L+Z6YeM6ZyA6KaB6LCD55So5o6l5Y+j5a+85Ye65pS26JePDQogICAgfSwNCg0KICAgIC8vIOWkhOeQhuaUtuiXj+WIhumhteWPmOWMlg0KICAgIGhhbmRsZUZhdm9yaXRlUGFnZUNoYW5nZSh2YWwpIHsNCiAgICAgIHRoaXMuZmF2b3JpdGVzQ3VycmVudFBhZ2UgPSB2YWw7DQogICAgICAvLyDlrp7pmYXlupTnlKjkuK3ov5nph4zpnIDopoHosIPnlKjmjqXlj6Pojrflj5blr7nlupTpobXnmoTmlbDmja4NCiAgICB9LA0KDQogICAgLy8g5aSE55CG5pS26JeP6Lez6L2s6aG16Z2iDQogICAgaGFuZGxlRmF2b3JpdGVKdW1wUGFnZSgpIHsNCiAgICAgIGlmICghdGhpcy5mYXZvcml0ZXNKdW1wUGFnZSkgew0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIGNvbnN0IHBhZ2UgPSBwYXJzZUludCh0aGlzLmZhdm9yaXRlc0p1bXBQYWdlKTsNCiAgICAgIGlmIChpc05hTihwYWdlKSB8fCBwYWdlIDwgMSB8fCBwYWdlID4gTWF0aC5jZWlsKHRoaXMuZmF2b3JpdGVzVG90YWwgLyB0aGlzLmZhdm9yaXRlc1BhZ2VTaXplKSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+i+k+WFpeacieaViOeahOmhteeggScpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIHRoaXMuZmF2b3JpdGVzQ3VycmVudFBhZ2UgPSBwYWdlOw0KICAgICAgLy8g5a6e6ZmF5bqU55So5Lit6L+Z6YeM6ZyA6KaB6LCD55So5o6l5Y+j6I635Y+W5a+55bqU6aG155qE5pWw5o2uDQogICAgfQ0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAimBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/account", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"page-layout\">\r\n      <!-- 左侧导航栏 -->\r\n      <div class=\"left-sidebar\">\r\n        <div class=\"user-info\">\r\n          <div class=\"avatar\">\r\n            <img src=\"@/assets/images/profile.jpg\" alt=\"用户头像\">\r\n          </div>\r\n          <div class=\"user-id\">***********</div>\r\n          <div class=\"register-date\">2023-04-28注册</div>\r\n        </div>\r\n\r\n        <div class=\"sidebar-menu\">\r\n          <el-menu\r\n            :default-active=\"activeMenuItem\"\r\n            class=\"sidebar-menu-list\"\r\n            @select=\"handleMenuSelect\"\r\n          >\r\n            <el-menu-item index=\"account\">\r\n              <i class=\"el-icon-user\"></i>\r\n              <span>我的账号</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"favorite\">\r\n              <i class=\"el-icon-star-on\"></i>\r\n              <span>我的收藏</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"download\">\r\n              <i class=\"el-icon-download\"></i>\r\n              <span>我的下载</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"contact\">\r\n              <i class=\"el-icon-notebook-1\"></i>\r\n              <span>我的联系人</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"material\">\r\n              <i class=\"el-icon-chat-line-round\"></i>\r\n              <span>素材库</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"privacy\">\r\n              <i class=\"el-icon-setting\"></i>\r\n              <span>系统设置</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"user-management\">\r\n              <i class=\"el-icon-user\"></i>\r\n              <span>用户管理</span>\r\n            </el-menu-item>\r\n          </el-menu>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧内容区 -->\r\n      <div class=\"content\">\r\n        <!-- 账户信息内容 -->\r\n        <div v-if=\"activeMenuItem === 'account'\" class=\"account-container\">\r\n          <div class=\"account-header\">\r\n            <div class=\"title\">我的账号</div>\r\n            <div class=\"actions\">\r\n              <el-button size=\"small\" @click=\"showAccountInfo\">账号安全</el-button>\r\n              <el-button type=\"primary\" size=\"small\" @click=\"showPasswordForm\">修改密码</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 账户信息内容 -->\r\n          <div v-if=\"!showPasswordChange\" class=\"account-info\">\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">用户账号：</div>\r\n              <div class=\"value\">***********</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">手机号码：</div>\r\n              <div class=\"value\">***********</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">创建时间：</div>\r\n              <div class=\"value\">2023-04-28</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">专业认证分类：</div>\r\n              <div class=\"value\">互联网+</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">方案：</div>\r\n              <div class=\"value\">【免费】+【限时】</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">总积分值：</div>\r\n              <div class=\"value\">2000</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">积分等级：</div>\r\n              <div class=\"value\">初级VIP</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">剩余可用积分：</div>\r\n              <div class=\"value\">【250积分 = 600次】</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">预计可用天数：</div>\r\n              <div class=\"value\">365</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">预计到期日：</div>\r\n              <div class=\"value\">365</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">剩余次数：</div>\r\n              <div class=\"value\">10000次</div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 修改密码表单 -->\r\n          <div v-if=\"showPasswordChange\" class=\"password-form\">\r\n            <div class=\"form-group\">\r\n              <div class=\"form-label\">旧密码：</div>\r\n              <el-input\r\n                v-model=\"passwordForm.oldPassword\"\r\n                type=\"password\"\r\n                placeholder=\"请输入旧密码\"\r\n                show-password\r\n              ></el-input>\r\n            </div>\r\n\r\n            <div class=\"form-group\">\r\n              <div class=\"form-label\">新密码：</div>\r\n              <el-input\r\n                v-model=\"passwordForm.newPassword\"\r\n                type=\"password\"\r\n                placeholder=\"8-16 密码必须同时包含数字、大小写字母和符号\"\r\n                show-password\r\n              ></el-input>\r\n            </div>\r\n\r\n            <div class=\"form-group\">\r\n              <div class=\"form-label\">确认新密码：</div>\r\n              <el-input\r\n                v-model=\"passwordForm.confirmPassword\"\r\n                type=\"password\"\r\n                placeholder=\"请再次输入新密码\"\r\n                show-password\r\n              ></el-input>\r\n            </div>\r\n\r\n            <div class=\"form-actions\">\r\n              <el-button type=\"primary\" @click=\"changePassword\">确认修改</el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 我的下载内容 -->\r\n        <div v-if=\"activeMenuItem === 'download'\" class=\"download-container\">\r\n          <div class=\"download-header\">\r\n            <div class=\"title\">我的下载</div>\r\n            <div class=\"actions\">\r\n              <el-button type=\"primary\" size=\"small\" @click=\"batchDownload\">批量下载</el-button>\r\n              <el-button type=\"danger\" size=\"small\" @click=\"batchDelete\">批量删除</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"download-content\">\r\n            <el-table\r\n              :data=\"downloadList\"\r\n              style=\"width: 100%\"\r\n              @selection-change=\"handleSelectionChange\">\r\n              <el-table-column\r\n                type=\"selection\"\r\n                width=\"55\">\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"name\"\r\n                label=\"名称\"\r\n                width=\"300\">\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"dataSize\"\r\n                label=\"数据量\"\r\n                width=\"100\">\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"createTime\"\r\n                label=\"生成时间\"\r\n                width=\"180\">\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"status\"\r\n                label=\"下载状态\"\r\n                width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <span class=\"download-status\">{{ scope.row.status }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                label=\"操作\"\r\n                width=\"120\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    icon=\"el-icon-download\"\r\n                    @click=\"handleDownload(scope.row)\">\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    icon=\"el-icon-delete\"\r\n                    @click=\"handleDelete(scope.row)\">\r\n                  </el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <div class=\"pagination-container\">\r\n              <span>共 {{ total }} 条记录</span>\r\n              <el-pagination\r\n                background\r\n                layout=\"prev, pager, next, jumper\"\r\n                :total=\"total\"\r\n                :current-page.sync=\"currentPage\"\r\n                :page-size=\"pageSize\"\r\n                @current-change=\"handleCurrentChange\">\r\n              </el-pagination>\r\n              <span>前往第</span>\r\n              <el-input\r\n                v-model=\"jumpPage\"\r\n                size=\"mini\"\r\n                class=\"jump-page-input\">\r\n              </el-input>\r\n              <span>页</span>\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"primary\"\r\n                @click=\"handleJumpPage\">\r\n                确定\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 我的收藏内容 -->\r\n        <div v-if=\"activeMenuItem === 'favorite'\" class=\"favorite-container\">\r\n          <div class=\"favorite-header\">\r\n            <div class=\"title\">我的收藏</div>\r\n          </div>\r\n\r\n          <div class=\"favorite-toolbar\">\r\n            <div class=\"toolbar-left\">\r\n              <el-checkbox v-model=\"selectAllFavorites\">全选</el-checkbox>\r\n              <el-button size=\"small\" type=\"danger\" :disabled=\"selectedFavorites.length === 0\" @click=\"batchCancelFavorite\">批量取消收藏</el-button>\r\n              <el-button size=\"small\" icon=\"el-icon-refresh\">刷新</el-button>\r\n            </div>\r\n\r\n            <div class=\"toolbar-right\">\r\n              <div class=\"date-filter\">\r\n                <el-date-picker\r\n                  v-model=\"favoriteStartDate\"\r\n                  type=\"date\"\r\n                  placeholder=\"开始日期\"\r\n                  size=\"small\"\r\n                  format=\"yyyy-MM-dd\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                  style=\"width: 130px;\"\r\n                ></el-date-picker>\r\n                <span class=\"date-separator\">-</span>\r\n                <el-date-picker\r\n                  v-model=\"favoriteEndDate\"\r\n                  type=\"date\"\r\n                  placeholder=\"结束日期\"\r\n                  size=\"small\"\r\n                  format=\"yyyy-MM-dd\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                  style=\"width: 130px;\"\r\n                ></el-date-picker>\r\n              </div>\r\n\r\n              <div class=\"search-box\">\r\n                <el-input\r\n                  v-model=\"favoriteSearchKeyword\"\r\n                  placeholder=\"搜索内容\"\r\n                  size=\"small\"\r\n                  prefix-icon=\"el-icon-search\"\r\n                  clearable\r\n                  style=\"width: 200px;\"\r\n                ></el-input>\r\n              </div>\r\n\r\n              <el-button type=\"primary\" size=\"small\" icon=\"el-icon-download\" @click=\"exportFavorites\">全部下载</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"favorite-content\">\r\n            <div class=\"favorite-list\">\r\n              <div v-for=\"item in favoriteList\" :key=\"item.id\" class=\"favorite-item\">\r\n                <div class=\"item-checkbox\">\r\n                  <el-checkbox v-model=\"item.selected\" @change=\"handleFavoriteSelect\"></el-checkbox>\r\n                </div>\r\n                <div class=\"item-content\">\r\n                  <div class=\"item-title\">\r\n                    <span class=\"title-tag\">{{ item.tag }}</span>\r\n                    <span class=\"title-text\">{{ item.title }}</span>\r\n                  </div>\r\n                  <div class=\"item-info\">\r\n                    <span class=\"info-time\">收藏时间: {{ item.time }}</span>\r\n                    <span class=\"info-source\">来源: {{ item.source }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"item-actions\">\r\n                  <el-button\r\n                    type=\"text\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-delete\"\r\n                    class=\"cancel-favorite-btn\"\r\n                    @click=\"cancelFavorite(item)\">\r\n                    取消收藏\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"favorite-pagination\">\r\n              <div class=\"pagination-info\">\r\n                共 <span>{{ favoritesTotal }}</span> 条记录\r\n              </div>\r\n              <div class=\"pagination-controls\">\r\n                <el-pagination\r\n                  background\r\n                  layout=\"prev, pager, next\"\r\n                  :total=\"favoritesTotal\"\r\n                  :current-page.sync=\"favoritesCurrentPage\"\r\n                  :page-size=\"favoritesPageSize\"\r\n                  @current-change=\"handleFavoritePageChange\">\r\n                </el-pagination>\r\n              </div>\r\n              <div class=\"pagination-jump\">\r\n                <span>前往</span>\r\n                <el-input\r\n                  v-model=\"favoritesJumpPage\"\r\n                  size=\"mini\"\r\n                  class=\"jump-page-input\">\r\n                </el-input>\r\n                <span>页</span>\r\n                <el-button\r\n                  size=\"mini\"\r\n                  type=\"primary\"\r\n                  @click=\"handleFavoriteJumpPage\">\r\n                  确定\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 素材库内容 -->\r\n        <div v-if=\"activeMenuItem === 'material'\" class=\"material-container\">\r\n          <div class=\"material-header\">\r\n            <div class=\"title\">素材库</div>\r\n          </div>\r\n\r\n          <div class=\"material-content\">\r\n            <div class=\"material-add-box\" @click=\"showAddMaterialDialog\">\r\n              <div class=\"add-icon\">\r\n                <i class=\"el-icon-plus\"></i>\r\n              </div>\r\n              <div class=\"add-text\">新建素材包</div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 新建素材对话框 -->\r\n          <el-dialog\r\n            title=\"新建素材包\"\r\n            :visible.sync=\"addMaterialDialogVisible\"\r\n            width=\"400px\"\r\n            center\r\n            :show-close=\"false\"\r\n            custom-class=\"material-dialog\"\r\n          >\r\n            <div class=\"material-form\">\r\n              <div class=\"form-item\">\r\n                <div class=\"form-label\">素材包名称：</div>\r\n                <el-input v-model=\"newMaterialName\" placeholder=\"请输入素材包名称\"></el-input>\r\n              </div>\r\n            </div>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n              <el-button type=\"primary\" @click=\"addMaterial\">确定</el-button>\r\n              <el-button @click=\"addMaterialDialogVisible = false\">取消</el-button>\r\n            </div>\r\n          </el-dialog>\r\n        </div>\r\n\r\n        <!-- 我的联系人内容 -->\r\n        <div v-if=\"activeMenuItem === 'contact'\" class=\"contact-container\">\r\n          <div class=\"contact-header\">\r\n            <div class=\"title\">我的联系人</div>\r\n            <div class=\"contact-tabs\">\r\n              <el-radio-group v-model=\"contactActiveTab\" size=\"small\">\r\n                <el-radio-button label=\"wechat\">微信</el-radio-button>\r\n                <el-radio-button label=\"sms\">短信</el-radio-button>\r\n                <el-radio-button label=\"email\">邮箱</el-radio-button>\r\n                <el-radio-button label=\"qwechat\">企微群</el-radio-button>\r\n                <el-radio-button label=\"dingtalk\">钉钉群</el-radio-button>\r\n                <el-radio-button label=\"feishu\">飞书群</el-radio-button>\r\n              </el-radio-group>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"contact-toolbar\">\r\n            <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"showAddContactDialog\">添加/导入联系人</el-button>\r\n          </div>\r\n\r\n          <div class=\"contact-content\">\r\n            <el-table\r\n              :data=\"contactList\"\r\n              style=\"width: 100%\"\r\n              border\r\n              stripe\r\n              :header-cell-style=\"{background:'#f5f7fa', color:'#606266'}\"\r\n            >\r\n              <el-table-column\r\n                label=\"头像\"\r\n                width=\"80\"\r\n                align=\"center\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <el-avatar :size=\"40\" icon=\"el-icon-user\"></el-avatar>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"name\"\r\n                label=\"姓名\"\r\n                width=\"120\"\r\n                align=\"center\"\r\n              />\r\n              <el-table-column\r\n                prop=\"createTime\"\r\n                label=\"创建/更新\"\r\n                width=\"180\"\r\n                align=\"center\"\r\n              />\r\n              <el-table-column\r\n                prop=\"location\"\r\n                label=\"位置/时间\"\r\n                width=\"180\"\r\n                align=\"center\"\r\n              />\r\n              <el-table-column\r\n                label=\"操作\"\r\n                align=\"center\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    icon=\"el-icon-edit\"\r\n                    @click=\"handleEditContact(scope.row)\"\r\n                  >编辑</el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    icon=\"el-icon-delete\"\r\n                    class=\"delete-btn\"\r\n                    @click=\"handleDeleteContact(scope.row)\"\r\n                  >删除</el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n\r\n          <!-- 添加联系人对话框 -->\r\n          <el-dialog title=\"添加联系人\" :visible.sync=\"addContactDialogVisible\" width=\"500px\" center>\r\n            <el-form ref=\"contactForm\" :model=\"contactForm\" :rules=\"contactFormRules\" label-width=\"80px\">\r\n              <el-form-item label=\"姓名\" prop=\"name\">\r\n                <el-input v-model=\"contactForm.name\" placeholder=\"请输入姓名\" />\r\n              </el-form-item>\r\n              <el-form-item label=\"手机号码\" prop=\"phone\">\r\n                <el-input v-model=\"contactForm.phone\" placeholder=\"请输入手机号码\" />\r\n              </el-form-item>\r\n              <el-form-item label=\"邮箱\" prop=\"email\">\r\n                <el-input v-model=\"contactForm.email\" placeholder=\"请输入邮箱\" />\r\n              </el-form-item>\r\n              <el-form-item label=\"位置\" prop=\"location\">\r\n                <el-input v-model=\"contactForm.location\" placeholder=\"请输入位置\" />\r\n              </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n              <el-button @click=\"addContactDialogVisible = false\">取消</el-button>\r\n              <el-button type=\"primary\" @click=\"submitContactForm\">确定</el-button>\r\n            </div>\r\n          </el-dialog>\r\n        </div>\r\n\r\n        <!-- 用户管理内容 -->\r\n        <div v-if=\"activeMenuItem === 'user-management'\">\r\n          <user-management />\r\n        </div>\r\n\r\n        <!-- 系统设置内容 -->\r\n        <div v-if=\"activeMenuItem === 'privacy'\" class=\"settings-container\">\r\n          <div class=\"settings-header\">\r\n            <div class=\"title\">系统设置</div>\r\n          </div>\r\n\r\n          <div class=\"settings-content\">\r\n            <!-- 左侧设置菜单 -->\r\n            <div class=\"settings-sidebar\">\r\n              <div class=\"settings-menu\">\r\n                <div :class=\"['menu-item', currentSettingsTab === 'basic' ? 'active' : '']\" @click=\"switchSettingsTab('basic')\">\r\n                  <i class=\"el-icon-setting\"></i>\r\n                  <span>基础设置</span>\r\n                </div>\r\n                <div :class=\"['menu-item', currentSettingsTab === 'updates' ? 'active' : '']\" @click=\"switchSettingsTab('updates')\">\r\n                  <i class=\"el-icon-document\"></i>\r\n                  <span>更新说明</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 右侧设置内容 -->\r\n            <div class=\"settings-main\">\r\n              <!-- 基础设置内容 -->\r\n              <div v-if=\"currentSettingsTab === 'basic'\" class=\"settings-section\">\r\n                <div class=\"section-title\">文章管理选项：</div>\r\n                <div class=\"options-group\">\r\n                  <el-checkbox v-model=\"settings.auto\">自动</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.manual\">手动</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.downloadImages\">下载图片</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.backup\">备份</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.realTimeSync\">实时同步</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.humanReview\">人工审核</el-checkbox>\r\n                </div>\r\n\r\n                <div class=\"options-group\">\r\n                  <el-checkbox v-model=\"settings.autoPublish\">自动发布</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.keywords\">关键词</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.highQuality\">高质量</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.multiPlatform\">多平台发布</el-checkbox>\r\n                </div>\r\n\r\n                <div class=\"options-group\">\r\n                  <el-checkbox v-model=\"settings.autoSave\">自动保存</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.batchProcess\">批量处理</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.imageProcess\">图片处理</el-checkbox>\r\n                </div>\r\n\r\n                <div class=\"section-title\">下载设置：</div>\r\n                <div class=\"download-settings\">\r\n                  <div class=\"setting-item\">\r\n                    <span class=\"label\">下载路径：</span>\r\n                    <span class=\"value\">自动生成文件夹</span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"section-title\">高级自动化设置：</div>\r\n                <div class=\"options-group\">\r\n                  <el-checkbox v-model=\"settings.autoOn\">开启</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.autoOff\">关闭</el-checkbox>\r\n                </div>\r\n\r\n                <div class=\"save-button\">\r\n                  <el-button type=\"primary\" @click=\"saveSettings\">保存</el-button>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 更新说明内容 -->\r\n              <div v-if=\"currentSettingsTab === 'updates'\" class=\"updates-section\">\r\n                <div class=\"updates-header\">\r\n                  <div class=\"version-title\">{{ selectedVersion.version }} 版本更新说明</div>\r\n                  <div class=\"version-date\">{{ selectedVersion.date }}</div>\r\n                </div>\r\n\r\n                <div class=\"updates-content\">\r\n                  <div class=\"version-list\">\r\n                    <div\r\n                      v-for=\"version in versionList\"\r\n                      :key=\"version.version\"\r\n                      :class=\"['version-item', selectedVersion.version === version.version ? 'active' : '']\"\r\n                      @click=\"selectVersion(version.version)\"\r\n                    >\r\n                      {{ version.version }}\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div class=\"update-details\">\r\n                    <div class=\"update-notes\">\r\n                      <div v-for=\"(note, index) in selectedVersion.notes\" :key=\"index\" class=\"note-item\">\r\n                        <div class=\"note-number\">{{ index + 1 }}. </div>\r\n                        <div class=\"note-content\" v-html=\"note\"></div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UserManagement from './user-management.vue';\r\n\r\nexport default {\r\n  name: \"AccountManagement\",\r\n  components: {\r\n    UserManagement\r\n  },\r\n  watch: {\r\n    selectAllFavorites(val) {\r\n      this.watchSelectAllFavorites(val);\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      activeMenuItem: 'account', // 默认选中我的账号\r\n      currentSettingsTab: 'basic', // 默认选中基础设置\r\n      // 用户信息\r\n      userInfo: {\r\n        userId: '***********',\r\n        phoneNumber: '***********',\r\n        registerDate: '2023-04-28',\r\n        category: '互联网+',\r\n        plan: '【免费】+【限时】',\r\n        totalPoints: '2000',\r\n        pointsLevel: '初级VIP',\r\n        availablePoints: '【250积分 = 600次】',\r\n        estimatedDays: '365',\r\n        expirationDate: '365',\r\n        remainingCount: '10000次'\r\n      },\r\n      // 系统设置\r\n      settings: {\r\n        auto: true,\r\n        manual: true,\r\n        downloadImages: true,\r\n        backup: true,\r\n        realTimeSync: false,\r\n        humanReview: true,\r\n        autoPublish: false,\r\n        keywords: true,\r\n        highQuality: false,\r\n        multiPlatform: true,\r\n        autoSave: true,\r\n        batchProcess: true,\r\n        imageProcess: true,\r\n        autoOn: true,\r\n        autoOff: false\r\n      },\r\n      // 下载列表\r\n      downloadList: [\r\n        {\r\n          id: 1,\r\n          name: '方案_20240428163743_1',\r\n          dataSize: '1323',\r\n          createTime: '2024-04-28 16:37:57',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '方案_20240428163743_1',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-28 16:37:57',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '方案_20240427173742_4',\r\n          dataSize: '1893',\r\n          createTime: '2024-04-27 17:37:42',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 4,\r\n          name: '方案_20240427173742_3',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-27 17:37:42',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 5,\r\n          name: '方案_20240427173742_2',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-27 17:37:42',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 6,\r\n          name: '方案_20240427173742_2',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-27 17:37:42',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 7,\r\n          name: '方案_20240427173742_1',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-27 17:37:42',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 8,\r\n          name: '台账_20240427173129_5',\r\n          dataSize: '1281',\r\n          createTime: '2024-04-27 17:31:29',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 9,\r\n          name: '台账_20240427173129_4',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-27 17:31:29',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 10,\r\n          name: '台账_20240427173129_3',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-27 17:31:29',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 11,\r\n          name: '台账_20240427173129_2',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-27 17:31:29',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 12,\r\n          name: '台账_20240427173129_1',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-27 17:31:29',\r\n          status: '已完成'\r\n        }\r\n      ],\r\n      // 分页相关\r\n      total: 12,\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      jumpPage: '',\r\n      // 选中的下载项\r\n      selectedDownloads: [],\r\n      // 版本列表\r\n      versionList: [\r\n        { version: '6.2.9', date: '2024.01.19' },\r\n        { version: '6.2.8', date: '2023.12.15' },\r\n        { version: '6.2.7', date: '2023.11.20' },\r\n        { version: '6.2.6', date: '2023.10.18' },\r\n        { version: '6.2.5', date: '2023.09.25' },\r\n        { version: '6.2.4', date: '2023.08.30' },\r\n        { version: '6.2.3', date: '2023.07.28' },\r\n        { version: '6.2.2', date: '2023.06.22' },\r\n        { version: '6.2.1', date: '2023.05.15' },\r\n        { version: '6.2.0', date: '2023.04.10' }\r\n      ],\r\n      // 当前选中的版本\r\n      selectedVersion: {\r\n        version: '6.2.9',\r\n        date: '2024.01.19',\r\n        notes: [\r\n          '1. 【新增功能】新增<b>个人中心</b>功能，用户可以查看和管理个人信息。',\r\n          '2. 【功能优化】优化了<b>搜索引擎</b>的性能，提高了搜索速度和准确性。',\r\n          '3. 【界面调整】调整了<b>首页布局</b>，使界面更加简洁美观。',\r\n          '4. 【问题修复】修复了在某些情况下<b>\"数据导出\"</b>功能失效的问题。',\r\n          '5. 【安全增强】增强了系统的<b>安全性</b>，提高了数据保护能力。'\r\n        ]\r\n      },\r\n      // 素材库相关\r\n      addMaterialDialogVisible: false,\r\n      newMaterialName: '',\r\n      materialList: [],\r\n      // 修改密码相关\r\n      showPasswordChange: false,\r\n      passwordForm: {\r\n        oldPassword: '',\r\n        newPassword: '',\r\n        confirmPassword: ''\r\n      },\r\n      // 我的收藏相关\r\n      selectAllFavorites: false,\r\n      selectedFavorites: [],\r\n      favoriteStartDate: '',\r\n      favoriteEndDate: '',\r\n      favoriteSearchKeyword: '',\r\n      favoriteList: [\r\n        {\r\n          id: 1,\r\n          selected: false,\r\n          tag: 'HOT',\r\n          title: '新产品市场分析报告',\r\n          time: '2023-04-29 20:37:51',\r\n          source: '市场调查部门 A 数据库'\r\n        },\r\n        {\r\n          id: 2,\r\n          selected: false,\r\n          tag: 'NEW',\r\n          title: '2024年第一季度行业趋势分析',\r\n          time: '2024-04-28 15:22:36',\r\n          source: '行业研究中心'\r\n        },\r\n        {\r\n          id: 3,\r\n          selected: false,\r\n          tag: 'HOT',\r\n          title: '竞品分析与市场定位策略',\r\n          time: '2024-04-27 09:15:42',\r\n          source: '战略规划部'\r\n        },\r\n        {\r\n          id: 4,\r\n          selected: false,\r\n          tag: 'TOP',\r\n          title: '用户行为数据分析报告',\r\n          time: '2024-04-26 14:30:18',\r\n          source: '数据分析部门'\r\n        },\r\n        {\r\n          id: 5,\r\n          selected: false,\r\n          tag: 'NEW',\r\n          title: '新媒体营销策略白皮书',\r\n          time: '2024-04-25 11:45:23',\r\n          source: '营销部门'\r\n        },\r\n        {\r\n          id: 6,\r\n          selected: false,\r\n          tag: 'HOT',\r\n          title: '产品迭代计划与路线图',\r\n          time: '2024-04-24 16:20:37',\r\n          source: '产品部门'\r\n        },\r\n        {\r\n          id: 7,\r\n          selected: false,\r\n          tag: 'TOP',\r\n          title: '行业政策解读与影响分析',\r\n          time: '2024-04-23 10:05:12',\r\n          source: '政策研究中心'\r\n        }\r\n      ],\r\n      favoritesTotal: 7,\r\n      favoritesCurrentPage: 1,\r\n      favoritesPageSize: 10,\r\n      favoritesJumpPage: '',\r\n\r\n      // 我的联系人相关\r\n      contactActiveTab: 'wechat', // 默认选中微信标签\r\n      contactList: [\r\n        {\r\n          id: 1,\r\n          name: '张三',\r\n          phone: '13800138001',\r\n          email: '<EMAIL>',\r\n          location: '北京市朝阳区',\r\n          createTime: '2024-04-28 10:30:45'\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '李四',\r\n          phone: '13800138002',\r\n          email: '<EMAIL>',\r\n          location: '上海市浦东新区',\r\n          createTime: '2024-04-27 15:20:36'\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '王五',\r\n          phone: '13800138003',\r\n          email: '<EMAIL>',\r\n          location: '广州市天河区',\r\n          createTime: '2024-04-26 09:15:22'\r\n        }\r\n      ],\r\n      addContactDialogVisible: false,\r\n      contactForm: {\r\n        id: null,\r\n        name: '',\r\n        phone: '',\r\n        email: '',\r\n        location: ''\r\n      },\r\n      contactFormRules: {\r\n        name: [\r\n          { required: true, message: '请输入姓名', trigger: 'blur' }\r\n        ],\r\n        phone: [\r\n          { required: true, message: '请输入手机号码', trigger: 'blur' },\r\n          { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }\r\n        ],\r\n        email: [\r\n          { required: true, message: '请输入邮箱', trigger: 'blur' },\r\n          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  methods: {\r\n    // 处理菜单项选择\r\n    handleMenuSelect(index) {\r\n      this.activeMenuItem = index;\r\n    },\r\n    // 显示账户信息\r\n    showAccountInfo() {\r\n      this.showPasswordChange = false;\r\n    },\r\n    // 显示修改密码表单\r\n    showPasswordForm() {\r\n      this.showPasswordChange = true;\r\n      this.passwordForm = {\r\n        oldPassword: '',\r\n        newPassword: '',\r\n        confirmPassword: ''\r\n      };\r\n    },\r\n    // 修改密码\r\n    changePassword() {\r\n      // 表单验证\r\n      if (!this.passwordForm.oldPassword) {\r\n        this.$message.warning('请输入旧密码');\r\n        return;\r\n      }\r\n      if (!this.passwordForm.newPassword) {\r\n        this.$message.warning('请输入新密码');\r\n        return;\r\n      }\r\n      if (!this.passwordForm.confirmPassword) {\r\n        this.$message.warning('请确认新密码');\r\n        return;\r\n      }\r\n      if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {\r\n        this.$message.warning('两次输入的新密码不一致');\r\n        return;\r\n      }\r\n\r\n      // 提交修改密码请求\r\n      this.$message.success('密码修改成功');\r\n      this.showPasswordChange = false;\r\n    },\r\n    // 切换设置选项卡\r\n    switchSettingsTab(tab) {\r\n      this.currentSettingsTab = tab;\r\n    },\r\n    // 保存设置\r\n    saveSettings() {\r\n      this.$message.success('设置已保存');\r\n    },\r\n    // 处理表格选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedDownloads = selection;\r\n    },\r\n    // 批量下载\r\n    batchDownload() {\r\n      if (this.selectedDownloads.length === 0) {\r\n        this.$message.warning('请选择要下载的文件');\r\n        return;\r\n      }\r\n      this.$message.success(`已开始下载${this.selectedDownloads.length}个文件`);\r\n    },\r\n    // 批量删除\r\n    batchDelete() {\r\n      if (this.selectedDownloads.length === 0) {\r\n        this.$message.warning('请选择要删除的文件');\r\n        return;\r\n      }\r\n      this.$confirm('确定要删除选中的文件吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$message.success(`已删除${this.selectedDownloads.length}个文件`);\r\n        // 实际应用中这里需要调用接口删除文件\r\n      }).catch(() => {\r\n        this.$message.info('已取消删除');\r\n      });\r\n    },\r\n    // 下载单个文件\r\n    handleDownload(row) {\r\n      this.$message.success(`开始下载: ${row.name}`);\r\n    },\r\n    // 删除单个文件\r\n    handleDelete(row) {\r\n      this.$confirm('确定要删除该文件吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$message.success(`已删除: ${row.name}`);\r\n        // 实际应用中这里需要调用接口删除文件\r\n      }).catch(() => {\r\n        this.$message.info('已取消删除');\r\n      });\r\n    },\r\n    // 处理页码变化\r\n    handleCurrentChange(val) {\r\n      this.currentPage = val;\r\n      // 实际应用中这里需要调用接口获取对应页的数据\r\n    },\r\n    // 处理跳转页面\r\n    handleJumpPage() {\r\n      if (!this.jumpPage) {\r\n        return;\r\n      }\r\n      const page = parseInt(this.jumpPage);\r\n      if (isNaN(page) || page < 1 || page > Math.ceil(this.total / this.pageSize)) {\r\n        this.$message.warning('请输入有效的页码');\r\n        return;\r\n      }\r\n      this.currentPage = page;\r\n      // 实际应用中这里需要调用接口获取对应页的数据\r\n    },\r\n    // 选择版本\r\n    selectVersion(version) {\r\n      const versionData = this.versionList.find(v => v.version === version);\r\n      if (versionData) {\r\n        // 根据版本号获取对应的更新说明\r\n        let notes = [];\r\n        if (version === '6.2.9') {\r\n          notes = [\r\n            '1. 【新增功能】新增<b>个人中心</b>功能，用户可以查看和管理个人信息。',\r\n            '2. 【功能优化】优化了<b>搜索引擎</b>的性能，提高了搜索速度和准确性。',\r\n            '3. 【界面调整】调整了<b>首页布局</b>，使界面更加简洁美观。',\r\n            '4. 【问题修复】修复了在某些情况下<b>\"数据导出\"</b>功能失效的问题。',\r\n            '5. 【安全增强】增强了系统的<b>安全性</b>，提高了数据保护能力。'\r\n          ];\r\n        } else if (version === '6.2.8') {\r\n          notes = [\r\n            '1. 【新增功能】新增<b>数据分析</b>模块，提供更全面的数据统计。',\r\n            '2. 【功能优化】优化了<b>文件上传</b>功能，支持更多文件格式。',\r\n            '3. 【问题修复】修复了部分用户<b>无法登录</b>的问题。'\r\n          ];\r\n        } else {\r\n          notes = [\r\n            '1. 【功能优化】优化系统性能，提升用户体验。',\r\n            '2. 【问题修复】修复已知问题，提高系统稳定性。'\r\n          ];\r\n        }\r\n\r\n        this.selectedVersion = {\r\n          version: versionData.version,\r\n          date: versionData.date,\r\n          notes: notes\r\n        };\r\n      }\r\n    },\r\n    // 显示添加素材对话框\r\n    showAddMaterialDialog() {\r\n      this.newMaterialName = '';\r\n      this.addMaterialDialogVisible = true;\r\n    },\r\n    // 添加素材\r\n    addMaterial() {\r\n      if (!this.newMaterialName.trim()) {\r\n        this.$message.warning('请输入素材名称');\r\n        return;\r\n      }\r\n\r\n      // 添加新素材\r\n      this.materialList.push({\r\n        id: Date.now(),\r\n        name: this.newMaterialName,\r\n        createTime: new Date().toLocaleString()\r\n      });\r\n\r\n      this.$message.success(`素材\"${this.newMaterialName}\"创建成功`);\r\n      this.addMaterialDialogVisible = false;\r\n    },\r\n\r\n    // 处理收藏项选择\r\n    handleFavoriteSelect() {\r\n      this.selectedFavorites = this.favoriteList.filter(item => item.selected);\r\n      // 检查是否全选\r\n      this.selectAllFavorites = this.selectedFavorites.length === this.favoriteList.length;\r\n    },\r\n\r\n    // 全选/取消全选收藏项\r\n    watchSelectAllFavorites(val) {\r\n      this.favoriteList.forEach(item => {\r\n        item.selected = val;\r\n      });\r\n      this.selectedFavorites = val ? [...this.favoriteList] : [];\r\n    },\r\n\r\n    // 批量取消收藏\r\n    batchCancelFavorite() {\r\n      if (this.selectedFavorites.length === 0) {\r\n        this.$message.warning('请选择要取消收藏的项');\r\n        return;\r\n      }\r\n\r\n      this.$confirm('确定要取消选中的收藏吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 实际应用中这里需要调用接口取消收藏\r\n        const selectedIds = this.selectedFavorites.map(item => item.id);\r\n        this.favoriteList = this.favoriteList.filter(item => !selectedIds.includes(item.id));\r\n        this.selectedFavorites = [];\r\n        this.selectAllFavorites = false;\r\n        this.$message.success('已取消收藏');\r\n      }).catch(() => {\r\n        this.$message.info('已取消操作');\r\n      });\r\n    },\r\n\r\n    // 取消单个收藏\r\n    cancelFavorite(item) {\r\n      this.$confirm('确定要取消收藏\"' + item.title + '\"吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 实际应用中这里需要调用接口取消收藏\r\n        this.favoriteList = this.favoriteList.filter(i => i.id !== item.id);\r\n        // 更新选中的收藏项\r\n        this.selectedFavorites = this.selectedFavorites.filter(i => i.id !== item.id);\r\n        // 更新总数\r\n        this.favoritesTotal = this.favoriteList.length;\r\n        this.$message.success('已取消收藏');\r\n      }).catch(() => {\r\n        this.$message.info('已取消操作');\r\n      });\r\n    },\r\n\r\n    // 显示添加联系人对话框\r\n    showAddContactDialog() {\r\n      this.contactForm = {\r\n        id: null,\r\n        name: '',\r\n        phone: '',\r\n        email: '',\r\n        location: ''\r\n      };\r\n      this.addContactDialogVisible = true;\r\n    },\r\n\r\n    // 提交联系人表单\r\n    submitContactForm() {\r\n      this.$refs.contactForm.validate(valid => {\r\n        if (valid) {\r\n          if (this.contactForm.id) {\r\n            // 编辑联系人\r\n            const index = this.contactList.findIndex(item => item.id === this.contactForm.id);\r\n            if (index !== -1) {\r\n              // 更新创建时间\r\n              this.contactForm.createTime = new Date().toLocaleString('zh-CN', {\r\n                year: 'numeric',\r\n                month: '2-digit',\r\n                day: '2-digit',\r\n                hour: '2-digit',\r\n                minute: '2-digit',\r\n                second: '2-digit',\r\n                hour12: false\r\n              }).replace(/\\//g, '-');\r\n              this.contactList.splice(index, 1, this.contactForm);\r\n              this.$message.success('联系人修改成功');\r\n            }\r\n          } else {\r\n            // 添加联系人\r\n            const newContact = {\r\n              ...this.contactForm,\r\n              id: this.contactList.length > 0 ? Math.max(...this.contactList.map(item => item.id)) + 1 : 1,\r\n              createTime: new Date().toLocaleString('zh-CN', {\r\n                year: 'numeric',\r\n                month: '2-digit',\r\n                day: '2-digit',\r\n                hour: '2-digit',\r\n                minute: '2-digit',\r\n                second: '2-digit',\r\n                hour12: false\r\n              }).replace(/\\//g, '-')\r\n            };\r\n            this.contactList.push(newContact);\r\n            this.$message.success('联系人添加成功');\r\n          }\r\n          this.addContactDialogVisible = false;\r\n        }\r\n      });\r\n    },\r\n\r\n    // 编辑联系人\r\n    handleEditContact(row) {\r\n      this.contactForm = JSON.parse(JSON.stringify(row));\r\n      this.addContactDialogVisible = true;\r\n    },\r\n\r\n    // 删除联系人\r\n    handleDeleteContact(row) {\r\n      this.$confirm(`确定要删除联系人\"${row.name}\"吗？`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 实际应用中这里需要调用接口删除联系人\r\n        this.contactList = this.contactList.filter(item => item.id !== row.id);\r\n        this.$message.success('联系人删除成功');\r\n      }).catch(() => {\r\n        this.$message.info('已取消删除');\r\n      });\r\n    },\r\n\r\n    // 导出收藏\r\n    exportFavorites() {\r\n      this.$message.success('开始导出收藏');\r\n      // 实际应用中这里需要调用接口导出收藏\r\n    },\r\n\r\n    // 处理收藏分页变化\r\n    handleFavoritePageChange(val) {\r\n      this.favoritesCurrentPage = val;\r\n      // 实际应用中这里需要调用接口获取对应页的数据\r\n    },\r\n\r\n    // 处理收藏跳转页面\r\n    handleFavoriteJumpPage() {\r\n      if (!this.favoritesJumpPage) {\r\n        return;\r\n      }\r\n\r\n      const page = parseInt(this.favoritesJumpPage);\r\n      if (isNaN(page) || page < 1 || page > Math.ceil(this.favoritesTotal / this.favoritesPageSize)) {\r\n        this.$message.warning('请输入有效的页码');\r\n        return;\r\n      }\r\n\r\n      this.favoritesCurrentPage = page;\r\n      // 实际应用中这里需要调用接口获取对应页的数据\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background-color: #f5f5f5;\r\n  min-height: 100vh;\r\n  padding: 20px;\r\n}\r\n\r\n.page-layout {\r\n  display: flex;\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.left-sidebar {\r\n  width: 200px;\r\n  background-color: #fff;\r\n  border-right: 1px solid #e6e6e6;\r\n}\r\n\r\n.user-info {\r\n  padding: 20px;\r\n  text-align: center;\r\n  border-bottom: 1px solid #e6e6e6;\r\n\r\n  .avatar {\r\n    width: 60px;\r\n    height: 60px;\r\n    margin: 0 auto 10px;\r\n    border-radius: 50%;\r\n    overflow: hidden;\r\n\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n      object-fit: cover;\r\n    }\r\n  }\r\n\r\n  .user-id {\r\n    font-size: 16px;\r\n    font-weight: bold;\r\n    margin-bottom: 5px;\r\n  }\r\n\r\n  .register-date {\r\n    font-size: 12px;\r\n    color: #999;\r\n  }\r\n}\r\n\r\n.sidebar-menu {\r\n  .sidebar-menu-list {\r\n    border-right: none;\r\n  }\r\n\r\n  .el-menu-item {\r\n    height: 50px;\r\n    line-height: 50px;\r\n\r\n    i {\r\n      margin-right: 5px;\r\n      color: #666;\r\n    }\r\n  }\r\n\r\n  .el-menu-item.is-active {\r\n    background-color: #f0f9eb;\r\n    color: #67c23a;\r\n\r\n    i {\r\n      color: #67c23a;\r\n    }\r\n  }\r\n}\r\n\r\n.content {\r\n  flex: 1;\r\n  padding: 20px;\r\n}\r\n\r\n.account-container {\r\n  background-color: #fff;\r\n}\r\n\r\n.account-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-bottom: 20px;\r\n  margin-bottom: 20px;\r\n  border-bottom: 1px solid #eee;\r\n\r\n  .title {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n.account-info {\r\n  .info-item {\r\n    display: flex;\r\n    margin-bottom: 20px;\r\n    line-height: 24px;\r\n\r\n    .label {\r\n      width: 120px;\r\n      color: #666;\r\n      text-align: right;\r\n      padding-right: 10px;\r\n    }\r\n\r\n    .value {\r\n      flex: 1;\r\n      color: #333;\r\n    }\r\n  }\r\n}\r\n\r\n/* 系统设置样式 */\r\n.settings-container {\r\n  background-color: #fff;\r\n}\r\n\r\n.settings-header {\r\n  padding-bottom: 15px;\r\n  margin-bottom: 15px;\r\n\r\n  .title {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n.settings-content {\r\n  display: flex;\r\n}\r\n\r\n.settings-sidebar {\r\n  width: 120px;\r\n  border-right: 1px solid #eee;\r\n  padding-right: 10px;\r\n}\r\n\r\n.settings-menu {\r\n  .menu-item {\r\n    padding: 10px 0;\r\n    cursor: pointer;\r\n    display: flex;\r\n    align-items: center;\r\n    color: #666;\r\n    font-size: 14px;\r\n\r\n    i {\r\n      margin-right: 8px;\r\n      color: #1890ff;\r\n    }\r\n\r\n    &.active {\r\n      color: #1890ff;\r\n      font-weight: bold;\r\n    }\r\n\r\n    &:hover {\r\n      color: #1890ff;\r\n    }\r\n  }\r\n}\r\n\r\n.settings-main {\r\n  flex: 1;\r\n  padding-left: 20px;\r\n}\r\n\r\n.settings-section {\r\n  .section-title {\r\n    font-weight: bold;\r\n    margin: 15px 0 10px;\r\n    color: #333;\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n.options-group {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin-bottom: 15px;\r\n\r\n  .el-checkbox {\r\n    margin-right: 15px;\r\n    margin-bottom: 10px;\r\n    min-width: 80px;\r\n  }\r\n}\r\n\r\n.download-settings {\r\n  margin-bottom: 15px;\r\n\r\n  .setting-item {\r\n    display: flex;\r\n    margin-bottom: 10px;\r\n    font-size: 14px;\r\n\r\n    .label {\r\n      width: 80px;\r\n      color: #666;\r\n    }\r\n\r\n    .value {\r\n      flex: 1;\r\n      color: #333;\r\n    }\r\n  }\r\n}\r\n\r\n.save-button {\r\n  margin-top: 20px;\r\n\r\n  .el-button {\r\n    background-color: #1890ff;\r\n    border-color: #1890ff;\r\n    padding: 8px 20px;\r\n  }\r\n}\r\n\r\n/* 我的下载样式 */\r\n.download-container {\r\n  background-color: #fff;\r\n}\r\n\r\n.download-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-bottom: 15px;\r\n  margin-bottom: 15px;\r\n\r\n  .title {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .actions {\r\n    .el-button {\r\n      margin-left: 10px;\r\n    }\r\n  }\r\n}\r\n\r\n.download-content {\r\n  .el-table {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .download-status {\r\n    color: #67c23a;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-top: 20px;\r\n\r\n  span {\r\n    margin: 0 5px;\r\n  }\r\n\r\n  .jump-page-input {\r\n    width: 50px;\r\n    margin: 0 5px;\r\n  }\r\n}\r\n\r\n/* 修改密码表单样式 */\r\n.password-form {\r\n  max-width: 500px;\r\n  margin: 20px 0;\r\n\r\n  .form-group {\r\n    margin-bottom: 20px;\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .form-label {\r\n      width: 100px;\r\n      text-align: right;\r\n      margin-right: 15px;\r\n      color: #606266;\r\n    }\r\n\r\n    .el-input {\r\n      width: 300px;\r\n    }\r\n  }\r\n\r\n  .form-actions {\r\n    margin-top: 30px;\r\n    padding-left: 115px;\r\n\r\n    .el-button {\r\n      width: 100px;\r\n    }\r\n  }\r\n}\r\n\r\n/* 我的收藏样式 */\r\n.favorite-container {\r\n  background-color: #fff;\r\n}\r\n\r\n.favorite-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-bottom: 15px;\r\n  margin-bottom: 15px;\r\n  border-bottom: 1px solid #eee;\r\n\r\n  .title {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n.favorite-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n\r\n  .toolbar-left {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .el-checkbox {\r\n      margin-right: 15px;\r\n    }\r\n\r\n    .el-button {\r\n      margin-right: 10px;\r\n    }\r\n  }\r\n\r\n  .toolbar-right {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .date-filter {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-right: 15px;\r\n\r\n      .date-separator {\r\n        margin: 0 5px;\r\n      }\r\n    }\r\n\r\n    .search-box {\r\n      margin-right: 15px;\r\n    }\r\n  }\r\n}\r\n\r\n.favorite-content {\r\n  margin-top: 20px;\r\n\r\n  .favorite-list {\r\n    border: 1px solid #ebeef5;\r\n    border-radius: 4px;\r\n\r\n    .favorite-item {\r\n      display: flex;\r\n      padding: 15px;\r\n      border-bottom: 1px solid #ebeef5;\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      .item-checkbox {\r\n        margin-right: 15px;\r\n        display: flex;\r\n        align-items: center;\r\n      }\r\n\r\n      .item-content {\r\n        flex: 1;\r\n\r\n        .item-title {\r\n          margin-bottom: 8px;\r\n\r\n          .title-tag {\r\n            display: inline-block;\r\n            padding: 2px 6px;\r\n            font-size: 12px;\r\n            color: #fff;\r\n            background-color: #f56c6c;\r\n            border-radius: 2px;\r\n            margin-right: 8px;\r\n          }\r\n\r\n          .title-text {\r\n            font-size: 16px;\r\n            font-weight: 500;\r\n            color: #303133;\r\n          }\r\n        }\r\n\r\n        .item-info {\r\n          font-size: 13px;\r\n          color: #909399;\r\n\r\n          .info-time {\r\n            margin-right: 15px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .item-actions {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-left: 15px;\r\n\r\n        .cancel-favorite-btn {\r\n          color: #f56c6c;\r\n\r\n          &:hover {\r\n            color: #ff7c7c;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .favorite-pagination {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-top: 20px;\r\n    padding: 10px 0;\r\n\r\n    .pagination-info {\r\n      font-size: 14px;\r\n      color: #606266;\r\n\r\n      span {\r\n        font-weight: bold;\r\n        color: #303133;\r\n      }\r\n    }\r\n\r\n    .pagination-controls {\r\n      flex: 1;\r\n      text-align: center;\r\n    }\r\n\r\n    .pagination-jump {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      span {\r\n        margin: 0 5px;\r\n        font-size: 14px;\r\n        color: #606266;\r\n      }\r\n\r\n      .jump-page-input {\r\n        width: 50px;\r\n        margin: 0 5px;\r\n      }\r\n\r\n      .el-button {\r\n        margin-left: 5px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 我的联系人样式 */\r\n.contact-container {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.contact-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n\r\n  .title {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .contact-tabs {\r\n    .el-radio-group {\r\n      .el-radio-button {\r\n        margin-right: -1px;\r\n\r\n        &:first-child .el-radio-button__inner {\r\n          border-radius: 4px 0 0 4px;\r\n        }\r\n\r\n        &:last-child .el-radio-button__inner {\r\n          border-radius: 0 4px 4px 0;\r\n        }\r\n\r\n        .el-radio-button__inner {\r\n          padding: 8px 15px;\r\n          font-size: 13px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.contact-toolbar {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.contact-content {\r\n  .el-table {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .delete-btn {\r\n    color: #f56c6c;\r\n  }\r\n}\r\n\r\n/* 素材库样式 */\r\n.material-container {\r\n  background-color: #fff;\r\n}\r\n\r\n.material-header {\r\n  padding-bottom: 15px;\r\n  margin-bottom: 15px;\r\n\r\n  .title {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n.material-content {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n\r\n  .material-add-box {\r\n    width: 200px;\r\n    height: 150px;\r\n    border: 1px dashed #d9d9d9;\r\n    border-radius: 4px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n    cursor: pointer;\r\n    margin-right: 20px;\r\n    margin-bottom: 20px;\r\n\r\n    &:hover {\r\n      border-color: #1890ff;\r\n\r\n      .add-icon, .add-text {\r\n        color: #1890ff;\r\n      }\r\n    }\r\n\r\n    .add-icon {\r\n      font-size: 30px;\r\n      color: #8c8c8c;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .add-text {\r\n      font-size: 14px;\r\n      color: #8c8c8c;\r\n    }\r\n  }\r\n}\r\n\r\n.material-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px;\r\n    text-align: center;\r\n    border-bottom: 1px solid #f0f0f0;\r\n  }\r\n\r\n  .material-form {\r\n    padding: 20px 0;\r\n\r\n    .form-item {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 15px;\r\n\r\n      .form-label {\r\n        width: 80px;\r\n        text-align: right;\r\n        margin-right: 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .dialog-footer {\r\n    text-align: center;\r\n\r\n    .el-button {\r\n      width: 80px;\r\n    }\r\n  }\r\n}\r\n\r\n/* 更新说明样式 */\r\n.updates-section {\r\n  .updates-header {\r\n    margin-bottom: 20px;\r\n\r\n    .version-title {\r\n      font-size: 16px;\r\n      font-weight: bold;\r\n      margin-bottom: 5px;\r\n    }\r\n\r\n    .version-date {\r\n      font-size: 12px;\r\n      color: #999;\r\n    }\r\n  }\r\n\r\n  .updates-content {\r\n    display: flex;\r\n\r\n    .version-list {\r\n      width: 100px;\r\n      border-right: 1px solid #eee;\r\n      padding-right: 15px;\r\n      margin-right: 20px;\r\n\r\n      .version-item {\r\n        padding: 8px 0;\r\n        cursor: pointer;\r\n        color: #666;\r\n        font-size: 14px;\r\n        text-align: center;\r\n        border-radius: 4px;\r\n        margin-bottom: 5px;\r\n\r\n        &:hover {\r\n          background-color: #f5f7fa;\r\n        }\r\n\r\n        &.active {\r\n          background-color: #e6f7ff;\r\n          color: #1890ff;\r\n          font-weight: bold;\r\n        }\r\n      }\r\n    }\r\n\r\n    .update-details {\r\n      flex: 1;\r\n\r\n      .update-notes {\r\n        .note-item {\r\n          display: flex;\r\n          margin-bottom: 10px;\r\n          line-height: 1.6;\r\n\r\n          .note-number {\r\n            margin-right: 5px;\r\n            color: #666;\r\n          }\r\n\r\n          .note-content {\r\n            flex: 1;\r\n\r\n            b {\r\n              color: #1890ff;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}