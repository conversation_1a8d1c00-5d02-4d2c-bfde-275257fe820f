{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\utils\\generator\\drawingDefault.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\utils\\generator\\drawingDefault.js", "mtime": 1749104047634}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749104418875}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IFt7CiAgbGF5b3V0OiAnY29sRm9ybUl0ZW0nLAogIHRhZ0ljb246ICdpbnB1dCcsCiAgbGFiZWw6ICfmiYvmnLrlj7cnLAogIHZNb2RlbDogJ21vYmlsZScsCiAgZm9ybUlkOiA2LAogIHRhZzogJ2VsLWlucHV0JywKICBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeaJi+acuuWPtycsCiAgZGVmYXVsdFZhbHVlOiAnJywKICBzcGFuOiAyNCwKICBzdHlsZTogewogICAgd2lkdGg6ICcxMDAlJwogIH0sCiAgY2xlYXJhYmxlOiB0cnVlLAogIHByZXBlbmQ6ICcnLAogIGFwcGVuZDogJycsCiAgJ3ByZWZpeC1pY29uJzogJ2VsLWljb24tbW9iaWxlJywKICAnc3VmZml4LWljb24nOiAnJywKICBtYXhsZW5ndGg6IDExLAogICdzaG93LXdvcmQtbGltaXQnOiB0cnVlLAogIHJlYWRvbmx5OiBmYWxzZSwKICBkaXNhYmxlZDogZmFsc2UsCiAgcmVxdWlyZWQ6IHRydWUsCiAgY2hhbmdlVGFnOiB0cnVlLAogIHJlZ0xpc3Q6IFt7CiAgICBwYXR0ZXJuOiAnL14xKDN8NHw1fDd8OHw5KVxcZHs5fSQvJywKICAgIG1lc3NhZ2U6ICfmiYvmnLrlj7fmoLzlvI/plJnor68nCiAgfV0KfV07"}, {"version": 3, "names": ["layout", "tagIcon", "label", "vModel", "formId", "tag", "placeholder", "defaultValue", "span", "style", "width", "clearable", "prepend", "append", "maxlength", "readonly", "disabled", "required", "changeTag", "regList", "pattern", "message"], "sources": ["D:/thinktank/thinktankui/src/utils/generator/drawingDefault.js"], "sourcesContent": ["export default [\r\n  {\r\n    layout: 'colFormItem',\r\n    tagIcon: 'input',\r\n    label: '手机号',\r\n    vModel: 'mobile',\r\n    formId: 6,\r\n    tag: 'el-input',\r\n    placeholder: '请输入手机号',\r\n    defaultValue: '',\r\n    span: 24,\r\n    style: { width: '100%' },\r\n    clearable: true,\r\n    prepend: '',\r\n    append: '',\r\n    'prefix-icon': 'el-icon-mobile',\r\n    'suffix-icon': '',\r\n    maxlength: 11,\r\n    'show-word-limit': true,\r\n    readonly: false,\r\n    disabled: false,\r\n    required: true,\r\n    changeTag: true,\r\n    regList: [{\r\n      pattern: '/^1(3|4|5|7|8|9)\\\\d{9}$/',\r\n      message: '手机号格式错误'\r\n    }]\r\n  }\r\n]\r\n"], "mappings": ";;;;;;iCAAe,CACb;EACEA,MAAM,EAAE,aAAa;EACrBC,OAAO,EAAE,OAAO;EAChBC,KAAK,EAAE,KAAK;EACZC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,CAAC;EACTC,GAAG,EAAE,UAAU;EACfC,WAAW,EAAE,QAAQ;EACrBC,YAAY,EAAE,EAAE;EAChBC,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE;IAAEC,KAAK,EAAE;EAAO,CAAC;EACxBC,SAAS,EAAE,IAAI;EACfC,OAAO,EAAE,EAAE;EACXC,MAAM,EAAE,EAAE;EACV,aAAa,EAAE,gBAAgB;EAC/B,aAAa,EAAE,EAAE;EACjBC,SAAS,EAAE,EAAE;EACb,iBAAiB,EAAE,IAAI;EACvBC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAI;EACfC,OAAO,EAAE,CAAC;IACRC,OAAO,EAAE,0BAA0B;IACnCC,OAAO,EAAE;EACX,CAAC;AACH,CAAC,CACF", "ignoreList": []}]}