{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\align.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\align.js", "mtime": 1749104419720}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLkFsaWduU3R5bGUgPSBleHBvcnRzLkFsaWduQ2xhc3MgPSBleHBvcnRzLkFsaWduQXR0cmlidXRlID0gdm9pZCAwOwp2YXIgX3BhcmNobWVudCA9IHJlcXVpcmUoInBhcmNobWVudCIpOwp2YXIgY29uZmlnID0gewogIHNjb3BlOiBfcGFyY2htZW50LlNjb3BlLkJMT0NLLAogIHdoaXRlbGlzdDogWydyaWdodCcsICdjZW50ZXInLCAnanVzdGlmeSddCn07CnZhciBBbGlnbkF0dHJpYnV0ZSA9IGV4cG9ydHMuQWxpZ25BdHRyaWJ1dGUgPSBuZXcgX3BhcmNobWVudC5BdHRyaWJ1dG9yKCdhbGlnbicsICdhbGlnbicsIGNvbmZpZyk7CnZhciBBbGlnbkNsYXNzID0gZXhwb3J0cy5BbGlnbkNsYXNzID0gbmV3IF9wYXJjaG1lbnQuQ2xhc3NBdHRyaWJ1dG9yKCdhbGlnbicsICdxbC1hbGlnbicsIGNvbmZpZyk7CnZhciBBbGlnblN0eWxlID0gZXhwb3J0cy5BbGlnblN0eWxlID0gbmV3IF9wYXJjaG1lbnQuU3R5bGVBdHRyaWJ1dG9yKCdhbGlnbicsICd0ZXh0LWFsaWduJywgY29uZmlnKTs="}, {"version": 3, "names": ["_parchment", "require", "config", "scope", "<PERSON><PERSON>", "BLOCK", "whitelist", "AlignAttribute", "exports", "Attributor", "AlignClass", "ClassAttributor", "AlignStyle", "StyleAttributor"], "sources": ["../../src/formats/align.ts"], "sourcesContent": ["import { Attributor, ClassAttributor, Scope, StyleAttributor } from 'parchment';\n\nconst config = {\n  scope: Scope.BLOCK,\n  whitelist: ['right', 'center', 'justify'],\n};\n\nconst AlignAttribute = new Attributor('align', 'align', config);\nconst AlignClass = new ClassAttributor('align', 'ql-align', config);\nconst AlignStyle = new StyleAttributor('align', 'text-align', config);\n\nexport { AlignAttribute, AlignClass, AlignStyle };\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAEA,IAAMC,MAAM,GAAG;EACbC,KAAK,EAAEC,gBAAK,CAACC,KAAK;EAClBC,SAAS,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS;AAC1C,CAAC;AAED,IAAMC,cAAc,GAAAC,OAAA,CAAAD,cAAA,GAAG,IAAIE,qBAAU,CAAC,OAAO,EAAE,OAAO,EAAEP,MAAM,CAAC;AAC/D,IAAMQ,UAAU,GAAAF,OAAA,CAAAE,UAAA,GAAG,IAAIC,0BAAe,CAAC,OAAO,EAAE,UAAU,EAAET,MAAM,CAAC;AACnE,IAAMU,UAAU,GAAAJ,OAAA,CAAAI,UAAA,GAAG,IAAIC,0BAAe,CAAC,OAAO,EAAE,YAAY,EAAEX,MAAM,CAAC", "ignoreList": []}]}