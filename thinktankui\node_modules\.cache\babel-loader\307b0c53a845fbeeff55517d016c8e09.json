{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\api\\monitor\\jobLog.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\api\\monitor\\jobLog.js", "mtime": 1749104047591}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749104418875}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmNsZWFuSm9iTG9nID0gY2xlYW5Kb2JMb2c7CmV4cG9ydHMuZGVsSm9iTG9nID0gZGVsSm9iTG9nOwpleHBvcnRzLmxpc3RKb2JMb2cgPSBsaXN0Sm9iTG9nOwp2YXIgX3JlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvcmVxdWVzdCIpKTsKLy8g5p+l6K+i6LCD5bqm5pel5b+X5YiX6KGoCmZ1bmN0aW9uIGxpc3RKb2JMb2cocXVlcnkpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9tb25pdG9yL2pvYkxvZy9saXN0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOWIoOmZpOiwg+W6puaXpeW/lwpmdW5jdGlvbiBkZWxKb2JMb2coam9iTG9nSWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9tb25pdG9yL2pvYkxvZy8nICsgam9iTG9nSWQsCiAgICBtZXRob2Q6ICdkZWxldGUnCiAgfSk7Cn0KCi8vIOa4heepuuiwg+W6puaXpeW/lwpmdW5jdGlvbiBjbGVhbkpvYkxvZygpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9tb25pdG9yL2pvYkxvZy9jbGVhbicsCiAgICBtZXRob2Q6ICdkZWxldGUnCiAgfSk7Cn0="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listJobLog", "query", "request", "url", "method", "params", "delJobLog", "jobLogId", "cleanJobLog"], "sources": ["D:/thinktank/thinktankui/src/api/monitor/jobLog.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询调度日志列表\r\nexport function listJobLog(query) {\r\n  return request({\r\n    url: '/monitor/jobLog/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 删除调度日志\r\nexport function delJobLog(jobLogId) {\r\n  return request({\r\n    url: '/monitor/jobLog/' + jobLogId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 清空调度日志\r\nexport function cleanJobLog() {\r\n  return request({\r\n    url: '/monitor/jobLog/clean',\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,SAASA,CAACC,QAAQ,EAAE;EAClC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB,GAAGI,QAAQ;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,WAAWA,CAAA,EAAG;EAC5B,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}