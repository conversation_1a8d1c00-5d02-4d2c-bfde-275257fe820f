{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\opinion-overview\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\opinion-overview\\index.vue", "mtime": 1749104047642}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "_interopRequireWildcard", "require", "name", "data", "activeMenuItem", "searchText", "activeTab", "originalTopNav", "undefined", "mapChart", "platformChart", "sentiment<PERSON>hart", "trendChart", "hotArticles", "icon", "type", "title", "source", "author", "announcements", "level", "time", "mapData", "value", "platformData", "color", "sentimentData", "mounted", "_this", "$store", "state", "settings", "topNav", "dispatch", "key", "$nextTick", "initTrendChart", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "methods", "handleMenuSelect", "index", "console", "log", "handleTabClick", "tab", "_this2", "initMap", "initPlatformChart", "initSentimentChart", "chartContainer", "document", "getElementById", "init", "dates", "today", "Date", "i", "date", "setDate", "getDate", "push", "getMonth", "weiboData", "generateRandomData", "wechatData", "douyinData", "toutiaData", "otherData", "option", "tooltip", "trigger", "axisPointer", "label", "backgroundColor", "formatter", "params", "result", "for<PERSON>ach", "param", "concat", "seriesName", "legend", "top", "textStyle", "fontSize", "grid", "left", "right", "bottom", "containLabel", "xAxis", "boundaryGap", "axisLabel", "axisLine", "lineStyle", "yAxis", "splitLine", "series", "stack", "areaStyle", "graphic", "LinearGradient", "offset", "width", "itemStyle", "setOption", "text", "fontWeight", "orient", "radius", "center", "avoidLabelOverlap", "show", "position", "emphasis", "labelLine", "count", "min", "max", "Math", "floor", "random"], "sources": ["src/views/opinion-overview/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"opinion-overview\">\r\n    <!-- 左侧导航栏 -->\r\n    <div class=\"left-sidebar\">\r\n      <div class=\"sidebar-header\">\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\">新建方案</el-button>\r\n      </div>\r\n\r\n      <div class=\"sidebar-search\">\r\n        <el-input\r\n          v-model=\"searchText\"\r\n          placeholder=\"搜索方案\"\r\n          size=\"small\"\r\n          prefix-icon=\"el-icon-search\">\r\n        </el-input>\r\n      </div>\r\n\r\n      <div class=\"sidebar-menu\">\r\n        <div class=\"menu-section\">\r\n          <div class=\"section-title\">已有方案</div>\r\n          <el-menu\r\n            :default-active=\"activeMenuItem\"\r\n            class=\"sidebar-menu-list\"\r\n            @select=\"handleMenuSelect\">\r\n            <el-menu-item index=\"基础(1)\">\r\n              <i class=\"el-icon-s-custom\"></i>\r\n              <span>基础(1)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"品牌(1)\">\r\n              <i class=\"el-icon-s-goods\"></i>\r\n              <span>品牌(1)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"方太\" class=\"active-item\">\r\n              <i class=\"el-icon-star-off\"></i>\r\n              <span>方太</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"人物(0)\">\r\n              <i class=\"el-icon-user\"></i>\r\n              <span>人物(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"机构(0)\">\r\n              <i class=\"el-icon-office-building\"></i>\r\n              <span>机构(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"产品(0)\">\r\n              <i class=\"el-icon-goods\"></i>\r\n              <span>产品(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"事件(0)\">\r\n              <i class=\"el-icon-warning\"></i>\r\n              <span>事件(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"话题(0)\">\r\n              <i class=\"el-icon-chat-dot-round\"></i>\r\n              <span>话题(0)</span>\r\n            </el-menu-item>\r\n          </el-menu>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 右侧内容区 -->\r\n    <div class=\"right-content\">\r\n      <!-- 顶部导航标签栏 -->\r\n      <!-- 主要内容区域 -->\r\n      <div class=\"main-content\">\r\n        <!-- 根据activeTab显示不同内容 -->\r\n        <div v-if=\"activeTab === 'opinion-monitor'\">\r\n          <!-- 舆情监测内容 -->\r\n\r\n          <!-- 舆情趋势图表 -->\r\n          <div class=\"section-card\">\r\n            <div class=\"chart-container\">\r\n              <div id=\"trend-chart\" style=\"width: 100%; height: 400px;\"></div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 热门文章和最新公告 -->\r\n          <div class=\"bottom-section\">\r\n            <div class=\"left-articles\">\r\n              <div class=\"article-card\">\r\n                <div class=\"card-header\">\r\n                  <h3><i class=\"el-icon-document\" style=\"color: #1890ff; margin-right: 8px;\"></i>热门文章</h3>\r\n                </div>\r\n                <div class=\"article-list\">\r\n                  <div class=\"article-item\" v-for=\"(article, index) in hotArticles\" :key=\"index\">\r\n                    <div class=\"article-icon\" :class=\"article.type\">{{ article.icon }}</div>\r\n                    <div class=\"article-content\">\r\n                      <div class=\"article-title\">{{ article.title }}</div>\r\n                      <div class=\"article-meta\">\r\n                        <span class=\"article-source\">{{ article.source }}</span>\r\n                        <span class=\"article-author\">{{ article.author }}</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"right-announcements\">\r\n              <div class=\"announcement-card\">\r\n                <div class=\"card-header\">\r\n                  <h3><i class=\"el-icon-bell\" style=\"color: #52c41a; margin-right: 8px;\"></i>最新公告</h3>\r\n                </div>\r\n                <div class=\"announcement-list\">\r\n                  <div class=\"announcement-item\" v-for=\"(announcement, index) in announcements\" :key=\"index\">\r\n                    <div class=\"announcement-indicator\" :class=\"announcement.level\"></div>\r\n                    <div class=\"announcement-content\">\r\n                      <div class=\"announcement-title\">{{ announcement.title }}</div>\r\n                      <div class=\"announcement-time\">{{ announcement.time }}</div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-else-if=\"activeTab === 'info-summary'\">\r\n          <!-- 信息汇总内容 -->\r\n          <!-- 近30天舆情发布地区 -->\r\n          <div class=\"section-card\">\r\n            <div class=\"card-header\">\r\n              <h3><i class=\"el-icon-location\" style=\"color: #409EFF; margin-right: 8px;\"></i>近30天舆情发布地区</h3>\r\n              <div class=\"stats\">\r\n                <div class=\"stat-item positive\">\r\n                  <span class=\"label\">正面舆情</span>\r\n                  <span class=\"value\">111930</span>\r\n                </div>\r\n                <div class=\"stat-item neutral\">\r\n                  <span class=\"label\">中性舆情</span>\r\n                  <span class=\"value\">1118</span>\r\n                </div>\r\n                <div class=\"stat-item negative\">\r\n                  <span class=\"label\">负面舆情</span>\r\n                  <span class=\"value\">444</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"map-container\">\r\n              <div id=\"china-map\" style=\"width: 100%; height: 400px; background-color: #f0f2f5; display: flex; align-items: center; justify-content: center; color: #999;\">\r\n                <div>地图组件加载中...</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 今日舆情总量和平台分析 -->\r\n          <div class=\"bottom-section\">\r\n          <div class=\"left-charts\">\r\n            <!-- 近30天平台分析 -->\r\n            <div class=\"chart-card\">\r\n              <div class=\"card-header\">\r\n                <h3><i class=\"el-icon-pie-chart\" style=\"color: #52C41A; margin-right: 8px;\"></i>近30天平台分析</h3>\r\n                <el-button type=\"text\" icon=\"el-icon-download\">导出</el-button>\r\n              </div>\r\n              <div id=\"platform-chart\" style=\"width: 100%; height: 300px;\"></div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"right-stats\">\r\n            <!-- 今日舆情总量 -->\r\n            <div class=\"stats-card\">\r\n              <div class=\"card-header\">\r\n                <h3><i class=\"el-icon-data-line\" style=\"color: #FA8C16; margin-right: 8px;\"></i>今日舆情总量</h3>\r\n              </div>\r\n              <div class=\"total-count\">0 0 0,0 0 4,6 8 1</div>\r\n              <div class=\"platform-stats\">\r\n                <div class=\"platform-item weibo\">\r\n                  <div class=\"platform-icon\">微</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">微博</span>\r\n                    <span class=\"platform-count\">534</span>\r\n                    <span class=\"platform-change\">今日新增 -0.8%</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"platform-item wechat\">\r\n                  <div class=\"platform-icon\">微</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">微信</span>\r\n                    <span class=\"platform-count\">1483</span>\r\n                    <span class=\"platform-change\">今日新增 15.2%</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"platform-item weibo-red\">\r\n                  <div class=\"platform-icon\">微</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">微博</span>\r\n                    <span class=\"platform-count\">279</span>\r\n                    <span class=\"platform-change\">今日新增 -0.8%</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"platform-item xiaohongshu\">\r\n                  <div class=\"platform-icon\">小</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">小红书</span>\r\n                    <span class=\"platform-count\">129</span>\r\n                    <span class=\"platform-change\">今日新增 3.2%</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"platform-item app\">\r\n                  <div class=\"platform-icon\">A</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">APP</span>\r\n                    <span class=\"platform-count\">764</span>\r\n                    <span class=\"platform-change\">今日新增 -1.8%</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"platform-item toutiao\">\r\n                  <div class=\"platform-icon\">头</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">头条</span>\r\n                    <span class=\"platform-count\">1455</span>\r\n                    <span class=\"platform-change\">今日新增 4.5%</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"platform-item douyin\">\r\n                  <div class=\"platform-icon\">抖</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">抖音</span>\r\n                    <span class=\"platform-count\">23</span>\r\n                    <span class=\"platform-change\">今日新增 100%</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"platform-item news\">\r\n                  <div class=\"platform-icon\">新</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">新闻</span>\r\n                    <span class=\"platform-count\">2</span>\r\n                    <span class=\"platform-change\">今日新增 100%</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"platform-item forum\">\r\n                  <div class=\"platform-icon\">论</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">论坛</span>\r\n                    <span class=\"platform-count\">12</span>\r\n                    <span class=\"platform-change\">今日新增 -2.8%</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 近30天情感属性 -->\r\n            <div class=\"chart-card\">\r\n              <div class=\"card-header\">\r\n                <h3><i class=\"el-icon-sunny\" style=\"color: #722ED1; margin-right: 8px;\"></i>近30天情感属性</h3>\r\n                <el-button type=\"text\" icon=\"el-icon-download\">导出</el-button>\r\n              </div>\r\n              <div id=\"sentiment-chart\" style=\"width: 100%; height: 200px;\"></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: 'OpinionOverview',\r\n  data() {\r\n    return {\r\n      activeMenuItem: '方太',\r\n      searchText: '',\r\n      activeTab: 'opinion-monitor', // 默认激活舆情监测标签\r\n      originalTopNav: undefined, // 存储原始的topNav状态\r\n      // 图表实例\r\n      mapChart: null,\r\n      platformChart: null,\r\n      sentimentChart: null,\r\n      trendChart: null, // 趋势图表实例\r\n      // 热门文章数据\r\n      hotArticles: [\r\n        {\r\n          icon: '红',\r\n          type: 'negative',\r\n          title: '某品牌产品质量问题引发消费者不满',\r\n          source: '新浪财经',\r\n          author: '财经记者'\r\n        },\r\n        {\r\n          icon: '黄',\r\n          type: 'neutral',\r\n          title: '市场分析：家电行业发展趋势',\r\n          source: '中国经济网',\r\n          author: '市场分析师'\r\n        },\r\n        {\r\n          icon: '绿',\r\n          type: 'positive',\r\n          title: '创新技术推动行业发展',\r\n          source: '科技日报',\r\n          author: '科技记者'\r\n        },\r\n        {\r\n          icon: '红',\r\n          type: 'negative',\r\n          title: '消费者投诉处理不当引发关注',\r\n          source: '消费者报',\r\n          author: '消费维权'\r\n        },\r\n        {\r\n          icon: '绿',\r\n          type: 'positive',\r\n          title: '企业社会责任获得认可',\r\n          source: '人民日报',\r\n          author: '社会记者'\r\n        }\r\n      ],\r\n      // 最新公告数据\r\n      announcements: [\r\n        {\r\n          level: 'high',\r\n          title: '舆情监测系统升级通知',\r\n          time: '2023-04-20 10:30:00'\r\n        },\r\n        {\r\n          level: 'medium',\r\n          title: '五一假期监测安排',\r\n          time: '2023-04-19 16:45:00'\r\n        },\r\n        {\r\n          level: 'low',\r\n          title: '数据统计报告已生成',\r\n          time: '2023-04-19 14:20:00'\r\n        },\r\n        {\r\n          level: 'high',\r\n          title: '重要舆情预警提醒',\r\n          time: '2023-04-19 09:15:00'\r\n        },\r\n        {\r\n          level: 'medium',\r\n          title: '系统维护完成通知',\r\n          time: '2023-04-18 18:30:00'\r\n        }\r\n      ],\r\n      // 地图数据\r\n      mapData: [\r\n        {name: '北京', value: 15000},\r\n        {name: '上海', value: 12000},\r\n        {name: '广东', value: 18000},\r\n        {name: '浙江', value: 8000},\r\n        {name: '江苏', value: 9000},\r\n        {name: '山东', value: 7000},\r\n        {name: '四川', value: 6000},\r\n        {name: '湖北', value: 5000}\r\n      ],\r\n      // 平台数据\r\n      platformData: [\r\n        {name: '微博', value: 35.2, color: '#ff6b6b'},\r\n        {name: '微信', value: 28.6, color: '#51cf66'},\r\n        {name: '抖音', value: 18.4, color: '#339af0'},\r\n        {name: '今日头条', value: 12.8, color: '#ffd43b'},\r\n        {name: '其他', value: 5.0, color: '#868e96'}\r\n      ],\r\n      // 情感数据\r\n      sentimentData: [\r\n        {name: '正面', value: 65.2, color: '#52c41a'},\r\n        {name: '中性', value: 28.3, color: '#faad14'},\r\n        {name: '负面', value: 6.5, color: '#ff4d4f'}\r\n      ]\r\n    }\r\n  },\r\n  mounted() {\r\n    // 隐藏顶部导航栏\r\n    this.originalTopNav = this.$store.state.settings.topNav\r\n    this.$store.dispatch('settings/changeSetting', {\r\n      key: 'topNav',\r\n      value: false\r\n    })\r\n\r\n    this.$nextTick(() => {\r\n      // 根据当前激活的标签初始化对应的图表\r\n      if (this.activeTab === 'opinion-monitor') {\r\n        this.initTrendChart()\r\n      } else if (this.activeTab === 'info-summary') {\r\n        this.initCharts()\r\n      }\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    // 恢复顶部导航栏设置\r\n    if (this.originalTopNav !== undefined) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'topNav',\r\n        value: this.originalTopNav\r\n      })\r\n    }\r\n\r\n    // 销毁图表实例\r\n    if (this.mapChart) {\r\n      this.mapChart.dispose()\r\n    }\r\n    if (this.platformChart) {\r\n      this.platformChart.dispose()\r\n    }\r\n    if (this.sentimentChart) {\r\n      this.sentimentChart.dispose()\r\n    }\r\n    if (this.trendChart) {\r\n      this.trendChart.dispose()\r\n    }\r\n  },\r\n  methods: {\r\n    handleMenuSelect(index) {\r\n      this.activeMenuItem = index\r\n      console.log('Menu selected:', index)\r\n      // 这里可以根据选择的方案加载不同的数据\r\n    },\r\n    handleTabClick(tab) {\r\n      console.log('Tab clicked:', tab.name)\r\n      // 根据不同标签加载不同内容\r\n      this.$nextTick(() => {\r\n        if (tab.name === 'opinion-monitor') {\r\n          this.initTrendChart()\r\n        } else if (tab.name === 'info-summary') {\r\n          this.initCharts()\r\n        }\r\n      })\r\n    },\r\n    initCharts() {\r\n      // 初始化图表\r\n      this.initMap()\r\n      this.initPlatformChart()\r\n      this.initSentimentChart()\r\n    },\r\n    initTrendChart() {\r\n      // 初始化舆情趋势图表\r\n      const chartContainer = document.getElementById('trend-chart')\r\n      if (!chartContainer) return\r\n\r\n      this.trendChart = echarts.init(chartContainer)\r\n\r\n      // 生成30天的日期数据\r\n      const dates = []\r\n      const today = new Date()\r\n      for (let i = 29; i >= 0; i--) {\r\n        const date = new Date(today)\r\n        date.setDate(date.getDate() - i)\r\n        dates.push(date.getMonth() + 1 + '.' + date.getDate())\r\n      }\r\n\r\n      // 模拟各平台的数据\r\n      const weiboData = this.generateRandomData(30, 1500, 2500)\r\n      const wechatData = this.generateRandomData(30, 1200, 2000)\r\n      const douyinData = this.generateRandomData(30, 800, 1500)\r\n      const toutiaData = this.generateRandomData(30, 600, 1200)\r\n      const otherData = this.generateRandomData(30, 300, 800)\r\n\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross',\r\n            label: {\r\n              backgroundColor: '#6a7985'\r\n            }\r\n          },\r\n          formatter: function(params) {\r\n            let result = params[0].name + '<br/>'\r\n            params.forEach(param => {\r\n              result += `<span style=\"color:${param.color}\">●</span> ${param.seriesName}: ${param.value}<br/>`\r\n            })\r\n            return result\r\n          }\r\n        },\r\n        legend: {\r\n          data: ['微博', '微信', '抖音', '头条', '其他'],\r\n          top: 20,\r\n          textStyle: {\r\n            fontSize: 12\r\n          }\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          top: '15%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: dates,\r\n          axisLabel: {\r\n            fontSize: 10,\r\n            color: '#666'\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#e0e0e0'\r\n            }\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          axisLabel: {\r\n            fontSize: 10,\r\n            color: '#666'\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#e0e0e0'\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: '#f0f0f0'\r\n            }\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '微博',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(24, 144, 255, 0.6)' },\r\n                { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#1890ff',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#1890ff'\r\n            },\r\n            data: weiboData\r\n          },\r\n          {\r\n            name: '微信',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(82, 196, 26, 0.6)' },\r\n                { offset: 1, color: 'rgba(82, 196, 26, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#52c41a',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#52c41a'\r\n            },\r\n            data: wechatData\r\n          },\r\n          {\r\n            name: '抖音',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(114, 46, 209, 0.6)' },\r\n                { offset: 1, color: 'rgba(114, 46, 209, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#722ed1',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#722ed1'\r\n            },\r\n            data: douyinData\r\n          },\r\n          {\r\n            name: '头条',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(250, 140, 22, 0.6)' },\r\n                { offset: 1, color: 'rgba(250, 140, 22, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#fa8c16',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#fa8c16'\r\n            },\r\n            data: toutiaData\r\n          },\r\n          {\r\n            name: '其他',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(160, 217, 17, 0.6)' },\r\n                { offset: 1, color: 'rgba(160, 217, 17, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#a0d911',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#a0d911'\r\n            },\r\n            data: otherData\r\n          }\r\n        ]\r\n      }\r\n\r\n      this.trendChart.setOption(option)\r\n    },\r\n    initMap() {\r\n      // 暂时跳过地图初始化，避免缺少地图数据导致的错误\r\n      console.log('地图初始化已跳过，需要引入中国地图数据')\r\n    },\r\n    initPlatformChart() {\r\n      // 初始化平台分析面积图\r\n      const chartContainer = document.getElementById('platform-chart')\r\n      if (!chartContainer) return\r\n\r\n      this.platformChart = echarts.init(chartContainer)\r\n\r\n      // 生成30天的日期数据\r\n      const dates = []\r\n      const today = new Date()\r\n      for (let i = 29; i >= 0; i--) {\r\n        const date = new Date(today)\r\n        date.setDate(date.getDate() - i)\r\n        dates.push(date.getMonth() + 1 + '.' + date.getDate())\r\n      }\r\n\r\n      // 模拟各平台的数据\r\n      const weiboData = this.generateRandomData(30, 1500, 2000)\r\n      const wechatData = this.generateRandomData(30, 1200, 1800)\r\n      const douyinData = this.generateRandomData(30, 800, 1200)\r\n      const toutiaData = this.generateRandomData(30, 600, 1000)\r\n      const otherData = this.generateRandomData(30, 300, 600)\r\n\r\n      const option = {\r\n        title: {\r\n          text: '近30天平台舆情趋势',\r\n          left: 'center',\r\n          textStyle: {\r\n            fontSize: 16,\r\n            fontWeight: 'normal',\r\n            color: '#333'\r\n          }\r\n        },\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross',\r\n            label: {\r\n              backgroundColor: '#6a7985'\r\n            }\r\n          },\r\n          formatter: function (params) {\r\n            let result = params[0].name + '<br/>'\r\n            params.forEach(param => {\r\n              result += `<span style=\"color:${param.color}\">●</span> ${param.seriesName}: ${param.value}<br/>`\r\n            })\r\n            return result\r\n          }\r\n        },\r\n        legend: {\r\n          data: ['微博', '微信', '抖音', '头条', '其他'],\r\n          top: 30,\r\n          textStyle: {\r\n            fontSize: 12\r\n          }\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          top: '15%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: dates,\r\n          axisLabel: {\r\n            fontSize: 10,\r\n            color: '#666'\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#e0e0e0'\r\n            }\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          axisLabel: {\r\n            fontSize: 10,\r\n            color: '#666'\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#e0e0e0'\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: '#f0f0f0'\r\n            }\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '微博',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(24, 144, 255, 0.6)' },\r\n                { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#1890ff',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#1890ff'\r\n            },\r\n            data: weiboData\r\n          },\r\n          {\r\n            name: '微信',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(82, 196, 26, 0.6)' },\r\n                { offset: 1, color: 'rgba(82, 196, 26, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#52c41a',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#52c41a'\r\n            },\r\n            data: wechatData\r\n          },\r\n          {\r\n            name: '抖音',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(114, 46, 209, 0.6)' },\r\n                { offset: 1, color: 'rgba(114, 46, 209, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#722ed1',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#722ed1'\r\n            },\r\n            data: douyinData\r\n          },\r\n          {\r\n            name: '头条',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(250, 140, 22, 0.6)' },\r\n                { offset: 1, color: 'rgba(250, 140, 22, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#fa8c16',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#fa8c16'\r\n            },\r\n            data: toutiaData\r\n          },\r\n          {\r\n            name: '其他',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(160, 217, 17, 0.6)' },\r\n                { offset: 1, color: 'rgba(160, 217, 17, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#a0d911',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#a0d911'\r\n            },\r\n            data: otherData\r\n          }\r\n        ]\r\n      }\r\n\r\n      this.platformChart.setOption(option)\r\n    },\r\n    initSentimentChart() {\r\n      // 初始化情感属性饼图\r\n      const chartContainer = document.getElementById('sentiment-chart')\r\n      if (!chartContainer) return\r\n\r\n      this.sentimentChart = echarts.init(chartContainer)\r\n\r\n      const option = {\r\n        title: {\r\n          text: '情感分布',\r\n          left: 'center',\r\n          textStyle: {\r\n            fontSize: 14,\r\n            fontWeight: 'normal',\r\n            color: '#333'\r\n          }\r\n        },\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\r\n        },\r\n        legend: {\r\n          orient: 'vertical',\r\n          left: 'left',\r\n          top: 'middle',\r\n          data: ['正面', '中性', '负面'],\r\n          textStyle: {\r\n            fontSize: 12\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '情感分布',\r\n            type: 'pie',\r\n            radius: ['40%', '70%'],\r\n            center: ['60%', '50%'],\r\n            avoidLabelOverlap: false,\r\n            label: {\r\n              show: false,\r\n              position: 'center'\r\n            },\r\n            emphasis: {\r\n              label: {\r\n                show: true,\r\n                fontSize: '18',\r\n                fontWeight: 'bold'\r\n              }\r\n            },\r\n            labelLine: {\r\n              show: false\r\n            },\r\n            data: [\r\n              {\r\n                name: '正面',\r\n                value: 65.2,\r\n                itemStyle: {\r\n                  color: '#52c41a'\r\n                }\r\n              },\r\n              {\r\n                name: '中性',\r\n                value: 28.3,\r\n                itemStyle: {\r\n                  color: '#faad14'\r\n                }\r\n              },\r\n              {\r\n                name: '负面',\r\n                value: 6.5,\r\n                itemStyle: {\r\n                  color: '#ff4d4f'\r\n                }\r\n              }\r\n            ]\r\n          }\r\n        ]\r\n      }\r\n\r\n      this.sentimentChart.setOption(option)\r\n    },\r\n    // 生成随机数据的辅助方法\r\n    generateRandomData(count, min, max) {\r\n      const data = []\r\n      for (let i = 0; i < count; i++) {\r\n        const value = Math.floor(Math.random() * (max - min + 1)) + min\r\n        data.push(value)\r\n      }\r\n      return data\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.opinion-overview {\r\n  display: flex;\r\n  height: 100vh;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.left-sidebar {\r\n  width: 280px;\r\n  background-color: #fff;\r\n  border-right: 1px solid #e8e8e8;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .sidebar-header {\r\n    padding: 16px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n\r\n  .sidebar-search {\r\n    padding: 16px;\r\n  }\r\n\r\n  .sidebar-menu {\r\n    flex: 1;\r\n    padding: 0 16px;\r\n\r\n    .section-title {\r\n      font-size: 14px;\r\n      color: #666;\r\n      margin-bottom: 8px;\r\n    }\r\n\r\n    .sidebar-menu-list {\r\n      border: none;\r\n\r\n      .el-menu-item {\r\n        height: 40px;\r\n        line-height: 40px;\r\n        margin-bottom: 4px;\r\n        border-radius: 4px;\r\n\r\n        &.active-item {\r\n          background-color: #e6f7ff;\r\n          color: #1890ff;\r\n        }\r\n\r\n        &:hover {\r\n          background-color: #f5f5f5;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.right-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .top-tabs {\r\n    background-color: #fff;\r\n    border-bottom: 1px solid #e8e8e8;\r\n\r\n    .el-tabs {\r\n      .el-tabs__header {\r\n        margin: 0;\r\n\r\n        .el-tabs__nav-wrap {\r\n          padding: 0 24px;\r\n        }\r\n\r\n        .el-tabs__item {\r\n          height: 50px;\r\n          line-height: 50px;\r\n          font-size: 14px;\r\n\r\n          &.is-active {\r\n            color: #1890ff;\r\n            border-bottom-color: #1890ff;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .main-content {\r\n    flex: 1;\r\n    padding: 24px;\r\n    overflow-y: auto;\r\n  }\r\n}\r\n\r\n.section-card {\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n  .card-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 24px;\r\n\r\n    h3 {\r\n      margin: 0;\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n    }\r\n\r\n    .stats {\r\n      display: flex;\r\n      gap: 24px;\r\n\r\n      .stat-item {\r\n        text-align: center;\r\n\r\n        .label {\r\n          display: block;\r\n          font-size: 14px;\r\n          color: #666;\r\n          margin-bottom: 4px;\r\n        }\r\n\r\n        .value {\r\n          display: block;\r\n          font-size: 24px;\r\n          font-weight: 600;\r\n        }\r\n\r\n        &.positive .value {\r\n          color: #52c41a;\r\n        }\r\n\r\n        &.neutral .value {\r\n          color: #faad14;\r\n        }\r\n\r\n        &.negative .value {\r\n          color: #ff4d4f;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.bottom-section {\r\n  display: flex;\r\n  gap: 24px;\r\n\r\n  .left-charts {\r\n    flex: 2;\r\n  }\r\n\r\n  .right-stats {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 24px;\r\n  }\r\n\r\n  .left-articles {\r\n    flex: 1;\r\n  }\r\n\r\n  .right-announcements {\r\n    flex: 1;\r\n  }\r\n}\r\n\r\n.chart-card {\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n  .card-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 16px;\r\n\r\n    h3 {\r\n      margin: 0;\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n    }\r\n  }\r\n}\r\n\r\n.stats-card {\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n  .total-count {\r\n    font-size: 32px;\r\n    font-weight: 600;\r\n    text-align: center;\r\n    margin: 24px 0;\r\n    letter-spacing: 2px;\r\n  }\r\n\r\n  .platform-stats {\r\n    .platform-item {\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 12px 0;\r\n      border-bottom: 1px solid #f0f0f0;\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      .platform-icon {\r\n        width: 32px;\r\n        height: 32px;\r\n        border-radius: 4px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        color: #fff;\r\n        font-weight: 600;\r\n        margin-right: 12px;\r\n      }\r\n\r\n      &.weibo .platform-icon {\r\n        background-color: #1890ff;\r\n      }\r\n\r\n      &.wechat .platform-icon {\r\n        background-color: #52c41a;\r\n      }\r\n\r\n      &.weibo-red .platform-icon {\r\n        background-color: #ff4d4f;\r\n      }\r\n\r\n      &.xiaohongshu .platform-icon {\r\n        background-color: #eb2f96;\r\n      }\r\n\r\n      &.app .platform-icon {\r\n        background-color: #13c2c2;\r\n      }\r\n\r\n      &.toutiao .platform-icon {\r\n        background-color: #fa8c16;\r\n      }\r\n\r\n      &.douyin .platform-icon {\r\n        background-color: #722ed1;\r\n      }\r\n\r\n      &.news .platform-icon {\r\n        background-color: #faad14;\r\n      }\r\n\r\n      &.forum .platform-icon {\r\n        background-color: #a0d911;\r\n      }\r\n\r\n      .platform-info {\r\n        flex: 1;\r\n\r\n        .platform-name {\r\n          display: block;\r\n          font-size: 14px;\r\n          color: #333;\r\n        }\r\n\r\n        .platform-count {\r\n          display: block;\r\n          font-size: 18px;\r\n          font-weight: 600;\r\n          color: #333;\r\n        }\r\n\r\n        .platform-change {\r\n          display: block;\r\n          font-size: 12px;\r\n          color: #666;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 热门文章样式\r\n.article-card {\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n  .card-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 16px;\r\n\r\n    h3 {\r\n      margin: 0;\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n    }\r\n  }\r\n\r\n  .article-list {\r\n    .article-item {\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 12px 0;\r\n      border-bottom: 1px solid #f0f0f0;\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      .article-icon {\r\n        width: 32px;\r\n        height: 32px;\r\n        border-radius: 4px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        color: #fff;\r\n        font-weight: 600;\r\n        margin-right: 12px;\r\n        font-size: 14px;\r\n\r\n        &.negative {\r\n          background-color: #ff4d4f;\r\n        }\r\n\r\n        &.neutral {\r\n          background-color: #faad14;\r\n        }\r\n\r\n        &.positive {\r\n          background-color: #52c41a;\r\n        }\r\n      }\r\n\r\n      .article-content {\r\n        flex: 1;\r\n\r\n        .article-title {\r\n          font-size: 14px;\r\n          color: #333;\r\n          margin-bottom: 4px;\r\n          line-height: 1.4;\r\n        }\r\n\r\n        .article-meta {\r\n          font-size: 12px;\r\n          color: #666;\r\n\r\n          .article-source {\r\n            margin-right: 12px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 最新公告样式\r\n.announcement-card {\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n  .card-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 16px;\r\n\r\n    h3 {\r\n      margin: 0;\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n    }\r\n  }\r\n\r\n  .announcement-list {\r\n    .announcement-item {\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 12px 0;\r\n      border-bottom: 1px solid #f0f0f0;\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      .announcement-indicator {\r\n        width: 8px;\r\n        height: 8px;\r\n        border-radius: 50%;\r\n        margin-right: 12px;\r\n\r\n        &.high {\r\n          background-color: #ff4d4f;\r\n        }\r\n\r\n        &.medium {\r\n          background-color: #faad14;\r\n        }\r\n\r\n        &.low {\r\n          background-color: #52c41a;\r\n        }\r\n      }\r\n\r\n      .announcement-content {\r\n        flex: 1;\r\n\r\n        .announcement-title {\r\n          font-size: 14px;\r\n          color: #333;\r\n          margin-bottom: 4px;\r\n          line-height: 1.4;\r\n        }\r\n\r\n        .announcement-time {\r\n          font-size: 12px;\r\n          color: #666;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;AAmQA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,cAAA;MACAC,UAAA;MACAC,SAAA;MAAA;MACAC,cAAA,EAAAC,SAAA;MAAA;MACA;MACAC,QAAA;MACAC,aAAA;MACAC,cAAA;MACAC,UAAA;MAAA;MACA;MACAC,WAAA,GACA;QACAC,IAAA;QACAC,IAAA;QACAC,KAAA;QACAC,MAAA;QACAC,MAAA;MACA,GACA;QACAJ,IAAA;QACAC,IAAA;QACAC,KAAA;QACAC,MAAA;QACAC,MAAA;MACA,GACA;QACAJ,IAAA;QACAC,IAAA;QACAC,KAAA;QACAC,MAAA;QACAC,MAAA;MACA,GACA;QACAJ,IAAA;QACAC,IAAA;QACAC,KAAA;QACAC,MAAA;QACAC,MAAA;MACA,GACA;QACAJ,IAAA;QACAC,IAAA;QACAC,KAAA;QACAC,MAAA;QACAC,MAAA;MACA,EACA;MACA;MACAC,aAAA,GACA;QACAC,KAAA;QACAJ,KAAA;QACAK,IAAA;MACA,GACA;QACAD,KAAA;QACAJ,KAAA;QACAK,IAAA;MACA,GACA;QACAD,KAAA;QACAJ,KAAA;QACAK,IAAA;MACA,GACA;QACAD,KAAA;QACAJ,KAAA;QACAK,IAAA;MACA,GACA;QACAD,KAAA;QACAJ,KAAA;QACAK,IAAA;MACA,EACA;MACA;MACAC,OAAA,GACA;QAAApB,IAAA;QAAAqB,KAAA;MAAA,GACA;QAAArB,IAAA;QAAAqB,KAAA;MAAA,GACA;QAAArB,IAAA;QAAAqB,KAAA;MAAA,GACA;QAAArB,IAAA;QAAAqB,KAAA;MAAA,GACA;QAAArB,IAAA;QAAAqB,KAAA;MAAA,GACA;QAAArB,IAAA;QAAAqB,KAAA;MAAA,GACA;QAAArB,IAAA;QAAAqB,KAAA;MAAA,GACA;QAAArB,IAAA;QAAAqB,KAAA;MAAA,EACA;MACA;MACAC,YAAA,GACA;QAAAtB,IAAA;QAAAqB,KAAA;QAAAE,KAAA;MAAA,GACA;QAAAvB,IAAA;QAAAqB,KAAA;QAAAE,KAAA;MAAA,GACA;QAAAvB,IAAA;QAAAqB,KAAA;QAAAE,KAAA;MAAA,GACA;QAAAvB,IAAA;QAAAqB,KAAA;QAAAE,KAAA;MAAA,GACA;QAAAvB,IAAA;QAAAqB,KAAA;QAAAE,KAAA;MAAA,EACA;MACA;MACAC,aAAA,GACA;QAAAxB,IAAA;QAAAqB,KAAA;QAAAE,KAAA;MAAA,GACA;QAAAvB,IAAA;QAAAqB,KAAA;QAAAE,KAAA;MAAA,GACA;QAAAvB,IAAA;QAAAqB,KAAA;QAAAE,KAAA;MAAA;IAEA;EACA;EACAE,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA;IACA,KAAArB,cAAA,QAAAsB,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,MAAA;IACA,KAAAH,MAAA,CAAAI,QAAA;MACAC,GAAA;MACAX,KAAA;IACA;IAEA,KAAAY,SAAA;MACA;MACA,IAAAP,KAAA,CAAAtB,SAAA;QACAsB,KAAA,CAAAQ,cAAA;MACA,WAAAR,KAAA,CAAAtB,SAAA;QACAsB,KAAA,CAAAS,UAAA;MACA;IACA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA;IACA,SAAA/B,cAAA,KAAAC,SAAA;MACA,KAAAqB,MAAA,CAAAI,QAAA;QACAC,GAAA;QACAX,KAAA,OAAAhB;MACA;IACA;;IAEA;IACA,SAAAE,QAAA;MACA,KAAAA,QAAA,CAAA8B,OAAA;IACA;IACA,SAAA7B,aAAA;MACA,KAAAA,aAAA,CAAA6B,OAAA;IACA;IACA,SAAA5B,cAAA;MACA,KAAAA,cAAA,CAAA4B,OAAA;IACA;IACA,SAAA3B,UAAA;MACA,KAAAA,UAAA,CAAA2B,OAAA;IACA;EACA;EACAC,OAAA;IACAC,gBAAA,WAAAA,iBAAAC,KAAA;MACA,KAAAtC,cAAA,GAAAsC,KAAA;MACAC,OAAA,CAAAC,GAAA,mBAAAF,KAAA;MACA;IACA;IACAG,cAAA,WAAAA,eAAAC,GAAA;MAAA,IAAAC,MAAA;MACAJ,OAAA,CAAAC,GAAA,iBAAAE,GAAA,CAAA5C,IAAA;MACA;MACA,KAAAiC,SAAA;QACA,IAAAW,GAAA,CAAA5C,IAAA;UACA6C,MAAA,CAAAX,cAAA;QACA,WAAAU,GAAA,CAAA5C,IAAA;UACA6C,MAAA,CAAAV,UAAA;QACA;MACA;IACA;IACAA,UAAA,WAAAA,WAAA;MACA;MACA,KAAAW,OAAA;MACA,KAAAC,iBAAA;MACA,KAAAC,kBAAA;IACA;IACAd,cAAA,WAAAA,eAAA;MACA;MACA,IAAAe,cAAA,GAAAC,QAAA,CAAAC,cAAA;MACA,KAAAF,cAAA;MAEA,KAAAvC,UAAA,GAAAb,OAAA,CAAAuD,IAAA,CAAAH,cAAA;;MAEA;MACA,IAAAI,KAAA;MACA,IAAAC,KAAA,OAAAC,IAAA;MACA,SAAAC,CAAA,OAAAA,CAAA,OAAAA,CAAA;QACA,IAAAC,IAAA,OAAAF,IAAA,CAAAD,KAAA;QACAG,IAAA,CAAAC,OAAA,CAAAD,IAAA,CAAAE,OAAA,KAAAH,CAAA;QACAH,KAAA,CAAAO,IAAA,CAAAH,IAAA,CAAAI,QAAA,eAAAJ,IAAA,CAAAE,OAAA;MACA;;MAEA;MACA,IAAAG,SAAA,QAAAC,kBAAA;MACA,IAAAC,UAAA,QAAAD,kBAAA;MACA,IAAAE,UAAA,QAAAF,kBAAA;MACA,IAAAG,UAAA,QAAAH,kBAAA;MACA,IAAAI,SAAA,QAAAJ,kBAAA;MAEA,IAAAK,MAAA;QACAC,OAAA;UACAC,OAAA;UACAC,WAAA;YACA1D,IAAA;YACA2D,KAAA;cACAC,eAAA;YACA;UACA;UACAC,SAAA,WAAAA,UAAAC,MAAA;YACA,IAAAC,MAAA,GAAAD,MAAA,IAAA3E,IAAA;YACA2E,MAAA,CAAAE,OAAA,WAAAC,KAAA;cACAF,MAAA,2BAAAG,MAAA,CAAAD,KAAA,CAAAvD,KAAA,uBAAAwD,MAAA,CAAAD,KAAA,CAAAE,UAAA,QAAAD,MAAA,CAAAD,KAAA,CAAAzD,KAAA;YACA;YACA,OAAAuD,MAAA;UACA;QACA;QACAK,MAAA;UACAhF,IAAA;UACAiF,GAAA;UACAC,SAAA;YACAC,QAAA;UACA;QACA;QACAC,IAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;UACAN,GAAA;UACAO,YAAA;QACA;QACAC,KAAA;UACA7E,IAAA;UACA8E,WAAA;UACA1F,IAAA,EAAAoD,KAAA;UACAuC,SAAA;YACAR,QAAA;YACA7D,KAAA;UACA;UACAsE,QAAA;YACAC,SAAA;cACAvE,KAAA;YACA;UACA;QACA;QACAwE,KAAA;UACAlF,IAAA;UACA+E,SAAA;YACAR,QAAA;YACA7D,KAAA;UACA;UACAsE,QAAA;YACAC,SAAA;cACAvE,KAAA;YACA;UACA;UACAyE,SAAA;YACAF,SAAA;cACAvE,KAAA;YACA;UACA;QACA;QACA0E,MAAA,GACA;UACAjG,IAAA;UACAa,IAAA;UACAqF,KAAA;UACAC,SAAA;YACA5E,KAAA,MAAA1B,OAAA,CAAAuG,OAAA,CAAAC,cAAA,cACA;cAAAC,MAAA;cAAA/E,KAAA;YAAA,GACA;cAAA+E,MAAA;cAAA/E,KAAA;YAAA,EACA;UACA;UACAuE,SAAA;YACAvE,KAAA;YACAgF,KAAA;UACA;UACAC,SAAA;YACAjF,KAAA;UACA;UACAtB,IAAA,EAAA6D;QACA,GACA;UACA9D,IAAA;UACAa,IAAA;UACAqF,KAAA;UACAC,SAAA;YACA5E,KAAA,MAAA1B,OAAA,CAAAuG,OAAA,CAAAC,cAAA,cACA;cAAAC,MAAA;cAAA/E,KAAA;YAAA,GACA;cAAA+E,MAAA;cAAA/E,KAAA;YAAA,EACA;UACA;UACAuE,SAAA;YACAvE,KAAA;YACAgF,KAAA;UACA;UACAC,SAAA;YACAjF,KAAA;UACA;UACAtB,IAAA,EAAA+D;QACA,GACA;UACAhE,IAAA;UACAa,IAAA;UACAqF,KAAA;UACAC,SAAA;YACA5E,KAAA,MAAA1B,OAAA,CAAAuG,OAAA,CAAAC,cAAA,cACA;cAAAC,MAAA;cAAA/E,KAAA;YAAA,GACA;cAAA+E,MAAA;cAAA/E,KAAA;YAAA,EACA;UACA;UACAuE,SAAA;YACAvE,KAAA;YACAgF,KAAA;UACA;UACAC,SAAA;YACAjF,KAAA;UACA;UACAtB,IAAA,EAAAgE;QACA,GACA;UACAjE,IAAA;UACAa,IAAA;UACAqF,KAAA;UACAC,SAAA;YACA5E,KAAA,MAAA1B,OAAA,CAAAuG,OAAA,CAAAC,cAAA,cACA;cAAAC,MAAA;cAAA/E,KAAA;YAAA,GACA;cAAA+E,MAAA;cAAA/E,KAAA;YAAA,EACA;UACA;UACAuE,SAAA;YACAvE,KAAA;YACAgF,KAAA;UACA;UACAC,SAAA;YACAjF,KAAA;UACA;UACAtB,IAAA,EAAAiE;QACA,GACA;UACAlE,IAAA;UACAa,IAAA;UACAqF,KAAA;UACAC,SAAA;YACA5E,KAAA,MAAA1B,OAAA,CAAAuG,OAAA,CAAAC,cAAA,cACA;cAAAC,MAAA;cAAA/E,KAAA;YAAA,GACA;cAAA+E,MAAA;cAAA/E,KAAA;YAAA,EACA;UACA;UACAuE,SAAA;YACAvE,KAAA;YACAgF,KAAA;UACA;UACAC,SAAA;YACAjF,KAAA;UACA;UACAtB,IAAA,EAAAkE;QACA;MAEA;MAEA,KAAAzD,UAAA,CAAA+F,SAAA,CAAArC,MAAA;IACA;IACAtB,OAAA,WAAAA,QAAA;MACA;MACAL,OAAA,CAAAC,GAAA;IACA;IACAK,iBAAA,WAAAA,kBAAA;MACA;MACA,IAAAE,cAAA,GAAAC,QAAA,CAAAC,cAAA;MACA,KAAAF,cAAA;MAEA,KAAAzC,aAAA,GAAAX,OAAA,CAAAuD,IAAA,CAAAH,cAAA;;MAEA;MACA,IAAAI,KAAA;MACA,IAAAC,KAAA,OAAAC,IAAA;MACA,SAAAC,CAAA,OAAAA,CAAA,OAAAA,CAAA;QACA,IAAAC,IAAA,OAAAF,IAAA,CAAAD,KAAA;QACAG,IAAA,CAAAC,OAAA,CAAAD,IAAA,CAAAE,OAAA,KAAAH,CAAA;QACAH,KAAA,CAAAO,IAAA,CAAAH,IAAA,CAAAI,QAAA,eAAAJ,IAAA,CAAAE,OAAA;MACA;;MAEA;MACA,IAAAG,SAAA,QAAAC,kBAAA;MACA,IAAAC,UAAA,QAAAD,kBAAA;MACA,IAAAE,UAAA,QAAAF,kBAAA;MACA,IAAAG,UAAA,QAAAH,kBAAA;MACA,IAAAI,SAAA,QAAAJ,kBAAA;MAEA,IAAAK,MAAA;QACAtD,KAAA;UACA4F,IAAA;UACApB,IAAA;UACAH,SAAA;YACAC,QAAA;YACAuB,UAAA;YACApF,KAAA;UACA;QACA;QACA8C,OAAA;UACAC,OAAA;UACAC,WAAA;YACA1D,IAAA;YACA2D,KAAA;cACAC,eAAA;YACA;UACA;UACAC,SAAA,WAAAA,UAAAC,MAAA;YACA,IAAAC,MAAA,GAAAD,MAAA,IAAA3E,IAAA;YACA2E,MAAA,CAAAE,OAAA,WAAAC,KAAA;cACAF,MAAA,2BAAAG,MAAA,CAAAD,KAAA,CAAAvD,KAAA,uBAAAwD,MAAA,CAAAD,KAAA,CAAAE,UAAA,QAAAD,MAAA,CAAAD,KAAA,CAAAzD,KAAA;YACA;YACA,OAAAuD,MAAA;UACA;QACA;QACAK,MAAA;UACAhF,IAAA;UACAiF,GAAA;UACAC,SAAA;YACAC,QAAA;UACA;QACA;QACAC,IAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;UACAN,GAAA;UACAO,YAAA;QACA;QACAC,KAAA;UACA7E,IAAA;UACA8E,WAAA;UACA1F,IAAA,EAAAoD,KAAA;UACAuC,SAAA;YACAR,QAAA;YACA7D,KAAA;UACA;UACAsE,QAAA;YACAC,SAAA;cACAvE,KAAA;YACA;UACA;QACA;QACAwE,KAAA;UACAlF,IAAA;UACA+E,SAAA;YACAR,QAAA;YACA7D,KAAA;UACA;UACAsE,QAAA;YACAC,SAAA;cACAvE,KAAA;YACA;UACA;UACAyE,SAAA;YACAF,SAAA;cACAvE,KAAA;YACA;UACA;QACA;QACA0E,MAAA,GACA;UACAjG,IAAA;UACAa,IAAA;UACAqF,KAAA;UACAC,SAAA;YACA5E,KAAA,MAAA1B,OAAA,CAAAuG,OAAA,CAAAC,cAAA,cACA;cAAAC,MAAA;cAAA/E,KAAA;YAAA,GACA;cAAA+E,MAAA;cAAA/E,KAAA;YAAA,EACA;UACA;UACAuE,SAAA;YACAvE,KAAA;YACAgF,KAAA;UACA;UACAC,SAAA;YACAjF,KAAA;UACA;UACAtB,IAAA,EAAA6D;QACA,GACA;UACA9D,IAAA;UACAa,IAAA;UACAqF,KAAA;UACAC,SAAA;YACA5E,KAAA,MAAA1B,OAAA,CAAAuG,OAAA,CAAAC,cAAA,cACA;cAAAC,MAAA;cAAA/E,KAAA;YAAA,GACA;cAAA+E,MAAA;cAAA/E,KAAA;YAAA,EACA;UACA;UACAuE,SAAA;YACAvE,KAAA;YACAgF,KAAA;UACA;UACAC,SAAA;YACAjF,KAAA;UACA;UACAtB,IAAA,EAAA+D;QACA,GACA;UACAhE,IAAA;UACAa,IAAA;UACAqF,KAAA;UACAC,SAAA;YACA5E,KAAA,MAAA1B,OAAA,CAAAuG,OAAA,CAAAC,cAAA,cACA;cAAAC,MAAA;cAAA/E,KAAA;YAAA,GACA;cAAA+E,MAAA;cAAA/E,KAAA;YAAA,EACA;UACA;UACAuE,SAAA;YACAvE,KAAA;YACAgF,KAAA;UACA;UACAC,SAAA;YACAjF,KAAA;UACA;UACAtB,IAAA,EAAAgE;QACA,GACA;UACAjE,IAAA;UACAa,IAAA;UACAqF,KAAA;UACAC,SAAA;YACA5E,KAAA,MAAA1B,OAAA,CAAAuG,OAAA,CAAAC,cAAA,cACA;cAAAC,MAAA;cAAA/E,KAAA;YAAA,GACA;cAAA+E,MAAA;cAAA/E,KAAA;YAAA,EACA;UACA;UACAuE,SAAA;YACAvE,KAAA;YACAgF,KAAA;UACA;UACAC,SAAA;YACAjF,KAAA;UACA;UACAtB,IAAA,EAAAiE;QACA,GACA;UACAlE,IAAA;UACAa,IAAA;UACAqF,KAAA;UACAC,SAAA;YACA5E,KAAA,MAAA1B,OAAA,CAAAuG,OAAA,CAAAC,cAAA,cACA;cAAAC,MAAA;cAAA/E,KAAA;YAAA,GACA;cAAA+E,MAAA;cAAA/E,KAAA;YAAA,EACA;UACA;UACAuE,SAAA;YACAvE,KAAA;YACAgF,KAAA;UACA;UACAC,SAAA;YACAjF,KAAA;UACA;UACAtB,IAAA,EAAAkE;QACA;MAEA;MAEA,KAAA3D,aAAA,CAAAiG,SAAA,CAAArC,MAAA;IACA;IACApB,kBAAA,WAAAA,mBAAA;MACA;MACA,IAAAC,cAAA,GAAAC,QAAA,CAAAC,cAAA;MACA,KAAAF,cAAA;MAEA,KAAAxC,cAAA,GAAAZ,OAAA,CAAAuD,IAAA,CAAAH,cAAA;MAEA,IAAAmB,MAAA;QACAtD,KAAA;UACA4F,IAAA;UACApB,IAAA;UACAH,SAAA;YACAC,QAAA;YACAuB,UAAA;YACApF,KAAA;UACA;QACA;QACA8C,OAAA;UACAC,OAAA;UACAI,SAAA;QACA;QACAO,MAAA;UACA2B,MAAA;UACAtB,IAAA;UACAJ,GAAA;UACAjF,IAAA;UACAkF,SAAA;YACAC,QAAA;UACA;QACA;QACAa,MAAA,GACA;UACAjG,IAAA;UACAa,IAAA;UACAgG,MAAA;UACAC,MAAA;UACAC,iBAAA;UACAvC,KAAA;YACAwC,IAAA;YACAC,QAAA;UACA;UACAC,QAAA;YACA1C,KAAA;cACAwC,IAAA;cACA5B,QAAA;cACAuB,UAAA;YACA;UACA;UACAQ,SAAA;YACAH,IAAA;UACA;UACA/G,IAAA,GACA;YACAD,IAAA;YACAqB,KAAA;YACAmF,SAAA;cACAjF,KAAA;YACA;UACA,GACA;YACAvB,IAAA;YACAqB,KAAA;YACAmF,SAAA;cACAjF,KAAA;YACA;UACA,GACA;YACAvB,IAAA;YACAqB,KAAA;YACAmF,SAAA;cACAjF,KAAA;YACA;UACA;QAEA;MAEA;MAEA,KAAAd,cAAA,CAAAgG,SAAA,CAAArC,MAAA;IACA;IACA;IACAL,kBAAA,WAAAA,mBAAAqD,KAAA,EAAAC,GAAA,EAAAC,GAAA;MACA,IAAArH,IAAA;MACA,SAAAuD,CAAA,MAAAA,CAAA,GAAA4D,KAAA,EAAA5D,CAAA;QACA,IAAAnC,KAAA,GAAAkG,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,MAAAH,GAAA,GAAAD,GAAA,SAAAA,GAAA;QACApH,IAAA,CAAA2D,IAAA,CAAAvC,KAAA;MACA;MACA,OAAApB,IAAA;IACA;EACA;AACA", "ignoreList": []}]}