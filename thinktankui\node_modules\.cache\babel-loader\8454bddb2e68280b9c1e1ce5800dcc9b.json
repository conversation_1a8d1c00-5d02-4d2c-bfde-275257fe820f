{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\plugins\\tab.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\plugins\\tab.js", "mtime": 1749104047629}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749104418875}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_store", "_interopRequireDefault", "require", "_router", "_default", "exports", "default", "refreshPage", "obj", "_router$currentRoute", "router", "currentRoute", "path", "query", "matched", "undefined", "for<PERSON>ach", "m", "components", "name", "includes", "store", "dispatch", "then", "_obj", "replace", "closeOpenPage", "push", "closePage", "_ref", "visitedViews", "latestView", "slice", "fullPath", "closeAllPage", "closeLeftPage", "closeRightPage", "closeOtherPage", "openPage", "title", "url", "params", "meta", "updatePage"], "sources": ["D:/thinktank/thinktankui/src/plugins/tab.js"], "sourcesContent": ["import store from '@/store'\r\nimport router from '@/router';\r\n\r\nexport default {\r\n  // 刷新当前tab页签\r\n  refreshPage(obj) {\r\n    const { path, query, matched } = router.currentRoute;\r\n    if (obj === undefined) {\r\n      matched.forEach((m) => {\r\n        if (m.components && m.components.default && m.components.default.name) {\r\n          if (!['Layout', 'ParentView'].includes(m.components.default.name)) {\r\n            obj = { name: m.components.default.name, path: path, query: query };\r\n          }\r\n        }\r\n      });\r\n    }\r\n    return store.dispatch('tagsView/delCachedView', obj).then(() => {\r\n      const { path, query } = obj\r\n      router.replace({\r\n        path: '/redirect' + path,\r\n        query: query\r\n      })\r\n    })\r\n  },\r\n  // 关闭当前tab页签，打开新页签\r\n  closeOpenPage(obj) {\r\n    store.dispatch(\"tagsView/delView\", router.currentRoute);\r\n    if (obj !== undefined) {\r\n      return router.push(obj);\r\n    }\r\n  },\r\n  // 关闭指定tab页签\r\n  closePage(obj) {\r\n    if (obj === undefined) {\r\n      return store.dispatch('tagsView/delView', router.currentRoute).then(({ visitedViews }) => {\r\n        const latestView = visitedViews.slice(-1)[0]\r\n        if (latestView) {\r\n          return router.push(latestView.fullPath)\r\n        }\r\n        return router.push('/');\r\n      });\r\n    }\r\n    return store.dispatch('tagsView/delView', obj);\r\n  },\r\n  // 关闭所有tab页签\r\n  closeAllPage() {\r\n    return store.dispatch('tagsView/delAllViews');\r\n  },\r\n  // 关闭左侧tab页签\r\n  closeLeftPage(obj) {\r\n    return store.dispatch('tagsView/delLeftTags', obj || router.currentRoute);\r\n  },\r\n  // 关闭右侧tab页签\r\n  closeRightPage(obj) {\r\n    return store.dispatch('tagsView/delRightTags', obj || router.currentRoute);\r\n  },\r\n  // 关闭其他tab页签\r\n  closeOtherPage(obj) {\r\n    return store.dispatch('tagsView/delOthersViews', obj || router.currentRoute);\r\n  },\r\n  // 添加tab页签\r\n  openPage(title, url, params) {\r\n    const obj = { path: url, meta: { title: title } }\r\n    store.dispatch('tagsView/addView', obj);\r\n    return router.push({ path: url, query: params });\r\n  },\r\n  // 修改tab页签\r\n  updatePage(obj) {\r\n    return store.dispatch('tagsView/updateVisitedView', obj);\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAF,sBAAA,CAAAC,OAAA;AAA8B,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEf;EACb;EACAC,WAAW,WAAXA,WAAWA,CAACC,GAAG,EAAE;IACf,IAAAC,oBAAA,GAAiCC,eAAM,CAACC,YAAY;MAA5CC,IAAI,GAAAH,oBAAA,CAAJG,IAAI;MAAEC,KAAK,GAAAJ,oBAAA,CAALI,KAAK;MAAEC,OAAO,GAAAL,oBAAA,CAAPK,OAAO;IAC5B,IAAIN,GAAG,KAAKO,SAAS,EAAE;MACrBD,OAAO,CAACE,OAAO,CAAC,UAACC,CAAC,EAAK;QACrB,IAAIA,CAAC,CAACC,UAAU,IAAID,CAAC,CAACC,UAAU,CAACZ,OAAO,IAAIW,CAAC,CAACC,UAAU,CAACZ,OAAO,CAACa,IAAI,EAAE;UACrE,IAAI,CAAC,CAAC,QAAQ,EAAE,YAAY,CAAC,CAACC,QAAQ,CAACH,CAAC,CAACC,UAAU,CAACZ,OAAO,CAACa,IAAI,CAAC,EAAE;YACjEX,GAAG,GAAG;cAAEW,IAAI,EAAEF,CAAC,CAACC,UAAU,CAACZ,OAAO,CAACa,IAAI;cAAEP,IAAI,EAAEA,IAAI;cAAEC,KAAK,EAAEA;YAAM,CAAC;UACrE;QACF;MACF,CAAC,CAAC;IACJ;IACA,OAAOQ,cAAK,CAACC,QAAQ,CAAC,wBAAwB,EAAEd,GAAG,CAAC,CAACe,IAAI,CAAC,YAAM;MAC9D,IAAAC,IAAA,GAAwBhB,GAAG;QAAnBI,IAAI,GAAAY,IAAA,CAAJZ,IAAI;QAAEC,KAAK,GAAAW,IAAA,CAALX,KAAK;MACnBH,eAAM,CAACe,OAAO,CAAC;QACbb,IAAI,EAAE,WAAW,GAAGA,IAAI;QACxBC,KAAK,EAAEA;MACT,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD;EACAa,aAAa,WAAbA,aAAaA,CAAClB,GAAG,EAAE;IACjBa,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAEZ,eAAM,CAACC,YAAY,CAAC;IACvD,IAAIH,GAAG,KAAKO,SAAS,EAAE;MACrB,OAAOL,eAAM,CAACiB,IAAI,CAACnB,GAAG,CAAC;IACzB;EACF,CAAC;EACD;EACAoB,SAAS,WAATA,SAASA,CAACpB,GAAG,EAAE;IACb,IAAIA,GAAG,KAAKO,SAAS,EAAE;MACrB,OAAOM,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAEZ,eAAM,CAACC,YAAY,CAAC,CAACY,IAAI,CAAC,UAAAM,IAAA,EAAsB;QAAA,IAAnBC,YAAY,GAAAD,IAAA,CAAZC,YAAY;QACjF,IAAMC,UAAU,GAAGD,YAAY,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,IAAID,UAAU,EAAE;UACd,OAAOrB,eAAM,CAACiB,IAAI,CAACI,UAAU,CAACE,QAAQ,CAAC;QACzC;QACA,OAAOvB,eAAM,CAACiB,IAAI,CAAC,GAAG,CAAC;MACzB,CAAC,CAAC;IACJ;IACA,OAAON,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAEd,GAAG,CAAC;EAChD,CAAC;EACD;EACA0B,YAAY,WAAZA,YAAYA,CAAA,EAAG;IACb,OAAOb,cAAK,CAACC,QAAQ,CAAC,sBAAsB,CAAC;EAC/C,CAAC;EACD;EACAa,aAAa,WAAbA,aAAaA,CAAC3B,GAAG,EAAE;IACjB,OAAOa,cAAK,CAACC,QAAQ,CAAC,sBAAsB,EAAEd,GAAG,IAAIE,eAAM,CAACC,YAAY,CAAC;EAC3E,CAAC;EACD;EACAyB,cAAc,WAAdA,cAAcA,CAAC5B,GAAG,EAAE;IAClB,OAAOa,cAAK,CAACC,QAAQ,CAAC,uBAAuB,EAAEd,GAAG,IAAIE,eAAM,CAACC,YAAY,CAAC;EAC5E,CAAC;EACD;EACA0B,cAAc,WAAdA,cAAcA,CAAC7B,GAAG,EAAE;IAClB,OAAOa,cAAK,CAACC,QAAQ,CAAC,yBAAyB,EAAEd,GAAG,IAAIE,eAAM,CAACC,YAAY,CAAC;EAC9E,CAAC;EACD;EACA2B,QAAQ,WAARA,QAAQA,CAACC,KAAK,EAAEC,GAAG,EAAEC,MAAM,EAAE;IAC3B,IAAMjC,GAAG,GAAG;MAAEI,IAAI,EAAE4B,GAAG;MAAEE,IAAI,EAAE;QAAEH,KAAK,EAAEA;MAAM;IAAE,CAAC;IACjDlB,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAEd,GAAG,CAAC;IACvC,OAAOE,eAAM,CAACiB,IAAI,CAAC;MAAEf,IAAI,EAAE4B,GAAG;MAAE3B,KAAK,EAAE4B;IAAO,CAAC,CAAC;EAClD,CAAC;EACD;EACAE,UAAU,WAAVA,UAAUA,CAACnC,GAAG,EAAE;IACd,OAAOa,cAAK,CAACC,QAAQ,CAAC,4BAA4B,EAAEd,GAAG,CAAC;EAC1D;AACF,CAAC", "ignoreList": []}]}