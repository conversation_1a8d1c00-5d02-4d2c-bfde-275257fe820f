{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\dashboard\\index.vue?vue&type=template&id=106c86ed&scoped=true", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\dashboard\\index.vue", "mtime": 1749104047639}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749104421286}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uICgpIHsKICB2YXIgX3ZtID0gdGhpcwogIHZhciBfaCA9IF92bS4kY3JlYXRlRWxlbWVudAogIHZhciBfYyA9IF92bS5fc2VsZi5fYyB8fCBfaAogIHJldHVybiBfYygiZGl2IiwgWwogICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJwYWdlLWhlYWRlci1jb250ZW50IiB9LCBbCiAgICAgIF9jKAogICAgICAgICJkaXYiLAogICAgICAgIHsgc3RhdGljQ2xhc3M6ICJhdmF0YXIiIH0sCiAgICAgICAgWwogICAgICAgICAgX2MoImEtYXZhdGFyIiwgewogICAgICAgICAgICBhdHRyczogeyBzaXplOiAibGFyZ2UiLCBzcmM6IF92bS5jdXJyZW50VXNlci5hdmF0YXIgfSwKICAgICAgICAgIH0pLAogICAgICAgIF0sCiAgICAgICAgMQogICAgICApLAogICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogImNvbnRlbnQiIH0sIFsKICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogImNvbnRlbnQtdGl0bGUiIH0sIFsKICAgICAgICAgIF92bS5fdigiICIgKyBfdm0uX3MoX3ZtLmN1cnJlbnRVc2VyLm5hbWUpKSwKICAgICAgICAgIF9jKCJzcGFuIiwgeyBzdGF0aWNDbGFzczogIndlbGNvbWUtdGV4dCIgfSwgWwogICAgICAgICAgICBfdm0uX3YoIu+8jOasoui/juS9v+eUqOiIhuaDheebkeaOp+ezu+e7nyIpLAogICAgICAgICAgXSksCiAgICAgICAgXSksCiAgICAgICAgX2MoImRpdiIsIFsKICAgICAgICAgIF92bS5fdigKICAgICAgICAgICAgX3ZtLl9zKF92bS5jdXJyZW50VXNlci50aXRsZSkgKwogICAgICAgICAgICAgICIgfCAiICsKICAgICAgICAgICAgICBfdm0uX3MoX3ZtLmN1cnJlbnRVc2VyLmdyb3VwKQogICAgICAgICAgKSwKICAgICAgICBdKSwKICAgICAgXSksCiAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAiZXh0cmEtY29udGVudCIgfSwgWwogICAgICAgIF9jKAogICAgICAgICAgImRpdiIsCiAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAic3RhdC1pdGVtIiB9LAogICAgICAgICAgWwogICAgICAgICAgICBfYygiYS1zdGF0aXN0aWMiLCB7CiAgICAgICAgICAgICAgYXR0cnM6IHsgdGl0bGU6ICLku4rml6Xng63ngrkiLCB2YWx1ZTogX3ZtLmhvdFRvcGljc0NvdW50IH0sCiAgICAgICAgICAgIH0pLAogICAgICAgICAgXSwKICAgICAgICAgIDEKICAgICAgICApLAogICAgICAgIF9jKAogICAgICAgICAgImRpdiIsCiAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAic3RhdC1pdGVtIiB9LAogICAgICAgICAgWwogICAgICAgICAgICBfYygiYS1zdGF0aXN0aWMiLCB7CiAgICAgICAgICAgICAgYXR0cnM6IHsgdGl0bGU6ICLotJ/pnaLkv6Hmga8iLCB2YWx1ZTogX3ZtLm5lZ2F0aXZlQ291bnQgfSwKICAgICAgICAgICAgfSksCiAgICAgICAgICBdLAogICAgICAgICAgMQogICAgICAgICksCiAgICAgICAgX2MoCiAgICAgICAgICAiZGl2IiwKICAgICAgICAgIHsgc3RhdGljQ2xhc3M6ICJzdGF0LWl0ZW0iIH0sCiAgICAgICAgICBbCiAgICAgICAgICAgIF9jKCJhLXN0YXRpc3RpYyIsIHsKICAgICAgICAgICAgICBhdHRyczogeyB0aXRsZTogIuaAu+ebkeaOp+mHjyIsIHZhbHVlOiBfdm0udG90YWxNb25pdG9yQ291bnQgfSwKICAgICAgICAgICAgfSksCiAgICAgICAgICBdLAogICAgICAgICAgMQogICAgICAgICksCiAgICAgIF0pLAogICAgXSksCiAgICBfYygKICAgICAgImRpdiIsCiAgICAgIFsKICAgICAgICBfYygKICAgICAgICAgICJhLXJvdyIsCiAgICAgICAgICB7IGF0dHJzOiB7IGd1dHRlcjogMjQgfSB9LAogICAgICAgICAgWwogICAgICAgICAgICBfYygKICAgICAgICAgICAgICAiYS1jb2wiLAogICAgICAgICAgICAgIHsgYXR0cnM6IHsgeGw6IDE2LCBsZzogMjQsIG1kOiAyNCwgc206IDI0LCB4czogMjQgfSB9LAogICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAiYS1jYXJkIiwKICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgIHN0YXRpY1N0eWxlOiB7ICJtYXJnaW4tYm90dG9tIjogIjI0cHgiIH0sCiAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICAgIGxvYWRpbmc6IF92bS5sb2FkaW5nLAogICAgICAgICAgICAgICAgICAgICAgYm9yZGVyZWQ6IGZhbHNlLAogICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICIyNOWwj+aXtuS8oOaSrei2i+WKvyIsCiAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgImRpdiIsCiAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgIHN0YXRpY1N0eWxlOiB7IGhlaWdodDogIjQwMHB4IiwgcG9zaXRpb246ICJyZWxhdGl2ZSIgfSwKICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgICAgICJ2LWNoYXJ0IiwKICAgICAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb3JjZUZpdDogdHJ1ZSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAiNDAwIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YTogX3ZtLnRyZW5kRGF0YSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2NhbGU6IF92bS50cmVuZFNjYWxlLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJ2LXRvb2x0aXAiLCB7IGF0dHJzOiB7IGNyb3NzaGFpcnM6ICIiIH0gfSksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfYygidi1heGlzIiwgeyBhdHRyczogeyBkYXRhS2V5OiAidGltZSIgfSB9KSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJ2LWF4aXMiLCB7IGF0dHJzOiB7IGRhdGFLZXk6ICJ2YWx1ZSIgfSB9KSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJ2LWxlZ2VuZCIpLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoInYtbGluZSIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwb3NpdGlvbjogInRpbWUqdmFsdWUiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAidHlwZSIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZTogMiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoInYtcG9pbnQiLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcG9zaXRpb246ICJ0aW1lKnZhbHVlIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogInR5cGUiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU6IDQsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2hhcGU6ICJjaXJjbGUiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgICAgICAgICAgICAxCiAgICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAgICAgMQogICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgIF0KICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgImEtcm93IiwKICAgICAgICAgICAgICAgICAgeyBhdHRyczogeyBndXR0ZXI6IDI0IH0gfSwKICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgImEtY29sIiwKICAgICAgICAgICAgICAgICAgICAgIHsgYXR0cnM6IHsgc3BhbjogMTIgfSB9LAogICAgICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAiYS1jYXJkIiwKICAgICAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0aWNTdHlsZTogeyAibWFyZ2luLWJvdHRvbSI6ICIyNHB4IiB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbG9hZGluZzogX3ZtLmxvYWRpbmcsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlcmVkOiBmYWxzZSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICLlubPlj7DmnaXmupDljaDmr5QiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAiZGl2IiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyBzdGF0aWNTdHlsZTogeyBoZWlnaHQ6ICIzMDBweCIgfSB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAidi1jaGFydCIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9yY2VGaXQ6IHRydWUsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAiMzAwIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhOiBfdm0ucGxhdGZvcm1EYXRhLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNjYWxlOiBfdm0ucGxhdGZvcm1TY2FsZSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJ2LXRvb2x0aXAiKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoInYtbGVnZW5kIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IGRhdGFLZXk6ICJ0eXBlIiB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoInYtY29vcmQiLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICJ0aGV0YSIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByYWRpdXM6ICIwLjc1IiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlubmVyUmFkaXVzOiAiMC42IiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoInYtcGllIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwb3NpdGlvbjogInBlcmNlbnQiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICJ0eXBlIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsOiBfdm0ubGFiZWxDb25maWcsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgMQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDEKICAgICAgICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICAgICAgXQogICAgICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgICAgICAgIDEKICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgImEtY29sIiwKICAgICAgICAgICAgICAgICAgICAgIHsgYXR0cnM6IHsgc3BhbjogMTIgfSB9LAogICAgICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAiYS1jYXJkIiwKICAgICAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0aWNTdHlsZTogeyAibWFyZ2luLWJvdHRvbSI6ICIyNHB4IiB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbG9hZGluZzogX3ZtLmxvYWRpbmcsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlcmVkOiBmYWxzZSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICLmg4XmhJ/liIbluIPljaDmr5QiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAiZGl2IiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyBzdGF0aWNTdHlsZTogeyBoZWlnaHQ6ICIzMDBweCIgfSB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAidi1jaGFydCIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9yY2VGaXQ6IHRydWUsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAiMzAwIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhOiBfdm0uc2VudGltZW50RGF0YSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzY2FsZTogX3ZtLnNlbnRpbWVudFNjYWxlLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoInYtdG9vbHRpcCIpLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfYygidi1heGlzIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IGRhdGFLZXk6ICJ0aW1lIiB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoInYtYXhpcyIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogeyBkYXRhS2V5OiAidmFsdWUiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfYygidi1sZWdlbmQiLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgZGF0YUtleTogInR5cGUiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfYygidi1hcmVhIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwb3NpdGlvbjogInRpbWUqdmFsdWUiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICJ0eXBlIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wYWNpdHk6IDAuNiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAxCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgMQogICAgICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgICAgICBdCiAgICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAgICAgMQogICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgIDEKICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgImEtY2FyZCIsCiAgICAgICAgICAgICAgICAgIHsgYXR0cnM6IHsgbG9hZGluZzogX3ZtLmxvYWRpbmcsIGJvcmRlcmVkOiBmYWxzZSB9IH0sCiAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICBfYygidGVtcGxhdGUiLCB7IHNsb3Q6ICJ0aXRsZSIgfSwgWwogICAgICAgICAgICAgICAgICAgICAgX2MoImkiLCB7CiAgICAgICAgICAgICAgICAgICAgICAgIHN0YXRpY0NsYXNzOiAiZWwtaWNvbi1ob3QiLAogICAgICAgICAgICAgICAgICAgICAgICBzdGF0aWNTdHlsZTogewogICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAiI0Y1MjIyRCIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgIm1hcmdpbi1yaWdodCI6ICI4cHgiLAogICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgICAgICBfdm0uX3YoIiDng63pl6jmlofnq6AgIiksCiAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgICAiYS1saXN0IiwKICAgICAgICAgICAgICAgICAgICAgIF92bS5fbChfdm0uaG90QXJ0aWNsZXMsIGZ1bmN0aW9uIChpdGVtLCBpbmRleCkgewogICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgImEtbGlzdC1pdGVtIiwKICAgICAgICAgICAgICAgICAgICAgICAgICB7IGtleTogaW5kZXggfSwKICAgICAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgImEtbGlzdC1pdGVtLW1ldGEiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAidGVtcGxhdGUiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyBzbG90OiAiYXZhdGFyIiB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAiYS10YWciLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsgYXR0cnM6IHsgY29sb3I6IGl0ZW0udGFnQ29sb3IgfSB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFtfdm0uX3YoX3ZtLl9zKGl0ZW0udGFnKSldCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgMQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAiZGl2IiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsgYXR0cnM6IHsgc2xvdDogInRpdGxlIiB9LCBzbG90OiAidGl0bGUiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJhIiwgeyBhdHRyczogeyBocmVmOiBpdGVtLmxpbmsgfSB9LCBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX3ZtLl92KF92bS5fcyhpdGVtLnRpdGxlKSksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAiZGl2IiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgc2xvdDogImRlc2NyaXB0aW9uIiB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzbG90OiAiZGVzY3JpcHRpb24iLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoInNwYW4iLCBbX3ZtLl92KF92bS5fcyhpdGVtLnNvdXJjZSkpXSksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICJzcGFuIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7IHN0YXRpY1N0eWxlOiB7IGZsb2F0OiAicmlnaHQiIH0gfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbX3ZtLl92KF92bS5fcyhpdGVtLnB1Ymxpc2hUaW1lKSldCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgMgogICAgICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAgICAgICAgIDEKICAgICAgICAgICAgICAgICAgICAgICAgKQogICAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgICAgICAxCiAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgICAgMgogICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgIDEKICAgICAgICAgICAgKSwKICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgImEtY29sIiwKICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICBzdGF0aWNTdHlsZTogeyBwYWRkaW5nOiAiMCAxMnB4IiB9LAogICAgICAgICAgICAgICAgYXR0cnM6IHsgeGw6IDgsIGxnOiAyNCwgbWQ6IDI0LCBzbTogMjQsIHhzOiAyNCB9LAogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICJhLWNhcmQiLAogICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgc3RhdGljU3R5bGU6IHsgIm1hcmdpbi1ib3R0b20iOiAiMjRweCIgfSwKICAgICAgICAgICAgICAgICAgICBhdHRyczogeyBib3JkZXJlZDogZmFsc2UgfSwKICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgIF9jKCJ0ZW1wbGF0ZSIsIHsgc2xvdDogInRpdGxlIiB9LCBbCiAgICAgICAgICAgICAgICAgICAgICBfYygiaSIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgc3RhdGljQ2xhc3M6ICJlbC1pY29uLXdhcm5pbmctb3V0bGluZSIsCiAgICAgICAgICAgICAgICAgICAgICAgIHN0YXRpY1N0eWxlOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICIjRkE4QzE2IiwKICAgICAgICAgICAgICAgICAgICAgICAgICAibWFyZ2luLXJpZ2h0IjogIjhweCIsCiAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgICAgIF92bS5fdigiIOmihOitpuaWueahiOiuvue9riAiKSwKICAgICAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICJkaXYiLAogICAgICAgICAgICAgICAgICAgICAgeyBzdGF0aWNDbGFzczogInNldHRpbmctYnV0dG9ucyIgfSwKICAgICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgImEtYnV0dG9uIiwKICAgICAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0aWNTdHlsZTogeyAibWFyZ2luLXJpZ2h0IjogIjhweCIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHR5cGU6ICJwcmltYXJ5IiwgaWNvbjogInVzZXIiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICBbX3ZtLl92KCLmjqXmlLbkurrorr7nva4iKV0KICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgImEtYnV0dG9uIiwKICAgICAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0aWNTdHlsZTogeyAibWFyZ2luLXJpZ2h0IjogIjhweCIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHR5cGU6ICJwcmltYXJ5IiwgaWNvbjogInNldHRpbmciIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICBbX3ZtLl92KCLpooToraborr7nva4iKV0KICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgImEtYnV0dG9uIiwKICAgICAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0aWNTdHlsZTogeyAibWFyZ2luLXJpZ2h0IjogIjhweCIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHR5cGU6ICJwcmltYXJ5IiwgaWNvbjogImJlbGwiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICBbX3ZtLl92KCLlhbPplK7or43orr7nva4iKV0KICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgImEtYnV0dG9uIiwKICAgICAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogeyB0eXBlOiAicHJpbWFyeSIsIGljb246ICJzZWFyY2giIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbjogeyBjbGljazogX3ZtLmdvVG9TZWFyY2ggfSwKICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgIFtfdm0uX3YoIuaQnOe0oua1i+ivlSIpXQogICAgICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgICAgICAgIDEKICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICAgIF9jKCJhLWRpdmlkZXIiKSwKICAgICAgICAgICAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogInN3aXRjaC1idXR0b25zIiB9LCBbCiAgICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICAgImRpdiIsCiAgICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0aWNTdHlsZTogewogICAgICAgICAgICAgICAgICAgICAgICAgICAgIm1hcmdpbi1ib3R0b20iOiAiMTZweCIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiAiZmxleCIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAianVzdGlmeS1jb250ZW50IjogInNwYWNlLWJldHdlZW4iLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgImFsaWduLWl0ZW1zIjogImNlbnRlciIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJzcGFuIiwgW192bS5fdigi6aKE6K2m5byA5YWzIildKSwKICAgICAgICAgICAgICAgICAgICAgICAgICBfYygiYS1zd2l0Y2giLCB7IGF0dHJzOiB7IGNoZWNrZWQ6ICIiIH0gfSksCiAgICAgICAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgICAgICAgIDEKICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICAgImRpdiIsCiAgICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0aWNTdHlsZTogewogICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogImZsZXgiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgImp1c3RpZnktY29udGVudCI6ICJzcGFjZS1iZXR3ZWVuIiwKICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoImEtYnV0dG9uIiwgW192bS5fdigi6Ieq5Yqo6aKE6K2mIildKSwKICAgICAgICAgICAgICAgICAgICAgICAgICBfYygiYS1idXR0b24iLCB7IGF0dHJzOiB7IHR5cGU6ICJwcmltYXJ5IiB9IH0sIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF92bS5fdigi5Lq65bel6aKE6K2mIiksCiAgICAgICAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgICAgICAgIDEKICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgIDIKICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgImEtY2FyZCIsCiAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICBzdGF0aWNTdHlsZTogeyAibWFyZ2luLWJvdHRvbSI6ICIyNHB4IiB9LAogICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IGJvcmRlcmVkOiBmYWxzZSB9LAogICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgX2MoInRlbXBsYXRlIiwgeyBzbG90OiAidGl0bGUiIH0sIFsKICAgICAgICAgICAgICAgICAgICAgIF9jKCJpIiwgewogICAgICAgICAgICAgICAgICAgICAgICBzdGF0aWNDbGFzczogImVsLWljb24tZG9jdW1lbnQiLAogICAgICAgICAgICAgICAgICAgICAgICBzdGF0aWNTdHlsZTogewogICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAiIzUyQzQxQSIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgIm1hcmdpbi1yaWdodCI6ICI4cHgiLAogICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgICAgICBfdm0uX3YoIiDmiqXlkYrmqKHmnb8gIiksCiAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgICAiYS1saXN0IiwKICAgICAgICAgICAgICAgICAgICAgIF92bS5fbChfdm0ucmVwb3J0VGVtcGxhdGVzLCBmdW5jdGlvbiAoaXRlbSwgaW5kZXgpIHsKICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIF9jKAogICAgICAgICAgICAgICAgICAgICAgICAgICJhLWxpc3QtaXRlbSIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgeyBrZXk6IGluZGV4IH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoImEtbGlzdC1pdGVtLW1ldGEiLCBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICJkaXYiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsgYXR0cnM6IHsgc2xvdDogInRpdGxlIiB9LCBzbG90OiAidGl0bGUiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgW192bS5fdihfdm0uX3MoaXRlbS50aXRsZSkpXQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAiZGl2IiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogeyBzbG90OiAiZGVzY3JpcHRpb24iIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzbG90OiAiZGVzY3JpcHRpb24iLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgW192bS5fdihfdm0uX3MoaXRlbS5jcmVhdGVUaW1lKSldCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAiZGl2IiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJhLWJ1dHRvbiIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHR5cGU6ICJsaW5rIiwgaWNvbjogImVkaXQiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoImEtYnV0dG9uIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgdHlwZTogImxpbmsiLCBpY29uOiAiY29weSIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfYygiYS1idXR0b24iLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogeyB0eXBlOiAibGluayIsIGljb246ICJkZWxldGUiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDEKICAgICAgICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgICAgICAgICAgICAxCiAgICAgICAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgMQogICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgIDIKICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgImEtY2FyZCIsCiAgICAgICAgICAgICAgICAgIHsgYXR0cnM6IHsgbG9hZGluZzogX3ZtLmxvYWRpbmcsIGJvcmRlcmVkOiBmYWxzZSB9IH0sCiAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICBfYygidGVtcGxhdGUiLCB7IHNsb3Q6ICJ0aXRsZSIgfSwgWwogICAgICAgICAgICAgICAgICAgICAgX2MoImkiLCB7CiAgICAgICAgICAgICAgICAgICAgICAgIHN0YXRpY0NsYXNzOiAiZWwtaWNvbi13YXJuaW5nIiwKICAgICAgICAgICAgICAgICAgICAgICAgc3RhdGljU3R5bGU6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogIiNGRjRENEYiLAogICAgICAgICAgICAgICAgICAgICAgICAgICJtYXJnaW4tcmlnaHQiOiAiOHB4IiwKICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgX3ZtLl92KCIg5pyA5paw6LSf6Z2iICIpLAogICAgICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgImEtbGlzdCIsCiAgICAgICAgICAgICAgICAgICAgICBfdm0uX2woX3ZtLm5lZ2F0aXZlTmV3cywgZnVuY3Rpb24gKGl0ZW0sIGluZGV4KSB7CiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAiYS1saXN0LWl0ZW0iLAogICAgICAgICAgICAgICAgICAgICAgICAgIHsga2V5OiBpbmRleCB9LAogICAgICAgICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAiYS1saXN0LWl0ZW0tbWV0YSIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICJ0ZW1wbGF0ZSIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7IHNsb3Q6ICJhdmF0YXIiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJhLWJhZGdlIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHN0YXR1czogImVycm9yIiB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAxCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICJkaXYiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyBhdHRyczogeyBzbG90OiAidGl0bGUiIH0sIHNsb3Q6ICJ0aXRsZSIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoImEiLCB7IGF0dHJzOiB7IGhyZWY6IGl0ZW0ubGluayB9IH0sIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdm0uX3YoX3ZtLl9zKGl0ZW0udGl0bGUpKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICJkaXYiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogeyBzbG90OiAiZGVzY3JpcHRpb24iIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNsb3Q6ICJkZXNjcmlwdGlvbiIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgW192bS5fdihfdm0uX3MoaXRlbS5wdWJsaXNoVGltZSkpXQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDIKICAgICAgICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgICAgICAgICAgICAxCiAgICAgICAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgMQogICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgIDIKICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAxCiAgICAgICAgICAgICksCiAgICAgICAgICBdLAogICAgICAgICAgMQogICAgICAgICksCiAgICAgIF0sCiAgICAgIDEKICAgICksCiAgXSkKfQp2YXIgc3RhdGljUmVuZGVyRm5zID0gW10KcmVuZGVyLl93aXRoU3RyaXBwZWQgPSB0cnVlCgpleHBvcnQgeyByZW5kZXIsIHN0YXRpY1JlbmRlckZucyB9"}]}