{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\permission.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\permission.js", "mtime": 1749104047629}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749104418875}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKdmFyIF9vYmplY3RTcHJlYWQyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvb2JqZWN0U3ByZWFkMi5qcyIpKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKdmFyIF9yb3V0ZXIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4vcm91dGVyIikpOwp2YXIgX3N0b3JlID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL3N0b3JlIikpOwp2YXIgX2VsZW1lbnRVaSA9IHJlcXVpcmUoImVsZW1lbnQtdWkiKTsKdmFyIF9ucHJvZ3Jlc3MgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIm5wcm9ncmVzcyIpKTsKcmVxdWlyZSgibnByb2dyZXNzL25wcm9ncmVzcy5jc3MiKTsKdmFyIF9hdXRoID0gcmVxdWlyZSgiQC91dGlscy9hdXRoIik7CnZhciBfdmFsaWRhdGUgPSByZXF1aXJlKCJAL3V0aWxzL3ZhbGlkYXRlIik7CnZhciBfcmVxdWVzdCA9IHJlcXVpcmUoIkAvdXRpbHMvcmVxdWVzdCIpOwpfbnByb2dyZXNzLmRlZmF1bHQuY29uZmlndXJlKHsKICBzaG93U3Bpbm5lcjogZmFsc2UKfSk7CnZhciB3aGl0ZUxpc3QgPSBbJy9sb2dpbicsICcvcmVnaXN0ZXInXTsKdmFyIGlzV2hpdGVMaXN0ID0gZnVuY3Rpb24gaXNXaGl0ZUxpc3QocGF0aCkgewogIHJldHVybiB3aGl0ZUxpc3Quc29tZShmdW5jdGlvbiAocGF0dGVybikgewogICAgcmV0dXJuICgwLCBfdmFsaWRhdGUuaXNQYXRoTWF0Y2gpKHBhdHRlcm4sIHBhdGgpOwogIH0pOwp9Owpfcm91dGVyLmRlZmF1bHQuYmVmb3JlRWFjaChmdW5jdGlvbiAodG8sIGZyb20sIG5leHQpIHsKICBfbnByb2dyZXNzLmRlZmF1bHQuc3RhcnQoKTsKICBpZiAoKDAsIF9hdXRoLmdldFRva2VuKSgpKSB7CiAgICB0by5tZXRhLnRpdGxlICYmIF9zdG9yZS5kZWZhdWx0LmRpc3BhdGNoKCdzZXR0aW5ncy9zZXRUaXRsZScsIHRvLm1ldGEudGl0bGUpOwogICAgLyogaGFzIHRva2VuKi8KICAgIGlmICh0by5wYXRoID09PSAnL2xvZ2luJykgewogICAgICBuZXh0KHsKICAgICAgICBwYXRoOiAnLycKICAgICAgfSk7CiAgICAgIF9ucHJvZ3Jlc3MuZGVmYXVsdC5kb25lKCk7CiAgICB9IGVsc2UgaWYgKGlzV2hpdGVMaXN0KHRvLnBhdGgpKSB7CiAgICAgIG5leHQoKTsKICAgIH0gZWxzZSB7CiAgICAgIGlmIChfc3RvcmUuZGVmYXVsdC5nZXR0ZXJzLnJvbGVzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIF9yZXF1ZXN0LmlzUmVsb2dpbi5zaG93ID0gdHJ1ZTsKICAgICAgICAvLyDliKTmlq3lvZPliY3nlKjmiLfmmK/lkKblt7Lmi4nlj5blrox1c2VyX2luZm/kv6Hmga8KICAgICAgICBfc3RvcmUuZGVmYXVsdC5kaXNwYXRjaCgnR2V0SW5mbycpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgICAgX3JlcXVlc3QuaXNSZWxvZ2luLnNob3cgPSBmYWxzZTsKICAgICAgICAgIF9zdG9yZS5kZWZhdWx0LmRpc3BhdGNoKCdHZW5lcmF0ZVJvdXRlcycpLnRoZW4oZnVuY3Rpb24gKGFjY2Vzc1JvdXRlcykgewogICAgICAgICAgICAvLyDmoLnmja5yb2xlc+adg+mZkOeUn+aIkOWPr+iuv+mXrueahOi3r+eUseihqAogICAgICAgICAgICBfcm91dGVyLmRlZmF1bHQuYWRkUm91dGVzKGFjY2Vzc1JvdXRlcyk7IC8vIOWKqOaAgea3u+WKoOWPr+iuv+mXrui3r+eUseihqAogICAgICAgICAgICBuZXh0KCgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSgoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIHRvKSwge30sIHsKICAgICAgICAgICAgICByZXBsYWNlOiB0cnVlCiAgICAgICAgICAgIH0pKTsgLy8gaGFja+aWueazlSDnoa7kv51hZGRSb3V0ZXPlt7LlrozmiJAKICAgICAgICAgIH0pOwogICAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlcnIpIHsKICAgICAgICAgIF9zdG9yZS5kZWZhdWx0LmRpc3BhdGNoKCdMb2dPdXQnKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgX2VsZW1lbnRVaS5NZXNzYWdlLmVycm9yKGVycik7CiAgICAgICAgICAgIG5leHQoewogICAgICAgICAgICAgIHBhdGg6ICcvJwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0pOwogICAgICAgIH0pOwogICAgICB9IGVsc2UgewogICAgICAgIG5leHQoKTsKICAgICAgfQogICAgfQogIH0gZWxzZSB7CiAgICAvLyDmsqHmnIl0b2tlbgogICAgaWYgKGlzV2hpdGVMaXN0KHRvLnBhdGgpKSB7CiAgICAgIC8vIOWcqOWFjeeZu+W9leeZveWQjeWNle+8jOebtOaOpei/m+WFpQogICAgICBuZXh0KCk7CiAgICB9IGVsc2UgewogICAgICBuZXh0KCIvbG9naW4/cmVkaXJlY3Q9Ii5jb25jYXQoZW5jb2RlVVJJQ29tcG9uZW50KHRvLmZ1bGxQYXRoKSkpOyAvLyDlkKbliJnlhajpg6jph43lrprlkJHliLDnmbvlvZXpobUKICAgICAgX25wcm9ncmVzcy5kZWZhdWx0LmRvbmUoKTsKICAgIH0KICB9Cn0pOwpfcm91dGVyLmRlZmF1bHQuYWZ0ZXJFYWNoKGZ1bmN0aW9uICgpIHsKICBfbnByb2dyZXNzLmRlZmF1bHQuZG9uZSgpOwp9KTs="}, {"version": 3, "names": ["_router", "_interopRequireDefault", "require", "_store", "_elementUi", "_nprogress", "_auth", "_validate", "_request", "NProgress", "configure", "showSpinner", "whiteList", "isWhiteList", "path", "some", "pattern", "isPathMatch", "router", "beforeEach", "to", "from", "next", "start", "getToken", "meta", "title", "store", "dispatch", "done", "getters", "roles", "length", "is<PERSON><PERSON>gin", "show", "then", "accessRoutes", "addRoutes", "_objectSpread2", "default", "replace", "catch", "err", "Message", "error", "concat", "encodeURIComponent", "fullPath", "after<PERSON>ach"], "sources": ["D:/thinktank/thinktankui/src/permission.js"], "sourcesContent": ["import router from './router'\r\nimport store from './store'\r\nimport { Message } from 'element-ui'\r\nimport NProgress from 'nprogress'\r\nimport 'nprogress/nprogress.css'\r\nimport { getToken } from '@/utils/auth'\r\nimport { isPathMatch } from '@/utils/validate'\r\nimport { isRelogin } from '@/utils/request'\r\n\r\nNProgress.configure({ showSpinner: false })\r\n\r\nconst whiteList = ['/login', '/register']\r\n\r\nconst isWhiteList = (path) => {\r\n  return whiteList.some(pattern => isPathMatch(pattern, path))\r\n}\r\n\r\nrouter.beforeEach((to, from, next) => {\r\n  NProgress.start()\r\n  if (getToken()) {\r\n    to.meta.title && store.dispatch('settings/setTitle', to.meta.title)\r\n    /* has token*/\r\n    if (to.path === '/login') {\r\n      next({ path: '/' })\r\n      NProgress.done()\r\n    } else if (isWhiteList(to.path)) {\r\n      next()\r\n    } else {\r\n      if (store.getters.roles.length === 0) {\r\n        isRelogin.show = true\r\n        // 判断当前用户是否已拉取完user_info信息\r\n        store.dispatch('GetInfo').then(() => {\r\n          isRelogin.show = false\r\n          store.dispatch('GenerateRoutes').then(accessRoutes => {\r\n            // 根据roles权限生成可访问的路由表\r\n            router.addRoutes(accessRoutes) // 动态添加可访问路由表\r\n            next({ ...to, replace: true }) // hack方法 确保addRoutes已完成\r\n          })\r\n        }).catch(err => {\r\n            store.dispatch('LogOut').then(() => {\r\n              Message.error(err)\r\n              next({ path: '/' })\r\n            })\r\n          })\r\n      } else {\r\n        next()\r\n      }\r\n    }\r\n  } else {\r\n    // 没有token\r\n    if (isWhiteList(to.path)) {\r\n      // 在免登录白名单，直接进入\r\n      next()\r\n    } else {\r\n      next(`/login?redirect=${encodeURIComponent(to.fullPath)}`) // 否则全部重定向到登录页\r\n      NProgress.done()\r\n    }\r\n  }\r\n})\r\n\r\nrouter.afterEach(() => {\r\n  NProgress.done()\r\n})\r\n"], "mappings": ";;;;;AAAA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACAA,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,SAAA,GAAAL,OAAA;AACA,IAAAM,QAAA,GAAAN,OAAA;AAEAO,kBAAS,CAACC,SAAS,CAAC;EAAEC,WAAW,EAAE;AAAM,CAAC,CAAC;AAE3C,IAAMC,SAAS,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC;AAEzC,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIC,IAAI,EAAK;EAC5B,OAAOF,SAAS,CAACG,IAAI,CAAC,UAAAC,OAAO;IAAA,OAAI,IAAAC,qBAAW,EAACD,OAAO,EAAEF,IAAI,CAAC;EAAA,EAAC;AAC9D,CAAC;AAEDI,eAAM,CAACC,UAAU,CAAC,UAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,EAAK;EACpCb,kBAAS,CAACc,KAAK,CAAC,CAAC;EACjB,IAAI,IAAAC,cAAQ,EAAC,CAAC,EAAE;IACdJ,EAAE,CAACK,IAAI,CAACC,KAAK,IAAIC,cAAK,CAACC,QAAQ,CAAC,mBAAmB,EAAER,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;IACnE;IACA,IAAIN,EAAE,CAACN,IAAI,KAAK,QAAQ,EAAE;MACxBQ,IAAI,CAAC;QAAER,IAAI,EAAE;MAAI,CAAC,CAAC;MACnBL,kBAAS,CAACoB,IAAI,CAAC,CAAC;IAClB,CAAC,MAAM,IAAIhB,WAAW,CAACO,EAAE,CAACN,IAAI,CAAC,EAAE;MAC/BQ,IAAI,CAAC,CAAC;IACR,CAAC,MAAM;MACL,IAAIK,cAAK,CAACG,OAAO,CAACC,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;QACpCC,kBAAS,CAACC,IAAI,GAAG,IAAI;QACrB;QACAP,cAAK,CAACC,QAAQ,CAAC,SAAS,CAAC,CAACO,IAAI,CAAC,YAAM;UACnCF,kBAAS,CAACC,IAAI,GAAG,KAAK;UACtBP,cAAK,CAACC,QAAQ,CAAC,gBAAgB,CAAC,CAACO,IAAI,CAAC,UAAAC,YAAY,EAAI;YACpD;YACAlB,eAAM,CAACmB,SAAS,CAACD,YAAY,CAAC,EAAC;YAC/Bd,IAAI,KAAAgB,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAMnB,EAAE;cAAEoB,OAAO,EAAE;YAAI,EAAE,CAAC,EAAC;UACjC,CAAC,CAAC;QACJ,CAAC,CAAC,CAACC,KAAK,CAAC,UAAAC,GAAG,EAAI;UACZf,cAAK,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAACO,IAAI,CAAC,YAAM;YAClCQ,kBAAO,CAACC,KAAK,CAACF,GAAG,CAAC;YAClBpB,IAAI,CAAC;cAAER,IAAI,EAAE;YAAI,CAAC,CAAC;UACrB,CAAC,CAAC;QACJ,CAAC,CAAC;MACN,CAAC,MAAM;QACLQ,IAAI,CAAC,CAAC;MACR;IACF;EACF,CAAC,MAAM;IACL;IACA,IAAIT,WAAW,CAACO,EAAE,CAACN,IAAI,CAAC,EAAE;MACxB;MACAQ,IAAI,CAAC,CAAC;IACR,CAAC,MAAM;MACLA,IAAI,oBAAAuB,MAAA,CAAoBC,kBAAkB,CAAC1B,EAAE,CAAC2B,QAAQ,CAAC,CAAE,CAAC,EAAC;MAC3DtC,kBAAS,CAACoB,IAAI,CAAC,CAAC;IAClB;EACF;AACF,CAAC,CAAC;AAEFX,eAAM,CAAC8B,SAAS,CAAC,YAAM;EACrBvC,kBAAS,CAACoB,IAAI,CAAC,CAAC;AAClB,CAAC,CAAC", "ignoreList": []}]}