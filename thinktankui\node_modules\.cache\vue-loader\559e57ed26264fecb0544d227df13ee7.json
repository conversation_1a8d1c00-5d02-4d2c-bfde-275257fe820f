{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\FileUpload\\index.vue?vue&type=style&index=0&id=211f81e0&scoped=true&lang=scss", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\FileUpload\\index.vue", "mtime": 1749104047618}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749104419119}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749104421224}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749104419872}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749104418479}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoudXBsb2FkLWZpbGUtdXBsb2FkZXIgew0KICBtYXJnaW4tYm90dG9tOiA1cHg7DQp9DQoudXBsb2FkLWZpbGUtbGlzdCAuZWwtdXBsb2FkLWxpc3RfX2l0ZW0gew0KICBib3JkZXI6IDFweCBzb2xpZCAjZTRlN2VkOw0KICBsaW5lLWhlaWdodDogMjsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KfQ0KLnVwbG9hZC1maWxlLWxpc3QgLmVsZS11cGxvYWQtbGlzdF9faXRlbS1jb250ZW50IHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBjb2xvcjogaW5oZXJpdDsNCn0NCi5lbGUtdXBsb2FkLWxpc3RfX2l0ZW0tY29udGVudC1hY3Rpb24gLmVsLWxpbmsgew0KICBtYXJnaW4tcmlnaHQ6IDEwcHg7DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/FileUpload", "sourcesContent": ["<template>\r\n  <div class=\"upload-file\">\r\n    <el-upload\r\n      multiple\r\n      :action=\"uploadFileUrl\"\r\n      :before-upload=\"handleBeforeUpload\"\r\n      :file-list=\"fileList\"\r\n      :limit=\"limit\"\r\n      :on-error=\"handleUploadError\"\r\n      :on-exceed=\"handleExceed\"\r\n      :on-success=\"handleUploadSuccess\"\r\n      :show-file-list=\"false\"\r\n      :headers=\"headers\"\r\n      class=\"upload-file-uploader\"\r\n      ref=\"fileUpload\"\r\n      v-if=\"!disabled\"\r\n    >\r\n      <!-- 上传按钮 -->\r\n      <el-button size=\"mini\" type=\"primary\">选取文件</el-button>\r\n      <!-- 上传提示 -->\r\n      <div class=\"el-upload__tip\" slot=\"tip\" v-if=\"showTip\">\r\n        请上传\r\n        <template v-if=\"fileSize\"> 大小不超过 <b style=\"color: #f56c6c\">{{ fileSize }}MB</b> </template>\r\n        <template v-if=\"fileType\"> 格式为 <b style=\"color: #f56c6c\">{{ fileType.join(\"/\") }}</b> </template>\r\n        的文件\r\n      </div>\r\n    </el-upload>\r\n\r\n    <!-- 文件列表 -->\r\n    <transition-group class=\"upload-file-list el-upload-list el-upload-list--text\" name=\"el-fade-in-linear\" tag=\"ul\">\r\n      <li :key=\"file.url\" class=\"el-upload-list__item ele-upload-list__item-content\" v-for=\"(file, index) in fileList\">\r\n        <el-link :href=\"`${baseUrl}${file.url}`\" :underline=\"false\" target=\"_blank\">\r\n          <span class=\"el-icon-document\"> {{ getFileName(file.name) }} </span>\r\n        </el-link>\r\n        <div class=\"ele-upload-list__item-content-action\">\r\n          <el-link :underline=\"false\" @click=\"handleDelete(index)\" type=\"danger\" v-if=\"!disabled\">删除</el-link>\r\n        </div>\r\n      </li>\r\n    </transition-group>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"FileUpload\",\r\n  props: {\r\n    // 值\r\n    value: [String, Object, Array],\r\n    // 数量限制\r\n    limit: {\r\n      type: Number,\r\n      default: 5\r\n    },\r\n    // 大小限制(MB)\r\n    fileSize: {\r\n      type: Number,\r\n      default: 5\r\n    },\r\n    // 文件类型, 例如['png', 'jpg', 'jpeg']\r\n    fileType: {\r\n      type: Array,\r\n      default: () => [\"doc\", \"docx\", \"xls\", \"xlsx\", \"ppt\", \"pptx\", \"txt\", \"pdf\"]\r\n    },\r\n    // 是否显示提示\r\n    isShowTip: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    // 禁用组件（仅查看文件）\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      number: 0,\r\n      uploadList: [],\r\n      baseUrl: process.env.VUE_APP_BASE_API,\r\n      uploadFileUrl: process.env.VUE_APP_BASE_API + \"/common/upload\", // 上传文件服务器地址\r\n      headers: {\r\n        Authorization: \"Bearer \" + getToken(),\r\n      },\r\n      fileList: [],\r\n    };\r\n  },\r\n  watch: {\r\n    value: {\r\n      handler(val) {\r\n        if (val) {\r\n          let temp = 1;\r\n          // 首先将值转为数组\r\n          const list = Array.isArray(val) ? val : this.value.split(',');\r\n          // 然后将数组转为对象数组\r\n          this.fileList = list.map(item => {\r\n            if (typeof item === \"string\") {\r\n              item = { name: item, url: item };\r\n            }\r\n            item.uid = item.uid || new Date().getTime() + temp++;\r\n            return item;\r\n          });\r\n        } else {\r\n          this.fileList = [];\r\n          return [];\r\n        }\r\n      },\r\n      deep: true,\r\n      immediate: true\r\n    }\r\n  },\r\n  computed: {\r\n    // 是否显示提示\r\n    showTip() {\r\n      return this.isShowTip && (this.fileType || this.fileSize);\r\n    },\r\n  },\r\n  methods: {\r\n    // 上传前校检格式和大小\r\n    handleBeforeUpload(file) {\r\n      // 校检文件类型\r\n      if (this.fileType) {\r\n        const fileName = file.name.split('.');\r\n        const fileExt = fileName[fileName.length - 1];\r\n        const isTypeOk = this.fileType.indexOf(fileExt) >= 0;\r\n        if (!isTypeOk) {\r\n          this.$modal.msgError(`文件格式不正确，请上传${this.fileType.join(\"/\")}格式文件!`);\r\n          return false;\r\n        }\r\n      }\r\n      // 校检文件名是否包含特殊字符\r\n      if (file.name.includes(',')) {\r\n        this.$modal.msgError('文件名不正确，不能包含英文逗号!');\r\n        return false;\r\n      }\r\n      // 校检文件大小\r\n      if (this.fileSize) {\r\n        const isLt = file.size / 1024 / 1024 < this.fileSize;\r\n        if (!isLt) {\r\n          this.$modal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`);\r\n          return false;\r\n        }\r\n      }\r\n      this.$modal.loading(\"正在上传文件，请稍候...\");\r\n      this.number++;\r\n      return true;\r\n    },\r\n    // 文件个数超出\r\n    handleExceed() {\r\n      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`);\r\n    },\r\n    // 上传失败\r\n    handleUploadError(err) {\r\n      this.$modal.msgError(\"上传文件失败，请重试\");\r\n      this.$modal.closeLoading();\r\n    },\r\n    // 上传成功回调\r\n    handleUploadSuccess(res, file) {\r\n      if (res.code === 200) {\r\n        this.uploadList.push({ name: res.fileName, url: res.fileName });\r\n        this.uploadedSuccessfully();\r\n      } else {\r\n        this.number--;\r\n        this.$modal.closeLoading();\r\n        this.$modal.msgError(res.msg);\r\n        this.$refs.fileUpload.handleRemove(file);\r\n        this.uploadedSuccessfully();\r\n      }\r\n    },\r\n    // 删除文件\r\n    handleDelete(index) {\r\n      this.fileList.splice(index, 1);\r\n      this.$emit(\"input\", this.listToString(this.fileList));\r\n    },\r\n    // 上传结束处理\r\n    uploadedSuccessfully() {\r\n      if (this.number > 0 && this.uploadList.length === this.number) {\r\n        this.fileList = this.fileList.concat(this.uploadList);\r\n        this.uploadList = [];\r\n        this.number = 0;\r\n        this.$emit(\"input\", this.listToString(this.fileList));\r\n        this.$modal.closeLoading();\r\n      }\r\n    },\r\n    // 获取文件名称\r\n    getFileName(name) {\r\n      // 如果是url那么取最后的名字 如果不是直接返回\r\n      if (name.lastIndexOf(\"/\") > -1) {\r\n        return name.slice(name.lastIndexOf(\"/\") + 1);\r\n      } else {\r\n        return name;\r\n      }\r\n    },\r\n    // 对象转成指定字符串分隔\r\n    listToString(list, separator) {\r\n      let strs = \"\";\r\n      separator = separator || \",\";\r\n      for (let i in list) {\r\n        strs += list[i].url + separator;\r\n      }\r\n      return strs != '' ? strs.substr(0, strs.length - 1) : '';\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.upload-file-uploader {\r\n  margin-bottom: 5px;\r\n}\r\n.upload-file-list .el-upload-list__item {\r\n  border: 1px solid #e4e7ed;\r\n  line-height: 2;\r\n  margin-bottom: 10px;\r\n  position: relative;\r\n}\r\n.upload-file-list .ele-upload-list__item-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  color: inherit;\r\n}\r\n.ele-upload-list__item-content-action .el-link {\r\n  margin-right: 10px;\r\n}\r\n</style>\r\n"]}]}