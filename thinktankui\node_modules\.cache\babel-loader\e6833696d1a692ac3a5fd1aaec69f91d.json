{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\core\\composition.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\core\\composition.js", "mtime": 1749104421253}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_embed", "_interopRequireDefault", "require", "_emitter", "Composition", "scroll", "emitter", "_classCallCheck2", "default", "_defineProperty2", "setupListeners", "_createClass2", "key", "value", "_this", "domNode", "addEventListener", "event", "isComposing", "handleCompositionStart", "queueMicrotask", "handleCompositionEnd", "blot", "target", "Node", "find", "Embed", "emit", "Emitter", "events", "COMPOSITION_BEFORE_START", "batchStart", "COMPOSITION_START", "COMPOSITION_BEFORE_END", "batchEnd", "COMPOSITION_END", "_default", "exports"], "sources": ["../../src/core/composition.ts"], "sourcesContent": ["import Embed from '../blots/embed.js';\nimport type Scroll from '../blots/scroll.js';\nimport Emitter from './emitter.js';\n\nclass Composition {\n  isComposing = false;\n\n  constructor(\n    private scroll: Scroll,\n    private emitter: Emitter,\n  ) {\n    this.setupListeners();\n  }\n\n  private setupListeners() {\n    this.scroll.domNode.addEventListener('compositionstart', (event) => {\n      if (!this.isComposing) {\n        this.handleCompositionStart(event);\n      }\n    });\n\n    this.scroll.domNode.addEventListener('compositionend', (event) => {\n      if (this.isComposing) {\n        // Webkit makes DOM changes after compositionend, so we use microtask to\n        // ensure the order.\n        // https://bugs.webkit.org/show_bug.cgi?id=31902\n        queueMicrotask(() => {\n          this.handleCompositionEnd(event);\n        });\n      }\n    });\n  }\n\n  private handleCompositionStart(event: CompositionEvent) {\n    const blot =\n      event.target instanceof Node\n        ? this.scroll.find(event.target, true)\n        : null;\n\n    if (blot && !(blot instanceof Embed)) {\n      this.emitter.emit(Emitter.events.COMPOSITION_BEFORE_START, event);\n      this.scroll.batchStart();\n      this.emitter.emit(Emitter.events.COMPOSITION_START, event);\n      this.isComposing = true;\n    }\n  }\n\n  private handleCompositionEnd(event: CompositionEvent) {\n    this.emitter.emit(Emitter.events.COMPOSITION_BEFORE_END, event);\n    this.scroll.batchEnd();\n    this.emitter.emit(Emitter.events.COMPOSITION_END, event);\n    this.isComposing = false;\n  }\n}\n\nexport default Composition;\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,QAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAkC,IAE5BE,WAAW;EAGf,SAAAA,YACUC,MAAc,EACdC,OAAgB,EACxB;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAJ,WAAA;IAAA,IAAAK,gBAAA,CAAAD,OAAA,uBALY,KAAK;IAKjB,KAFQH,MAAc,GAAdA,MAAc;IAAA,KACdC,OAAgB,GAAhBA,OAAgB;IAExB,IAAI,CAACI,cAAc,CAAC,CAAC;EACvB;EAAA,WAAAC,aAAA,CAAAH,OAAA,EAAAJ,WAAA;IAAAQ,GAAA;IAAAC,KAAA,EAEQ,SAAAH,cAAcA,CAAA,EAAG;MAAA,IAAAI,KAAA;MACvB,IAAI,CAACT,MAAM,CAACU,OAAO,CAACC,gBAAgB,CAAC,kBAAkB,EAAG,UAAAC,KAAK,EAAK;QAClE,IAAI,CAACH,KAAI,CAACI,WAAW,EAAE;UACrBJ,KAAI,CAACK,sBAAsB,CAACF,KAAK,CAAC;QACpC;MACF,CAAC,CAAC;MAEF,IAAI,CAACZ,MAAM,CAACU,OAAO,CAACC,gBAAgB,CAAC,gBAAgB,EAAG,UAAAC,KAAK,EAAK;QAChE,IAAIH,KAAI,CAACI,WAAW,EAAE;UACpB;UACA;UACA;UACAE,cAAc,CAAC,YAAM;YACnBN,KAAI,CAACO,oBAAoB,CAACJ,KAAK,CAAC;UAClC,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;EAAA;IAAAL,GAAA;IAAAC,KAAA,EAEQ,SAAAM,sBAAsBA,CAACF,KAAuB,EAAE;MACtD,IAAMK,IAAI,GACRL,KAAK,CAACM,MAAM,YAAYC,IAAI,GACxB,IAAI,CAACnB,MAAM,CAACoB,IAAI,CAACR,KAAK,CAACM,MAAM,EAAE,IAAI,CAAC,GACpC,IAAI;MAEV,IAAID,IAAI,IAAI,EAAEA,IAAI,YAAYI,cAAK,CAAC,EAAE;QACpC,IAAI,CAACpB,OAAO,CAACqB,IAAI,CAACC,gBAAO,CAACC,MAAM,CAACC,wBAAwB,EAAEb,KAAK,CAAC;QACjE,IAAI,CAACZ,MAAM,CAAC0B,UAAU,CAAC,CAAC;QACxB,IAAI,CAACzB,OAAO,CAACqB,IAAI,CAACC,gBAAO,CAACC,MAAM,CAACG,iBAAiB,EAAEf,KAAK,CAAC;QAC1D,IAAI,CAACC,WAAW,GAAG,IAAI;MACzB;IACF;EAAA;IAAAN,GAAA;IAAAC,KAAA,EAEQ,SAAAQ,oBAAoBA,CAACJ,KAAuB,EAAE;MACpD,IAAI,CAACX,OAAO,CAACqB,IAAI,CAACC,gBAAO,CAACC,MAAM,CAACI,sBAAsB,EAAEhB,KAAK,CAAC;MAC/D,IAAI,CAACZ,MAAM,CAAC6B,QAAQ,CAAC,CAAC;MACtB,IAAI,CAAC5B,OAAO,CAACqB,IAAI,CAACC,gBAAO,CAACC,MAAM,CAACM,eAAe,EAAElB,KAAK,CAAC;MACxD,IAAI,CAACC,WAAW,GAAG,KAAK;IAC1B;EAAA;AAAA;AAAA,IAAAkB,QAAA,GAAAC,OAAA,CAAA7B,OAAA,GAGaJ,WAAW", "ignoreList": []}]}