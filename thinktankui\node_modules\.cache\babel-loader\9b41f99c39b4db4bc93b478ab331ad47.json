{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\FileUpload\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\FileUpload\\index.vue", "mtime": 1749104047618}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_auth", "require", "name", "props", "value", "String", "Object", "Array", "limit", "type", "Number", "default", "fileSize", "fileType", "isShowTip", "Boolean", "disabled", "data", "number", "uploadList", "baseUrl", "process", "env", "VUE_APP_BASE_API", "uploadFileUrl", "headers", "Authorization", "getToken", "fileList", "watch", "handler", "val", "temp", "list", "isArray", "split", "map", "item", "url", "uid", "Date", "getTime", "deep", "immediate", "computed", "showTip", "methods", "handleBeforeUpload", "file", "fileName", "fileExt", "length", "isTypeOk", "indexOf", "$modal", "msgError", "concat", "join", "includes", "isLt", "size", "loading", "handleExceed", "handleUploadError", "err", "closeLoading", "handleUploadSuccess", "res", "code", "push", "uploadedSuccessfully", "msg", "$refs", "fileUpload", "handleRemove", "handleDelete", "index", "splice", "$emit", "listToString", "getFileName", "lastIndexOf", "slice", "separator", "strs", "i", "substr"], "sources": ["src/components/FileUpload/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"upload-file\">\r\n    <el-upload\r\n      multiple\r\n      :action=\"uploadFileUrl\"\r\n      :before-upload=\"handleBeforeUpload\"\r\n      :file-list=\"fileList\"\r\n      :limit=\"limit\"\r\n      :on-error=\"handleUploadError\"\r\n      :on-exceed=\"handleExceed\"\r\n      :on-success=\"handleUploadSuccess\"\r\n      :show-file-list=\"false\"\r\n      :headers=\"headers\"\r\n      class=\"upload-file-uploader\"\r\n      ref=\"fileUpload\"\r\n      v-if=\"!disabled\"\r\n    >\r\n      <!-- 上传按钮 -->\r\n      <el-button size=\"mini\" type=\"primary\">选取文件</el-button>\r\n      <!-- 上传提示 -->\r\n      <div class=\"el-upload__tip\" slot=\"tip\" v-if=\"showTip\">\r\n        请上传\r\n        <template v-if=\"fileSize\"> 大小不超过 <b style=\"color: #f56c6c\">{{ fileSize }}MB</b> </template>\r\n        <template v-if=\"fileType\"> 格式为 <b style=\"color: #f56c6c\">{{ fileType.join(\"/\") }}</b> </template>\r\n        的文件\r\n      </div>\r\n    </el-upload>\r\n\r\n    <!-- 文件列表 -->\r\n    <transition-group class=\"upload-file-list el-upload-list el-upload-list--text\" name=\"el-fade-in-linear\" tag=\"ul\">\r\n      <li :key=\"file.url\" class=\"el-upload-list__item ele-upload-list__item-content\" v-for=\"(file, index) in fileList\">\r\n        <el-link :href=\"`${baseUrl}${file.url}`\" :underline=\"false\" target=\"_blank\">\r\n          <span class=\"el-icon-document\"> {{ getFileName(file.name) }} </span>\r\n        </el-link>\r\n        <div class=\"ele-upload-list__item-content-action\">\r\n          <el-link :underline=\"false\" @click=\"handleDelete(index)\" type=\"danger\" v-if=\"!disabled\">删除</el-link>\r\n        </div>\r\n      </li>\r\n    </transition-group>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"FileUpload\",\r\n  props: {\r\n    // 值\r\n    value: [String, Object, Array],\r\n    // 数量限制\r\n    limit: {\r\n      type: Number,\r\n      default: 5\r\n    },\r\n    // 大小限制(MB)\r\n    fileSize: {\r\n      type: Number,\r\n      default: 5\r\n    },\r\n    // 文件类型, 例如['png', 'jpg', 'jpeg']\r\n    fileType: {\r\n      type: Array,\r\n      default: () => [\"doc\", \"docx\", \"xls\", \"xlsx\", \"ppt\", \"pptx\", \"txt\", \"pdf\"]\r\n    },\r\n    // 是否显示提示\r\n    isShowTip: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    // 禁用组件（仅查看文件）\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      number: 0,\r\n      uploadList: [],\r\n      baseUrl: process.env.VUE_APP_BASE_API,\r\n      uploadFileUrl: process.env.VUE_APP_BASE_API + \"/common/upload\", // 上传文件服务器地址\r\n      headers: {\r\n        Authorization: \"Bearer \" + getToken(),\r\n      },\r\n      fileList: [],\r\n    };\r\n  },\r\n  watch: {\r\n    value: {\r\n      handler(val) {\r\n        if (val) {\r\n          let temp = 1;\r\n          // 首先将值转为数组\r\n          const list = Array.isArray(val) ? val : this.value.split(',');\r\n          // 然后将数组转为对象数组\r\n          this.fileList = list.map(item => {\r\n            if (typeof item === \"string\") {\r\n              item = { name: item, url: item };\r\n            }\r\n            item.uid = item.uid || new Date().getTime() + temp++;\r\n            return item;\r\n          });\r\n        } else {\r\n          this.fileList = [];\r\n          return [];\r\n        }\r\n      },\r\n      deep: true,\r\n      immediate: true\r\n    }\r\n  },\r\n  computed: {\r\n    // 是否显示提示\r\n    showTip() {\r\n      return this.isShowTip && (this.fileType || this.fileSize);\r\n    },\r\n  },\r\n  methods: {\r\n    // 上传前校检格式和大小\r\n    handleBeforeUpload(file) {\r\n      // 校检文件类型\r\n      if (this.fileType) {\r\n        const fileName = file.name.split('.');\r\n        const fileExt = fileName[fileName.length - 1];\r\n        const isTypeOk = this.fileType.indexOf(fileExt) >= 0;\r\n        if (!isTypeOk) {\r\n          this.$modal.msgError(`文件格式不正确，请上传${this.fileType.join(\"/\")}格式文件!`);\r\n          return false;\r\n        }\r\n      }\r\n      // 校检文件名是否包含特殊字符\r\n      if (file.name.includes(',')) {\r\n        this.$modal.msgError('文件名不正确，不能包含英文逗号!');\r\n        return false;\r\n      }\r\n      // 校检文件大小\r\n      if (this.fileSize) {\r\n        const isLt = file.size / 1024 / 1024 < this.fileSize;\r\n        if (!isLt) {\r\n          this.$modal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`);\r\n          return false;\r\n        }\r\n      }\r\n      this.$modal.loading(\"正在上传文件，请稍候...\");\r\n      this.number++;\r\n      return true;\r\n    },\r\n    // 文件个数超出\r\n    handleExceed() {\r\n      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`);\r\n    },\r\n    // 上传失败\r\n    handleUploadError(err) {\r\n      this.$modal.msgError(\"上传文件失败，请重试\");\r\n      this.$modal.closeLoading();\r\n    },\r\n    // 上传成功回调\r\n    handleUploadSuccess(res, file) {\r\n      if (res.code === 200) {\r\n        this.uploadList.push({ name: res.fileName, url: res.fileName });\r\n        this.uploadedSuccessfully();\r\n      } else {\r\n        this.number--;\r\n        this.$modal.closeLoading();\r\n        this.$modal.msgError(res.msg);\r\n        this.$refs.fileUpload.handleRemove(file);\r\n        this.uploadedSuccessfully();\r\n      }\r\n    },\r\n    // 删除文件\r\n    handleDelete(index) {\r\n      this.fileList.splice(index, 1);\r\n      this.$emit(\"input\", this.listToString(this.fileList));\r\n    },\r\n    // 上传结束处理\r\n    uploadedSuccessfully() {\r\n      if (this.number > 0 && this.uploadList.length === this.number) {\r\n        this.fileList = this.fileList.concat(this.uploadList);\r\n        this.uploadList = [];\r\n        this.number = 0;\r\n        this.$emit(\"input\", this.listToString(this.fileList));\r\n        this.$modal.closeLoading();\r\n      }\r\n    },\r\n    // 获取文件名称\r\n    getFileName(name) {\r\n      // 如果是url那么取最后的名字 如果不是直接返回\r\n      if (name.lastIndexOf(\"/\") > -1) {\r\n        return name.slice(name.lastIndexOf(\"/\") + 1);\r\n      } else {\r\n        return name;\r\n      }\r\n    },\r\n    // 对象转成指定字符串分隔\r\n    listToString(list, separator) {\r\n      let strs = \"\";\r\n      separator = separator || \",\";\r\n      for (let i in list) {\r\n        strs += list[i].url + separator;\r\n      }\r\n      return strs != '' ? strs.substr(0, strs.length - 1) : '';\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.upload-file-uploader {\r\n  margin-bottom: 5px;\r\n}\r\n.upload-file-list .el-upload-list__item {\r\n  border: 1px solid #e4e7ed;\r\n  line-height: 2;\r\n  margin-bottom: 10px;\r\n  position: relative;\r\n}\r\n.upload-file-list .ele-upload-list__item-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  color: inherit;\r\n}\r\n.ele-upload-list__item-content-action .el-link {\r\n  margin-right: 10px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AA2CA,IAAAA,KAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAC,IAAA;EACAC,KAAA;IACA;IACAC,KAAA,GAAAC,MAAA,EAAAC,MAAA,EAAAC,KAAA;IACA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACA;IACAC,QAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACA;IACAE,QAAA;MACAJ,IAAA,EAAAF,KAAA;MACAI,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACA;IACAG,SAAA;MACAL,IAAA,EAAAM,OAAA;MACAJ,OAAA;IACA;IACA;IACAK,QAAA;MACAP,IAAA,EAAAM,OAAA;MACAJ,OAAA;IACA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA;MACAC,UAAA;MACAC,OAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACAC,aAAA,EAAAH,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MAAA;MACAE,OAAA;QACAC,aAAA,kBAAAC,cAAA;MACA;MACAC,QAAA;IACA;EACA;EACAC,KAAA;IACAzB,KAAA;MACA0B,OAAA,WAAAA,QAAAC,GAAA;QACA,IAAAA,GAAA;UACA,IAAAC,IAAA;UACA;UACA,IAAAC,IAAA,GAAA1B,KAAA,CAAA2B,OAAA,CAAAH,GAAA,IAAAA,GAAA,QAAA3B,KAAA,CAAA+B,KAAA;UACA;UACA,KAAAP,QAAA,GAAAK,IAAA,CAAAG,GAAA,WAAAC,IAAA;YACA,WAAAA,IAAA;cACAA,IAAA;gBAAAnC,IAAA,EAAAmC,IAAA;gBAAAC,GAAA,EAAAD;cAAA;YACA;YACAA,IAAA,CAAAE,GAAA,GAAAF,IAAA,CAAAE,GAAA,QAAAC,IAAA,GAAAC,OAAA,KAAAT,IAAA;YACA,OAAAK,IAAA;UACA;QACA;UACA,KAAAT,QAAA;UACA;QACA;MACA;MACAc,IAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,OAAA,WAAAA,QAAA;MACA,YAAA/B,SAAA,UAAAD,QAAA,SAAAD,QAAA;IACA;EACA;EACAkC,OAAA;IACA;IACAC,kBAAA,WAAAA,mBAAAC,IAAA;MACA;MACA,SAAAnC,QAAA;QACA,IAAAoC,QAAA,GAAAD,IAAA,CAAA9C,IAAA,CAAAiC,KAAA;QACA,IAAAe,OAAA,GAAAD,QAAA,CAAAA,QAAA,CAAAE,MAAA;QACA,IAAAC,QAAA,QAAAvC,QAAA,CAAAwC,OAAA,CAAAH,OAAA;QACA,KAAAE,QAAA;UACA,KAAAE,MAAA,CAAAC,QAAA,sEAAAC,MAAA,MAAA3C,QAAA,CAAA4C,IAAA;UACA;QACA;MACA;MACA;MACA,IAAAT,IAAA,CAAA9C,IAAA,CAAAwD,QAAA;QACA,KAAAJ,MAAA,CAAAC,QAAA;QACA;MACA;MACA;MACA,SAAA3C,QAAA;QACA,IAAA+C,IAAA,GAAAX,IAAA,CAAAY,IAAA,sBAAAhD,QAAA;QACA,KAAA+C,IAAA;UACA,KAAAL,MAAA,CAAAC,QAAA,iEAAAC,MAAA,MAAA5C,QAAA;UACA;QACA;MACA;MACA,KAAA0C,MAAA,CAAAO,OAAA;MACA,KAAA3C,MAAA;MACA;IACA;IACA;IACA4C,YAAA,WAAAA,aAAA;MACA,KAAAR,MAAA,CAAAC,QAAA,iEAAAC,MAAA,MAAAhD,KAAA;IACA;IACA;IACAuD,iBAAA,WAAAA,kBAAAC,GAAA;MACA,KAAAV,MAAA,CAAAC,QAAA;MACA,KAAAD,MAAA,CAAAW,YAAA;IACA;IACA;IACAC,mBAAA,WAAAA,oBAAAC,GAAA,EAAAnB,IAAA;MACA,IAAAmB,GAAA,CAAAC,IAAA;QACA,KAAAjD,UAAA,CAAAkD,IAAA;UAAAnE,IAAA,EAAAiE,GAAA,CAAAlB,QAAA;UAAAX,GAAA,EAAA6B,GAAA,CAAAlB;QAAA;QACA,KAAAqB,oBAAA;MACA;QACA,KAAApD,MAAA;QACA,KAAAoC,MAAA,CAAAW,YAAA;QACA,KAAAX,MAAA,CAAAC,QAAA,CAAAY,GAAA,CAAAI,GAAA;QACA,KAAAC,KAAA,CAAAC,UAAA,CAAAC,YAAA,CAAA1B,IAAA;QACA,KAAAsB,oBAAA;MACA;IACA;IACA;IACAK,YAAA,WAAAA,aAAAC,KAAA;MACA,KAAAhD,QAAA,CAAAiD,MAAA,CAAAD,KAAA;MACA,KAAAE,KAAA,eAAAC,YAAA,MAAAnD,QAAA;IACA;IACA;IACA0C,oBAAA,WAAAA,qBAAA;MACA,SAAApD,MAAA,aAAAC,UAAA,CAAAgC,MAAA,UAAAjC,MAAA;QACA,KAAAU,QAAA,QAAAA,QAAA,CAAA4B,MAAA,MAAArC,UAAA;QACA,KAAAA,UAAA;QACA,KAAAD,MAAA;QACA,KAAA4D,KAAA,eAAAC,YAAA,MAAAnD,QAAA;QACA,KAAA0B,MAAA,CAAAW,YAAA;MACA;IACA;IACA;IACAe,WAAA,WAAAA,YAAA9E,IAAA;MACA;MACA,IAAAA,IAAA,CAAA+E,WAAA;QACA,OAAA/E,IAAA,CAAAgF,KAAA,CAAAhF,IAAA,CAAA+E,WAAA;MACA;QACA,OAAA/E,IAAA;MACA;IACA;IACA;IACA6E,YAAA,WAAAA,aAAA9C,IAAA,EAAAkD,SAAA;MACA,IAAAC,IAAA;MACAD,SAAA,GAAAA,SAAA;MACA,SAAAE,CAAA,IAAApD,IAAA;QACAmD,IAAA,IAAAnD,IAAA,CAAAoD,CAAA,EAAA/C,GAAA,GAAA6C,SAAA;MACA;MACA,OAAAC,IAAA,SAAAA,IAAA,CAAAE,MAAA,IAAAF,IAAA,CAAAjC,MAAA;IACA;EACA;AACA", "ignoreList": []}]}