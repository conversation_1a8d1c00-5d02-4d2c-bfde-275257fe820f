{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\utils\\validate.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\utils\\validate.js", "mtime": 1749104047634}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749104418875}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["isPathMatch", "pattern", "path", "regexPattern", "replace", "regex", "RegExp", "concat", "test", "isEmpty", "value", "undefined", "isHttp", "url", "indexOf", "isExternal", "validUsername", "str", "valid_map", "trim", "validURL", "reg", "validLowerCase", "validUpperCase", "validAlphabets", "validEmail", "email", "isString", "String", "isArray", "arg", "Array", "Object", "prototype", "toString", "call"], "sources": ["D:/thinktank/thinktankui/src/utils/validate.js"], "sourcesContent": ["/**\r\n * 路径匹配器\r\n * @param {string} pattern\r\n * @param {string} path\r\n * @returns {Boolean}\r\n */\r\nexport function isPathMatch(pattern, path) {\r\n  const regexPattern = pattern.replace(/\\//g, '\\\\/').replace(/\\*\\*/g, '.*').replace(/\\*/g, '[^\\\\/]*')\r\n  const regex = new RegExp(`^${regexPattern}$`)\r\n  return regex.test(path)\r\n}\r\n\r\n/**\r\n * 判断value字符串是否为空 \r\n * @param {string} value\r\n * @returns {Boolean}\r\n */\r\nexport function isEmpty(value) {\r\n  if (value == null || value == \"\" || value == undefined || value == \"undefined\") {\r\n    return true\r\n  }\r\n  return false\r\n}\r\n\r\n/**\r\n * 判断url是否是http或https \r\n * @param {string} url\r\n * @returns {Boolean}\r\n */\r\nexport function isHttp(url) {\r\n  return url.indexOf('http://') !== -1 || url.indexOf('https://') !== -1\r\n}\r\n\r\n/**\r\n * 判断path是否为外链\r\n * @param {string} path\r\n * @returns {Boolean}\r\n */\r\nexport function isExternal(path) {\r\n  return /^(https?:|mailto:|tel:)/.test(path)\r\n}\r\n\r\n/**\r\n * @param {string} str\r\n * @returns {Boolean}\r\n */\r\nexport function validUsername(str) {\r\n  const valid_map = ['admin', 'editor']\r\n  return valid_map.indexOf(str.trim()) >= 0\r\n}\r\n\r\n/**\r\n * @param {string} url\r\n * @returns {Boolean}\r\n */\r\nexport function validURL(url) {\r\n  const reg = /^(https?|ftp):\\/\\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\\.)*[a-zA-Z0-9-]+\\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\\/($|[a-zA-Z0-9.,?'\\\\+&%$#=~_-]+))*$/\r\n  return reg.test(url)\r\n}\r\n\r\n/**\r\n * @param {string} str\r\n * @returns {Boolean}\r\n */\r\nexport function validLowerCase(str) {\r\n  const reg = /^[a-z]+$/\r\n  return reg.test(str)\r\n}\r\n\r\n/**\r\n * @param {string} str\r\n * @returns {Boolean}\r\n */\r\nexport function validUpperCase(str) {\r\n  const reg = /^[A-Z]+$/\r\n  return reg.test(str)\r\n}\r\n\r\n/**\r\n * @param {string} str\r\n * @returns {Boolean}\r\n */\r\nexport function validAlphabets(str) {\r\n  const reg = /^[A-Za-z]+$/\r\n  return reg.test(str)\r\n}\r\n\r\n/**\r\n * @param {string} email\r\n * @returns {Boolean}\r\n */\r\nexport function validEmail(email) {\r\n  const reg = /^(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$/\r\n  return reg.test(email)\r\n}\r\n\r\n/**\r\n * @param {string} str\r\n * @returns {Boolean}\r\n */\r\nexport function isString(str) {\r\n  return typeof str === 'string' || str instanceof String\r\n}\r\n\r\n/**\r\n * @param {Array} arg\r\n * @returns {Boolean}\r\n */\r\nexport function isArray(arg) {\r\n  if (typeof Array.isArray === 'undefined') {\r\n    return Object.prototype.toString.call(arg) === '[object Array]'\r\n  }\r\n  return Array.isArray(arg)\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACO,SAASA,WAAWA,CAACC,OAAO,EAAEC,IAAI,EAAE;EACzC,IAAMC,YAAY,GAAGF,OAAO,CAACG,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAACA,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC;EACnG,IAAMC,KAAK,GAAG,IAAIC,MAAM,KAAAC,MAAA,CAAKJ,YAAY,MAAG,CAAC;EAC7C,OAAOE,KAAK,CAACG,IAAI,CAACN,IAAI,CAAC;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASO,OAAOA,CAACC,KAAK,EAAE;EAC7B,IAAIA,KAAK,IAAI,IAAI,IAAIA,KAAK,IAAI,EAAE,IAAIA,KAAK,IAAIC,SAAS,IAAID,KAAK,IAAI,WAAW,EAAE;IAC9E,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASE,MAAMA,CAACC,GAAG,EAAE;EAC1B,OAAOA,GAAG,CAACC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAID,GAAG,CAACC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AACxE;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASC,UAAUA,CAACb,IAAI,EAAE;EAC/B,OAAO,yBAAyB,CAACM,IAAI,CAACN,IAAI,CAAC;AAC7C;;AAEA;AACA;AACA;AACA;AACO,SAASc,aAAaA,CAACC,GAAG,EAAE;EACjC,IAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC;EACrC,OAAOA,SAAS,CAACJ,OAAO,CAACG,GAAG,CAACE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AAC3C;;AAEA;AACA;AACA;AACA;AACO,SAASC,QAAQA,CAACP,GAAG,EAAE;EAC5B,IAAMQ,GAAG,GAAG,4TAA4T;EACxU,OAAOA,GAAG,CAACb,IAAI,CAACK,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACO,SAASS,cAAcA,CAACL,GAAG,EAAE;EAClC,IAAMI,GAAG,GAAG,UAAU;EACtB,OAAOA,GAAG,CAACb,IAAI,CAACS,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACO,SAASM,cAAcA,CAACN,GAAG,EAAE;EAClC,IAAMI,GAAG,GAAG,UAAU;EACtB,OAAOA,GAAG,CAACb,IAAI,CAACS,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACO,SAASO,cAAcA,CAACP,GAAG,EAAE;EAClC,IAAMI,GAAG,GAAG,aAAa;EACzB,OAAOA,GAAG,CAACb,IAAI,CAACS,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACO,SAASQ,UAAUA,CAACC,KAAK,EAAE;EAChC,IAAML,GAAG,GAAG,yJAAyJ;EACrK,OAAOA,GAAG,CAACb,IAAI,CAACkB,KAAK,CAAC;AACxB;;AAEA;AACA;AACA;AACA;AACO,SAASC,QAAQA,CAACV,GAAG,EAAE;EAC5B,OAAO,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,YAAYW,MAAM;AACzD;;AAEA;AACA;AACA;AACA;AACO,SAASC,OAAOA,CAACC,GAAG,EAAE;EAC3B,IAAI,OAAOC,KAAK,CAACF,OAAO,KAAK,WAAW,EAAE;IACxC,OAAOG,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,GAAG,CAAC,KAAK,gBAAgB;EACjE;EACA,OAAOC,KAAK,CAACF,OAAO,CAACC,GAAG,CAAC;AAC3B", "ignoreList": []}]}