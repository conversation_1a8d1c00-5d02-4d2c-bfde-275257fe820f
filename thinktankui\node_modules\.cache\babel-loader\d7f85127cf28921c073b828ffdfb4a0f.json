{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@babel\\runtime\\helpers\\isNativeReflectConstruct.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\@babel\\runtime\\helpers\\isNativeReflectConstruct.js", "mtime": 1749104423949}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:cmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnJlZmxlY3QuY29uc3RydWN0LmpzIik7CmZ1bmN0aW9uIF9pc05hdGl2ZVJlZmxlY3RDb25zdHJ1Y3QoKSB7CiAgdHJ5IHsKICAgIHZhciB0ID0gIUJvb2xlYW4ucHJvdG90eXBlLnZhbHVlT2YuY2FsbChSZWZsZWN0LmNvbnN0cnVjdChCb29sZWFuLCBbXSwgZnVuY3Rpb24gKCkge30pKTsKICB9IGNhdGNoICh0KSB7fQogIHJldHVybiAobW9kdWxlLmV4cG9ydHMgPSBfaXNOYXRpdmVSZWZsZWN0Q29uc3RydWN0ID0gZnVuY3Rpb24gX2lzTmF0aXZlUmVmbGVjdENvbnN0cnVjdCgpIHsKICAgIHJldHVybiAhIXQ7CiAgfSwgbW9kdWxlLmV4cG9ydHMuX19lc01vZHVsZSA9IHRydWUsIG1vZHVsZS5leHBvcnRzWyJkZWZhdWx0Il0gPSBtb2R1bGUuZXhwb3J0cykoKTsKfQptb2R1bGUuZXhwb3J0cyA9IF9pc05hdGl2ZVJlZmxlY3RDb25zdHJ1Y3QsIG1vZHVsZS5leHBvcnRzLl9fZXNNb2R1bGUgPSB0cnVlLCBtb2R1bGUuZXhwb3J0c1siZGVmYXVsdCJdID0gbW9kdWxlLmV4cG9ydHM7"}, {"version": 3, "names": ["_isNativeReflectConstruct", "t", "Boolean", "prototype", "valueOf", "call", "Reflect", "construct", "module", "exports", "__esModule"], "sources": ["D:/thinktank/thinktankui/node_modules/@babel/runtime/helpers/isNativeReflectConstruct.js"], "sourcesContent": ["function _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {}));\n  } catch (t) {}\n  return (module.exports = _isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports)();\n}\nmodule.exports = _isNativeReflectConstruct, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "mappings": ";AAAA,SAASA,yBAAyBA,CAAA,EAAG;EACnC,IAAI;IACF,IAAIC,CAAC,GAAG,CAACC,OAAO,CAACC,SAAS,CAACC,OAAO,CAACC,IAAI,CAACC,OAAO,CAACC,SAAS,CAACL,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;EACzF,CAAC,CAAC,OAAOD,CAAC,EAAE,CAAC;EACb,OAAO,CAACO,MAAM,CAACC,OAAO,GAAGT,yBAAyB,GAAG,SAASA,yBAAyBA,CAAA,EAAG;IACxF,OAAO,CAAC,CAACC,CAAC;EACZ,CAAC,EAAEO,MAAM,CAACC,OAAO,CAACC,UAAU,GAAG,IAAI,EAAEF,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO,EAAE,CAAC;AACpF;AACAD,MAAM,CAACC,OAAO,GAAGT,yBAAyB,EAAEQ,MAAM,CAACC,OAAO,CAACC,UAAU,GAAG,IAAI,EAAEF,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO", "ignoreList": []}]}