{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\SizeSelect\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\SizeSelect\\index.vue", "mtime": 1749104047621}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHNpemVPcHRpb25zOiBbDQogICAgICAgIHsgbGFiZWw6ICdEZWZhdWx0JywgdmFsdWU6ICdkZWZhdWx0JyB9LA0KICAgICAgICB7IGxhYmVsOiAnTWVkaXVtJywgdmFsdWU6ICdtZWRpdW0nIH0sDQogICAgICAgIHsgbGFiZWw6ICdTbWFsbCcsIHZhbHVlOiAnc21hbGwnIH0sDQogICAgICAgIHsgbGFiZWw6ICdNaW5pJywgdmFsdWU6ICdtaW5pJyB9DQogICAgICBdDQogICAgfQ0KICB9LA0KICBjb21wdXRlZDogew0KICAgIHNpemUoKSB7DQogICAgICByZXR1cm4gdGhpcy4kc3RvcmUuZ2V0dGVycy5zaXplDQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgaGFuZGxlU2V0U2l6ZShzaXplKSB7DQogICAgICB0aGlzLiRFTEVNRU5ULnNpemUgPSBzaXplDQogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnYXBwL3NldFNpemUnLCBzaXplKQ0KICAgICAgdGhpcy5yZWZyZXNoVmlldygpDQogICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgbWVzc2FnZTogJ1N3aXRjaCBTaXplIFN1Y2Nlc3MnLA0KICAgICAgICB0eXBlOiAnc3VjY2VzcycNCiAgICAgIH0pDQogICAgfSwNCiAgICByZWZyZXNoVmlldygpIHsNCiAgICAgIC8vIEluIG9yZGVyIHRvIG1ha2UgdGhlIGNhY2hlZCBwYWdlIHJlLXJlbmRlcmVkDQogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgndGFnc1ZpZXcvZGVsQWxsQ2FjaGVkVmlld3MnLCB0aGlzLiRyb3V0ZSkNCg0KICAgICAgY29uc3QgeyBmdWxsUGF0aCB9ID0gdGhpcy4kcm91dGUNCg0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICB0aGlzLiRyb3V0ZXIucmVwbGFjZSh7DQogICAgICAgICAgcGF0aDogJy9yZWRpcmVjdCcgKyBmdWxsUGF0aA0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9DQogIH0NCg0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "file": "index.vue", "sourceRoot": "src/components/SizeSelect", "sourcesContent": ["<template>\r\n  <el-dropdown trigger=\"click\" @command=\"handleSetSize\">\r\n    <div>\r\n      <svg-icon class-name=\"size-icon\" icon-class=\"size\" />\r\n    </div>\r\n    <el-dropdown-menu slot=\"dropdown\">\r\n      <el-dropdown-item v-for=\"item of sizeOptions\" :key=\"item.value\" :disabled=\"size===item.value\" :command=\"item.value\">\r\n        {{ item.label }}\r\n      </el-dropdown-item>\r\n    </el-dropdown-menu>\r\n  </el-dropdown>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      sizeOptions: [\r\n        { label: 'Default', value: 'default' },\r\n        { label: 'Medium', value: 'medium' },\r\n        { label: 'Small', value: 'small' },\r\n        { label: 'Mini', value: 'mini' }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    size() {\r\n      return this.$store.getters.size\r\n    }\r\n  },\r\n  methods: {\r\n    handleSetSize(size) {\r\n      this.$ELEMENT.size = size\r\n      this.$store.dispatch('app/setSize', size)\r\n      this.refreshView()\r\n      this.$message({\r\n        message: 'Switch Size Success',\r\n        type: 'success'\r\n      })\r\n    },\r\n    refreshView() {\r\n      // In order to make the cached page re-rendered\r\n      this.$store.dispatch('tagsView/delAllCachedViews', this.$route)\r\n\r\n      const { fullPath } = this.$route\r\n\r\n      this.$nextTick(() => {\r\n        this.$router.replace({\r\n          path: '/redirect' + fullPath\r\n        })\r\n      })\r\n    }\r\n  }\r\n\r\n}\r\n</script>\r\n"]}]}