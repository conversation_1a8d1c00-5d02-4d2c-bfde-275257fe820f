{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\spread-analysis\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\spread-analysis\\index.vue", "mtime": 1749104047642}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgKiBhcyBlY2hhcnRzIGZyb20gJ2VjaGFydHMnDQppbXBvcnQgJ2VjaGFydHMtd29yZGNsb3VkJzsgLy8g5byV5YWl6K+N5LqR5Zu+DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ1NwcmVhZEFuYWx5c2lzJywNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgb3JpZ2luYWxUb3BOYXY6IHVuZGVmaW5lZCwgLy8g5a2Y5YKo5Y6f5aeL55qEdG9wTmF254q25oCBDQogICAgICAvLyDmlrnlpKrnrZvpgInnm7jlhbPmlbDmja4NCiAgICAgIGZhbmd0YWlUaW1lUmFuZ2U6ICd0b2RheScsDQogICAgICBmYW5ndGFpQ3VzdG9tVGltZVJhbmdlOiBbXSwNCiAgICAgIGZhbmd0YWlTZWxlY3RlZFBsYXRmb3JtczogWydhbGxfcGxhdGZvcm0nXSwNCiAgICAgIGZhbmd0YWlTZWxlY3RlZFNlbnRpbWVudHM6IFsnYWxsX3NlbnRpbWVudCddLA0KICAgICAgc3VtbWFyeUNhcmRzOiBbDQogICAgICAgIHsgdGl0bGU6ICfkv6Hmga/mgLvph48nLCB2YWx1ZTogJzQ2NTMnLCBjb2xvcjogJyM0MDlFRkYnIH0sDQogICAgICAgIHsgdGl0bGU6ICfmraPpnaLlo7Dph48nLCB2YWx1ZTogJzU4JywgY29sb3I6ICcjNjdDMjNBJyB9LA0KICAgICAgICB7IHRpdGxlOiAn5Lit5oCn5aOw6YePJywgdmFsdWU6ICc0NTgzJywgY29sb3I6ICcjRTZBMjNDJyB9LA0KICAgICAgICB7IHRpdGxlOiAn6LSf6Z2i5aOw6YePJywgdmFsdWU6ICcxMicsIGNvbG9yOiAnI0Y1NkM2QycgfSwNCiAgICAgICAgeyB0aXRsZTogJ+WqkuS9k+aAu+aVsCcsIHZhbHVlOiAnMTg1MycsIGNvbG9yOiAnIzkwOTM5OScgfQ0KICAgICAgXSwNCiAgICAgIGFjdGl2ZVRhYjogJ3NwcmVhZCcsDQogICAgICBzZWFyY2hUeXBlOiAnYnJhbmQnLA0KICAgICAgYnJhbmROYW1lOiAn5pa55aSqJywNCiAgICAgIHNlYXJjaFF1ZXJ5OiAnJywNCiAgICAgIGNoYXJ0czogew0KICAgICAgICB0cmVuZENoYXJ0OiBudWxsLA0KICAgICAgICBzb3VyY2VDaGFydDogbnVsbCwNCiAgICAgICAgaGVhdENoYXJ0OiBudWxsLA0KICAgICAgICBldmVudENoYXJ0OiBudWxsLA0KICAgICAgICBzb3VyY2VEaXN0Q2hhcnQ6IG51bGwsDQogICAgICAgIGtleXdvcmRDbG91ZENoYXJ0OiBudWxsDQogICAgICB9DQogICAgfQ0KICB9LA0KICBtb3VudGVkKCkgew0KICAgIC8vIOmakOiXj+mhtumDqOWvvOiIquagjw0KICAgIHRoaXMub3JpZ2luYWxUb3BOYXYgPSB0aGlzLiRzdG9yZS5zdGF0ZS5zZXR0aW5ncy50b3BOYXYNCiAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnc2V0dGluZ3MvY2hhbmdlU2V0dGluZycsIHsNCiAgICAgIGtleTogJ3RvcE5hdicsDQogICAgICB2YWx1ZTogZmFsc2UNCiAgICB9KQ0KDQogICAgdGhpcy5pbml0Q2hhcnRzKCkNCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICBzZWFyY2hUeXBlKG5ld1ZhbCkgew0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICB0aGlzLnJlc2l6ZUNoYXJ0cygpOw0KICAgICAgICBpZiAobmV3VmFsID09PSAnYnJhbmQnICYmIHRoaXMuYnJhbmROYW1lID09PSAn5pa55aSqJykgew0KICAgICAgICAgIC8vIFBvdGVudGlhbGx5IHJlLXJlbmRlciBvciB1cGRhdGUgY2hhcnRzIHNwZWNpZmljIHRvICfmlrnlpKonIHZpZXcgaWYgbmVlZGVkDQogICAgICAgICAgaWYgKCF0aGlzLmNoYXJ0cy5rZXl3b3JkQ2xvdWRDaGFydCkgew0KICAgICAgICAgICAgIHRoaXMuY2hhcnRzLmtleXdvcmRDbG91ZENoYXJ0ID0gZWNoYXJ0cy5pbml0KHRoaXMuJHJlZnMua2V5d29yZENsb3VkQ2hhcnQpOw0KICAgICAgICAgICAgIHRoaXMucmVuZGVyS2V5d29yZENsb3VkQ2hhcnQoKTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy5yZW5kZXJLZXl3b3JkQ2xvdWRDaGFydCgpOyAvLyBSZS1yZW5kZXIgaWYgYWxyZWFkeSBpbml0aWFsaXplZA0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgaGFuZGxlVGFiU2VsZWN0KGtleSkgew0KICAgICAgdGhpcy5hY3RpdmVUYWIgPSBrZXkNCiAgICB9LA0KICAgIGhhbmRsZUZhbmd0YWlGaWx0ZXJDaGFuZ2UoKSB7DQogICAgICAvLyDlpITnkIbmlrnlpKrnrZvpgInmnaHku7blj5jljJbvvIzlj6/ku6Xop6blj5HmlbDmja7ph43mlrDliqDovb3miJblm77ooajmm7TmlrANCiAgICAgIGNvbnNvbGUubG9nKCfmlrnlpKrnrZvpgInmnaHku7blj5jljJY6Jywgew0KICAgICAgICB0aW1lUmFuZ2U6IHRoaXMuZmFuZ3RhaVRpbWVSYW5nZSwNCiAgICAgICAgY3VzdG9tVGltZTogdGhpcy5mYW5ndGFpQ3VzdG9tVGltZVJhbmdlLA0KICAgICAgICBwbGF0Zm9ybXM6IHRoaXMuZmFuZ3RhaVNlbGVjdGVkUGxhdGZvcm1zLA0KICAgICAgICBzZW50aW1lbnRzOiB0aGlzLmZhbmd0YWlTZWxlY3RlZFNlbnRpbWVudHMNCiAgICAgIH0pOw0KICAgICAgLy8g6Kem5Y+R5Zu+6KGo5pu05paw6YC76L6RDQogICAgICB0aGlzLmFwcGx5RmFuZ3RhaUZpbHRlcnMoKTsNCiAgICB9LA0KICAgIGFwcGx5RmFuZ3RhaUZpbHRlcnMoKSB7DQogICAgICAvLyDlupTnlKjmlrnlpKrnrZvpgInmnaHku7bvvIzlrp7pmYXlupTnlKjkuK3kvJrosIPnlKhBUEnojrflj5bmlbDmja7lubbmm7TmlrDlm77ooagNCiAgICAgIGNvbnNvbGUubG9nKCflupTnlKjmlrnlpKrnrZvpgIknKTsNCiAgICAgIC8vIOekuuS+i++8mumHjeaWsOa4suafk+aJgOacieWbvuihqA0KICAgICAgdGhpcy5yZW5kZXJUcmVuZENoYXJ0KCk7IC8vIOazqOaEj++8muWbvuS4gOS4reatpOWbvuihqOS4uuKAnOaVsOaNruaxh+aAu+KAnQ0KICAgICAgdGhpcy5yZW5kZXJTb3VyY2VDaGFydCgpOyAvLyDms6jmhI/vvJrlm77kuIDkuK3mraTlm77ooajkuLrigJzlubPlj7Dlo7Dph4/ljaDmr5TigJ0NCiAgICAgIHRoaXMucmVuZGVySGVhdENoYXJ0KCk7IC8vIOazqOaEj++8muWbvuS4gOS4reatpOWbvuihqOS4uuKAnOaDheaEn+eUu+WDj+KAnQ0KICAgICAgdGhpcy5yZW5kZXJFdmVudENoYXJ0KCk7IC8vIOazqOaEj++8muWbvuS4gOS4reatpOWbvuihqOS4uuKAnOWqkuS9k+exu+Wei+WIhuW4g+KAnQ0KICAgICAgdGhpcy5yZW5kZXJTb3VyY2VEaXN0Q2hhcnQoKTsgLy8g5rOo5oSP77ya5Zu+5LiA5Lit5q2k5Zu+6KGo5Li64oCc5aqS5L2T5aOw6YeP5Y2g5q+U4oCdDQogICAgICBpZiAodGhpcy5zZWFyY2hUeXBlID09PSAnYnJhbmQnICYmIHRoaXMuYnJhbmROYW1lID09PSAn5pa55aSqJyAmJiB0aGlzLiRyZWZzLmtleXdvcmRDbG91ZENoYXJ0KSB7DQogICAgICAgIGlmICghdGhpcy5jaGFydHMua2V5d29yZENsb3VkQ2hhcnQpIHsNCiAgICAgICAgICAgIHRoaXMuY2hhcnRzLmtleXdvcmRDbG91ZENoYXJ0ID0gZWNoYXJ0cy5pbml0KHRoaXMuJHJlZnMua2V5d29yZENsb3VkQ2hhcnQpOw0KICAgICAgICB9DQogICAgICAgIHRoaXMucmVuZGVyS2V5d29yZENsb3VkQ2hhcnQoKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIHJlc2V0RmFuZ3RhaUZpbHRlcnMoKSB7DQogICAgICB0aGlzLmZhbmd0YWlUaW1lUmFuZ2UgPSAndG9kYXknOw0KICAgICAgdGhpcy5mYW5ndGFpQ3VzdG9tVGltZVJhbmdlID0gW107DQogICAgICB0aGlzLmZhbmd0YWlTZWxlY3RlZFBsYXRmb3JtcyA9IFsnYWxsX3BsYXRmb3JtJ107DQogICAgICB0aGlzLmZhbmd0YWlTZWxlY3RlZFNlbnRpbWVudHMgPSBbJ2FsbF9zZW50aW1lbnQnXTsNCiAgICAgIGNvbnNvbGUubG9nKCfph43nva7mlrnlpKrnrZvpgIknKTsNCiAgICAgIHRoaXMuYXBwbHlGYW5ndGFpRmlsdGVycygpOw0KICAgIH0sDQogICAgaW5pdENoYXJ0cygpIHsNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgLy8g5Yid5aeL5YyW5Lyg5pKt54Ot5bqm6LaL5Yq/5Zu+DQogICAgICAgIHRoaXMuY2hhcnRzLnRyZW5kQ2hhcnQgPSBlY2hhcnRzLmluaXQodGhpcy4kcmVmcy50cmVuZENoYXJ0KQ0KICAgICAgICB0aGlzLnJlbmRlclRyZW5kQ2hhcnQoKQ0KDQogICAgICAgIC8vIOWIneWni+WMluW5s+WPsOadpea6kOWNoOavlOWbvg0KICAgICAgICB0aGlzLmNoYXJ0cy5zb3VyY2VDaGFydCA9IGVjaGFydHMuaW5pdCh0aGlzLiRyZWZzLnNvdXJjZUNoYXJ0KQ0KICAgICAgICB0aGlzLnJlbmRlclNvdXJjZUNoYXJ0KCkNCg0KICAgICAgICAvLyDliJ3lp4vljJbkvKDmkq3ng63luqblm74NCiAgICAgICAgdGhpcy5jaGFydHMuaGVhdENoYXJ0ID0gZWNoYXJ0cy5pbml0KHRoaXMuJHJlZnMuaGVhdENoYXJ0KQ0KICAgICAgICB0aGlzLnJlbmRlckhlYXRDaGFydCgpDQoNCiAgICAgICAgLy8g5Yid5aeL5YyW6IiG5oOF5LqL5Lu25YiG5biD5Zu+DQogICAgICAgIHRoaXMuY2hhcnRzLmV2ZW50Q2hhcnQgPSBlY2hhcnRzLmluaXQodGhpcy4kcmVmcy5ldmVudENoYXJ0KQ0KICAgICAgICB0aGlzLnJlbmRlckV2ZW50Q2hhcnQoKQ0KDQogICAgICAgIC8vIOWIneWni+WMluiIhuaDheadpea6kOWNoOavlOWbvg0KICAgICAgICB0aGlzLmNoYXJ0cy5zb3VyY2VEaXN0Q2hhcnQgPSBlY2hhcnRzLmluaXQodGhpcy4kcmVmcy5zb3VyY2VEaXN0Q2hhcnQpDQogICAgICAgIHRoaXMucmVuZGVyU291cmNlRGlzdENoYXJ0KCkNCg0KICAgICAgICAvLyDlpoLmnpzliJ3lp4vliqDovb3ml7blsLHmmK/mlrnlpKrlk4HniYzvvIzliJnliJ3lp4vljJbor43kupHlm74NCiAgICAgICAgaWYgKHRoaXMuc2VhcmNoVHlwZSA9PT0gJ2JyYW5kJyAmJiB0aGlzLmJyYW5kTmFtZSA9PT0gJ+aWueWkqicgJiYgdGhpcy4kcmVmcy5rZXl3b3JkQ2xvdWRDaGFydCkgew0KICAgICAgICAgIHRoaXMuY2hhcnRzLmtleXdvcmRDbG91ZENoYXJ0ID0gZWNoYXJ0cy5pbml0KHRoaXMuJHJlZnMua2V5d29yZENsb3VkQ2hhcnQpDQogICAgICAgICAgdGhpcy5yZW5kZXJLZXl3b3JkQ2xvdWRDaGFydCgpDQogICAgICAgIH0NCg0KICAgICAgICAvLyDnm5HlkKznqpflj6PlpKflsI/lj5jljJbvvIzosIPmlbTlm77ooajlpKflsI8NCiAgICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMucmVzaXplQ2hhcnRzKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHJlbmRlcktleXdvcmRDbG91ZENoYXJ0KCkgew0KICAgICAgaWYgKCF0aGlzLmNoYXJ0cy5rZXl3b3JkQ2xvdWRDaGFydCkgcmV0dXJuOw0KICAgICAgY29uc3Qgb3B0aW9uID0gew0KICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgc2hvdzogdHJ1ZQ0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXM6IFt7DQogICAgICAgICAgdHlwZTogJ3dvcmRDbG91ZCcsDQogICAgICAgICAgZ3JpZFNpemU6IDIsDQogICAgICAgICAgc2l6ZVJhbmdlOiBbMTIsIDUwXSwNCiAgICAgICAgICByb3RhdGlvblJhbmdlOiBbLTkwLCA5MF0sDQogICAgICAgICAgc2hhcGU6ICdwZW50YWdvbicsDQogICAgICAgICAgd2lkdGg6ICc5MCUnLA0KICAgICAgICAgIGhlaWdodDogJzkwJScsDQogICAgICAgICAgdGV4dFN0eWxlOiB7DQogICAgICAgICAgICBmb250RmFtaWx5OiAnc2Fucy1zZXJpZicsDQogICAgICAgICAgICBmb250V2VpZ2h0OiAnYm9sZCcsDQogICAgICAgICAgICBjb2xvcjogZnVuY3Rpb24gKCkgew0KICAgICAgICAgICAgICByZXR1cm4gJ3JnYignICsgWw0KICAgICAgICAgICAgICAgIE1hdGgucm91bmQoTWF0aC5yYW5kb20oKSAqIDE2MCksDQogICAgICAgICAgICAgICAgTWF0aC5yb3VuZChNYXRoLnJhbmRvbSgpICogMTYwKSwNCiAgICAgICAgICAgICAgICBNYXRoLnJvdW5kKE1hdGgucmFuZG9tKCkgKiAxNjApDQogICAgICAgICAgICAgIF0uam9pbignLCcpICsgJyknOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgZW1waGFzaXM6IHsNCiAgICAgICAgICAgIGZvY3VzOiAnc2VsZicsDQogICAgICAgICAgICB0ZXh0U3R5bGU6IHsNCiAgICAgICAgICAgICAgdGV4dFNoYWRvd0JsdXI6IDEwLA0KICAgICAgICAgICAgICB0ZXh0U2hhZG93Q29sb3I6ICcjMzMzJw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgZGF0YTogWw0KICAgICAgICAgICAgeyBuYW1lOiAn5pa55aSq5Lqn5ZOBJywgdmFsdWU6IDEwMDAwIH0sDQogICAgICAgICAgICB7IG5hbWU6ICfljqjmiL/nlLXlmagnLCB2YWx1ZTogNjE4MSB9LA0KICAgICAgICAgICAgeyBuYW1lOiAn5rK554Of5py6JywgdmFsdWU6IDQzODYgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+a0l+eil+acuicsIHZhbHVlOiA0MDU1IH0sDQogICAgICAgICAgICB7IG5hbWU6ICfpm4bmiJDngbYnLCB2YWx1ZTogMjQ2NyB9LA0KICAgICAgICAgICAgeyBuYW1lOiAn5ZSu5ZCO5pyN5YqhJywgdmFsdWU6IDIyNDQgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+aZuuiDveWOqOeUtScsIHZhbHVlOiAxODk4IH0sDQogICAgICAgICAgICB7IG5hbWU6ICfpq5jnq6/ljqjnlLUnLCB2YWx1ZTogMTQ4NCB9LA0KICAgICAgICAgICAgeyBuYW1lOiAn55So5oi35L2T6aqMJywgdmFsdWU6IDExMTIgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+aWsOWTgeWPkeW4gycsIHZhbHVlOiA5NjUgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+i0qOmHj+mXrumimCcsIHZhbHVlOiA4NDcgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+S7t+agvOS8mOaDoCcsIHZhbHVlOiA1ODIgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+WuieijheacjeWKoScsIHZhbHVlOiA1NTUgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+WTgeeJjOa0u+WKqCcsIHZhbHVlOiA1NTAgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+e6v+S4iui0reS5sCcsIHZhbHVlOiA0NjIgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+e6v+S4i+mXqOW6lycsIHZhbHVlOiAzNjYgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+WOqOeUteenkeaKgCcsIHZhbHVlOiAzNjAgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+eOr+S/neeQhuW/tScsIHZhbHVlOiAyODIgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+iuvuiuoee+juWtpicsIHZhbHVlOiAyNzMgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+WuouaIt+ivhOS7tycsIHZhbHVlOiAyNjUgfQ0KICAgICAgICAgIF0NCiAgICAgICAgfV0NCiAgICAgIH07DQogICAgICB0aGlzLmNoYXJ0cy5rZXl3b3JkQ2xvdWRDaGFydC5zZXRPcHRpb24ob3B0aW9uKTsNCiAgICB9LA0KICAgIHJlc2l6ZUNoYXJ0cygpIHsNCiAgICAgIE9iamVjdC52YWx1ZXModGhpcy5jaGFydHMpLmZvckVhY2goY2hhcnQgPT4gew0KICAgICAgICBjaGFydCAmJiBjaGFydC5yZXNpemUoKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHJlbmRlcktleXdvcmRDbG91ZENoYXJ0KCkgew0KICAgICAgaWYgKCF0aGlzLmNoYXJ0cy5rZXl3b3JkQ2xvdWRDaGFydCkgcmV0dXJuOw0KICAgICAgY29uc3Qgb3B0aW9uID0gew0KICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgc2hvdzogdHJ1ZQ0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXM6IFt7DQogICAgICAgICAgdHlwZTogJ3dvcmRDbG91ZCcsDQogICAgICAgICAgZ3JpZFNpemU6IDIsDQogICAgICAgICAgc2l6ZVJhbmdlOiBbMTIsIDUwXSwNCiAgICAgICAgICByb3RhdGlvblJhbmdlOiBbLTkwLCA5MF0sDQogICAgICAgICAgc2hhcGU6ICdwZW50YWdvbicsDQogICAgICAgICAgd2lkdGg6ICc5MCUnLA0KICAgICAgICAgIGhlaWdodDogJzkwJScsDQogICAgICAgICAgdGV4dFN0eWxlOiB7DQogICAgICAgICAgICBmb250RmFtaWx5OiAnc2Fucy1zZXJpZicsDQogICAgICAgICAgICBmb250V2VpZ2h0OiAnYm9sZCcsDQogICAgICAgICAgICBjb2xvcjogZnVuY3Rpb24gKCkgew0KICAgICAgICAgICAgICByZXR1cm4gJ3JnYignICsgWw0KICAgICAgICAgICAgICAgIE1hdGgucm91bmQoTWF0aC5yYW5kb20oKSAqIDE2MCksDQogICAgICAgICAgICAgICAgTWF0aC5yb3VuZChNYXRoLnJhbmRvbSgpICogMTYwKSwNCiAgICAgICAgICAgICAgICBNYXRoLnJvdW5kKE1hdGgucmFuZG9tKCkgKiAxNjApDQogICAgICAgICAgICAgIF0uam9pbignLCcpICsgJyknOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgZW1waGFzaXM6IHsNCiAgICAgICAgICAgIGZvY3VzOiAnc2VsZicsDQogICAgICAgICAgICB0ZXh0U3R5bGU6IHsNCiAgICAgICAgICAgICAgdGV4dFNoYWRvd0JsdXI6IDEwLA0KICAgICAgICAgICAgICB0ZXh0U2hhZG93Q29sb3I6ICcjMzMzJw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgZGF0YTogWw0KICAgICAgICAgICAgeyBuYW1lOiAn5pa55aSq5Lqn5ZOBJywgdmFsdWU6IDEwMDAwIH0sDQogICAgICAgICAgICB7IG5hbWU6ICfljqjmiL/nlLXlmagnLCB2YWx1ZTogNjE4MSB9LA0KICAgICAgICAgICAgeyBuYW1lOiAn5rK554Of5py6JywgdmFsdWU6IDQzODYgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+a0l+eil+acuicsIHZhbHVlOiA0MDU1IH0sDQogICAgICAgICAgICB7IG5hbWU6ICfpm4bmiJDngbYnLCB2YWx1ZTogMjQ2NyB9LA0KICAgICAgICAgICAgeyBuYW1lOiAn5ZSu5ZCO5pyN5YqhJywgdmFsdWU6IDIyNDQgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+aZuuiDveWOqOeUtScsIHZhbHVlOiAxODk4IH0sDQogICAgICAgICAgICB7IG5hbWU6ICfpq5jnq6/ljqjnlLUnLCB2YWx1ZTogMTQ4NCB9LA0KICAgICAgICAgICAgeyBuYW1lOiAn55So5oi35L2T6aqMJywgdmFsdWU6IDExMTIgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+aWsOWTgeWPkeW4gycsIHZhbHVlOiA5NjUgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+i0qOmHj+mXrumimCcsIHZhbHVlOiA4NDcgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+S7t+agvOS8mOaDoCcsIHZhbHVlOiA1ODIgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+WuieijheacjeWKoScsIHZhbHVlOiA1NTUgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+WTgeeJjOa0u+WKqCcsIHZhbHVlOiA1NTAgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+e6v+S4iui0reS5sCcsIHZhbHVlOiA0NjIgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+e6v+S4i+mXqOW6lycsIHZhbHVlOiAzNjYgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+WOqOeUteenkeaKgCcsIHZhbHVlOiAzNjAgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+eOr+S/neeQhuW/tScsIHZhbHVlOiAyODIgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+iuvuiuoee+juWtpicsIHZhbHVlOiAyNzMgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+WuouaIt+ivhOS7tycsIHZhbHVlOiAyNjUgfQ0KICAgICAgICAgIF0NCiAgICAgICAgfV0NCiAgICAgIH07DQogICAgICB0aGlzLmNoYXJ0cy5rZXl3b3JkQ2xvdWRDaGFydC5zZXRPcHRpb24ob3B0aW9uKTsNCiAgICB9LA0KICAgIHJlbmRlclRyZW5kQ2hhcnQoKSB7DQogICAgICAvLyDkvKDmkq3ng63luqbotovlir/lm77phY3nva4NCiAgICAgIGNvbnN0IG9wdGlvbiA9IHsNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdheGlzJw0KICAgICAgICB9LA0KICAgICAgICBncmlkOiB7DQogICAgICAgICAgbGVmdDogJzMlJywNCiAgICAgICAgICByaWdodDogJzQlJywNCiAgICAgICAgICBib3R0b206ICczJScsDQogICAgICAgICAgY29udGFpbkxhYmVsOiB0cnVlDQogICAgICAgIH0sDQogICAgICAgIHhBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywNCiAgICAgICAgICBib3VuZGFyeUdhcDogZmFsc2UsDQogICAgICAgICAgZGF0YTogWycwMDowMCcsICcwMTowMCcsICcwMjowMCcsICcwMzowMCcsICcwNDowMCcsICcwNTowMCcsICcwNjowMCcsICcwNzowMCcsICcwODowMCcsICcwOTowMCcsICcxMDowMCcsICcxMTowMCcsICcxMjowMCcsICcxMzowMCcsICcxNDowMCcsICcxNTowMCcsICcxNjowMCcsICcxNzowMCcsICcxODowMCcsICcxOTowMCcsICcyMDowMCddDQogICAgICAgIH0sDQogICAgICAgIHlBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ3ZhbHVlJw0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXM6IFt7DQogICAgICAgICAgbmFtZTogJ+S8oOaSreeDreW6picsDQogICAgICAgICAgdHlwZTogJ2xpbmUnLA0KICAgICAgICAgIHNtb290aDogdHJ1ZSwNCiAgICAgICAgICBsaW5lU3R5bGU6IHsNCiAgICAgICAgICAgIGNvbG9yOiAnIzY3QzIzQScsDQogICAgICAgICAgICB3aWR0aDogMg0KICAgICAgICAgIH0sDQogICAgICAgICAgYXJlYVN0eWxlOiB7DQogICAgICAgICAgICBjb2xvcjogew0KICAgICAgICAgICAgICB0eXBlOiAnbGluZWFyJywNCiAgICAgICAgICAgICAgeDogMCwNCiAgICAgICAgICAgICAgeTogMCwNCiAgICAgICAgICAgICAgeDI6IDAsDQogICAgICAgICAgICAgIHkyOiAxLA0KICAgICAgICAgICAgICBjb2xvclN0b3BzOiBbew0KICAgICAgICAgICAgICAgIG9mZnNldDogMCwNCiAgICAgICAgICAgICAgICBjb2xvcjogJ3JnYmEoMTAzLCAxOTQsIDU4LCAwLjMpJw0KICAgICAgICAgICAgICB9LCB7DQogICAgICAgICAgICAgICAgb2Zmc2V0OiAxLA0KICAgICAgICAgICAgICAgIGNvbG9yOiAncmdiYSgxMDMsIDE5NCwgNTgsIDAuMSknDQogICAgICAgICAgICAgIH1dDQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICBkYXRhOiBbMTAwLCAxMjAsIDExMCwgMTI1LCAxMzAsIDE1MCwgMTYwLCAxNzAsIDE4MCwgMTkwLCAyMTAsIDIzMCwgMjEwLCAyMDAsIDE5MCwgMTgwLCAyMDAsIDIxMCwgMjIwLCAyMTAsIDIwMF0NCiAgICAgICAgfV0NCiAgICAgIH0NCiAgICAgIHRoaXMuY2hhcnRzLnRyZW5kQ2hhcnQuc2V0T3B0aW9uKG9wdGlvbikNCiAgICB9LA0KICAgIHJlbmRlclNvdXJjZUNoYXJ0KCkgew0KICAgICAgLy8g5bmz5Y+w5p2l5rqQ5Y2g5q+U5Zu+6YWN572uDQogICAgICBjb25zdCBvcHRpb24gPSB7DQogICAgICAgIHRvb2x0aXA6IHsNCiAgICAgICAgICB0cmlnZ2VyOiAnaXRlbScsDQogICAgICAgICAgZm9ybWF0dGVyOiAne2F9IDxici8+e2J9OiB7Y30gKHtkfSUpJw0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXM6IFt7DQogICAgICAgICAgbmFtZTogJ+W5s+WPsOadpea6kCcsDQogICAgICAgICAgdHlwZTogJ3BpZScsDQogICAgICAgICAgcmFkaXVzOiBbJzQwJScsICc3MCUnXSwNCiAgICAgICAgICBhdm9pZExhYmVsT3ZlcmxhcDogZmFsc2UsDQogICAgICAgICAgbGFiZWw6IHsNCiAgICAgICAgICAgIHNob3c6IHRydWUsDQogICAgICAgICAgICBwb3NpdGlvbjogJ291dHNpZGUnLA0KICAgICAgICAgICAgZm9ybWF0dGVyOiAne2J9OiB7ZH0lJw0KICAgICAgICAgIH0sDQogICAgICAgICAgZW1waGFzaXM6IHsNCiAgICAgICAgICAgIGxhYmVsOiB7DQogICAgICAgICAgICAgIHNob3c6IHRydWUsDQogICAgICAgICAgICAgIGZvbnRTaXplOiAnMTYnLA0KICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnYm9sZCcNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIGxhYmVsTGluZTogew0KICAgICAgICAgICAgc2hvdzogdHJ1ZQ0KICAgICAgICAgIH0sDQogICAgICAgICAgZGF0YTogWw0KICAgICAgICAgICAgeyB2YWx1ZTogMzEuMjYsIG5hbWU6ICflvq7ljZonLCBpdGVtU3R5bGU6IHsgY29sb3I6ICcjNENEMzg0JyB9IH0sDQogICAgICAgICAgICB7IHZhbHVlOiAzMS4xMSwgbmFtZTogJ+W+ruS/oScsIGl0ZW1TdHlsZTogeyBjb2xvcjogJyMzNkEzRjcnIH0gfSwNCiAgICAgICAgICAgIHsgdmFsdWU6IDE2LjMyLCBuYW1lOiAnQVBQJywgaXRlbVN0eWxlOiB7IGNvbG9yOiAnI0Y1NkM2QycgfSB9LA0KICAgICAgICAgICAgeyB2YWx1ZTogMTAuNDcsIG5hbWU6ICfnvZHnq5knLCBpdGVtU3R5bGU6IHsgY29sb3I6ICcjRTZBMjNDJyB9IH0sDQogICAgICAgICAgICB7IHZhbHVlOiA1LjQ3LCBuYW1lOiAn6K665Z2bJywgaXRlbVN0eWxlOiB7IGNvbG9yOiAnIzkwOTM5OScgfSB9LA0KICAgICAgICAgICAgeyB2YWx1ZTogMy4yOSwgbmFtZTogJ+aKpeWIiicsIGl0ZW1TdHlsZTogeyBjb2xvcjogJyM5QjU1RkYnIH0gfSwNCiAgICAgICAgICAgIHsgdmFsdWU6IDIuMDgsIG5hbWU6ICflhbbku5YnLCBpdGVtU3R5bGU6IHsgY29sb3I6ICcjRkY5QTJGJyB9IH0NCiAgICAgICAgICBdDQogICAgICAgIH1dDQogICAgICB9DQogICAgICB0aGlzLmNoYXJ0cy5zb3VyY2VDaGFydC5zZXRPcHRpb24ob3B0aW9uKQ0KICAgIH0sDQogICAgcmVuZGVySGVhdENoYXJ0KCkgew0KICAgICAgLy8g5Lyg5pKt54Ot5bqm5Zu+6YWN572uDQogICAgICBjb25zdCBvcHRpb24gPSB7DQogICAgICAgIHRvb2x0aXA6IHsNCiAgICAgICAgICB0cmlnZ2VyOiAnYXhpcycNCiAgICAgICAgfSwNCiAgICAgICAgZ3JpZDogew0KICAgICAgICAgIGxlZnQ6ICczJScsDQogICAgICAgICAgcmlnaHQ6ICc0JScsDQogICAgICAgICAgYm90dG9tOiAnMyUnLA0KICAgICAgICAgIGNvbnRhaW5MYWJlbDogdHJ1ZQ0KICAgICAgICB9LA0KICAgICAgICB4QXhpczogew0KICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsDQogICAgICAgICAgYm91bmRhcnlHYXA6IGZhbHNlLA0KICAgICAgICAgIGRhdGE6IFsnMDA6MDAnLCAnMDI6MDAnLCAnMDQ6MDAnLCAnMDY6MDAnLCAnMDg6MDAnLCAnMTA6MDAnLCAnMTI6MDAnLCAnMTQ6MDAnLCAnMTY6MDAnLCAnMTg6MDAnLCAnMjA6MDAnXQ0KICAgICAgICB9LA0KICAgICAgICB5QXhpczogew0KICAgICAgICAgIHR5cGU6ICd2YWx1ZScNCiAgICAgICAgfSwNCiAgICAgICAgbGVnZW5kOiB7DQogICAgICAgICAgZGF0YTogWyfmraPpnaLlo7Dph48nLCAn5Lit5oCn5aOw6YePJywgJ+i0n+mdouWjsOmHjyddLA0KICAgICAgICAgIGJvdHRvbTogMTANCiAgICAgICAgfSwNCiAgICAgICAgc2VyaWVzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogJ+ato+mdouWjsOmHjycsDQogICAgICAgICAgICB0eXBlOiAnbGluZScsDQogICAgICAgICAgICBzbW9vdGg6IHRydWUsDQogICAgICAgICAgICBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNjdDMjNBJyB9LA0KICAgICAgICAgICAgYXJlYVN0eWxlOiB7IGNvbG9yOiBuZXcgZWNoYXJ0cy5ncmFwaGljLkxpbmVhckdyYWRpZW50KDAsIDAsIDAsIDEsIFt7IG9mZnNldDogMCwgY29sb3I6ICdyZ2JhKDEwMywgMTk0LCA1OCwgMC4zKScgfSwgeyBvZmZzZXQ6IDEsIGNvbG9yOiAncmdiYSgxMDMsIDE5NCwgNTgsIDAuMSknIH1dKSB9LA0KICAgICAgICAgICAgZGF0YTogWzEwLCAxNSwgMTIsIDE4LCAyMiwgMjUsIDMwLCAyOCwgMzUsIDQwLCAzOCwgNDIsIDQ1LCA1MCwgNDgsIDUyLCA1NSwgNTgsIDYwLCA1NiwgNTBdDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAn5Lit5oCn5aOw6YePJywNCiAgICAgICAgICAgIHR5cGU6ICdsaW5lJywNCiAgICAgICAgICAgIHNtb290aDogdHJ1ZSwNCiAgICAgICAgICAgIGxpbmVTdHlsZTogeyBjb2xvcjogJyNFNkEyM0MnIH0sDQogICAgICAgICAgICBhcmVhU3R5bGU6IHsgY29sb3I6IG5ldyBlY2hhcnRzLmdyYXBoaWMuTGluZWFyR3JhZGllbnQoMCwgMCwgMCwgMSwgW3sgb2Zmc2V0OiAwLCBjb2xvcjogJ3JnYmEoMjMwLCAxNjIsIDYwLCAwLjMpJyB9LCB7IG9mZnNldDogMSwgY29sb3I6ICdyZ2JhKDIzMCwgMTYyLCA2MCwgMC4xKScgfV0pIH0sDQogICAgICAgICAgICBkYXRhOiBbMTAwLCAxMjAsIDExMCwgMTI1LCAxMzAsIDE1MCwgMTYwLCAxNzAsIDE4MCwgMTkwLCAyMTAsIDIzMCwgMjEwLCAyMDAsIDE5MCwgMTgwLCAyMDAsIDIxMCwgMjIwLCAyMTAsIDIwMF0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG5hbWU6ICfotJ/pnaLlo7Dph48nLA0KICAgICAgICAgICAgdHlwZTogJ2xpbmUnLA0KICAgICAgICAgICAgc21vb3RoOiB0cnVlLA0KICAgICAgICAgICAgbGluZVN0eWxlOiB7IGNvbG9yOiAnI0Y1NkM2QycgfSwNCiAgICAgICAgICAgIGFyZWFTdHlsZTogeyBjb2xvcjogbmV3IGVjaGFydHMuZ3JhcGhpYy5MaW5lYXJHcmFkaWVudCgwLCAwLCAwLCAxLCBbeyBvZmZzZXQ6IDAsIGNvbG9yOiAncmdiYSgyNDUsIDEwOCwgMTA4LCAwLjMpJyB9LCB7IG9mZnNldDogMSwgY29sb3I6ICdyZ2JhKDI0NSwgMTA4LCAxMDgsIDAuMSknIH1dKSB9LA0KICAgICAgICAgICAgZGF0YTogWzUsIDgsIDYsIDEwLCAxMiwgOSwgMTEsIDE0LCAxMCwgMTMsIDE1LCAxMiwgMTYsIDE4LCAxNSwgMTcsIDIwLCAxNiwgMTksIDIyLCAxOF0NCiAgICAgICAgICB9DQogICAgICAgIF0NCiAgICAgIH0NCiAgICAgIHRoaXMuY2hhcnRzLmhlYXRDaGFydC5zZXRPcHRpb24ob3B0aW9uKQ0KICAgIH0sDQogICAgcmVuZGVyRXZlbnRDaGFydCgpIHsNCiAgICAgIC8vIOiIhuaDheS6i+S7tuWIhuW4g+WbvumFjee9rg0KICAgICAgY29uc3Qgb3B0aW9uID0gew0KICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgdHJpZ2dlcjogJ2F4aXMnDQogICAgICAgIH0sDQogICAgICAgIGdyaWQ6IHsNCiAgICAgICAgICBsZWZ0OiAnMyUnLA0KICAgICAgICAgIHJpZ2h0OiAnNCUnLA0KICAgICAgICAgIGJvdHRvbTogJzMlJywNCiAgICAgICAgICBjb250YWluTGFiZWw6IHRydWUNCiAgICAgICAgfSwNCiAgICAgICAgeEF4aXM6IHsNCiAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLA0KICAgICAgICAgIGRhdGE6IFsn5paw6Ze7JywgJ0FQUCcsICforrrlnZsnLCAn5Y2a5a6iJywgJ+W+ruWNmicsICfop4bpopEnLCAn5b6u5L+hJ10NCiAgICAgICAgfSwNCiAgICAgICAgeUF4aXM6IHsNCiAgICAgICAgICB0eXBlOiAndmFsdWUnDQogICAgICAgIH0sDQogICAgICAgIHNlcmllczogW3sNCiAgICAgICAgICBuYW1lOiAn5LqL5Lu25pWw6YePJywNCiAgICAgICAgICB0eXBlOiAnYmFyJywNCiAgICAgICAgICBiYXJXaWR0aDogJzYwJScsDQogICAgICAgICAgZGF0YTogWzMyMCwgMjgwLCAyMjAsIDE4MCwgMTUwLCAxMjAsIDEwMF0NCiAgICAgICAgfV0NCiAgICAgIH0NCiAgICAgIHRoaXMuY2hhcnRzLmV2ZW50Q2hhcnQuc2V0T3B0aW9uKG9wdGlvbikNCiAgICB9LA0KICAgIHJlbmRlclNvdXJjZURpc3RDaGFydCgpIHsNCiAgICAgIC8vIOiIhuaDheadpea6kOWNoOavlOWbvumFjee9rg0KICAgICAgY29uc3Qgb3B0aW9uID0gew0KICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgdHJpZ2dlcjogJ2F4aXMnLA0KICAgICAgICAgIGF4aXNQb2ludGVyOiB7DQogICAgICAgICAgICB0eXBlOiAnY3Jvc3MnLA0KICAgICAgICAgICAgbGFiZWw6IHsNCiAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnIzZhNzk4NScNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0sDQogICAgICAgIGdyaWQ6IHsNCiAgICAgICAgICBsZWZ0OiAnMyUnLA0KICAgICAgICAgIHJpZ2h0OiAnNCUnLA0KICAgICAgICAgIGJvdHRvbTogJzMlJywNCiAgICAgICAgICBjb250YWluTGFiZWw6IHRydWUNCiAgICAgICAgfSwNCiAgICAgICAgeEF4aXM6IHsNCiAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLA0KICAgICAgICAgIGJvdW5kYXJ5R2FwOiBmYWxzZSwNCiAgICAgICAgICBkYXRhOiBbJzAwOjAwJywgJzAyOjAwJywgJzA0OjAwJywgJzA2OjAwJywgJzA4OjAwJywgJzEwOjAwJywgJzEyOjAwJywgJzE0OjAwJywgJzE2OjAwJywgJzE4OjAwJywgJzIwOjAwJ10NCiAgICAgICAgfSwNCiAgICAgICAgeUF4aXM6IHsNCiAgICAgICAgICB0eXBlOiAndmFsdWUnDQogICAgICAgIH0sDQogICAgICAgIHNlcmllczogW3sNCiAgICAgICAgICBuYW1lOiAn5aqS5L2T5aOw6YeP5Y2g5q+UJywNCiAgICAgICAgICB0eXBlOiAncGllJywNCiAgICAgICAgICByYWRpdXM6IFsnNDAlJywgJzcwJSddLA0KICAgICAgICAgIGF2b2lkTGFiZWxPdmVybGFwOiBmYWxzZSwNCiAgICAgICAgICBsYWJlbDogew0KICAgICAgICAgICAgc2hvdzogdHJ1ZSwNCiAgICAgICAgICAgIHBvc2l0aW9uOiAnb3V0c2lkZScsDQogICAgICAgICAgICBmb3JtYXR0ZXI6ICd7Yn06IHtkfSUnDQogICAgICAgICAgfSwNCiAgICAgICAgICBlbXBoYXNpczogew0KICAgICAgICAgICAgbGFiZWw6IHsNCiAgICAgICAgICAgICAgc2hvdzogdHJ1ZSwNCiAgICAgICAgICAgICAgZm9udFNpemU6ICcxNicsDQogICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICdib2xkJw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgbGFiZWxMaW5lOiB7DQogICAgICAgICAgICBzaG93OiB0cnVlDQogICAgICAgICAgfSwNCiAgICAgICAgICBkYXRhOiBbDQogICAgICAgICAgICB7IHZhbHVlOiAzNSwgbmFtZTogJ+aWsOmXu+WuouaIt+errycsIGl0ZW1TdHlsZTogeyBjb2xvcjogJyM1NDcwQzYnfSB9LA0KICAgICAgICAgICAgeyB2YWx1ZTogMjUsIG5hbWU6ICfnpL7kuqTlqpLkvZMnLCBpdGVtU3R5bGU6IHsgY29sb3I6ICcjOTFDQzc1J30gfSwNCiAgICAgICAgICAgIHsgdmFsdWU6IDE1LCBuYW1lOiAn6KeG6aKR5bmz5Y+wJywgaXRlbVN0eWxlOiB7IGNvbG9yOiAnI0ZBQzg1OCd9IH0sDQogICAgICAgICAgICB7IHZhbHVlOiAxMCwgbmFtZTogJ+S8oOe7n+WqkuS9k+e9keermScsIGl0ZW1TdHlsZTogeyBjb2xvcjogJyNFRTY2NjYnfSB9LA0KICAgICAgICAgICAgeyB2YWx1ZTogOCwgbmFtZTogJ+iuuuWdm+WNmuWuoicsIGl0ZW1TdHlsZTogeyBjb2xvcjogJyM3M0MwREUnfSB9LA0KICAgICAgICAgICAgeyB2YWx1ZTogNywgbmFtZTogJ+WFtuS7licsIGl0ZW1TdHlsZTogeyBjb2xvcjogJyMzQkEyNzInfSB9DQogICAgICAgICAgXQ0KICAgICAgICB9XQ0KICAgICAgfQ0KICAgICAgdGhpcy5jaGFydHMuc291cmNlRGlzdENoYXJ0LnNldE9wdGlvbihvcHRpb24pDQogICAgfQ0KICB9LA0KICBiZWZvcmVEZXN0cm95KCkgew0KICAgIC8vIOaBouWkjemhtumDqOWvvOiIquagj+iuvue9rg0KICAgIGlmICh0aGlzLm9yaWdpbmFsVG9wTmF2ICE9PSB1bmRlZmluZWQpIHsNCiAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdzZXR0aW5ncy9jaGFuZ2VTZXR0aW5nJywgew0KICAgICAgICBrZXk6ICd0b3BOYXYnLA0KICAgICAgICB2YWx1ZTogdGhpcy5vcmlnaW5hbFRvcE5hdg0KICAgICAgfSkNCiAgICB9DQoNCiAgICAvLyDnp7vpmaTnqpflj6PlpKflsI/lj5jljJbnm5HlkKwNCiAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigncmVzaXplJywgdGhpcy5yZXNpemVDaGFydHMpDQogICAgLy8g6ZSA5q+B5Zu+6KGo5a6e5L6LDQogICAgT2JqZWN0LnZhbHVlcyh0aGlzLmNoYXJ0cykuZm9yRWFjaChjaGFydCA9PiB7DQogICAgICBjaGFydCAmJiBjaGFydC5kaXNwb3NlKCkNCiAgICB9KQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAs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file": "index.vue", "sourceRoot": "src/views/spread-analysis", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 内容区域 -->\r\n    <div class=\"content-container\">\r\n      <!-- 左侧搜索方案区域 -->\r\n      <div class=\"sidebar\">\r\n        <div class=\"search-plan\">\r\n          <div class=\"plan-actions\">\r\n            <el-button type=\"primary\" class=\"new-plan-btn\">\r\n              <i class=\"el-icon-plus\"></i> 新建方案\r\n            </el-button>\r\n            <el-button class=\"folder-btn\">\r\n              <i class=\"el-icon-folder-opened\"></i>\r\n            </el-button>\r\n          </div>\r\n          <div class=\"plan-search\">\r\n            <el-input\r\n              placeholder=\"方案搜索\"\r\n              prefix-icon=\"el-icon-search\"\r\n              v-model=\"searchQuery\"\r\n              clearable\r\n              size=\"small\"\r\n            ></el-input>\r\n          </div>\r\n          <div class=\"plan-divider\"></div>\r\n          <div class=\"plan-list\">\r\n            <div class=\"plan-item\" :class=\"{ 'active': searchType === 'product' }\">\r\n              <el-radio v-model=\"searchType\" label=\"product\">竞品(1)</el-radio>\r\n              <i class=\"el-icon-arrow-down\"></i>\r\n            </div>\r\n            <div class=\"plan-item\" :class=\"{ 'active': searchType === 'brand' }\">\r\n              <el-radio v-model=\"searchType\" label=\"brand\">品牌(1)</el-radio>\r\n              <i class=\"el-icon-arrow-up\"></i>\r\n            </div>\r\n            <div class=\"brand-detail\" v-if=\"searchType === 'brand'\">\r\n              <div class=\"brand-name\">方太</div>\r\n            </div>\r\n            <div class=\"plan-item\">\r\n              <el-radio v-model=\"searchType\" label=\"person\">人物(0)</el-radio>\r\n              <i class=\"el-icon-arrow-down\"></i>\r\n            </div>\r\n            <div class=\"plan-item\">\r\n              <el-radio v-model=\"searchType\" label=\"organization\">机构(0)</el-radio>\r\n              <i class=\"el-icon-arrow-down\"></i>\r\n            </div>\r\n            <div class=\"plan-item\">\r\n              <el-radio v-model=\"searchType\" label=\"product-detail\">产品(0)</el-radio>\r\n              <i class=\"el-icon-arrow-down\"></i>\r\n            </div>\r\n            <div class=\"plan-item\">\r\n              <el-radio v-model=\"searchType\" label=\"event\">事件(0)</el-radio>\r\n              <i class=\"el-icon-arrow-down\"></i>\r\n            </div>\r\n            <div class=\"plan-item\">\r\n              <el-radio v-model=\"searchType\" label=\"other\">其他(0)</el-radio>\r\n              <i class=\"el-icon-arrow-down\"></i>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧图表区域 -->\r\n      <div class=\"main-content\">\r\n        <!-- 筛选条件区域 - 方太 -->\r\n        <div class=\"fangtai-filter-container\" v-if=\"searchType === 'brand' && brandName === '方太'\">\r\n          <div class=\"filter-header\" style=\"display: flex; justify-content: space-between; align-items: center; padding-bottom: 15px; border-bottom: 1px solid #ebeef5;\">\r\n            <span class=\"filter-title\" style=\"font-size: 18px; font-weight: bold;\">\r\n              {{ brandName }}\r\n              <i class=\"el-icon-edit\" style=\"margin-left: 8px; cursor: pointer; font-size: 16px;\"></i>\r\n              <i class=\"el-icon-s-tools\" style=\"margin-left: 8px; cursor: pointer; font-size: 16px;\"></i>\r\n            </span>\r\n            <!-- Add any top-right actions for this header if needed -->\r\n          </div>\r\n\r\n          <div class=\"filter-content\" style=\"padding-top: 15px;\">\r\n            <!-- 时间范围筛选 -->\r\n            <el-form size=\"small\" label-width=\"80px\">\r\n              <el-form-item label=\"时间范围:\">\r\n                <el-radio-group v-model=\"fangtaiTimeRange\" @change=\"handleFangtaiFilterChange\">\r\n                  <el-radio-button label=\"today\">今天</el-radio-button>\r\n                  <el-radio-button label=\"yesterday\">昨天</el-radio-button>\r\n                  <el-radio-button label=\"7d\">近七天</el-radio-button>\r\n                  <el-radio-button label=\"30d\">近30天</el-radio-button>\r\n                  <el-radio-button label=\"custom\">自定义</el-radio-button>\r\n                </el-radio-group>\r\n                <el-date-picker\r\n                  v-if=\"fangtaiTimeRange === 'custom'\"\r\n                  v-model=\"fangtaiCustomTimeRange\"\r\n                  type=\"daterange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  style=\"margin-left: 10px; width: 240px;\"\r\n                  @change=\"handleFangtaiFilterChange\"\r\n                ></el-date-picker>\r\n              </el-form-item>\r\n\r\n              <!-- 平台类型筛选 -->\r\n              <el-form-item label=\"平台类型:\">\r\n                <el-checkbox-group v-model=\"fangtaiSelectedPlatforms\" @change=\"handleFangtaiFilterChange\">\r\n                  <el-checkbox label=\"all_platform\">全部</el-checkbox>\r\n                  <el-checkbox label=\"web\">网页</el-checkbox>\r\n                  <el-checkbox label=\"weibo\">微博</el-checkbox>\r\n                  <el-checkbox label=\"toutiao\">头条号</el-checkbox>\r\n                  <el-checkbox label=\"app\">APP</el-checkbox>\r\n                  <el-checkbox label=\"video\">视频</el-checkbox>\r\n                  <el-checkbox label=\"sms\">短信</el-checkbox>\r\n                  <el-checkbox label=\"newspaper\">报刊</el-checkbox>\r\n                  <el-checkbox label=\"sohu\">搜狐</el-checkbox>\r\n                </el-checkbox-group>\r\n              </el-form-item>\r\n\r\n              <!-- 情感倾向筛选 -->\r\n              <el-form-item label=\"情感倾向:\">\r\n                <el-checkbox-group v-model=\"fangtaiSelectedSentiments\" @change=\"handleFangtaiFilterChange\">\r\n                  <el-checkbox label=\"all_sentiment\">全部</el-checkbox>\r\n                  <el-checkbox label=\"positive\">正面</el-checkbox>\r\n                  <el-checkbox label=\"neutral\">中性</el-checkbox>\r\n                  <el-checkbox label=\"negative\">负面</el-checkbox>\r\n                  <el-checkbox label=\"warning_target\">预警对象</el-checkbox>\r\n                  <el-checkbox label=\"sensitive_info\">敏感信息</el-checkbox>\r\n                  <el-checkbox label=\"official_letter\">正式发函</el-checkbox>\r\n                  <el-checkbox label=\"delete_complaint\">删帖投诉</el-checkbox>\r\n                </el-checkbox-group>\r\n              </el-form-item>\r\n\r\n              <el-form-item>\r\n                <el-button type=\"primary\" @click=\"applyFangtaiFilters\">应用</el-button>\r\n                <el-button @click=\"resetFangtaiFilters\">重置</el-button>\r\n              </el-form-item>\r\n            </el-form>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 信息总览卡片区域 -->\r\n        <el-row :gutter=\"20\" class=\"summary-cards\" style=\"margin-top: 20px; margin-bottom: 20px;\">\r\n          <el-col :xl=\"4\" :lg=\"4\" :md=\"8\" :sm=\"12\" :xs=\"24\" v-for=\"card in summaryCards\" :key=\"card.title\">\r\n            <el-card shadow=\"hover\" class=\"summary-card\" :body-style=\"{ padding: '15px' }\">\r\n              <div style=\"font-size: 14px; color: #606266;\">{{ card.title }}</div>\r\n              <div style=\"font-size: 24px; font-weight: bold; margin-top: 5px;\" :style=\"{ color: card.color }\">{{ card.value }}</div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 第一行图表 - 数据汇总图 -->\r\n        <el-card class=\"chart-card\">\r\n          <div class=\"chart-header\">\r\n            <div class=\"chart-title\">\r\n              <i class=\"el-icon-data-line blue-line\"></i>\r\n              <span>数据汇总</span>\r\n            </div>\r\n            <div class=\"chart-actions\">\r\n              <el-button-group size=\"mini\">\r\n                <el-button type=\"primary\" plain>数据趋势</el-button>\r\n                <el-button plain>7天监测图</el-button>\r\n              </el-button-group>\r\n              <i class=\"el-icon-refresh\" style=\"margin-left:10px; cursor:pointer;\"></i>\r\n              <i class=\"el-icon-download\" style=\"margin-left:10px; cursor:pointer;\"></i>\r\n            </div>\r\n          </div>\r\n          <div class=\"chart-content\" style=\"height: 300px;\">\r\n            <!-- 这里放置传播热度趋势图表 -->\r\n            <div ref=\"trendChart\" class=\"chart\"></div>\r\n            <!-- 热点标记 -->\r\n            <div class=\"hotspot-markers\">\r\n              <!-- 热点标记内容将由JS动态生成 -->\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n\r\n        <!-- 关键热搜词云图 -->\r\n        <el-card class=\"chart-card\" style=\"margin-top: 20px;\" v-if=\"searchType === 'brand' && brandName === '方太'\">\r\n          <div class=\"chart-header\">\r\n            <div class=\"chart-title\">\r\n              <i class=\"el-icon-magic-stick blue-line\"></i> <!-- Consider a more relevant icon like el-icon-collection-tag or el-icon-price-tag -->\r\n              <span>关键热搜词云</span>\r\n            </div>\r\n            <div class=\"chart-actions\">\r\n              <i class=\"el-icon-refresh\" style=\"cursor:pointer;\"></i>\r\n              <i class=\"el-icon-download\" style=\"margin-left:10px; cursor:pointer;\"></i>\r\n            </div>\r\n          </div>\r\n          <div class=\"chart-content\" style=\"height: 300px;\">\r\n            <div ref=\"keywordCloudChart\" class=\"chart\"></div>\r\n          </div>\r\n        </el-card>\r\n\r\n        <!-- 第二行图表 -->\r\n        <el-row :gutter=\"20\" class=\"chart-row\">\r\n          <!-- 平台来源占比 -->\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"chart-card\">\r\n              <div class=\"chart-header\">\r\n                <div class=\"chart-title\">\r\n                  <i class=\"el-icon-pie-chart blue-line\"></i>\r\n                  <span>平台声量占比</span>\r\n                </div>\r\n                <div class=\"chart-actions\">\r\n                  <i class=\"el-icon-refresh\" style=\"cursor:pointer;\"></i>\r\n                  <i class=\"el-icon-download\" style=\"margin-left:10px; cursor:pointer;\"></i>\r\n                </div>\r\n              </div>\r\n              <div class=\"chart-content\" style=\"height: 250px;\">\r\n                <!-- 这里放置平台来源占比图表 -->\r\n                <div ref=\"sourceChart\" class=\"chart\"></div>\r\n                <!-- 图例 -->\r\n                <div class=\"chart-legend\">\r\n                  <div class=\"legend-item\">\r\n                    <span class=\"legend-color\" style=\"background-color: #4CD384;\"></span>\r\n                    <span class=\"legend-text\">微博</span>\r\n                  </div>\r\n                  <div class=\"legend-item\">\r\n                    <span class=\"legend-color\" style=\"background-color: #36A3F7;\"></span>\r\n                    <span class=\"legend-text\">微信</span>\r\n                  </div>\r\n                  <div class=\"legend-item\">\r\n                    <span class=\"legend-color\" style=\"background-color: #F56C6C;\"></span>\r\n                    <span class=\"legend-text\">APP</span>\r\n                  </div>\r\n                  <!-- 更多图例项 -->\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n\r\n          <!-- 传播热度 -->\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"chart-card\">\r\n              <div class=\"chart-header\">\r\n                <div class=\"chart-title\">\r\n                  <i class=\"el-icon-data-analysis blue-line\"></i>\r\n                  <span>情感画像</span>\r\n                </div>\r\n                <div class=\"chart-actions\">\r\n                  <i class=\"el-icon-refresh\"></i>\r\n                  <i class=\"el-icon-more\"></i>\r\n                </div>\r\n              </div>\r\n              <div class=\"chart-content\" style=\"height: 250px;\">\r\n                <!-- 这里放置传播热度图表 -->\r\n                <div ref=\"heatChart\" class=\"chart\"></div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 第三行图表 -->\r\n        <el-row :gutter=\"20\" class=\"chart-row\">\r\n          <!-- 舆情事件分布 -->\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"chart-card\">\r\n              <div class=\"chart-header\">\r\n                <div class=\"chart-title\">\r\n                  <i class=\"el-icon-s-data blue-line\"></i>\r\n                  <span>媒体类型分布</span>\r\n                </div>\r\n                <div class=\"chart-actions\">\r\n                  <i class=\"el-icon-refresh\"></i>\r\n                  <i class=\"el-icon-more\"></i>\r\n                </div>\r\n              </div>\r\n              <div class=\"chart-content\" style=\"height: 250px;\">\r\n                <!-- 这里放置舆情事件分布图表 -->\r\n                <div ref=\"eventChart\" class=\"chart\"></div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n\r\n          <!-- 舆情来源占比 -->\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"chart-card\">\r\n              <div class=\"chart-header\">\r\n                <div class=\"chart-title\">\r\n                  <i class=\"el-icon-pie-chart blue-line\"></i>\r\n                  <span>媒体声量占比</span>\r\n                </div>\r\n                <div class=\"chart-actions\">\r\n                  <i class=\"el-icon-refresh\"></i>\r\n                  <i class=\"el-icon-more\"></i>\r\n                </div>\r\n              </div>\r\n              <div class=\"chart-content\" style=\"height: 250px;\">\r\n                <!-- 这里放置舆情来源占比图表 -->\r\n                <div ref=\"sourceDistChart\" class=\"chart\"></div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nimport 'echarts-wordcloud'; // 引入词云图\r\n\r\nexport default {\r\n  name: 'SpreadAnalysis',\r\n  data() {\r\n    return {\r\n      originalTopNav: undefined, // 存储原始的topNav状态\r\n      // 方太筛选相关数据\r\n      fangtaiTimeRange: 'today',\r\n      fangtaiCustomTimeRange: [],\r\n      fangtaiSelectedPlatforms: ['all_platform'],\r\n      fangtaiSelectedSentiments: ['all_sentiment'],\r\n      summaryCards: [\r\n        { title: '信息总量', value: '4653', color: '#409EFF' },\r\n        { title: '正面声量', value: '58', color: '#67C23A' },\r\n        { title: '中性声量', value: '4583', color: '#E6A23C' },\r\n        { title: '负面声量', value: '12', color: '#F56C6C' },\r\n        { title: '媒体总数', value: '1853', color: '#909399' }\r\n      ],\r\n      activeTab: 'spread',\r\n      searchType: 'brand',\r\n      brandName: '方太',\r\n      searchQuery: '',\r\n      charts: {\r\n        trendChart: null,\r\n        sourceChart: null,\r\n        heatChart: null,\r\n        eventChart: null,\r\n        sourceDistChart: null,\r\n        keywordCloudChart: null\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    // 隐藏顶部导航栏\r\n    this.originalTopNav = this.$store.state.settings.topNav\r\n    this.$store.dispatch('settings/changeSetting', {\r\n      key: 'topNav',\r\n      value: false\r\n    })\r\n\r\n    this.initCharts()\r\n  },\r\n  watch: {\r\n    searchType(newVal) {\r\n      this.$nextTick(() => {\r\n        this.resizeCharts();\r\n        if (newVal === 'brand' && this.brandName === '方太') {\r\n          // Potentially re-render or update charts specific to '方太' view if needed\r\n          if (!this.charts.keywordCloudChart) {\r\n             this.charts.keywordCloudChart = echarts.init(this.$refs.keywordCloudChart);\r\n             this.renderKeywordCloudChart();\r\n          } else {\r\n            this.renderKeywordCloudChart(); // Re-render if already initialized\r\n          }\r\n        }\r\n      });\r\n    }\r\n  },\r\n  methods: {\r\n    handleTabSelect(key) {\r\n      this.activeTab = key\r\n    },\r\n    handleFangtaiFilterChange() {\r\n      // 处理方太筛选条件变化，可以触发数据重新加载或图表更新\r\n      console.log('方太筛选条件变化:', {\r\n        timeRange: this.fangtaiTimeRange,\r\n        customTime: this.fangtaiCustomTimeRange,\r\n        platforms: this.fangtaiSelectedPlatforms,\r\n        sentiments: this.fangtaiSelectedSentiments\r\n      });\r\n      // 触发图表更新逻辑\r\n      this.applyFangtaiFilters();\r\n    },\r\n    applyFangtaiFilters() {\r\n      // 应用方太筛选条件，实际应用中会调用API获取数据并更新图表\r\n      console.log('应用方太筛选');\r\n      // 示例：重新渲染所有图表\r\n      this.renderTrendChart(); // 注意：图一中此图表为“数据汇总”\r\n      this.renderSourceChart(); // 注意：图一中此图表为“平台声量占比”\r\n      this.renderHeatChart(); // 注意：图一中此图表为“情感画像”\r\n      this.renderEventChart(); // 注意：图一中此图表为“媒体类型分布”\r\n      this.renderSourceDistChart(); // 注意：图一中此图表为“媒体声量占比”\r\n      if (this.searchType === 'brand' && this.brandName === '方太' && this.$refs.keywordCloudChart) {\r\n        if (!this.charts.keywordCloudChart) {\r\n            this.charts.keywordCloudChart = echarts.init(this.$refs.keywordCloudChart);\r\n        }\r\n        this.renderKeywordCloudChart();\r\n      }\r\n    },\r\n    resetFangtaiFilters() {\r\n      this.fangtaiTimeRange = 'today';\r\n      this.fangtaiCustomTimeRange = [];\r\n      this.fangtaiSelectedPlatforms = ['all_platform'];\r\n      this.fangtaiSelectedSentiments = ['all_sentiment'];\r\n      console.log('重置方太筛选');\r\n      this.applyFangtaiFilters();\r\n    },\r\n    initCharts() {\r\n      this.$nextTick(() => {\r\n        // 初始化传播热度趋势图\r\n        this.charts.trendChart = echarts.init(this.$refs.trendChart)\r\n        this.renderTrendChart()\r\n\r\n        // 初始化平台来源占比图\r\n        this.charts.sourceChart = echarts.init(this.$refs.sourceChart)\r\n        this.renderSourceChart()\r\n\r\n        // 初始化传播热度图\r\n        this.charts.heatChart = echarts.init(this.$refs.heatChart)\r\n        this.renderHeatChart()\r\n\r\n        // 初始化舆情事件分布图\r\n        this.charts.eventChart = echarts.init(this.$refs.eventChart)\r\n        this.renderEventChart()\r\n\r\n        // 初始化舆情来源占比图\r\n        this.charts.sourceDistChart = echarts.init(this.$refs.sourceDistChart)\r\n        this.renderSourceDistChart()\r\n\r\n        // 如果初始加载时就是方太品牌，则初始化词云图\r\n        if (this.searchType === 'brand' && this.brandName === '方太' && this.$refs.keywordCloudChart) {\r\n          this.charts.keywordCloudChart = echarts.init(this.$refs.keywordCloudChart)\r\n          this.renderKeywordCloudChart()\r\n        }\r\n\r\n        // 监听窗口大小变化，调整图表大小\r\n        window.addEventListener('resize', this.resizeCharts)\r\n      })\r\n    },\r\n    renderKeywordCloudChart() {\r\n      if (!this.charts.keywordCloudChart) return;\r\n      const option = {\r\n        tooltip: {\r\n          show: true\r\n        },\r\n        series: [{\r\n          type: 'wordCloud',\r\n          gridSize: 2,\r\n          sizeRange: [12, 50],\r\n          rotationRange: [-90, 90],\r\n          shape: 'pentagon',\r\n          width: '90%',\r\n          height: '90%',\r\n          textStyle: {\r\n            fontFamily: 'sans-serif',\r\n            fontWeight: 'bold',\r\n            color: function () {\r\n              return 'rgb(' + [\r\n                Math.round(Math.random() * 160),\r\n                Math.round(Math.random() * 160),\r\n                Math.round(Math.random() * 160)\r\n              ].join(',') + ')';\r\n            }\r\n          },\r\n          emphasis: {\r\n            focus: 'self',\r\n            textStyle: {\r\n              textShadowBlur: 10,\r\n              textShadowColor: '#333'\r\n            }\r\n          },\r\n          data: [\r\n            { name: '方太产品', value: 10000 },\r\n            { name: '厨房电器', value: 6181 },\r\n            { name: '油烟机', value: 4386 },\r\n            { name: '洗碗机', value: 4055 },\r\n            { name: '集成灶', value: 2467 },\r\n            { name: '售后服务', value: 2244 },\r\n            { name: '智能厨电', value: 1898 },\r\n            { name: '高端厨电', value: 1484 },\r\n            { name: '用户体验', value: 1112 },\r\n            { name: '新品发布', value: 965 },\r\n            { name: '质量问题', value: 847 },\r\n            { name: '价格优惠', value: 582 },\r\n            { name: '安装服务', value: 555 },\r\n            { name: '品牌活动', value: 550 },\r\n            { name: '线上购买', value: 462 },\r\n            { name: '线下门店', value: 366 },\r\n            { name: '厨电科技', value: 360 },\r\n            { name: '环保理念', value: 282 },\r\n            { name: '设计美学', value: 273 },\r\n            { name: '客户评价', value: 265 }\r\n          ]\r\n        }]\r\n      };\r\n      this.charts.keywordCloudChart.setOption(option);\r\n    },\r\n    resizeCharts() {\r\n      Object.values(this.charts).forEach(chart => {\r\n        chart && chart.resize()\r\n      })\r\n    },\r\n    renderKeywordCloudChart() {\r\n      if (!this.charts.keywordCloudChart) return;\r\n      const option = {\r\n        tooltip: {\r\n          show: true\r\n        },\r\n        series: [{\r\n          type: 'wordCloud',\r\n          gridSize: 2,\r\n          sizeRange: [12, 50],\r\n          rotationRange: [-90, 90],\r\n          shape: 'pentagon',\r\n          width: '90%',\r\n          height: '90%',\r\n          textStyle: {\r\n            fontFamily: 'sans-serif',\r\n            fontWeight: 'bold',\r\n            color: function () {\r\n              return 'rgb(' + [\r\n                Math.round(Math.random() * 160),\r\n                Math.round(Math.random() * 160),\r\n                Math.round(Math.random() * 160)\r\n              ].join(',') + ')';\r\n            }\r\n          },\r\n          emphasis: {\r\n            focus: 'self',\r\n            textStyle: {\r\n              textShadowBlur: 10,\r\n              textShadowColor: '#333'\r\n            }\r\n          },\r\n          data: [\r\n            { name: '方太产品', value: 10000 },\r\n            { name: '厨房电器', value: 6181 },\r\n            { name: '油烟机', value: 4386 },\r\n            { name: '洗碗机', value: 4055 },\r\n            { name: '集成灶', value: 2467 },\r\n            { name: '售后服务', value: 2244 },\r\n            { name: '智能厨电', value: 1898 },\r\n            { name: '高端厨电', value: 1484 },\r\n            { name: '用户体验', value: 1112 },\r\n            { name: '新品发布', value: 965 },\r\n            { name: '质量问题', value: 847 },\r\n            { name: '价格优惠', value: 582 },\r\n            { name: '安装服务', value: 555 },\r\n            { name: '品牌活动', value: 550 },\r\n            { name: '线上购买', value: 462 },\r\n            { name: '线下门店', value: 366 },\r\n            { name: '厨电科技', value: 360 },\r\n            { name: '环保理念', value: 282 },\r\n            { name: '设计美学', value: 273 },\r\n            { name: '客户评价', value: 265 }\r\n          ]\r\n        }]\r\n      };\r\n      this.charts.keywordCloudChart.setOption(option);\r\n    },\r\n    renderTrendChart() {\r\n      // 传播热度趋势图配置\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis'\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: ['00:00', '01:00', '02:00', '03:00', '04:00', '05:00', '06:00', '07:00', '08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00']\r\n        },\r\n        yAxis: {\r\n          type: 'value'\r\n        },\r\n        series: [{\r\n          name: '传播热度',\r\n          type: 'line',\r\n          smooth: true,\r\n          lineStyle: {\r\n            color: '#67C23A',\r\n            width: 2\r\n          },\r\n          areaStyle: {\r\n            color: {\r\n              type: 'linear',\r\n              x: 0,\r\n              y: 0,\r\n              x2: 0,\r\n              y2: 1,\r\n              colorStops: [{\r\n                offset: 0,\r\n                color: 'rgba(103, 194, 58, 0.3)'\r\n              }, {\r\n                offset: 1,\r\n                color: 'rgba(103, 194, 58, 0.1)'\r\n              }]\r\n            }\r\n          },\r\n          data: [100, 120, 110, 125, 130, 150, 160, 170, 180, 190, 210, 230, 210, 200, 190, 180, 200, 210, 220, 210, 200]\r\n        }]\r\n      }\r\n      this.charts.trendChart.setOption(option)\r\n    },\r\n    renderSourceChart() {\r\n      // 平台来源占比图配置\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\r\n        },\r\n        series: [{\r\n          name: '平台来源',\r\n          type: 'pie',\r\n          radius: ['40%', '70%'],\r\n          avoidLabelOverlap: false,\r\n          label: {\r\n            show: true,\r\n            position: 'outside',\r\n            formatter: '{b}: {d}%'\r\n          },\r\n          emphasis: {\r\n            label: {\r\n              show: true,\r\n              fontSize: '16',\r\n              fontWeight: 'bold'\r\n            }\r\n          },\r\n          labelLine: {\r\n            show: true\r\n          },\r\n          data: [\r\n            { value: 31.26, name: '微博', itemStyle: { color: '#4CD384' } },\r\n            { value: 31.11, name: '微信', itemStyle: { color: '#36A3F7' } },\r\n            { value: 16.32, name: 'APP', itemStyle: { color: '#F56C6C' } },\r\n            { value: 10.47, name: '网站', itemStyle: { color: '#E6A23C' } },\r\n            { value: 5.47, name: '论坛', itemStyle: { color: '#909399' } },\r\n            { value: 3.29, name: '报刊', itemStyle: { color: '#9B55FF' } },\r\n            { value: 2.08, name: '其他', itemStyle: { color: '#FF9A2F' } }\r\n          ]\r\n        }]\r\n      }\r\n      this.charts.sourceChart.setOption(option)\r\n    },\r\n    renderHeatChart() {\r\n      // 传播热度图配置\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis'\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: ['00:00', '02:00', '04:00', '06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00']\r\n        },\r\n        yAxis: {\r\n          type: 'value'\r\n        },\r\n        legend: {\r\n          data: ['正面声量', '中性声量', '负面声量'],\r\n          bottom: 10\r\n        },\r\n        series: [\r\n          {\r\n            name: '正面声量',\r\n            type: 'line',\r\n            smooth: true,\r\n            lineStyle: { color: '#67C23A' },\r\n            areaStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: 'rgba(103, 194, 58, 0.3)' }, { offset: 1, color: 'rgba(103, 194, 58, 0.1)' }]) },\r\n            data: [10, 15, 12, 18, 22, 25, 30, 28, 35, 40, 38, 42, 45, 50, 48, 52, 55, 58, 60, 56, 50]\r\n          },\r\n          {\r\n            name: '中性声量',\r\n            type: 'line',\r\n            smooth: true,\r\n            lineStyle: { color: '#E6A23C' },\r\n            areaStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: 'rgba(230, 162, 60, 0.3)' }, { offset: 1, color: 'rgba(230, 162, 60, 0.1)' }]) },\r\n            data: [100, 120, 110, 125, 130, 150, 160, 170, 180, 190, 210, 230, 210, 200, 190, 180, 200, 210, 220, 210, 200]\r\n          },\r\n          {\r\n            name: '负面声量',\r\n            type: 'line',\r\n            smooth: true,\r\n            lineStyle: { color: '#F56C6C' },\r\n            areaStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: 'rgba(245, 108, 108, 0.3)' }, { offset: 1, color: 'rgba(245, 108, 108, 0.1)' }]) },\r\n            data: [5, 8, 6, 10, 12, 9, 11, 14, 10, 13, 15, 12, 16, 18, 15, 17, 20, 16, 19, 22, 18]\r\n          }\r\n        ]\r\n      }\r\n      this.charts.heatChart.setOption(option)\r\n    },\r\n    renderEventChart() {\r\n      // 舆情事件分布图配置\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis'\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: ['新闻', 'APP', '论坛', '博客', '微博', '视频', '微信']\r\n        },\r\n        yAxis: {\r\n          type: 'value'\r\n        },\r\n        series: [{\r\n          name: '事件数量',\r\n          type: 'bar',\r\n          barWidth: '60%',\r\n          data: [320, 280, 220, 180, 150, 120, 100]\r\n        }]\r\n      }\r\n      this.charts.eventChart.setOption(option)\r\n    },\r\n    renderSourceDistChart() {\r\n      // 舆情来源占比图配置\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross',\r\n            label: {\r\n              backgroundColor: '#6a7985'\r\n            }\r\n          }\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: ['00:00', '02:00', '04:00', '06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00']\r\n        },\r\n        yAxis: {\r\n          type: 'value'\r\n        },\r\n        series: [{\r\n          name: '媒体声量占比',\r\n          type: 'pie',\r\n          radius: ['40%', '70%'],\r\n          avoidLabelOverlap: false,\r\n          label: {\r\n            show: true,\r\n            position: 'outside',\r\n            formatter: '{b}: {d}%'\r\n          },\r\n          emphasis: {\r\n            label: {\r\n              show: true,\r\n              fontSize: '16',\r\n              fontWeight: 'bold'\r\n            }\r\n          },\r\n          labelLine: {\r\n            show: true\r\n          },\r\n          data: [\r\n            { value: 35, name: '新闻客户端', itemStyle: { color: '#5470C6'} },\r\n            { value: 25, name: '社交媒体', itemStyle: { color: '#91CC75'} },\r\n            { value: 15, name: '视频平台', itemStyle: { color: '#FAC858'} },\r\n            { value: 10, name: '传统媒体网站', itemStyle: { color: '#EE6666'} },\r\n            { value: 8, name: '论坛博客', itemStyle: { color: '#73C0DE'} },\r\n            { value: 7, name: '其他', itemStyle: { color: '#3BA272'} }\r\n          ]\r\n        }]\r\n      }\r\n      this.charts.sourceDistChart.setOption(option)\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    // 恢复顶部导航栏设置\r\n    if (this.originalTopNav !== undefined) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'topNav',\r\n        value: this.originalTopNav\r\n      })\r\n    }\r\n\r\n    // 移除窗口大小变化监听\r\n    window.removeEventListener('resize', this.resizeCharts)\r\n    // 销毁图表实例\r\n    Object.values(this.charts).forEach(chart => {\r\n      chart && chart.dispose()\r\n    })\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background-color: #f0f2f5;\r\n  min-height: 100vh;\r\n  padding: 0;\r\n}\r\n\r\n.top-nav-container {\r\n  background-color: #fff;\r\n  border-bottom: 1px solid #e6e6e6;\r\n\r\n  .top-menu {\r\n    border-bottom: none;\r\n\r\n    .el-menu-item {\r\n      height: 50px;\r\n      line-height: 50px;\r\n      font-size: 14px;\r\n\r\n      &.is-active, &.active-menu {\r\n        color: #1890ff;\r\n        border-bottom: 2px solid #1890ff;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.content-container {\r\n  padding: 20px;\r\n  display: flex;\r\n}\r\n\r\n.chart-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 4px;\r\n\r\n  .chart-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 12px 20px;\r\n    border-bottom: 1px solid #ebeef5;\r\n\r\n    .chart-title {\r\n      display: flex;\r\n      align-items: center;\r\n      font-size: 16px;\r\n      font-weight: 500;\r\n\r\n      .blue-line {\r\n        display: inline-block;\r\n        width: 3px;\r\n        height: 16px;\r\n        background-color: #1890ff;\r\n        margin-right: 8px;\r\n      }\r\n    }\r\n\r\n    .chart-tabs {\r\n      display: flex;\r\n\r\n      .tab-button {\r\n        margin-right: 10px;\r\n\r\n        &.active {\r\n          background-color: #1890ff;\r\n          border-color: #1890ff;\r\n        }\r\n      }\r\n    }\r\n\r\n    .chart-actions {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      i {\r\n        font-size: 16px;\r\n        color: #909399;\r\n        margin-left: 15px;\r\n        cursor: pointer;\r\n\r\n        &:hover {\r\n          color: #1890ff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .chart-content {\r\n    padding: 20px;\r\n\r\n    .chart {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n}\r\n\r\n.chart-row {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.chart-legend {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin-top: 15px;\r\n\r\n  .legend-item {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-right: 15px;\r\n    margin-bottom: 5px;\r\n\r\n    .legend-color {\r\n      display: inline-block;\r\n      width: 10px;\r\n      height: 10px;\r\n      border-radius: 2px;\r\n      margin-right: 5px;\r\n    }\r\n\r\n    .legend-text {\r\n      font-size: 12px;\r\n      color: #606266;\r\n    }\r\n  }\r\n}\r\n\r\n.hotspot-markers {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  pointer-events: none;\r\n}\r\n\r\n/* 左侧搜索方案区域样式 */\r\n.sidebar {\r\n  width: 240px;\r\n  margin-right: 20px;\r\n}\r\n\r\n.search-plan {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.plan-actions {\r\n  padding: 16px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n\r\n  .new-plan-btn {\r\n    flex: 1;\r\n    background-color: #FF9A2F;\r\n    border-color: #FF9A2F;\r\n\r\n    &:hover, &:focus {\r\n      background-color: #F08C1E;\r\n      border-color: #F08C1E;\r\n    }\r\n  }\r\n\r\n  .folder-btn {\r\n    margin-left: 10px;\r\n    padding: 10px;\r\n    color: #FF9A2F;\r\n    border-color: #DCDFE6;\r\n  }\r\n}\r\n\r\n.plan-search {\r\n  padding: 0 16px 16px;\r\n\r\n  .el-input ::v-deep .el-input__inner {\r\n    border-radius: 4px;\r\n    height: 32px;\r\n  }\r\n}\r\n\r\n.plan-divider {\r\n  height: 1px;\r\n  background-color: #EBEEF5;\r\n  margin: 0 16px;\r\n}\r\n\r\n.plan-list {\r\n  padding: 16px;\r\n}\r\n\r\n.plan-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n\r\n  &.active {\r\n    ::v-deep .el-radio__input.is-checked .el-radio__inner {\r\n      background-color: #409EFF;\r\n      border-color: #409EFF;\r\n    }\r\n\r\n    ::v-deep .el-radio__input.is-checked + .el-radio__label {\r\n      color: #303133;\r\n    }\r\n  }\r\n\r\n  i {\r\n    color: #909399;\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n.brand-detail {\r\n  margin: -10px 0 16px 24px;\r\n\r\n  .brand-name {\r\n    background-color: #E6F1FC;\r\n    color: #303133;\r\n    padding: 8px 12px;\r\n    border-radius: 4px;\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n/* 右侧内容区域样式 */\r\n.main-content {\r\n  flex: 1;\r\n}\r\n/* 样式可以根据需要进一步细化 */\r\n\r\n/* 方太筛选器专属样式 */\r\n.fangtai-filter-container .filter-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-bottom: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.fangtai-filter-container .filter-title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.fangtai-filter-container .filter-content {\r\n  padding-top: 15px;\r\n}\r\n\r\n.fangtai-filter-container .el-form-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.fangtai-filter-container .el-checkbox-group .el-checkbox {\r\n  margin-right: 20px;\r\n}\r\n\r\n/* 信息总览卡片样式 */\r\n.summary-cards {\r\n  margin-top: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.summary-card .el-card__body {\r\n  padding: 15px !important; /* 强制修改element-ui默认padding */\r\n}\r\n\r\n.summary-card div:first-child {\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n.summary-card div:last-child {\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  margin-top: 5px;\r\n}\r\n\r\n/* 通用图表卡片样式调整 */\r\n.chart-card .chart-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #f0f0f0; /* 统一边框颜色 */\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.chart-card .chart-title {\r\n  font-size: 16px; /* 统一标题字号 */\r\n  font-weight: 500;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.chart-card .chart-title .blue-line {\r\n  color: #409EFF; /* 统一图标颜色 */\r\n  margin-right: 8px;\r\n  font-size: 18px;\r\n}\r\n\r\n.chart-card .chart-actions .el-button-group .el-button {\r\n  padding: 5px 10px; /* 调整按钮组内按钮padding */\r\n}\r\n\r\n.chart-card .chart-actions .el-icon-refresh,\r\n.chart-card .chart-actions .el-icon-download,\r\n.chart-card .chart-actions .el-icon-more {\r\n  margin-left: 10px;\r\n  cursor: pointer;\r\n  font-size: 16px; /* 统一操作图标大小 */\r\n  color: #606266; /* 统一操作图标颜色 */\r\n}\r\n\r\n.chart-card .chart-actions .el-icon-refresh:hover,\r\n.chart-card .chart-actions .el-icon-download:hover,\r\n.chart-card .chart-actions .el-icon-more:hover {\r\n  color: #409EFF;\r\n}\r\n\r\n/* 平台来源占比图的图例样式 */\r\n.chart-legend {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 10px;\r\n  flex-wrap: wrap; /* 允许图例换行 */\r\n}\r\n\r\n.legend-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-right: 15px;\r\n  margin-bottom: 5px; /* 增加底部间距以便换行 */\r\n  font-size: 12px; /* 调整图例文字大小 */\r\n}\r\n\r\n.legend-color {\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 50%;\r\n  margin-right: 5px;\r\n  display: inline-block;\r\n}\r\n\r\n/* 左侧边栏激活状态 */\r\n.sidebar .plan-item.active {\r\n  background-color: #ecf5ff; /* Element UI 主题蓝的浅色 */\r\n  color: #409EFF;\r\n  border-right: 3px solid #409EFF;\r\n}\r\n\r\n.sidebar .plan-item.active .el-radio__label {\r\n color: #409EFF !important;\r\n}\r\n\r\n/* 顶部导航激活状态 */\r\n.top-menu .el-menu-item.is-active {\r\n  border-bottom-color: #409EFF !important;\r\n  color: #409EFF !important;\r\n}\r\n\r\n.top-menu .active-menu {\r\n   border-bottom-color: #409EFF !important;\r\n   color: #409EFF !important;\r\n   font-weight: bold;\r\n}\r\n\r\n/* 调整图表容器确保其撑满父容器 */\r\n.chart {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n/* 确保内容区域在小屏幕上也能良好显示 */\r\n.content-container {\r\n  display: flex;\r\n  flex-direction: row; /* 保持左右布局 */\r\n}\r\n\r\n.sidebar {\r\n  width: 220px; /* 固定左侧栏宽度 */\r\n  min-width: 220px;\r\n  border-right: 1px solid #e6e6e6;\r\n  padding-right: 15px;\r\n  margin-right: 15px;\r\n}\r\n\r\n.main-content {\r\n  flex-grow: 1; /* 右侧内容区域占据剩余空间 */\r\n  overflow-x: auto; /* 如果内容过宽，允许水平滚动 */\r\n}\r\n\r\n/* 响应式调整：当屏幕宽度小于768px时，可以考虑将侧边栏隐藏或变为抽屉式 */\r\n@media (max-width: 768px) {\r\n  .content-container {\r\n    flex-direction: column;\r\n  }\r\n  .sidebar {\r\n    width: 100%;\r\n    border-right: none;\r\n    border-bottom: 1px solid #e6e6e6;\r\n    margin-right: 0;\r\n    margin-bottom: 15px;\r\n    padding-right: 0;\r\n  }\r\n  .fangtai-filter-container .el-form-item .el-checkbox-group,\r\n  .fangtai-filter-container .el-form-item .el-radio-group {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n  }\r\n  .fangtai-filter-container .el-form-item .el-checkbox,\r\n  .fangtai-filter-container .el-form-item .el-radio-button {\r\n    margin-bottom: 5px;\r\n  }\r\n}\r\n</style>\r\n"]}]}