{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\utils\\scroll-to.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\utils\\scroll-to.js", "mtime": 1749104047634}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749104418875}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLnNjcm9sbFRvID0gc2Nyb2xsVG87Ck1hdGguZWFzZUluT3V0UXVhZCA9IGZ1bmN0aW9uICh0LCBiLCBjLCBkKSB7CiAgdCAvPSBkIC8gMjsKICBpZiAodCA8IDEpIHsKICAgIHJldHVybiBjIC8gMiAqIHQgKiB0ICsgYjsKICB9CiAgdC0tOwogIHJldHVybiAtYyAvIDIgKiAodCAqICh0IC0gMikgLSAxKSArIGI7Cn07CgovLyByZXF1ZXN0QW5pbWF0aW9uRnJhbWUgZm9yIFNtYXJ0IEFuaW1hdGluZyBodHRwOi8vZ29vLmdsL3N4NXN0cwp2YXIgcmVxdWVzdEFuaW1GcmFtZSA9IGZ1bmN0aW9uICgpIHsKICByZXR1cm4gd2luZG93LnJlcXVlc3RBbmltYXRpb25GcmFtZSB8fCB3aW5kb3cud2Via2l0UmVxdWVzdEFuaW1hdGlvbkZyYW1lIHx8IHdpbmRvdy5tb3pSZXF1ZXN0QW5pbWF0aW9uRnJhbWUgfHwgZnVuY3Rpb24gKGNhbGxiYWNrKSB7CiAgICB3aW5kb3cuc2V0VGltZW91dChjYWxsYmFjaywgMTAwMCAvIDYwKTsKICB9Owp9KCk7CgovKioNCiAqIEJlY2F1c2UgaXQncyBzbyBmdWNraW5nIGRpZmZpY3VsdCB0byBkZXRlY3QgdGhlIHNjcm9sbGluZyBlbGVtZW50LCBqdXN0IG1vdmUgdGhlbSBhbGwNCiAqIEBwYXJhbSB7bnVtYmVyfSBhbW91bnQNCiAqLwpmdW5jdGlvbiBtb3ZlKGFtb3VudCkgewogIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zY3JvbGxUb3AgPSBhbW91bnQ7CiAgZG9jdW1lbnQuYm9keS5wYXJlbnROb2RlLnNjcm9sbFRvcCA9IGFtb3VudDsKICBkb2N1bWVudC5ib2R5LnNjcm9sbFRvcCA9IGFtb3VudDsKfQpmdW5jdGlvbiBwb3NpdGlvbigpIHsKICByZXR1cm4gZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnNjcm9sbFRvcCB8fCBkb2N1bWVudC5ib2R5LnBhcmVudE5vZGUuc2Nyb2xsVG9wIHx8IGRvY3VtZW50LmJvZHkuc2Nyb2xsVG9wOwp9CgovKioNCiAqIEBwYXJhbSB7bnVtYmVyfSB0bw0KICogQHBhcmFtIHtudW1iZXJ9IGR1cmF0aW9uDQogKiBAcGFyYW0ge0Z1bmN0aW9ufSBjYWxsYmFjaw0KICovCmZ1bmN0aW9uIHNjcm9sbFRvKHRvLCBkdXJhdGlvbiwgY2FsbGJhY2spIHsKICB2YXIgc3RhcnQgPSBwb3NpdGlvbigpOwogIHZhciBjaGFuZ2UgPSB0byAtIHN0YXJ0OwogIHZhciBpbmNyZW1lbnQgPSAyMDsKICB2YXIgY3VycmVudFRpbWUgPSAwOwogIGR1cmF0aW9uID0gdHlwZW9mIGR1cmF0aW9uID09PSAndW5kZWZpbmVkJyA/IDUwMCA6IGR1cmF0aW9uOwogIHZhciBfYW5pbWF0ZVNjcm9sbCA9IGZ1bmN0aW9uIGFuaW1hdGVTY3JvbGwoKSB7CiAgICAvLyBpbmNyZW1lbnQgdGhlIHRpbWUKICAgIGN1cnJlbnRUaW1lICs9IGluY3JlbWVudDsKICAgIC8vIGZpbmQgdGhlIHZhbHVlIHdpdGggdGhlIHF1YWRyYXRpYyBpbi1vdXQgZWFzaW5nIGZ1bmN0aW9uCiAgICB2YXIgdmFsID0gTWF0aC5lYXNlSW5PdXRRdWFkKGN1cnJlbnRUaW1lLCBzdGFydCwgY2hhbmdlLCBkdXJhdGlvbik7CiAgICAvLyBtb3ZlIHRoZSBkb2N1bWVudC5ib2R5CiAgICBtb3ZlKHZhbCk7CiAgICAvLyBkbyB0aGUgYW5pbWF0aW9uIHVubGVzcyBpdHMgb3ZlcgogICAgaWYgKGN1cnJlbnRUaW1lIDwgZHVyYXRpb24pIHsKICAgICAgcmVxdWVzdEFuaW1GcmFtZShfYW5pbWF0ZVNjcm9sbCk7CiAgICB9IGVsc2UgewogICAgICBpZiAoY2FsbGJhY2sgJiYgdHlwZW9mIGNhbGxiYWNrID09PSAnZnVuY3Rpb24nKSB7CiAgICAgICAgLy8gdGhlIGFuaW1hdGlvbiBpcyBkb25lIHNvIGxldHMgY2FsbGJhY2sKICAgICAgICBjYWxsYmFjaygpOwogICAgICB9CiAgICB9CiAgfTsKICBfYW5pbWF0ZVNjcm9sbCgpOwp9"}, {"version": 3, "names": ["Math", "easeInOutQuad", "t", "b", "c", "d", "requestAnimFrame", "window", "requestAnimationFrame", "webkitRequestAnimationFrame", "mozRequestAnimationFrame", "callback", "setTimeout", "move", "amount", "document", "documentElement", "scrollTop", "body", "parentNode", "position", "scrollTo", "to", "duration", "start", "change", "increment", "currentTime", "animateScroll", "val"], "sources": ["D:/thinktank/thinktankui/src/utils/scroll-to.js"], "sourcesContent": ["Math.easeInOutQuad = function(t, b, c, d) {\r\n  t /= d / 2\r\n  if (t < 1) {\r\n    return c / 2 * t * t + b\r\n  }\r\n  t--\r\n  return -c / 2 * (t * (t - 2) - 1) + b\r\n}\r\n\r\n// requestAnimationFrame for Smart Animating http://goo.gl/sx5sts\r\nvar requestAnimFrame = (function() {\r\n  return window.requestAnimationFrame || window.webkitRequestAnimationFrame || window.mozRequestAnimationFrame || function(callback) { window.setTimeout(callback, 1000 / 60) }\r\n})()\r\n\r\n/**\r\n * Because it's so fucking difficult to detect the scrolling element, just move them all\r\n * @param {number} amount\r\n */\r\nfunction move(amount) {\r\n  document.documentElement.scrollTop = amount\r\n  document.body.parentNode.scrollTop = amount\r\n  document.body.scrollTop = amount\r\n}\r\n\r\nfunction position() {\r\n  return document.documentElement.scrollTop || document.body.parentNode.scrollTop || document.body.scrollTop\r\n}\r\n\r\n/**\r\n * @param {number} to\r\n * @param {number} duration\r\n * @param {Function} callback\r\n */\r\nexport function scrollTo(to, duration, callback) {\r\n  const start = position()\r\n  const change = to - start\r\n  const increment = 20\r\n  let currentTime = 0\r\n  duration = (typeof (duration) === 'undefined') ? 500 : duration\r\n  var animateScroll = function() {\r\n    // increment the time\r\n    currentTime += increment\r\n    // find the value with the quadratic in-out easing function\r\n    var val = Math.easeInOutQuad(currentTime, start, change, duration)\r\n    // move the document.body\r\n    move(val)\r\n    // do the animation unless its over\r\n    if (currentTime < duration) {\r\n      requestAnimFrame(animateScroll)\r\n    } else {\r\n      if (callback && typeof (callback) === 'function') {\r\n        // the animation is done so lets callback\r\n        callback()\r\n      }\r\n    }\r\n  }\r\n  animateScroll()\r\n}\r\n"], "mappings": ";;;;;;AAAAA,IAAI,CAACC,aAAa,GAAG,UAASC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACxCH,CAAC,IAAIG,CAAC,GAAG,CAAC;EACV,IAAIH,CAAC,GAAG,CAAC,EAAE;IACT,OAAOE,CAAC,GAAG,CAAC,GAAGF,CAAC,GAAGA,CAAC,GAAGC,CAAC;EAC1B;EACAD,CAAC,EAAE;EACH,OAAO,CAACE,CAAC,GAAG,CAAC,IAAIF,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGC,CAAC;AACvC,CAAC;;AAED;AACA,IAAIG,gBAAgB,GAAI,YAAW;EACjC,OAAOC,MAAM,CAACC,qBAAqB,IAAID,MAAM,CAACE,2BAA2B,IAAIF,MAAM,CAACG,wBAAwB,IAAI,UAASC,QAAQ,EAAE;IAAEJ,MAAM,CAACK,UAAU,CAACD,QAAQ,EAAE,IAAI,GAAG,EAAE,CAAC;EAAC,CAAC;AAC/K,CAAC,CAAE,CAAC;;AAEJ;AACA;AACA;AACA;AACA,SAASE,IAAIA,CAACC,MAAM,EAAE;EACpBC,QAAQ,CAACC,eAAe,CAACC,SAAS,GAAGH,MAAM;EAC3CC,QAAQ,CAACG,IAAI,CAACC,UAAU,CAACF,SAAS,GAAGH,MAAM;EAC3CC,QAAQ,CAACG,IAAI,CAACD,SAAS,GAAGH,MAAM;AAClC;AAEA,SAASM,QAAQA,CAAA,EAAG;EAClB,OAAOL,QAAQ,CAACC,eAAe,CAACC,SAAS,IAAIF,QAAQ,CAACG,IAAI,CAACC,UAAU,CAACF,SAAS,IAAIF,QAAQ,CAACG,IAAI,CAACD,SAAS;AAC5G;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASI,QAAQA,CAACC,EAAE,EAAEC,QAAQ,EAAEZ,QAAQ,EAAE;EAC/C,IAAMa,KAAK,GAAGJ,QAAQ,CAAC,CAAC;EACxB,IAAMK,MAAM,GAAGH,EAAE,GAAGE,KAAK;EACzB,IAAME,SAAS,GAAG,EAAE;EACpB,IAAIC,WAAW,GAAG,CAAC;EACnBJ,QAAQ,GAAI,OAAQA,QAAS,KAAK,WAAW,GAAI,GAAG,GAAGA,QAAQ;EAC/D,IAAIK,cAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAc;IAC7B;IACAD,WAAW,IAAID,SAAS;IACxB;IACA,IAAIG,GAAG,GAAG7B,IAAI,CAACC,aAAa,CAAC0B,WAAW,EAAEH,KAAK,EAAEC,MAAM,EAAEF,QAAQ,CAAC;IAClE;IACAV,IAAI,CAACgB,GAAG,CAAC;IACT;IACA,IAAIF,WAAW,GAAGJ,QAAQ,EAAE;MAC1BjB,gBAAgB,CAACsB,cAAa,CAAC;IACjC,CAAC,MAAM;MACL,IAAIjB,QAAQ,IAAI,OAAQA,QAAS,KAAK,UAAU,EAAE;QAChD;QACAA,QAAQ,CAAC,CAAC;MACZ;IACF;EACF,CAAC;EACDiB,cAAa,CAAC,CAAC;AACjB", "ignoreList": []}]}