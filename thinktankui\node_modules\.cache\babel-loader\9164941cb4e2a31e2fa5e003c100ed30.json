{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\core\\selection.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\core\\selection.js", "mtime": 1749104422625}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_parchment", "require", "_lodashEs", "_emitter", "_interopRequireDefault", "_logger", "debug", "logger", "Range", "exports", "_createClass2", "default", "index", "_classCallCheck2", "length", "arguments", "undefined", "Selection", "scroll", "emitter", "_this", "composing", "mouseDown", "root", "domNode", "cursor", "create", "savedRange", "<PERSON><PERSON><PERSON><PERSON>", "lastNative", "handleComposition", "handleDragging", "listenDOM", "document", "setTimeout", "update", "bind", "Emitter", "sources", "USER", "on", "events", "SCROLL_BEFORE_UPDATE", "hasFocus", "native", "getNativeRange", "start", "node", "textNode", "once", "SCROLL_UPDATE", "source", "mutations", "contains", "end", "setNativeRange", "offset", "triggeredByTyping", "some", "mutation", "type", "target", "SILENT", "ignored", "SCROLL_OPTIMIZE", "context", "range", "_context$range", "startNode", "startOffset", "endNode", "endOffset", "key", "value", "_this2", "COMPOSITION_BEFORE_START", "COMPOSITION_END", "parent", "restore", "_this3", "body", "focus", "preventScroll", "setRang<PERSON>", "format", "nativeRange", "collapsed", "query", "<PERSON><PERSON>", "BLOCK", "blot", "find", "LeafBlot", "after", "split", "insertBefore", "attach", "optimize", "data", "getBounds", "<PERSON><PERSON><PERSON><PERSON>", "Math", "min", "_this$scroll$leaf", "leaf", "_this$scroll$leaf2", "_slicedToArray2", "_this$scroll$leaf3", "_this$scroll$leaf4", "next", "_this$scroll$line", "line", "_this$scroll$line2", "_this$scroll$line3", "_this$scroll$line4", "nextLine", "_leaf$position", "position", "_leaf$position2", "createRange", "setStart", "_this$scroll$leaf5", "_this$scroll$leaf6", "_leaf$position3", "_leaf$position4", "setEnd", "getBoundingClientRect", "side", "rect", "Text", "Element", "bottom", "top", "height", "left", "right", "width", "selection", "getSelection", "rangeCount", "getRangeAt", "normalizeNative", "info", "getRange", "isConnected", "normalized", "normalizedToRange", "activeElement", "_this4", "positions", "push", "indexes", "map", "_position", "max", "apply", "_toConsumableArray2", "concat", "startContainer", "endContainer", "for<PERSON>ach", "childNodes", "<PERSON><PERSON><PERSON><PERSON>", "rangeToNative", "_this5", "getPosition", "inclusive", "_this5$scroll$leaf", "_this5$scroll$leaf2", "leafOffset", "force", "parentNode", "_ref", "tagName", "Array", "from", "indexOf", "removeAllRanges", "addRange", "blur", "API", "args", "oldRange", "_this$getRange", "_this$getRange2", "isEqual", "_this$emitter", "SELECTION_CHANGE", "cloneDeep", "emit", "EDITOR_CHANGE", "_this$emitter2", "descendant", "e", "_default"], "sources": ["../../src/core/selection.ts"], "sourcesContent": ["import { LeafBlot, Scope } from 'parchment';\nimport { cloneDeep, isEqual } from 'lodash-es';\nimport Emitter from './emitter.js';\nimport type { EmitterSource } from './emitter.js';\nimport logger from './logger.js';\nimport type Cursor from '../blots/cursor.js';\nimport type Scroll from '../blots/scroll.js';\n\nconst debug = logger('quill:selection');\n\ntype NativeRange = AbstractRange;\n\ninterface NormalizedRange {\n  start: {\n    node: NativeRange['startContainer'];\n    offset: NativeRange['startOffset'];\n  };\n  end: { node: NativeRange['endContainer']; offset: NativeRange['endOffset'] };\n  native: NativeRange;\n}\n\nexport interface Bounds {\n  bottom: number;\n  height: number;\n  left: number;\n  right: number;\n  top: number;\n  width: number;\n}\n\nexport class Range {\n  constructor(\n    public index: number,\n    public length = 0,\n  ) {}\n}\n\nclass Selection {\n  scroll: Scroll;\n  emitter: Emitter;\n  composing: boolean;\n  mouseDown: boolean;\n\n  root: HTMLElement;\n  cursor: Cursor;\n  savedRange: Range;\n  lastRange: Range | null;\n  lastNative: NormalizedRange | null;\n\n  constructor(scroll: Scroll, emitter: Emitter) {\n    this.emitter = emitter;\n    this.scroll = scroll;\n    this.composing = false;\n    this.mouseDown = false;\n    this.root = this.scroll.domNode;\n    // @ts-expect-error\n    this.cursor = this.scroll.create('cursor', this);\n    // savedRange is last non-null range\n    this.savedRange = new Range(0, 0);\n    this.lastRange = this.savedRange;\n    this.lastNative = null;\n    this.handleComposition();\n    this.handleDragging();\n    this.emitter.listenDOM('selectionchange', document, () => {\n      if (!this.mouseDown && !this.composing) {\n        setTimeout(this.update.bind(this, Emitter.sources.USER), 1);\n      }\n    });\n    this.emitter.on(Emitter.events.SCROLL_BEFORE_UPDATE, () => {\n      if (!this.hasFocus()) return;\n      const native = this.getNativeRange();\n      if (native == null) return;\n      if (native.start.node === this.cursor.textNode) return; // cursor.restore() will handle\n      this.emitter.once(\n        Emitter.events.SCROLL_UPDATE,\n        (source, mutations: MutationRecord[]) => {\n          try {\n            if (\n              this.root.contains(native.start.node) &&\n              this.root.contains(native.end.node)\n            ) {\n              this.setNativeRange(\n                native.start.node,\n                native.start.offset,\n                native.end.node,\n                native.end.offset,\n              );\n            }\n            const triggeredByTyping = mutations.some(\n              (mutation) =>\n                mutation.type === 'characterData' ||\n                mutation.type === 'childList' ||\n                (mutation.type === 'attributes' &&\n                  mutation.target === this.root),\n            );\n            this.update(triggeredByTyping ? Emitter.sources.SILENT : source);\n          } catch (ignored) {\n            // ignore\n          }\n        },\n      );\n    });\n    this.emitter.on(Emitter.events.SCROLL_OPTIMIZE, (mutations, context) => {\n      if (context.range) {\n        const { startNode, startOffset, endNode, endOffset } = context.range;\n        this.setNativeRange(startNode, startOffset, endNode, endOffset);\n        this.update(Emitter.sources.SILENT);\n      }\n    });\n    this.update(Emitter.sources.SILENT);\n  }\n\n  handleComposition() {\n    this.emitter.on(Emitter.events.COMPOSITION_BEFORE_START, () => {\n      this.composing = true;\n    });\n    this.emitter.on(Emitter.events.COMPOSITION_END, () => {\n      this.composing = false;\n      if (this.cursor.parent) {\n        const range = this.cursor.restore();\n        if (!range) return;\n        setTimeout(() => {\n          this.setNativeRange(\n            range.startNode,\n            range.startOffset,\n            range.endNode,\n            range.endOffset,\n          );\n        }, 1);\n      }\n    });\n  }\n\n  handleDragging() {\n    this.emitter.listenDOM('mousedown', document.body, () => {\n      this.mouseDown = true;\n    });\n    this.emitter.listenDOM('mouseup', document.body, () => {\n      this.mouseDown = false;\n      this.update(Emitter.sources.USER);\n    });\n  }\n\n  focus() {\n    if (this.hasFocus()) return;\n    this.root.focus({ preventScroll: true });\n    this.setRange(this.savedRange);\n  }\n\n  format(format: string, value: unknown) {\n    this.scroll.update();\n    const nativeRange = this.getNativeRange();\n    if (\n      nativeRange == null ||\n      !nativeRange.native.collapsed ||\n      this.scroll.query(format, Scope.BLOCK)\n    )\n      return;\n    if (nativeRange.start.node !== this.cursor.textNode) {\n      const blot = this.scroll.find(nativeRange.start.node, false);\n      if (blot == null) return;\n      // TODO Give blot ability to not split\n      if (blot instanceof LeafBlot) {\n        const after = blot.split(nativeRange.start.offset);\n        blot.parent.insertBefore(this.cursor, after);\n      } else {\n        // @ts-expect-error TODO: nativeRange.start.node doesn't seem to match function signature\n        blot.insertBefore(this.cursor, nativeRange.start.node); // Should never happen\n      }\n      this.cursor.attach();\n    }\n    this.cursor.format(format, value);\n    this.scroll.optimize();\n    this.setNativeRange(this.cursor.textNode, this.cursor.textNode.data.length);\n    this.update();\n  }\n\n  getBounds(index: number, length = 0) {\n    const scrollLength = this.scroll.length();\n    index = Math.min(index, scrollLength - 1);\n    length = Math.min(index + length, scrollLength - 1) - index;\n    let node: Node;\n    let [leaf, offset] = this.scroll.leaf(index);\n    if (leaf == null) return null;\n    if (length > 0 && offset === leaf.length()) {\n      const [next] = this.scroll.leaf(index + 1);\n      if (next) {\n        const [line] = this.scroll.line(index);\n        const [nextLine] = this.scroll.line(index + 1);\n        if (line === nextLine) {\n          leaf = next;\n          offset = 0;\n        }\n      }\n    }\n    [node, offset] = leaf.position(offset, true);\n    const range = document.createRange();\n    if (length > 0) {\n      range.setStart(node, offset);\n      [leaf, offset] = this.scroll.leaf(index + length);\n      if (leaf == null) return null;\n      [node, offset] = leaf.position(offset, true);\n      range.setEnd(node, offset);\n      return range.getBoundingClientRect();\n    }\n    let side: 'left' | 'right' = 'left';\n    let rect: DOMRect;\n    if (node instanceof Text) {\n      // Return null if the text node is empty because it is\n      // not able to get a useful client rect:\n      // https://github.com/w3c/csswg-drafts/issues/2514.\n      // Empty text nodes are most likely caused by TextBlot#optimize()\n      // not getting called when editor content changes.\n      if (!node.data.length) {\n        return null;\n      }\n      if (offset < node.data.length) {\n        range.setStart(node, offset);\n        range.setEnd(node, offset + 1);\n      } else {\n        range.setStart(node, offset - 1);\n        range.setEnd(node, offset);\n        side = 'right';\n      }\n      rect = range.getBoundingClientRect();\n    } else {\n      if (!(leaf.domNode instanceof Element)) return null;\n      rect = leaf.domNode.getBoundingClientRect();\n      if (offset > 0) side = 'right';\n    }\n    return {\n      bottom: rect.top + rect.height,\n      height: rect.height,\n      left: rect[side],\n      right: rect[side],\n      top: rect.top,\n      width: 0,\n    };\n  }\n\n  getNativeRange(): NormalizedRange | null {\n    const selection = document.getSelection();\n    if (selection == null || selection.rangeCount <= 0) return null;\n    const nativeRange = selection.getRangeAt(0);\n    if (nativeRange == null) return null;\n    const range = this.normalizeNative(nativeRange);\n    debug.info('getNativeRange', range);\n    return range;\n  }\n\n  getRange(): [Range, NormalizedRange] | [null, null] {\n    const root = this.scroll.domNode;\n    if ('isConnected' in root && !root.isConnected) {\n      // document.getSelection() forces layout on Blink, so we trend to\n      // not calling it.\n      return [null, null];\n    }\n    const normalized = this.getNativeRange();\n    if (normalized == null) return [null, null];\n    const range = this.normalizedToRange(normalized);\n    return [range, normalized];\n  }\n\n  hasFocus(): boolean {\n    return (\n      document.activeElement === this.root ||\n      (document.activeElement != null &&\n        contains(this.root, document.activeElement))\n    );\n  }\n\n  normalizedToRange(range: NormalizedRange) {\n    const positions: [Node, number][] = [\n      [range.start.node, range.start.offset],\n    ];\n    if (!range.native.collapsed) {\n      positions.push([range.end.node, range.end.offset]);\n    }\n    const indexes = positions.map((position) => {\n      const [node, offset] = position;\n      const blot = this.scroll.find(node, true);\n      // @ts-expect-error Fix me later\n      const index = blot.offset(this.scroll);\n      if (offset === 0) {\n        return index;\n      }\n      if (blot instanceof LeafBlot) {\n        return index + blot.index(node, offset);\n      }\n      // @ts-expect-error Fix me later\n      return index + blot.length();\n    });\n    const end = Math.min(Math.max(...indexes), this.scroll.length() - 1);\n    const start = Math.min(end, ...indexes);\n    return new Range(start, end - start);\n  }\n\n  normalizeNative(nativeRange: NativeRange) {\n    if (\n      !contains(this.root, nativeRange.startContainer) ||\n      (!nativeRange.collapsed && !contains(this.root, nativeRange.endContainer))\n    ) {\n      return null;\n    }\n    const range = {\n      start: {\n        node: nativeRange.startContainer,\n        offset: nativeRange.startOffset,\n      },\n      end: { node: nativeRange.endContainer, offset: nativeRange.endOffset },\n      native: nativeRange,\n    };\n    [range.start, range.end].forEach((position) => {\n      let { node, offset } = position;\n      while (!(node instanceof Text) && node.childNodes.length > 0) {\n        if (node.childNodes.length > offset) {\n          node = node.childNodes[offset];\n          offset = 0;\n        } else if (node.childNodes.length === offset) {\n          // @ts-expect-error Fix me later\n          node = node.lastChild;\n          if (node instanceof Text) {\n            offset = node.data.length;\n          } else if (node.childNodes.length > 0) {\n            // Container case\n            offset = node.childNodes.length;\n          } else {\n            // Embed case\n            offset = node.childNodes.length + 1;\n          }\n        } else {\n          break;\n        }\n      }\n      position.node = node;\n      position.offset = offset;\n    });\n    return range;\n  }\n\n  rangeToNative(range: Range): [Node | null, number, Node | null, number] {\n    const scrollLength = this.scroll.length();\n\n    const getPosition = (\n      index: number,\n      inclusive: boolean,\n    ): [Node | null, number] => {\n      index = Math.min(scrollLength - 1, index);\n      const [leaf, leafOffset] = this.scroll.leaf(index);\n      return leaf ? leaf.position(leafOffset, inclusive) : [null, -1];\n    };\n    return [\n      ...getPosition(range.index, false),\n      ...getPosition(range.index + range.length, true),\n    ];\n  }\n\n  setNativeRange(\n    startNode: Node | null,\n    startOffset?: number,\n    endNode = startNode,\n    endOffset = startOffset,\n    force = false,\n  ) {\n    debug.info('setNativeRange', startNode, startOffset, endNode, endOffset);\n    if (\n      startNode != null &&\n      (this.root.parentNode == null ||\n        startNode.parentNode == null ||\n        // @ts-expect-error Fix me later\n        endNode.parentNode == null)\n    ) {\n      return;\n    }\n    const selection = document.getSelection();\n    if (selection == null) return;\n    if (startNode != null) {\n      if (!this.hasFocus()) this.root.focus({ preventScroll: true });\n      const { native } = this.getNativeRange() || {};\n      if (\n        native == null ||\n        force ||\n        startNode !== native.startContainer ||\n        startOffset !== native.startOffset ||\n        endNode !== native.endContainer ||\n        endOffset !== native.endOffset\n      ) {\n        if (startNode instanceof Element && startNode.tagName === 'BR') {\n          // @ts-expect-error Fix me later\n          startOffset = Array.from(startNode.parentNode.childNodes).indexOf(\n            startNode,\n          );\n          startNode = startNode.parentNode;\n        }\n        if (endNode instanceof Element && endNode.tagName === 'BR') {\n          // @ts-expect-error Fix me later\n          endOffset = Array.from(endNode.parentNode.childNodes).indexOf(\n            endNode,\n          );\n          endNode = endNode.parentNode;\n        }\n        const range = document.createRange();\n        // @ts-expect-error Fix me later\n        range.setStart(startNode, startOffset);\n        // @ts-expect-error Fix me later\n        range.setEnd(endNode, endOffset);\n        selection.removeAllRanges();\n        selection.addRange(range);\n      }\n    } else {\n      selection.removeAllRanges();\n      this.root.blur();\n    }\n  }\n\n  setRange(range: Range | null, force: boolean, source?: EmitterSource): void;\n  setRange(range: Range | null, source?: EmitterSource): void;\n  setRange(\n    range: Range | null,\n    force: boolean | EmitterSource = false,\n    source: EmitterSource = Emitter.sources.API,\n  ): void {\n    if (typeof force === 'string') {\n      source = force;\n      force = false;\n    }\n    debug.info('setRange', range);\n    if (range != null) {\n      const args = this.rangeToNative(range);\n      this.setNativeRange(...args, force);\n    } else {\n      this.setNativeRange(null);\n    }\n    this.update(source);\n  }\n\n  update(source: EmitterSource = Emitter.sources.USER) {\n    const oldRange = this.lastRange;\n    const [lastRange, nativeRange] = this.getRange();\n    this.lastRange = lastRange;\n    this.lastNative = nativeRange;\n    if (this.lastRange != null) {\n      this.savedRange = this.lastRange;\n    }\n    if (!isEqual(oldRange, this.lastRange)) {\n      if (\n        !this.composing &&\n        nativeRange != null &&\n        nativeRange.native.collapsed &&\n        nativeRange.start.node !== this.cursor.textNode\n      ) {\n        const range = this.cursor.restore();\n        if (range) {\n          this.setNativeRange(\n            range.startNode,\n            range.startOffset,\n            range.endNode,\n            range.endOffset,\n          );\n        }\n      }\n      const args = [\n        Emitter.events.SELECTION_CHANGE,\n        cloneDeep(this.lastRange),\n        cloneDeep(oldRange),\n        source,\n      ];\n      this.emitter.emit(Emitter.events.EDITOR_CHANGE, ...args);\n      if (source !== Emitter.sources.SILENT) {\n        this.emitter.emit(...args);\n      }\n    }\n  }\n}\n\nfunction contains(parent: Node, descendant: Node) {\n  try {\n    // Firefox inserts inaccessible nodes around video elements\n    descendant.parentNode; // eslint-disable-line @typescript-eslint/no-unused-expressions\n  } catch (e) {\n    return false;\n  }\n  return parent.contains(descendant);\n}\n\nexport default Selection;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAC,sBAAA,CAAAH,OAAA;AAEA,IAAAI,OAAA,GAAAD,sBAAA,CAAAH,OAAA;AAIA,IAAMK,KAAK,GAAG,IAAAC,eAAM,EAAC,iBAAiB,CAAC;AAAA,IAsB1BC,KAAK,GAAAC,OAAA,CAAAD,KAAA,oBAAAE,aAAA,CAAAC,OAAA,EAChB,SAAAH,MACSI,KAAa,EAEpB;EAAA,IAAAC,gBAAA,CAAAF,OAAA,QAAAH,KAAA;EAAA,IADOM,MAAM,GAAAC,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC;EAAA,KADVH,KAAa,GAAbA,KAAa;EAAA,KACbE,MAAM,GAANA,MAAM;AACZ;AAAA,IAGCG,SAAS;EAYb,SAAAA,UAAYC,MAAc,EAAEC,OAAgB,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAP,gBAAA,CAAAF,OAAA,QAAAM,SAAA;IAC5C,IAAI,CAACE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACG,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,IAAI,GAAG,IAAI,CAACL,MAAM,CAACM,OAAO;IAC/B;IACA,IAAI,CAACC,MAAM,GAAG,IAAI,CAACP,MAAM,CAACQ,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC;IAChD;IACA,IAAI,CAACC,UAAU,GAAG,IAAInB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACjC,IAAI,CAACoB,SAAS,GAAG,IAAI,CAACD,UAAU;IAChC,IAAI,CAACE,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACC,cAAc,CAAC,CAAC;IACrB,IAAI,CAACZ,OAAO,CAACa,SAAS,CAAC,iBAAiB,EAAEC,QAAQ,EAAE,YAAM;MACxD,IAAI,CAACb,KAAI,CAACE,SAAS,IAAI,CAACF,KAAI,CAACC,SAAS,EAAE;QACtCa,UAAU,CAACd,KAAI,CAACe,MAAM,CAACC,IAAI,CAAChB,KAAI,EAAEiB,gBAAO,CAACC,OAAO,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;MAC7D;IACF,CAAC,CAAC;IACF,IAAI,CAACpB,OAAO,CAACqB,EAAE,CAACH,gBAAO,CAACI,MAAM,CAACC,oBAAoB,EAAE,YAAM;MACzD,IAAI,CAACtB,KAAI,CAACuB,QAAQ,CAAC,CAAC,EAAE;MACtB,IAAMC,MAAM,GAAGxB,KAAI,CAACyB,cAAc,CAAC,CAAC;MACpC,IAAID,MAAM,IAAI,IAAI,EAAE;MACpB,IAAIA,MAAM,CAACE,KAAK,CAACC,IAAI,KAAK3B,KAAI,CAACK,MAAM,CAACuB,QAAQ,EAAE,OAAO,CAAC;MACxD5B,KAAI,CAACD,OAAO,CAAC8B,IAAI,CACfZ,gBAAO,CAACI,MAAM,CAACS,aAAa,EAC5B,UAACC,MAAM,EAAEC,SAA2B,EAAK;QACvC,IAAI;UACF,IACEhC,KAAI,CAACG,IAAI,CAAC8B,QAAQ,CAACT,MAAM,CAACE,KAAK,CAACC,IAAI,CAAC,IACrC3B,KAAI,CAACG,IAAI,CAAC8B,QAAQ,CAACT,MAAM,CAACU,GAAG,CAACP,IAAI,CAAC,EACnC;YACA3B,KAAI,CAACmC,cAAc,CACjBX,MAAM,CAACE,KAAK,CAACC,IAAI,EACjBH,MAAM,CAACE,KAAK,CAACU,MAAM,EACnBZ,MAAM,CAACU,GAAG,CAACP,IAAI,EACfH,MAAM,CAACU,GAAG,CAACE,MACb,CAAC;UACH;UACA,IAAMC,iBAAiB,GAAGL,SAAS,CAACM,IAAI,CACrC,UAAAC,QAAQ;YAAA,OACPA,QAAQ,CAACC,IAAI,KAAK,eAAe,IACjCD,QAAQ,CAACC,IAAI,KAAK,WAAW,IAC5BD,QAAQ,CAACC,IAAI,KAAK,YAAY,IAC7BD,QAAQ,CAACE,MAAM,KAAKzC,KAAI,CAACG,IAC/B;UAAA,EAAC;UACDH,KAAI,CAACe,MAAM,CAACsB,iBAAiB,GAAGpB,gBAAO,CAACC,OAAO,CAACwB,MAAM,GAAGX,MAAM,CAAC;QAClE,CAAC,CAAC,OAAOY,OAAO,EAAE;UAChB;QAAA;MAEJ,CACF,CAAC;IACH,CAAC,CAAC;IACF,IAAI,CAAC5C,OAAO,CAACqB,EAAE,CAACH,gBAAO,CAACI,MAAM,CAACuB,eAAe,EAAE,UAACZ,SAAS,EAAEa,OAAO,EAAK;MACtE,IAAIA,OAAO,CAACC,KAAK,EAAE;QACjB,IAAAC,cAAA,GAAuDF,OAAO,CAACC,KAAK;UAA5DE,SAAS,GAAAD,cAAA,CAATC,SAAS;UAAEC,WAAW,GAAAF,cAAA,CAAXE,WAAW;UAAEC,OAAO,GAAAH,cAAA,CAAPG,OAAO;UAAEC,SAAA,GAAAJ,cAAA,CAAAI,SAAA;QACzCnD,KAAI,CAACmC,cAAc,CAACa,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,SAAS,CAAC;QAC/DnD,KAAI,CAACe,MAAM,CAACE,gBAAO,CAACC,OAAO,CAACwB,MAAM,CAAC;MACrC;IACF,CAAC,CAAC;IACF,IAAI,CAAC3B,MAAM,CAACE,gBAAO,CAACC,OAAO,CAACwB,MAAM,CAAC;EACrC;EAAA,WAAApD,aAAA,CAAAC,OAAA,EAAAM,SAAA;IAAAuD,GAAA;IAAAC,KAAA,EAEA,SAAA3C,iBAAiBA,CAAA,EAAG;MAAA,IAAA4C,MAAA;MAClB,IAAI,CAACvD,OAAO,CAACqB,EAAE,CAACH,gBAAO,CAACI,MAAM,CAACkC,wBAAwB,EAAE,YAAM;QAC7DD,MAAI,CAACrD,SAAS,GAAG,IAAI;MACvB,CAAC,CAAC;MACF,IAAI,CAACF,OAAO,CAACqB,EAAE,CAACH,gBAAO,CAACI,MAAM,CAACmC,eAAe,EAAE,YAAM;QACpDF,MAAI,CAACrD,SAAS,GAAG,KAAK;QACtB,IAAIqD,MAAI,CAACjD,MAAM,CAACoD,MAAM,EAAE;UACtB,IAAMX,KAAK,GAAGQ,MAAI,CAACjD,MAAM,CAACqD,OAAO,CAAC,CAAC;UACnC,IAAI,CAACZ,KAAK,EAAE;UACZhC,UAAU,CAAC,YAAM;YACfwC,MAAI,CAACnB,cAAc,CACjBW,KAAK,CAACE,SAAS,EACfF,KAAK,CAACG,WAAW,EACjBH,KAAK,CAACI,OAAO,EACbJ,KAAK,CAACK,SACR,CAAC;UACH,CAAC,EAAE,CAAC,CAAC;QACP;MACF,CAAC,CAAC;IACJ;EAAA;IAAAC,GAAA;IAAAC,KAAA,EAEA,SAAA1C,cAAcA,CAAA,EAAG;MAAA,IAAAgD,MAAA;MACf,IAAI,CAAC5D,OAAO,CAACa,SAAS,CAAC,WAAW,EAAEC,QAAQ,CAAC+C,IAAI,EAAE,YAAM;QACvDD,MAAI,CAACzD,SAAS,GAAG,IAAI;MACvB,CAAC,CAAC;MACF,IAAI,CAACH,OAAO,CAACa,SAAS,CAAC,SAAS,EAAEC,QAAQ,CAAC+C,IAAI,EAAE,YAAM;QACrDD,MAAI,CAACzD,SAAS,GAAG,KAAK;QACtByD,MAAI,CAAC5C,MAAM,CAACE,gBAAO,CAACC,OAAO,CAACC,IAAI,CAAC;MACnC,CAAC,CAAC;IACJ;EAAA;IAAAiC,GAAA;IAAAC,KAAA,EAEA,SAAAQ,KAAKA,CAAA,EAAG;MACN,IAAI,IAAI,CAACtC,QAAQ,CAAC,CAAC,EAAE;MACrB,IAAI,CAACpB,IAAI,CAAC0D,KAAK,CAAC;QAAEC,aAAa,EAAE;MAAK,CAAC,CAAC;MACxC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACxD,UAAU,CAAC;IAChC;EAAA;IAAA6C,GAAA;IAAAC,KAAA,EAEA,SAAAW,MAAMA,CAACA,OAAc,EAAEX,KAAc,EAAE;MACrC,IAAI,CAACvD,MAAM,CAACiB,MAAM,CAAC,CAAC;MACpB,IAAMkD,WAAW,GAAG,IAAI,CAACxC,cAAc,CAAC,CAAC;MACzC,IACEwC,WAAW,IAAI,IAAI,IACnB,CAACA,WAAW,CAACzC,MAAM,CAAC0C,SAAS,IAC7B,IAAI,CAACpE,MAAM,CAACqE,KAAK,CAACH,OAAM,EAAEI,gBAAK,CAACC,KAAK,CAAC,EAEtC;MACF,IAAIJ,WAAW,CAACvC,KAAK,CAACC,IAAI,KAAK,IAAI,CAACtB,MAAM,CAACuB,QAAQ,EAAE;QACnD,IAAM0C,IAAI,GAAG,IAAI,CAACxE,MAAM,CAACyE,IAAI,CAACN,WAAW,CAACvC,KAAK,CAACC,IAAI,EAAE,KAAK,CAAC;QAC5D,IAAI2C,IAAI,IAAI,IAAI,EAAE;QAClB;QACA,IAAIA,IAAI,YAAYE,mBAAQ,EAAE;UAC5B,IAAMC,KAAK,GAAGH,IAAI,CAACI,KAAK,CAACT,WAAW,CAACvC,KAAK,CAACU,MAAM,CAAC;UAClDkC,IAAI,CAACb,MAAM,CAACkB,YAAY,CAAC,IAAI,CAACtE,MAAM,EAAEoE,KAAK,CAAC;QAC9C,CAAC,MAAM;UACL;UACAH,IAAI,CAACK,YAAY,CAAC,IAAI,CAACtE,MAAM,EAAE4D,WAAW,CAACvC,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;QAC1D;QACA,IAAI,CAACtB,MAAM,CAACuE,MAAM,CAAC,CAAC;MACtB;MACA,IAAI,CAACvE,MAAM,CAAC2D,MAAM,CAACA,OAAM,EAAEX,KAAK,CAAC;MACjC,IAAI,CAACvD,MAAM,CAAC+E,QAAQ,CAAC,CAAC;MACtB,IAAI,CAAC1C,cAAc,CAAC,IAAI,CAAC9B,MAAM,CAACuB,QAAQ,EAAE,IAAI,CAACvB,MAAM,CAACuB,QAAQ,CAACkD,IAAI,CAACpF,MAAM,CAAC;MAC3E,IAAI,CAACqB,MAAM,CAAC,CAAC;IACf;EAAA;IAAAqC,GAAA;IAAAC,KAAA,EAEA,SAAA0B,SAASA,CAACvF,KAAa,EAAc;MAAA,IAAZE,MAAM,GAAAC,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC;MACjC,IAAMqF,YAAY,GAAG,IAAI,CAAClF,MAAM,CAACJ,MAAM,CAAC,CAAC;MACzCF,KAAK,GAAGyF,IAAI,CAACC,GAAG,CAAC1F,KAAK,EAAEwF,YAAY,GAAG,CAAC,CAAC;MACzCtF,MAAM,GAAGuF,IAAI,CAACC,GAAG,CAAC1F,KAAK,GAAGE,MAAM,EAAEsF,YAAY,GAAG,CAAC,CAAC,GAAGxF,KAAK;MAC3D,IAAImC,IAAU;MACd,IAAAwD,iBAAA,GAAqB,IAAI,CAACrF,MAAM,CAACsF,IAAI,CAAC5F,KAAK,CAAC;QAAA6F,kBAAA,OAAAC,eAAA,CAAA/F,OAAA,EAAA4F,iBAAA;QAAvCC,IAAI,GAAAC,kBAAA;QAAEjD,MAAM,GAAAiD,kBAAA;MACjB,IAAID,IAAI,IAAI,IAAI,EAAE,OAAO,IAAI;MAC7B,IAAI1F,MAAM,GAAG,CAAC,IAAI0C,MAAM,KAAKgD,IAAI,CAAC1F,MAAM,CAAC,CAAC,EAAE;QAC1C,IAAA6F,kBAAA,GAAe,IAAI,CAACzF,MAAM,CAACsF,IAAI,CAAC5F,KAAK,GAAG,CAAC,CAAC;UAAAgG,kBAAA,OAAAF,eAAA,CAAA/F,OAAA,EAAAgG,kBAAA;UAAnCE,IAAI,GAAAD,kBAAA;QACX,IAAIC,IAAI,EAAE;UACR,IAAAC,iBAAA,GAAe,IAAI,CAAC5F,MAAM,CAAC6F,IAAI,CAACnG,KAAK,CAAC;YAAAoG,kBAAA,OAAAN,eAAA,CAAA/F,OAAA,EAAAmG,iBAAA;YAA/BC,IAAI,GAAAC,kBAAA;UACX,IAAAC,kBAAA,GAAmB,IAAI,CAAC/F,MAAM,CAAC6F,IAAI,CAACnG,KAAK,GAAG,CAAC,CAAC;YAAAsG,kBAAA,OAAAR,eAAA,CAAA/F,OAAA,EAAAsG,kBAAA;YAAvCE,QAAQ,GAAAD,kBAAA;UACf,IAAIH,IAAI,KAAKI,QAAQ,EAAE;YACrBX,IAAI,GAAGK,IAAI;YACXrD,MAAM,GAAG,CAAC;UACZ;QACF;MACF;MAAA,IAAA4D,cAAA,GACiBZ,IAAI,CAACa,QAAQ,CAAC7D,MAAM,EAAE,IAAI,CAAC;MAAA,IAAA8D,eAAA,OAAAZ,eAAA,CAAA/F,OAAA,EAAAyG,cAAA;MAA3CrE,IAAI,GAAAuE,eAAA;MAAE9D,MAAM,GAAA8D,eAAA;MACb,IAAMpD,KAAK,GAAGjC,QAAQ,CAACsF,WAAW,CAAC,CAAC;MACpC,IAAIzG,MAAM,GAAG,CAAC,EAAE;QACdoD,KAAK,CAACsD,QAAQ,CAACzE,IAAI,EAAES,MAAM,CAAC;QAAA,IAAAiE,kBAAA,GACX,IAAI,CAACvG,MAAM,CAACsF,IAAI,CAAC5F,KAAK,GAAGE,MAAM,CAAC;QAAA,IAAA4G,kBAAA,OAAAhB,eAAA,CAAA/F,OAAA,EAAA8G,kBAAA;QAAhDjB,IAAI,GAAAkB,kBAAA;QAAElE,MAAM,GAAAkE,kBAAA;QACb,IAAIlB,IAAI,IAAI,IAAI,EAAE,OAAO,IAAI;QAAA,IAAAmB,eAAA,GACZnB,IAAI,CAACa,QAAQ,CAAC7D,MAAM,EAAE,IAAI,CAAC;QAAA,IAAAoE,eAAA,OAAAlB,eAAA,CAAA/F,OAAA,EAAAgH,eAAA;QAA3C5E,IAAI,GAAA6E,eAAA;QAAEpE,MAAM,GAAAoE,eAAA;QACb1D,KAAK,CAAC2D,MAAM,CAAC9E,IAAI,EAAES,MAAM,CAAC;QAC1B,OAAOU,KAAK,CAAC4D,qBAAqB,CAAC,CAAC;MACtC;MACA,IAAIC,IAAsB,GAAG,MAAM;MACnC,IAAIC,IAAa;MACjB,IAAIjF,IAAI,YAAYkF,IAAI,EAAE;QACxB;QACA;QACA;QACA;QACA;QACA,IAAI,CAAClF,IAAI,CAACmD,IAAI,CAACpF,MAAM,EAAE;UACrB,OAAO,IAAI;QACb;QACA,IAAI0C,MAAM,GAAGT,IAAI,CAACmD,IAAI,CAACpF,MAAM,EAAE;UAC7BoD,KAAK,CAACsD,QAAQ,CAACzE,IAAI,EAAES,MAAM,CAAC;UAC5BU,KAAK,CAAC2D,MAAM,CAAC9E,IAAI,EAAES,MAAM,GAAG,CAAC,CAAC;QAChC,CAAC,MAAM;UACLU,KAAK,CAACsD,QAAQ,CAACzE,IAAI,EAAES,MAAM,GAAG,CAAC,CAAC;UAChCU,KAAK,CAAC2D,MAAM,CAAC9E,IAAI,EAAES,MAAM,CAAC;UAC1BuE,IAAI,GAAG,OAAO;QAChB;QACAC,IAAI,GAAG9D,KAAK,CAAC4D,qBAAqB,CAAC,CAAC;MACtC,CAAC,MAAM;QACL,IAAI,EAAEtB,IAAI,CAAChF,OAAO,YAAY0G,OAAO,CAAC,EAAE,OAAO,IAAI;QACnDF,IAAI,GAAGxB,IAAI,CAAChF,OAAO,CAACsG,qBAAqB,CAAC,CAAC;QAC3C,IAAItE,MAAM,GAAG,CAAC,EAAEuE,IAAI,GAAG,OAAO;MAChC;MACA,OAAO;QACLI,MAAM,EAAEH,IAAI,CAACI,GAAG,GAAGJ,IAAI,CAACK,MAAM;QAC9BA,MAAM,EAAEL,IAAI,CAACK,MAAM;QACnBC,IAAI,EAAEN,IAAI,CAACD,IAAI,CAAC;QAChBQ,KAAK,EAAEP,IAAI,CAACD,IAAI,CAAC;QACjBK,GAAG,EAAEJ,IAAI,CAACI,GAAG;QACbI,KAAK,EAAE;MACT,CAAC;IACH;EAAA;IAAAhE,GAAA;IAAAC,KAAA,EAEA,SAAA5B,cAAcA,CAAA,EAA2B;MACvC,IAAM4F,SAAS,GAAGxG,QAAQ,CAACyG,YAAY,CAAC,CAAC;MACzC,IAAID,SAAS,IAAI,IAAI,IAAIA,SAAS,CAACE,UAAU,IAAI,CAAC,EAAE,OAAO,IAAI;MAC/D,IAAMtD,WAAW,GAAGoD,SAAS,CAACG,UAAU,CAAC,CAAC,CAAC;MAC3C,IAAIvD,WAAW,IAAI,IAAI,EAAE,OAAO,IAAI;MACpC,IAAMnB,KAAK,GAAG,IAAI,CAAC2E,eAAe,CAACxD,WAAW,CAAC;MAC/C/E,KAAK,CAACwI,IAAI,CAAC,gBAAgB,EAAE5E,KAAK,CAAC;MACnC,OAAOA,KAAK;IACd;EAAA;IAAAM,GAAA;IAAAC,KAAA,EAEA,SAAAsE,QAAQA,CAAA,EAA4C;MAClD,IAAMxH,IAAI,GAAG,IAAI,CAACL,MAAM,CAACM,OAAO;MAChC,IAAI,aAAa,IAAID,IAAI,IAAI,CAACA,IAAI,CAACyH,WAAW,EAAE;QAC9C;QACA;QACA,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;MACrB;MACA,IAAMC,UAAU,GAAG,IAAI,CAACpG,cAAc,CAAC,CAAC;MACxC,IAAIoG,UAAU,IAAI,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;MAC3C,IAAM/E,KAAK,GAAG,IAAI,CAACgF,iBAAiB,CAACD,UAAU,CAAC;MAChD,OAAO,CAAC/E,KAAK,EAAE+E,UAAU,CAAC;IAC5B;EAAA;IAAAzE,GAAA;IAAAC,KAAA,EAEA,SAAA9B,QAAQA,CAAA,EAAY;MAClB,OACEV,QAAQ,CAACkH,aAAa,KAAK,IAAI,CAAC5H,IAAI,IACnCU,QAAQ,CAACkH,aAAa,IAAI,IAAI,IAC7B9F,QAAQ,CAAC,IAAI,CAAC9B,IAAI,EAAEU,QAAQ,CAACkH,aAAa,CAAE;IAElD;EAAA;IAAA3E,GAAA;IAAAC,KAAA,EAEA,SAAAyE,iBAAiBA,CAAChF,KAAsB,EAAE;MAAA,IAAAkF,MAAA;MACxC,IAAMC,SAA2B,GAAG,CAClC,CAACnF,KAAK,CAACpB,KAAK,CAACC,IAAI,EAAEmB,KAAK,CAACpB,KAAK,CAACU,MAAM,CAAC,CACvC;MACD,IAAI,CAACU,KAAK,CAACtB,MAAM,CAAC0C,SAAS,EAAE;QAC3B+D,SAAS,CAACC,IAAI,CAAC,CAACpF,KAAK,CAACZ,GAAG,CAACP,IAAI,EAAEmB,KAAK,CAACZ,GAAG,CAACE,MAAM,CAAC,CAAC;MACpD;MACA,IAAM+F,OAAO,GAAGF,SAAS,CAACG,GAAG,CAAE,UAAAnC,QAAQ,EAAK;QAC1C,IAAAoC,SAAA,OAAA/C,eAAA,CAAA/F,OAAA,EAAuB0G,QAAQ;UAAxBtE,IAAI,GAAA0G,SAAA;UAAEjG,MAAM,GAAAiG,SAAA;QACnB,IAAM/D,IAAI,GAAG0D,MAAI,CAAClI,MAAM,CAACyE,IAAI,CAAC5C,IAAI,EAAE,IAAI,CAAC;QACzC;QACA,IAAMnC,KAAK,GAAG8E,IAAI,CAAClC,MAAM,CAAC4F,MAAI,CAAClI,MAAM,CAAC;QACtC,IAAIsC,MAAM,KAAK,CAAC,EAAE;UAChB,OAAO5C,KAAK;QACd;QACA,IAAI8E,IAAI,YAAYE,mBAAQ,EAAE;UAC5B,OAAOhF,KAAK,GAAG8E,IAAI,CAAC9E,KAAK,CAACmC,IAAI,EAAES,MAAM,CAAC;QACzC;QACA;QACA,OAAO5C,KAAK,GAAG8E,IAAI,CAAC5E,MAAM,CAAC,CAAC;MAC9B,CAAC,CAAC;MACF,IAAMwC,GAAG,GAAG+C,IAAI,CAACC,GAAG,CAACD,IAAI,CAACqD,GAAG,CAAAC,KAAA,CAARtD,IAAI,MAAAuD,mBAAA,CAAAjJ,OAAA,EAAQ4I,OAAO,EAAC,EAAE,IAAI,CAACrI,MAAM,CAACJ,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;MACpE,IAAMgC,KAAK,GAAGuD,IAAI,CAACC,GAAG,CAAAqD,KAAA,CAARtD,IAAI,GAAK/C,GAAG,EAAAuG,MAAA,KAAAD,mBAAA,CAAAjJ,OAAA,EAAK4I,OAAO,GAAC;MACvC,OAAO,IAAI/I,KAAK,CAACsC,KAAK,EAAEQ,GAAG,GAAGR,KAAK,CAAC;IACtC;EAAA;IAAA0B,GAAA;IAAAC,KAAA,EAEA,SAAAoE,eAAeA,CAACxD,WAAwB,EAAE;MACxC,IACE,CAAChC,QAAQ,CAAC,IAAI,CAAC9B,IAAI,EAAE8D,WAAW,CAACyE,cAAc,CAAC,IAC/C,CAACzE,WAAW,CAACC,SAAS,IAAI,CAACjC,QAAQ,CAAC,IAAI,CAAC9B,IAAI,EAAE8D,WAAW,CAAC0E,YAAY,CAAE,EAC1E;QACA,OAAO,IAAI;MACb;MACA,IAAM7F,KAAK,GAAG;QACZpB,KAAK,EAAE;UACLC,IAAI,EAAEsC,WAAW,CAACyE,cAAc;UAChCtG,MAAM,EAAE6B,WAAW,CAAChB;QACtB,CAAC;QACDf,GAAG,EAAE;UAAEP,IAAI,EAAEsC,WAAW,CAAC0E,YAAY;UAAEvG,MAAM,EAAE6B,WAAW,CAACd;QAAU,CAAC;QACtE3B,MAAM,EAAEyC;MACV,CAAC;MACD,CAACnB,KAAK,CAACpB,KAAK,EAAEoB,KAAK,CAACZ,GAAG,CAAC,CAAC0G,OAAO,CAAE,UAAA3C,QAAQ,EAAK;QAC7C,IAAMtE,IAAI,GAAasE,QAAQ,CAAzBtE,IAAI;UAAES,MAAA,GAAW6D,QAAQ,CAAnB7D,MAAA;QACZ,OAAO,EAAET,IAAI,YAAYkF,IAAI,CAAC,IAAIlF,IAAI,CAACkH,UAAU,CAACnJ,MAAM,GAAG,CAAC,EAAE;UAC5D,IAAIiC,IAAI,CAACkH,UAAU,CAACnJ,MAAM,GAAG0C,MAAM,EAAE;YACnCT,IAAI,GAAGA,IAAI,CAACkH,UAAU,CAACzG,MAAM,CAAC;YAC9BA,MAAM,GAAG,CAAC;UACZ,CAAC,MAAM,IAAIT,IAAI,CAACkH,UAAU,CAACnJ,MAAM,KAAK0C,MAAM,EAAE;YAC5C;YACAT,IAAI,GAAGA,IAAI,CAACmH,SAAS;YACrB,IAAInH,IAAI,YAAYkF,IAAI,EAAE;cACxBzE,MAAM,GAAGT,IAAI,CAACmD,IAAI,CAACpF,MAAM;YAC3B,CAAC,MAAM,IAAIiC,IAAI,CAACkH,UAAU,CAACnJ,MAAM,GAAG,CAAC,EAAE;cACrC;cACA0C,MAAM,GAAGT,IAAI,CAACkH,UAAU,CAACnJ,MAAM;YACjC,CAAC,MAAM;cACL;cACA0C,MAAM,GAAGT,IAAI,CAACkH,UAAU,CAACnJ,MAAM,GAAG,CAAC;YACrC;UACF,CAAC,MAAM;YACL;UACF;QACF;QACAuG,QAAQ,CAACtE,IAAI,GAAGA,IAAI;QACpBsE,QAAQ,CAAC7D,MAAM,GAAGA,MAAM;MAC1B,CAAC,CAAC;MACF,OAAOU,KAAK;IACd;EAAA;IAAAM,GAAA;IAAAC,KAAA,EAEA,SAAA0F,aAAaA,CAACjG,KAAY,EAA8C;MAAA,IAAAkG,MAAA;MACtE,IAAMhE,YAAY,GAAG,IAAI,CAAClF,MAAM,CAACJ,MAAM,CAAC,CAAC;MAEzC,IAAMuJ,WAAW,GAAG,SAAdA,WAAWA,CACfzJ,KAAa,EACb0J,SAAkB,EACQ;QAC1B1J,KAAK,GAAGyF,IAAI,CAACC,GAAG,CAACF,YAAY,GAAG,CAAC,EAAExF,KAAK,CAAC;QACzC,IAAA2J,kBAAA,GAA2BH,MAAI,CAAClJ,MAAM,CAACsF,IAAI,CAAC5F,KAAK,CAAC;UAAA4J,mBAAA,OAAA9D,eAAA,CAAA/F,OAAA,EAAA4J,kBAAA;UAA3C/D,IAAI,GAAAgE,mBAAA;UAAEC,UAAU,GAAAD,mBAAA;QACvB,OAAOhE,IAAI,GAAGA,IAAI,CAACa,QAAQ,CAACoD,UAAU,EAAEH,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;MACjE,CAAC;MACD,UAAAT,MAAA,KAAAD,mBAAA,CAAAjJ,OAAA,EACK0J,WAAW,CAACnG,KAAK,CAACtD,KAAK,EAAE,KAAK,CAAC,OAAAgJ,mBAAA,CAAAjJ,OAAA,EAC/B0J,WAAW,CAACnG,KAAK,CAACtD,KAAK,GAAGsD,KAAK,CAACpD,MAAM,EAAE,IAAI,CAAC;IAEpD;EAAA;IAAA0D,GAAA;IAAAC,KAAA,EAEA,SAAAlB,cAAcA,CACZa,SAAsB,EACtBC,WAAoB,EAIpB;MAAA,IAHAC,OAAO,GAAAvD,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGqD,SAAS;MAAA,IACnBG,SAAS,GAAAxD,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGsD,WAAW;MAAA,IACvBqG,KAAK,GAAA3J,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK;MAEbT,KAAK,CAACwI,IAAI,CAAC,gBAAgB,EAAE1E,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,SAAS,CAAC;MACxE,IACEH,SAAS,IAAI,IAAI,KAChB,IAAI,CAAC7C,IAAI,CAACoJ,UAAU,IAAI,IAAI,IAC3BvG,SAAS,CAACuG,UAAU,IAAI,IAAI;MAC5B;MACArG,OAAO,CAACqG,UAAU,IAAI,IAAI,CAAC,EAC7B;QACA;MACF;MACA,IAAMlC,SAAS,GAAGxG,QAAQ,CAACyG,YAAY,CAAC,CAAC;MACzC,IAAID,SAAS,IAAI,IAAI,EAAE;MACvB,IAAIrE,SAAS,IAAI,IAAI,EAAE;QACrB,IAAI,CAAC,IAAI,CAACzB,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACpB,IAAI,CAAC0D,KAAK,CAAC;UAAEC,aAAa,EAAE;QAAK,CAAC,CAAC;QAC9D,IAAA0F,IAAA,GAAmB,IAAI,CAAC/H,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC;UAAtCD,MAAA,GAAAgI,IAAA,CAAAhI,MAAA;QACR,IACEA,MAAM,IAAI,IAAI,IACd8H,KAAK,IACLtG,SAAS,KAAKxB,MAAM,CAACkH,cAAc,IACnCzF,WAAW,KAAKzB,MAAM,CAACyB,WAAW,IAClCC,OAAO,KAAK1B,MAAM,CAACmH,YAAY,IAC/BxF,SAAS,KAAK3B,MAAM,CAAC2B,SAAS,EAC9B;UACA,IAAIH,SAAS,YAAY8D,OAAO,IAAI9D,SAAS,CAACyG,OAAO,KAAK,IAAI,EAAE;YAC9D;YACAxG,WAAW,GAAGyG,KAAK,CAACC,IAAI,CAAC3G,SAAS,CAACuG,UAAU,CAACV,UAAU,CAAC,CAACe,OAAO,CAC/D5G,SACF,CAAC;YACDA,SAAS,GAAGA,SAAS,CAACuG,UAAU;UAClC;UACA,IAAIrG,OAAO,YAAY4D,OAAO,IAAI5D,OAAO,CAACuG,OAAO,KAAK,IAAI,EAAE;YAC1D;YACAtG,SAAS,GAAGuG,KAAK,CAACC,IAAI,CAACzG,OAAO,CAACqG,UAAU,CAACV,UAAU,CAAC,CAACe,OAAO,CAC3D1G,OACF,CAAC;YACDA,OAAO,GAAGA,OAAO,CAACqG,UAAU;UAC9B;UACA,IAAMzG,KAAK,GAAGjC,QAAQ,CAACsF,WAAW,CAAC,CAAC;UACpC;UACArD,KAAK,CAACsD,QAAQ,CAACpD,SAAS,EAAEC,WAAW,CAAC;UACtC;UACAH,KAAK,CAAC2D,MAAM,CAACvD,OAAO,EAAEC,SAAS,CAAC;UAChCkE,SAAS,CAACwC,eAAe,CAAC,CAAC;UAC3BxC,SAAS,CAACyC,QAAQ,CAAChH,KAAK,CAAC;QAC3B;MACF,CAAC,MAAM;QACLuE,SAAS,CAACwC,eAAe,CAAC,CAAC;QAC3B,IAAI,CAAC1J,IAAI,CAAC4J,IAAI,CAAC,CAAC;MAClB;IACF;EAAA;IAAA3G,GAAA;IAAAC,KAAA,EAIA,SAAAU,QAAQA,CACNjB,KAAmB,EAGb;MAAA,IAFNwG,KAA8B,GAAA3J,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK;MAAA,IACtCoC,MAAqB,GAAApC,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGsB,gBAAO,CAACC,OAAO,CAAC8I,GAAG;MAE3C,IAAI,OAAOV,KAAK,KAAK,QAAQ,EAAE;QAC7BvH,MAAM,GAAGuH,KAAK;QACdA,KAAK,GAAG,KAAK;MACf;MACApK,KAAK,CAACwI,IAAI,CAAC,UAAU,EAAE5E,KAAK,CAAC;MAC7B,IAAIA,KAAK,IAAI,IAAI,EAAE;QACjB,IAAMmH,IAAI,GAAG,IAAI,CAAClB,aAAa,CAACjG,KAAK,CAAC;QACtC,IAAI,CAACX,cAAc,CAAAoG,KAAA,CAAnB,IAAI,MAAAC,mBAAA,CAAAjJ,OAAA,EAAmB0K,IAAI,EAAAxB,MAAA,EAAEa,KAAK,GAAC;MACrC,CAAC,MAAM;QACL,IAAI,CAACnH,cAAc,CAAC,IAAI,CAAC;MAC3B;MACA,IAAI,CAACpB,MAAM,CAACgB,MAAM,CAAC;IACrB;EAAA;IAAAqB,GAAA;IAAAC,KAAA,EAEA,SAAAtC,MAAMA,CAAA,EAA+C;MAAA,IAA9CgB,MAAqB,GAAApC,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGsB,gBAAO,CAACC,OAAO,CAACC,IAAI;MACjD,IAAM+I,QAAQ,GAAG,IAAI,CAAC1J,SAAS;MAC/B,IAAA2J,cAAA,GAAiC,IAAI,CAACxC,QAAQ,CAAC,CAAC;QAAAyC,eAAA,OAAA9E,eAAA,CAAA/F,OAAA,EAAA4K,cAAA;QAAzC3J,SAAS,GAAA4J,eAAA;QAAEnG,WAAW,GAAAmG,eAAA;MAC7B,IAAI,CAAC5J,SAAS,GAAGA,SAAS;MAC1B,IAAI,CAACC,UAAU,GAAGwD,WAAW;MAC7B,IAAI,IAAI,CAACzD,SAAS,IAAI,IAAI,EAAE;QAC1B,IAAI,CAACD,UAAU,GAAG,IAAI,CAACC,SAAS;MAClC;MACA,IAAI,CAAC,IAAA6J,iBAAO,EAACH,QAAQ,EAAE,IAAI,CAAC1J,SAAS,CAAC,EAAE;QAAA,IAAA8J,aAAA;QACtC,IACE,CAAC,IAAI,CAACrK,SAAS,IACfgE,WAAW,IAAI,IAAI,IACnBA,WAAW,CAACzC,MAAM,CAAC0C,SAAS,IAC5BD,WAAW,CAACvC,KAAK,CAACC,IAAI,KAAK,IAAI,CAACtB,MAAM,CAACuB,QAAQ,EAC/C;UACA,IAAMkB,KAAK,GAAG,IAAI,CAACzC,MAAM,CAACqD,OAAO,CAAC,CAAC;UACnC,IAAIZ,KAAK,EAAE;YACT,IAAI,CAACX,cAAc,CACjBW,KAAK,CAACE,SAAS,EACfF,KAAK,CAACG,WAAW,EACjBH,KAAK,CAACI,OAAO,EACbJ,KAAK,CAACK,SACR,CAAC;UACH;QACF;QACA,IAAM8G,IAAI,GAAG,CACXhJ,gBAAO,CAACI,MAAM,CAACkJ,gBAAgB,EAC/B,IAAAC,mBAAS,EAAC,IAAI,CAAChK,SAAS,CAAC,EACzB,IAAAgK,mBAAS,EAACN,QAAQ,CAAC,EACnBnI,MAAM,CACP;QACD,CAAAuI,aAAA,OAAI,CAACvK,OAAO,EAAC0K,IAAI,CAAAlC,KAAA,CAAA+B,aAAA,GAACrJ,gBAAO,CAACI,MAAM,CAACqJ,aAAa,EAAAjC,MAAA,CAAKwB,IAAI,EAAC;QACxD,IAAIlI,MAAM,KAAKd,gBAAO,CAACC,OAAO,CAACwB,MAAM,EAAE;UAAA,IAAAiI,cAAA;UACrC,CAAAA,cAAA,OAAI,CAAC5K,OAAO,EAAC0K,IAAI,CAAAlC,KAAA,CAAAoC,cAAA,EAAIV,IAAI,CAAC;QAC5B;MACF;IACF;EAAA;AAAA;AAGF,SAAShI,QAAQA,CAACwB,MAAY,EAAEmH,UAAgB,EAAE;EAChD,IAAI;IACF;IACAA,UAAU,CAACrB,UAAU,CAAC,CAAC;EACzB,CAAC,CAAC,OAAOsB,CAAC,EAAE;IACV,OAAO,KAAK;EACd;EACA,OAAOpH,MAAM,CAACxB,QAAQ,CAAC2I,UAAU,CAAC;AACpC;AAAA,IAAAE,QAAA,GAAAzL,OAAA,CAAAE,OAAA,GAEeM,SAAS", "ignoreList": []}]}