{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\ImagePreview\\index.vue?vue&type=style&index=0&id=4b9dcf40&lang=scss&scoped=true", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\ImagePreview\\index.vue", "mtime": 1749104047621}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749104419119}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749104421224}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749104419872}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749104418479}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmVsLWltYWdlIHsNCiAgYm9yZGVyLXJhZGl1czogNXB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZWJlZWY1Ow0KICBib3gtc2hhZG93OiAwIDAgNXB4IDFweCAjY2NjOw0KICA6OnYtZGVlcCAuZWwtaW1hZ2VfX2lubmVyIHsNCiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zczsNCiAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgJjpob3ZlciB7DQogICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMik7DQogICAgfQ0KICB9DQogIDo6di1kZWVwIC5pbWFnZS1zbG90IHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgd2lkdGg6IDEwMCU7DQogICAgaGVpZ2h0OiAxMDAlOw0KICAgIGNvbG9yOiAjOTA5Mzk5Ow0KICAgIGZvbnQtc2l6ZTogMzBweDsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/ImagePreview", "sourcesContent": ["<template>\r\n  <el-image\r\n    :src=\"`${realSrc}`\"\r\n    fit=\"cover\"\r\n    :style=\"`width:${realWidth};height:${realHeight};`\"\r\n    :preview-src-list=\"realSrcList\"\r\n  >\r\n    <div slot=\"error\" class=\"image-slot\">\r\n      <i class=\"el-icon-picture-outline\"></i>\r\n    </div>\r\n  </el-image>\r\n</template>\r\n\r\n<script>\r\nimport { isExternal } from \"@/utils/validate\";\r\n\r\nexport default {\r\n  name: \"ImagePreview\",\r\n  props: {\r\n    src: {\r\n      type: String,\r\n      default: \"\"\r\n    },\r\n    width: {\r\n      type: [Number, String],\r\n      default: \"\"\r\n    },\r\n    height: {\r\n      type: [Number, String],\r\n      default: \"\"\r\n    }\r\n  },\r\n  computed: {\r\n    realSrc() {\r\n      if (!this.src) {\r\n        return;\r\n      }\r\n      let real_src = this.src.split(\",\")[0];\r\n      if (isExternal(real_src)) {\r\n        return real_src;\r\n      }\r\n      return process.env.VUE_APP_BASE_API + real_src;\r\n    },\r\n    realSrcList() {\r\n      if (!this.src) {\r\n        return;\r\n      }\r\n      let real_src_list = this.src.split(\",\");\r\n      let srcList = [];\r\n      real_src_list.forEach(item => {\r\n        if (isExternal(item)) {\r\n          return srcList.push(item);\r\n        }\r\n        return srcList.push(process.env.VUE_APP_BASE_API + item);\r\n      });\r\n      return srcList;\r\n    },\r\n    realWidth() {\r\n      return typeof this.width == \"string\" ? this.width : `${this.width}px`;\r\n    },\r\n    realHeight() {\r\n      return typeof this.height == \"string\" ? this.height : `${this.height}px`;\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.el-image {\r\n  border-radius: 5px;\r\n  background-color: #ebeef5;\r\n  box-shadow: 0 0 5px 1px #ccc;\r\n  ::v-deep .el-image__inner {\r\n    transition: all 0.3s;\r\n    cursor: pointer;\r\n    &:hover {\r\n      transform: scale(1.2);\r\n    }\r\n  }\r\n  ::v-deep .image-slot {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    width: 100%;\r\n    height: 100%;\r\n    color: #909399;\r\n    font-size: 30px;\r\n  }\r\n}\r\n</style>\r\n"]}]}