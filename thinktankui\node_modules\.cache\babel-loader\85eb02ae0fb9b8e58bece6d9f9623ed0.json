{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\redirect.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\redirect.vue", "mtime": 1749104047642}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnJlcGxhY2UuanMiKTsKdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB2YXIgX3RoaXMkJHJvdXRlID0gdGhpcy4kcm91dGUsCiAgICAgIHBhcmFtcyA9IF90aGlzJCRyb3V0ZS5wYXJhbXMsCiAgICAgIHF1ZXJ5ID0gX3RoaXMkJHJvdXRlLnF1ZXJ5OwogICAgdmFyIHBhdGggPSBwYXJhbXMucGF0aDsKICAgIHRoaXMuJHJvdXRlci5yZXBsYWNlKHsKICAgICAgcGF0aDogJy8nICsgcGF0aCwKICAgICAgcXVlcnk6IHF1ZXJ5CiAgICB9KTsKICB9LAogIHJlbmRlcjogZnVuY3Rpb24gcmVuZGVyKGgpIHsKICAgIHJldHVybiBoKCk7IC8vIGF2b2lkIHdhcm5pbmcgbWVzc2FnZQogIH0KfTs="}, {"version": 3, "names": ["created", "_this$$route", "$route", "params", "query", "path", "$router", "replace", "render", "h"], "sources": ["src/views/redirect.vue"], "sourcesContent": ["<script>\r\nexport default {\r\n  created() {\r\n    const { params, query } = this.$route\r\n    const { path } = params\r\n    this.$router.replace({ path: '/' + path, query })\r\n  },\r\n  render: function(h) {\r\n    return h() // avoid warning message\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;iCACA;EACAA,OAAA,WAAAA,QAAA;IACA,IAAAC,YAAA,QAAAC,MAAA;MAAAC,MAAA,GAAAF,YAAA,CAAAE,MAAA;MAAAC,KAAA,GAAAH,YAAA,CAAAG,KAAA;IACA,IAAAC,IAAA,GAAAF,MAAA,CAAAE,IAAA;IACA,KAAAC,OAAA,CAAAC,OAAA;MAAAF,IAAA,QAAAA,IAAA;MAAAD,KAAA,EAAAA;IAAA;EACA;EACAI,MAAA,WAAAA,OAAAC,CAAA;IACA,OAAAA,CAAA;EACA;AACA", "ignoreList": []}]}