{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\layout\\components\\Sidebar\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\layout\\components\\Sidebar\\index.vue", "mtime": 1749104047629}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_vuex", "require", "_Logo", "_interopRequireDefault", "_SidebarItem", "_variables2", "components", "SidebarItem", "Logo", "computed", "_objectSpread2", "default", "mapState", "mapGetters", "activeMenu", "route", "$route", "meta", "path", "showLogo", "$store", "state", "settings", "sidebarLogo", "variables", "isCollapse", "sidebar", "opened"], "sources": ["src/layout/components/Sidebar/index.vue"], "sourcesContent": ["<template>\r\n    <div :class=\"{'has-logo':showLogo}\" :style=\"{ backgroundColor: settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground }\">\r\n        <logo v-if=\"showLogo\" :collapse=\"isCollapse\" />\r\n        <el-scrollbar :class=\"settings.sideTheme\" wrap-class=\"scrollbar-wrapper\">\r\n            <el-menu\r\n                :default-active=\"activeMenu\"\r\n                :collapse=\"isCollapse\"\r\n                :background-color=\"settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground\"\r\n                :text-color=\"settings.sideTheme === 'theme-dark' ? variables.menuColor : variables.menuLightColor\"\r\n                :unique-opened=\"true\"\r\n                :active-text-color=\"settings.theme\"\r\n                :collapse-transition=\"false\"\r\n                mode=\"vertical\"\r\n            >\r\n                <sidebar-item\r\n                    v-for=\"(route, index) in sidebarRouters\"\r\n                    :key=\"route.path  + index\"\r\n                    :item=\"route\"\r\n                    :base-path=\"route.path\"\r\n                />\r\n            </el-menu>\r\n        </el-scrollbar>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters, mapState } from \"vuex\";\r\nimport Logo from \"./Logo\";\r\nimport SidebarItem from \"./SidebarItem\";\r\nimport variables from \"@/assets/styles/variables.scss\";\r\n\r\nexport default {\r\n    components: { SidebarItem, Logo },\r\n    computed: {\r\n        ...mapState([\"settings\"]),\r\n        ...mapGetters([\"sidebarRouters\", \"sidebar\"]),\r\n        activeMenu() {\r\n            const route = this.$route;\r\n            const { meta, path } = route;\r\n            // if set path, the sidebar will highlight the path you set\r\n            if (meta.activeMenu) {\r\n                return meta.activeMenu;\r\n            }\r\n            return path;\r\n        },\r\n        showLogo() {\r\n            return this.$store.state.settings.sidebarLogo;\r\n        },\r\n        variables() {\r\n            return variables;\r\n        },\r\n        isCollapse() {\r\n            return !this.sidebar.opened;\r\n        }\r\n    }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;AA0BA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,YAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,WAAA,GAAAF,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAK,UAAA;IAAAC,WAAA,EAAAA,oBAAA;IAAAC,IAAA,EAAAA;EAAA;EACAC,QAAA,MAAAC,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA,IAAAC,cAAA,kBACA,IAAAC,gBAAA;IACAC,UAAA,WAAAA,WAAA;MACA,IAAAC,KAAA,QAAAC,MAAA;MACA,IAAAC,IAAA,GAAAF,KAAA,CAAAE,IAAA;QAAAC,IAAA,GAAAH,KAAA,CAAAG,IAAA;MACA;MACA,IAAAD,IAAA,CAAAH,UAAA;QACA,OAAAG,IAAA,CAAAH,UAAA;MACA;MACA,OAAAI,IAAA;IACA;IACAC,QAAA,WAAAA,SAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,WAAA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA,OAAAA,mBAAA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,aAAAC,OAAA,CAAAC,MAAA;IACA;EAAA;AAEA", "ignoreList": []}]}