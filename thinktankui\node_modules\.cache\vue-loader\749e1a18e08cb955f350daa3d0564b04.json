{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\dashboard\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\dashboard\\index.vue", "mtime": 1749104047639}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgVmlzZXIgZnJvbSAndmlzZXItdnVlJzsNCmltcG9ydCB7DQogIEF2YXRhciwNCiAgQnV0dG9uLA0KICBDYXJkLA0KICBDb2wsDQogIExpc3QsDQogIFJvdywNCiAgU3RhdGlzdGljLA0KICBUYWcsDQogIERpdmlkZXIsDQogIFN3aXRjaCwNCiAgQmFkZ2UsDQp9IGZyb20gImFudC1kZXNpZ24tdnVlIjsNCmltcG9ydCAnYW50LWRlc2lnbi12dWUvZGlzdC9hbnRkLmNzcyc7DQppbXBvcnQgVnVlIGZyb20gInZ1ZSI7DQoNClZ1ZS51c2UoVmlzZXIpOw0KVnVlLmNvbXBvbmVudChBdmF0YXIubmFtZSwgQXZhdGFyKTsNClZ1ZS5jb21wb25lbnQoQnV0dG9uLm5hbWUsIEJ1dHRvbik7DQpWdWUuY29tcG9uZW50KENhcmQubmFtZSwgQ2FyZCk7DQpWdWUuY29tcG9uZW50KENhcmQuR3JpZC5uYW1lLCBDYXJkLkdyaWQpOw0KVnVlLmNvbXBvbmVudChDYXJkLk1ldGEubmFtZSwgQ2FyZC5NZXRhKTsNClZ1ZS5jb21wb25lbnQoQ29sLm5hbWUsIENvbCk7DQpWdWUuY29tcG9uZW50KExpc3QubmFtZSwgTGlzdCk7DQpWdWUuY29tcG9uZW50KExpc3QuSXRlbS5uYW1lLCBMaXN0Lkl0ZW0pOw0KVnVlLmNvbXBvbmVudChMaXN0Lkl0ZW0uTWV0YS5uYW1lLCBMaXN0Lkl0ZW0uTWV0YSk7DQpWdWUuY29tcG9uZW50KFJvdy5uYW1lLCBSb3cpOw0KVnVlLmNvbXBvbmVudChTdGF0aXN0aWMubmFtZSwgU3RhdGlzdGljKTsNClZ1ZS5jb21wb25lbnQoVGFnLm5hbWUsIFRhZyk7DQpWdWUuY29tcG9uZW50KERpdmlkZXIubmFtZSwgRGl2aWRlcik7DQpWdWUuY29tcG9uZW50KFN3aXRjaC5uYW1lLCBTd2l0Y2gpOw0KVnVlLmNvbXBvbmVudChCYWRnZS5uYW1lLCBCYWRnZSk7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkRhc2hCb2FyZCIsDQogIG1ldGhvZHM6IHsNCiAgICBnb1RvU2VhcmNoKCkgew0KICAgICAgLy8g6Lez6L2s5Yiw5pCc57Si57uT5p6c6aG16Z2i77yM5bm25Lyg6YCS5rWL6K+V5pCc57Si5YWz6ZSu6K+NDQogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7DQogICAgICAgIHBhdGg6ICcvc2VhcmNoLXJlc3VsdHMnLA0KICAgICAgICBxdWVyeTogew0KICAgICAgICAgIHE6ICfmlrnlpKog5Y6o55S1Jw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9DQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIG9yaWdpbmFsVG9wTmF2OiB1bmRlZmluZWQsIC8vIOWtmOWCqOWOn+Wni+eahHRvcE5hdueKtuaAgQ0KICAgICAgY3VycmVudFVzZXI6IHsNCiAgICAgICAgYXZhdGFyOg0KICAgICAgICAgICJodHRwczovL2d3LmFsaXBheW9iamVjdHMuY29tL3pvcy9ybXNwb3J0YWwvQmlhemZhbnhtYW1OUm94eFZ4a2EucG5nIiwNCiAgICAgICAgbmFtZTogIuaWueWkqiIsDQogICAgICAgIHVzZXJpZDogIjAwMDAwMDAxIiwNCiAgICAgICAgZW1haWw6ICJmYW5ndGFAZXhhbXBsZS5jb20iLA0KICAgICAgICBzaWduYXR1cmU6ICLoiIbmg4Xnm5HmjqfvvIzlrp7ml7bmiormjqciLA0KICAgICAgICB0aXRsZTogIuiIhuaDheWIhuaekOW4iCIsDQogICAgICAgIGdyb3VwOiAi6IiG5oOF55uR5o6n5Lit5b+DIiwNCiAgICAgIH0sDQogICAgICAvLyDnu5/orqHmlbDmja4NCiAgICAgIGhvdFRvcGljc0NvdW50OiAyNCwNCiAgICAgIG5lZ2F0aXZlQ291bnQ6IDgsDQogICAgICB0b3RhbE1vbml0b3JDb3VudDogMTQ0NiwNCg0KICAgICAgLy8gMjTlsI/ml7bkvKDmkq3otovlir/mlbDmja4NCiAgICAgIHRyZW5kRGF0YTogWw0KICAgICAgICB7IHRpbWU6ICIwMDowMCIsIHZhbHVlOiAzMDAsIHR5cGU6ICLmgLvph48iIH0sDQogICAgICAgIHsgdGltZTogIjAxOjAwIiwgdmFsdWU6IDI1MCwgdHlwZTogIuaAu+mHjyIgfSwNCiAgICAgICAgeyB0aW1lOiAiMDI6MDAiLCB2YWx1ZTogMjAwLCB0eXBlOiAi5oC76YePIiB9LA0KICAgICAgICB7IHRpbWU6ICIwMzowMCIsIHZhbHVlOiAxODAsIHR5cGU6ICLmgLvph48iIH0sDQogICAgICAgIHsgdGltZTogIjA0OjAwIiwgdmFsdWU6IDE1MCwgdHlwZTogIuaAu+mHjyIgfSwNCiAgICAgICAgeyB0aW1lOiAiMDU6MDAiLCB2YWx1ZTogMTcwLCB0eXBlOiAi5oC76YePIiB9LA0KICAgICAgICB7IHRpbWU6ICIwNjowMCIsIHZhbHVlOiAyMjAsIHR5cGU6ICLmgLvph48iIH0sDQogICAgICAgIHsgdGltZTogIjA3OjAwIiwgdmFsdWU6IDM1MCwgdHlwZTogIuaAu+mHjyIgfSwNCiAgICAgICAgeyB0aW1lOiAiMDg6MDAiLCB2YWx1ZTogNTAwLCB0eXBlOiAi5oC76YePIiB9LA0KICAgICAgICB7IHRpbWU6ICIwOTowMCIsIHZhbHVlOiA2MjAsIHR5cGU6ICLmgLvph48iIH0sDQogICAgICAgIHsgdGltZTogIjEwOjAwIiwgdmFsdWU6IDU1MCwgdHlwZTogIuaAu+mHjyIgfSwNCiAgICAgICAgeyB0aW1lOiAiMTE6MDAiLCB2YWx1ZTogNDgwLCB0eXBlOiAi5oC76YePIiB9LA0KICAgICAgICB7IHRpbWU6ICIxMjowMCIsIHZhbHVlOiA0MDAsIHR5cGU6ICLmgLvph48iIH0sDQogICAgICAgIHsgdGltZTogIjEzOjAwIiwgdmFsdWU6IDQ1MCwgdHlwZTogIuaAu+mHjyIgfSwNCiAgICAgICAgeyB0aW1lOiAiMTQ6MDAiLCB2YWx1ZTogNTAwLCB0eXBlOiAi5oC76YePIiB9LA0KICAgICAgICB7IHRpbWU6ICIxNTowMCIsIHZhbHVlOiA0NzAsIHR5cGU6ICLmgLvph48iIH0sDQogICAgICAgIHsgdGltZTogIjE2OjAwIiwgdmFsdWU6IDQ2MCwgdHlwZTogIuaAu+mHjyIgfSwNCiAgICAgICAgeyB0aW1lOiAiMTc6MDAiLCB2YWx1ZTogNTIwLCB0eXBlOiAi5oC76YePIiB9LA0KICAgICAgICB7IHRpbWU6ICIxODowMCIsIHZhbHVlOiA1ODAsIHR5cGU6ICLmgLvph48iIH0sDQogICAgICAgIHsgdGltZTogIjE5OjAwIiwgdmFsdWU6IDU1MCwgdHlwZTogIuaAu+mHjyIgfSwNCiAgICAgICAgeyB0aW1lOiAiMjA6MDAiLCB2YWx1ZTogNTAwLCB0eXBlOiAi5oC76YePIiB9LA0KICAgICAgICB7IHRpbWU6ICIyMTowMCIsIHZhbHVlOiA0NTAsIHR5cGU6ICLmgLvph48iIH0sDQogICAgICAgIHsgdGltZTogIjIyOjAwIiwgdmFsdWU6IDQwMCwgdHlwZTogIuaAu+mHjyIgfSwNCiAgICAgICAgeyB0aW1lOiAiMjM6MDAiLCB2YWx1ZTogMzUwLCB0eXBlOiAi5oC76YePIiB9LA0KDQogICAgICAgIHsgdGltZTogIjAwOjAwIiwgdmFsdWU6IDEwMCwgdHlwZTogIuW+ruWNmiIgfSwNCiAgICAgICAgeyB0aW1lOiAiMDE6MDAiLCB2YWx1ZTogODAsIHR5cGU6ICLlvq7ljZoiIH0sDQogICAgICAgIHsgdGltZTogIjAyOjAwIiwgdmFsdWU6IDYwLCB0eXBlOiAi5b6u5Y2aIiB9LA0KICAgICAgICB7IHRpbWU6ICIwMzowMCIsIHZhbHVlOiA1MCwgdHlwZTogIuW+ruWNmiIgfSwNCiAgICAgICAgeyB0aW1lOiAiMDQ6MDAiLCB2YWx1ZTogNDAsIHR5cGU6ICLlvq7ljZoiIH0sDQogICAgICAgIHsgdGltZTogIjA1OjAwIiwgdmFsdWU6IDUwLCB0eXBlOiAi5b6u5Y2aIiB9LA0KICAgICAgICB7IHRpbWU6ICIwNjowMCIsIHZhbHVlOiA3MCwgdHlwZTogIuW+ruWNmiIgfSwNCiAgICAgICAgeyB0aW1lOiAiMDc6MDAiLCB2YWx1ZTogMTAwLCB0eXBlOiAi5b6u5Y2aIiB9LA0KICAgICAgICB7IHRpbWU6ICIwODowMCIsIHZhbHVlOiAxNTAsIHR5cGU6ICLlvq7ljZoiIH0sDQogICAgICAgIHsgdGltZTogIjA5OjAwIiwgdmFsdWU6IDE4MCwgdHlwZTogIuW+ruWNmiIgfSwNCiAgICAgICAgeyB0aW1lOiAiMTA6MDAiLCB2YWx1ZTogMTYwLCB0eXBlOiAi5b6u5Y2aIiB9LA0KICAgICAgICB7IHRpbWU6ICIxMTowMCIsIHZhbHVlOiAxNDAsIHR5cGU6ICLlvq7ljZoiIH0sDQogICAgICAgIHsgdGltZTogIjEyOjAwIiwgdmFsdWU6IDEyMCwgdHlwZTogIuW+ruWNmiIgfSwNCiAgICAgICAgeyB0aW1lOiAiMTM6MDAiLCB2YWx1ZTogMTMwLCB0eXBlOiAi5b6u5Y2aIiB9LA0KICAgICAgICB7IHRpbWU6ICIxNDowMCIsIHZhbHVlOiAxNTAsIHR5cGU6ICLlvq7ljZoiIH0sDQogICAgICAgIHsgdGltZTogIjE1OjAwIiwgdmFsdWU6IDE0MCwgdHlwZTogIuW+ruWNmiIgfSwNCiAgICAgICAgeyB0aW1lOiAiMTY6MDAiLCB2YWx1ZTogMTMwLCB0eXBlOiAi5b6u5Y2aIiB9LA0KICAgICAgICB7IHRpbWU6ICIxNzowMCIsIHZhbHVlOiAxNTAsIHR5cGU6ICLlvq7ljZoiIH0sDQogICAgICAgIHsgdGltZTogIjE4OjAwIiwgdmFsdWU6IDE3MCwgdHlwZTogIuW+ruWNmiIgfSwNCiAgICAgICAgeyB0aW1lOiAiMTk6MDAiLCB2YWx1ZTogMTYwLCB0eXBlOiAi5b6u5Y2aIiB9LA0KICAgICAgICB7IHRpbWU6ICIyMDowMCIsIHZhbHVlOiAxNTAsIHR5cGU6ICLlvq7ljZoiIH0sDQogICAgICAgIHsgdGltZTogIjIxOjAwIiwgdmFsdWU6IDEzMCwgdHlwZTogIuW+ruWNmiIgfSwNCiAgICAgICAgeyB0aW1lOiAiMjI6MDAiLCB2YWx1ZTogMTIwLCB0eXBlOiAi5b6u5Y2aIiB9LA0KICAgICAgICB7IHRpbWU6ICIyMzowMCIsIHZhbHVlOiAxMDAsIHR5cGU6ICLlvq7ljZoiIH0sDQoNCiAgICAgICAgeyB0aW1lOiAiMDA6MDAiLCB2YWx1ZTogODAsIHR5cGU6ICLop4bpopEiIH0sDQogICAgICAgIHsgdGltZTogIjAxOjAwIiwgdmFsdWU6IDcwLCB0eXBlOiAi6KeG6aKRIiB9LA0KICAgICAgICB7IHRpbWU6ICIwMjowMCIsIHZhbHVlOiA2MCwgdHlwZTogIuinhumikSIgfSwNCiAgICAgICAgeyB0aW1lOiAiMDM6MDAiLCB2YWx1ZTogNTAsIHR5cGU6ICLop4bpopEiIH0sDQogICAgICAgIHsgdGltZTogIjA0OjAwIiwgdmFsdWU6IDQwLCB0eXBlOiAi6KeG6aKRIiB9LA0KICAgICAgICB7IHRpbWU6ICIwNTowMCIsIHZhbHVlOiA1MCwgdHlwZTogIuinhumikSIgfSwNCiAgICAgICAgeyB0aW1lOiAiMDY6MDAiLCB2YWx1ZTogNjAsIHR5cGU6ICLop4bpopEiIH0sDQogICAgICAgIHsgdGltZTogIjA3OjAwIiwgdmFsdWU6IDgwLCB0eXBlOiAi6KeG6aKRIiB9LA0KICAgICAgICB7IHRpbWU6ICIwODowMCIsIHZhbHVlOiAxMDAsIHR5cGU6ICLop4bpopEiIH0sDQogICAgICAgIHsgdGltZTogIjA5OjAwIiwgdmFsdWU6IDEyMCwgdHlwZTogIuinhumikSIgfSwNCiAgICAgICAgeyB0aW1lOiAiMTA6MDAiLCB2YWx1ZTogMTEwLCB0eXBlOiAi6KeG6aKRIiB9LA0KICAgICAgICB7IHRpbWU6ICIxMTowMCIsIHZhbHVlOiAxMDAsIHR5cGU6ICLop4bpopEiIH0sDQogICAgICAgIHsgdGltZTogIjEyOjAwIiwgdmFsdWU6IDkwLCB0eXBlOiAi6KeG6aKRIiB9LA0KICAgICAgICB7IHRpbWU6ICIxMzowMCIsIHZhbHVlOiAxMDAsIHR5cGU6ICLop4bpopEiIH0sDQogICAgICAgIHsgdGltZTogIjE0OjAwIiwgdmFsdWU6IDExMCwgdHlwZTogIuinhumikSIgfSwNCiAgICAgICAgeyB0aW1lOiAiMTU6MDAiLCB2YWx1ZTogMTAwLCB0eXBlOiAi6KeG6aKRIiB9LA0KICAgICAgICB7IHRpbWU6ICIxNjowMCIsIHZhbHVlOiA5MCwgdHlwZTogIuinhumikSIgfSwNCiAgICAgICAgeyB0aW1lOiAiMTc6MDAiLCB2YWx1ZTogMTAwLCB0eXBlOiAi6KeG6aKRIiB9LA0KICAgICAgICB7IHRpbWU6ICIxODowMCIsIHZhbHVlOiAxMTAsIHR5cGU6ICLop4bpopEiIH0sDQogICAgICAgIHsgdGltZTogIjE5OjAwIiwgdmFsdWU6IDEwMCwgdHlwZTogIuinhumikSIgfSwNCiAgICAgICAgeyB0aW1lOiAiMjA6MDAiLCB2YWx1ZTogOTAsIHR5cGU6ICLop4bpopEiIH0sDQogICAgICAgIHsgdGltZTogIjIxOjAwIiwgdmFsdWU6IDgwLCB0eXBlOiAi6KeG6aKRIiB9LA0KICAgICAgICB7IHRpbWU6ICIyMjowMCIsIHZhbHVlOiA3MCwgdHlwZTogIuinhumikSIgfSwNCiAgICAgICAgeyB0aW1lOiAiMjM6MDAiLCB2YWx1ZTogNjAsIHR5cGU6ICLop4bpopEiIH0sDQogICAgICBdLA0KICAgICAgdHJlbmRTY2FsZTogWw0KICAgICAgICB7DQogICAgICAgICAgZGF0YUtleTogJ3ZhbHVlJywNCiAgICAgICAgICBtaW46IDAsDQogICAgICAgIH0sDQogICAgICBdLA0KDQogICAgICAvLyDlubPlj7DmnaXmupDljaDmr5TmlbDmja4NCiAgICAgIHBsYXRmb3JtRGF0YTogWw0KICAgICAgICB7IHR5cGU6ICflvq7ljZonLCBwZXJjZW50OiAzMS4yIH0sDQogICAgICAgIHsgdHlwZTogJ+inhumikScsIHBlcmNlbnQ6IDE3LjkgfSwNCiAgICAgICAgeyB0eXBlOiAn5aS05p2h5Y+3JywgcGVyY2VudDogMTUuMyB9LA0KICAgICAgICB7IHR5cGU6ICdBUFAnLCBwZXJjZW50OiAxMi43IH0sDQogICAgICAgIHsgdHlwZTogJ+W+ruS/oScsIHBlcmNlbnQ6IDkuOCB9LA0KICAgICAgICB7IHR5cGU6ICflhbbku5YnLCBwZXJjZW50OiAxMy4xIH0sDQogICAgICBdLA0KICAgICAgcGxhdGZvcm1TY2FsZTogWw0KICAgICAgICB7DQogICAgICAgICAgZGF0YUtleTogJ3BlcmNlbnQnLA0KICAgICAgICAgIG1pbjogMCwNCiAgICAgICAgICBmb3JtYXR0ZXI6ICcuMCUnLA0KICAgICAgICB9LA0KICAgICAgXSwNCiAgICAgIGxhYmVsQ29uZmlnOiB7DQogICAgICAgIG9mZnNldDogLTIwLA0KICAgICAgICB0ZXh0U3R5bGU6IHsNCiAgICAgICAgICBmaWxsOiAnIzAwMCcsDQogICAgICAgICAgZm9udFNpemU6IDEyLA0KICAgICAgICAgIGZvbnRXZWlnaHQ6ICdib2xkJywNCiAgICAgICAgfSwNCiAgICAgICAgZm9ybWF0dGVyOiAodGV4dCwgaXRlbSkgPT4gew0KICAgICAgICAgIHJldHVybiBgJHtpdGVtLnBvaW50LnR5cGV9OiAke2l0ZW0ucG9pbnQucGVyY2VudH0lYDsNCiAgICAgICAgfSwNCiAgICAgIH0sDQoNCiAgICAgIC8vIOaDheaEn+WIhuW4g+WNoOavlOaVsOaNrg0KICAgICAgc2VudGltZW50RGF0YTogWw0KICAgICAgICB7IHRpbWU6ICcwMDowMCcsIHZhbHVlOiAzMDAsIHR5cGU6ICfkuK3mgKcnIH0sDQogICAgICAgIHsgdGltZTogJzAyOjAwJywgdmFsdWU6IDI4MCwgdHlwZTogJ+S4reaApycgfSwNCiAgICAgICAgeyB0aW1lOiAnMDQ6MDAnLCB2YWx1ZTogMjUwLCB0eXBlOiAn5Lit5oCnJyB9LA0KICAgICAgICB7IHRpbWU6ICcwNjowMCcsIHZhbHVlOiAyNjAsIHR5cGU6ICfkuK3mgKcnIH0sDQogICAgICAgIHsgdGltZTogJzA4OjAwJywgdmFsdWU6IDI4MCwgdHlwZTogJ+S4reaApycgfSwNCiAgICAgICAgeyB0aW1lOiAnMTA6MDAnLCB2YWx1ZTogMzAwLCB0eXBlOiAn5Lit5oCnJyB9LA0KICAgICAgICB7IHRpbWU6ICcxMjowMCcsIHZhbHVlOiAyODAsIHR5cGU6ICfkuK3mgKcnIH0sDQogICAgICAgIHsgdGltZTogJzE0OjAwJywgdmFsdWU6IDI1MCwgdHlwZTogJ+S4reaApycgfSwNCiAgICAgICAgeyB0aW1lOiAnMTY6MDAnLCB2YWx1ZTogMjYwLCB0eXBlOiAn5Lit5oCnJyB9LA0KICAgICAgICB7IHRpbWU6ICcxODowMCcsIHZhbHVlOiAyODAsIHR5cGU6ICfkuK3mgKcnIH0sDQogICAgICAgIHsgdGltZTogJzIwOjAwJywgdmFsdWU6IDMwMCwgdHlwZTogJ+S4reaApycgfSwNCiAgICAgICAgeyB0aW1lOiAnMjI6MDAnLCB2YWx1ZTogMjgwLCB0eXBlOiAn5Lit5oCnJyB9LA0KDQogICAgICAgIHsgdGltZTogJzAwOjAwJywgdmFsdWU6IDEwMCwgdHlwZTogJ+ato+mdoicgfSwNCiAgICAgICAgeyB0aW1lOiAnMDI6MDAnLCB2YWx1ZTogMTIwLCB0eXBlOiAn5q2j6Z2iJyB9LA0KICAgICAgICB7IHRpbWU6ICcwNDowMCcsIHZhbHVlOiAxNDAsIHR5cGU6ICfmraPpnaInIH0sDQogICAgICAgIHsgdGltZTogJzA2OjAwJywgdmFsdWU6IDEzMCwgdHlwZTogJ+ato+mdoicgfSwNCiAgICAgICAgeyB0aW1lOiAnMDg6MDAnLCB2YWx1ZTogMTIwLCB0eXBlOiAn5q2j6Z2iJyB9LA0KICAgICAgICB7IHRpbWU6ICcxMDowMCcsIHZhbHVlOiAxMDAsIHR5cGU6ICfmraPpnaInIH0sDQogICAgICAgIHsgdGltZTogJzEyOjAwJywgdmFsdWU6IDEyMCwgdHlwZTogJ+ato+mdoicgfSwNCiAgICAgICAgeyB0aW1lOiAnMTQ6MDAnLCB2YWx1ZTogMTQwLCB0eXBlOiAn5q2j6Z2iJyB9LA0KICAgICAgICB7IHRpbWU6ICcxNjowMCcsIHZhbHVlOiAxMzAsIHR5cGU6ICfmraPpnaInIH0sDQogICAgICAgIHsgdGltZTogJzE4OjAwJywgdmFsdWU6IDEyMCwgdHlwZTogJ+ato+mdoicgfSwNCiAgICAgICAgeyB0aW1lOiAnMjA6MDAnLCB2YWx1ZTogMTAwLCB0eXBlOiAn5q2j6Z2iJyB9LA0KICAgICAgICB7IHRpbWU6ICcyMjowMCcsIHZhbHVlOiAxMjAsIHR5cGU6ICfmraPpnaInIH0sDQoNCiAgICAgICAgeyB0aW1lOiAnMDA6MDAnLCB2YWx1ZTogNTAsIHR5cGU6ICfotJ/pnaInIH0sDQogICAgICAgIHsgdGltZTogJzAyOjAwJywgdmFsdWU6IDYwLCB0eXBlOiAn6LSf6Z2iJyB9LA0KICAgICAgICB7IHRpbWU6ICcwNDowMCcsIHZhbHVlOiA3MCwgdHlwZTogJ+i0n+mdoicgfSwNCiAgICAgICAgeyB0aW1lOiAnMDY6MDAnLCB2YWx1ZTogNjUsIHR5cGU6ICfotJ/pnaInIH0sDQogICAgICAgIHsgdGltZTogJzA4OjAwJywgdmFsdWU6IDYwLCB0eXBlOiAn6LSf6Z2iJyB9LA0KICAgICAgICB7IHRpbWU6ICcxMDowMCcsIHZhbHVlOiA1MCwgdHlwZTogJ+i0n+mdoicgfSwNCiAgICAgICAgeyB0aW1lOiAnMTI6MDAnLCB2YWx1ZTogNjAsIHR5cGU6ICfotJ/pnaInIH0sDQogICAgICAgIHsgdGltZTogJzE0OjAwJywgdmFsdWU6IDcwLCB0eXBlOiAn6LSf6Z2iJyB9LA0KICAgICAgICB7IHRpbWU6ICcxNjowMCcsIHZhbHVlOiA2NSwgdHlwZTogJ+i0n+mdoicgfSwNCiAgICAgICAgeyB0aW1lOiAnMTg6MDAnLCB2YWx1ZTogNjAsIHR5cGU6ICfotJ/pnaInIH0sDQogICAgICAgIHsgdGltZTogJzIwOjAwJywgdmFsdWU6IDUwLCB0eXBlOiAn6LSf6Z2iJyB9LA0KICAgICAgICB7IHRpbWU6ICcyMjowMCcsIHZhbHVlOiA2MCwgdHlwZTogJ+i0n+mdoicgfSwNCiAgICAgIF0sDQogICAgICBzZW50aW1lbnRTY2FsZTogWw0KICAgICAgICB7DQogICAgICAgICAgZGF0YUtleTogJ3ZhbHVlJywNCiAgICAgICAgICBtaW46IDAsDQogICAgICAgIH0sDQogICAgICBdLA0KDQogICAgICAvLyDng63pl6jmlofnq6DliJfooagNCiAgICAgIGhvdEFydGljbGVzOiBbDQogICAgICAgIHsNCiAgICAgICAgICBpZDogMSwNCiAgICAgICAgICB0aXRsZTogJ+aWueWkqueDreawtOWZqChGb3RpbGUp5a6Y5pa55peX6Iiw5bqXIOaWueWkqueDreawtOWZqChGb3RpbGUpLTExMSDmlrnlpKrng63msLTlmagoRm90aWxlKSA0MDAuNjgwOC42NTUuLi4nLA0KICAgICAgICAgIHRhZzogJ+eDremXqCcsDQogICAgICAgICAgdGFnQ29sb3I6ICcjZjUwJywNCiAgICAgICAgICBzb3VyY2U6ICfnlLXllYblubPlj7AnLA0KICAgICAgICAgIHB1Ymxpc2hUaW1lOiAnMjAyNS0wNC0yOSAyMDoyNDowMCcsDQogICAgICAgICAgbGluazogJ2h0dHBzOi8vZXhhbXBsZS5jb20vYXJ0aWNsZS8xJywNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAyLA0KICAgICAgICAgIHRpdGxlOiAn6L+R5Zyw6ZO6MzDlubTlnLDmmpblj6PnopHvvIzpnaDogIHlrZfkuozlhajmlrDorr7orqHkvY/kurrjgIIj5oiQ6YO95LqM5omL5oi/ICPlnLDmmpbnlLXmupAnLA0KICAgICAgICAgIHRhZzogJ+eDremXqCcsDQogICAgICAgICAgdGFnQ29sb3I6ICcjZjUwJywNCiAgICAgICAgICBzb3VyY2U6ICfop4bpopHlubPlj7AnLA0KICAgICAgICAgIHB1Ymxpc2hUaW1lOiAnMjAyNS0wNC0yOSAxNToxNToxNicsDQogICAgICAgICAgbGluazogJ2h0dHBzOi8vZXhhbXBsZS5jb20vYXJ0aWNsZS8yJywNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAzLA0KICAgICAgICAgIHRpdGxlOiAn546w5Zyo6L+Z5Liq56S+5Lya6KaB6Z2g6ISR6KKL5ZCD6aWt77yM5Y+v6IO95piv5LiA6L6I5a2Q55qE5LqL77yM5byA546p56yRIOaIkeS4gOS4quWunuS9k+S6uiDmm7TlupTor6XmnInlk4HotKjjgIInLA0KICAgICAgICAgIHRhZzogJ+eDremXqCcsDQogICAgICAgICAgdGFnQ29sb3I6ICcjZjUwJywNCiAgICAgICAgICBzb3VyY2U6ICfop4bpopHlubPlj7AnLA0KICAgICAgICAgIHB1Ymxpc2hUaW1lOiAnMjAyNS0wNC0yOSAxNDoyOTowOScsDQogICAgICAgICAgbGluazogJ2h0dHBzOi8vZXhhbXBsZS5jb20vYXJ0aWNsZS8zJywNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiA0LA0KICAgICAgICAgIHRpdGxlOiAn77yI5peg5qCH6aKY77yJUzIgViBiICMjIGIgIyMjIOmZkOWumjLkurrmlrnlpKrlhajnkIPpppbliJvkuIDku6Pnh4PmsJTngbblhajnkIPpppblj5EgMDMgQUxPUUFWIDAgMTMg5Zu+QS01OTg2IDU5Li4uJywNCiAgICAgICAgICB0YWc6ICfng63pl6gnLA0KICAgICAgICAgIHRhZ0NvbG9yOiAnI2Y1MCcsDQogICAgICAgICAgc291cmNlOiAn6KeG6aKR5bmz5Y+wJywNCiAgICAgICAgICBwdWJsaXNoVGltZTogJzIwMjUtMDQtMjkgMTQ6MTk6MjMnLA0KICAgICAgICAgIGxpbms6ICdodHRwczovL2V4YW1wbGUuY29tL2FydGljbGUvNCcsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpZDogNSwNCiAgICAgICAgICB0aXRsZTogJ+S9oOS6jui/h+i/meS6i+WQlz8g5p2l55yL5ZCI5LqGI+aWueWkquWumOe9kSAj5Y2X57KkICPmlrDmoaXlj6PlpKfljqjnmoTlrZflhpnlvpcgI+WQjOWfjuWnkOWmueeahOaci+WPi+eci+i/h+adpSAj55S15b2xJywNCiAgICAgICAgICB0YWc6ICfng63pl6gnLA0KICAgICAgICAgIHRhZ0NvbG9yOiAnI2Y1MCcsDQogICAgICAgICAgc291cmNlOiAn6KeG6aKR5bmz5Y+wJywNCiAgICAgICAgICBwdWJsaXNoVGltZTogJzIwMjUtMDQtMjkgMTI6NDg6MDQnLA0KICAgICAgICAgIGxpbms6ICdodHRwczovL2V4YW1wbGUuY29tL2FydGljbGUvNScsDQogICAgICAgIH0sDQogICAgICBdLA0KDQogICAgICAvLyDmiqXlkYrmqKHmnb/liJfooagNCiAgICAgIHJlcG9ydFRlbXBsYXRlczogWw0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDEsDQogICAgICAgICAgdGl0bGU6ICfoiIbmg4Ut5ZGo5oql6KGoJywNCiAgICAgICAgICBjcmVhdGVUaW1lOiAnMjAxOS0xMS0xNiAxODowMjowMCcsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpZDogMiwNCiAgICAgICAgICB0aXRsZTogJ+iIhuaDhS3mnIjmiqXooagnLA0KICAgICAgICAgIGNyZWF0ZVRpbWU6ICcyMDE5LTExLTE4IDE4OjA2OjUyJywNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAzLA0KICAgICAgICAgIHRpdGxlOiAn6IiG5oOFLeWto+W6puihqCcsDQogICAgICAgICAgY3JlYXRlVGltZTogJzIwMjEtMDktMTIgMTA6MTU6MDAnLA0KICAgICAgICB9LA0KICAgICAgXSwNCg0KICAgICAgLy8g5pyA5paw6LSf6Z2i5L+h5oGvDQogICAgICBuZWdhdGl2ZU5ld3M6IFsNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAxLA0KICAgICAgICAgIHRpdGxlOiAn5pa55aSq54Ot5rC05Zmo5Ye6546w6LSo6YeP6Zeu6aKY77yM5aSa5ZCN55So5oi35oqV6K+J5peg5Lq65aSE55CGJywNCiAgICAgICAgICBwdWJsaXNoVGltZTogJzIwMjUtMDQtMjkgMTk6MDM6MDAnLA0KICAgICAgICAgIGxpbms6ICdodHRwczovL2V4YW1wbGUuY29tL25lZ2F0aXZlLzEnLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDIsDQogICAgICAgICAgdGl0bGU6ICfmlrnlpKrljqjnlLXlronoo4XnlLXor53ml6DkurrmjqXlkKzvvIzlrqLmiLfmipXor4nmnI3liqHmgIHluqblt64nLA0KICAgICAgICAgIHB1Ymxpc2hUaW1lOiAnMjAyNS0wNC0yOSAxODoyMjo0MycsDQogICAgICAgICAgbGluazogJ2h0dHBzOi8vZXhhbXBsZS5jb20vbmVnYXRpdmUvMicsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpZDogMywNCiAgICAgICAgICB0aXRsZTogJ+iIquepuuWls+WPi+WBt+aLjeaXpeiusOiiq+ekvO+8jOe9keWPi+WQju+8jOaIkeWcqOW6iuS4iueci+WIsOS6hueUteinhuS4gOW5leeahOWluScsDQogICAgICAgICAgcHVibGlzaFRpbWU6ICcyMDI1LTA0LTI5IDE3OjQ4OjQ1JywNCiAgICAgICAgICBsaW5rOiAnaHR0cHM6Ly9leGFtcGxlLmNvbS9uZWdhdGl2ZS8zJywNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiA0LA0KICAgICAgICAgIHRpdGxlOiAn5p+Q5ZOB54mM5aSn5Z6L5oq95rK554Of5py65Zmq6Z+z6Zeu6aKY5byV5Y+R55So5oi35LiN5ruhJywNCiAgICAgICAgICBwdWJsaXNoVGltZTogJzIwMjUtMDQtMjkgMTU6MTU6MTYnLA0KICAgICAgICAgIGxpbms6ICdodHRwczovL2V4YW1wbGUuY29tL25lZ2F0aXZlLzQnLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDUsDQogICAgICAgICAgdGl0bGU6ICflrrbnlLXllK7lkI7mnI3liqHosIPmn6XvvJrlpJrlk4HniYzmnI3liqHotKjph4/lj4Llt67kuI3pvZAnLA0KICAgICAgICAgIHB1Ymxpc2hUaW1lOiAnMjAyNS0wNC0yOSAxMjoyNjowNCcsDQogICAgICAgICAgbGluazogJ2h0dHBzOi8vZXhhbXBsZS5jb20vbmVnYXRpdmUvNScsDQogICAgICAgIH0sDQogICAgICBdLA0KDQogICAgICBsb2FkaW5nOiB0cnVlDQogICAgfTsNCiAgfSwNCg0KICBtb3VudGVkKCkgew0KICAgIC8vIOmakOiXj+mhtumDqOWvvOiIquagjw0KICAgIHRoaXMub3JpZ2luYWxUb3BOYXYgPSB0aGlzLiRzdG9yZS5zdGF0ZS5zZXR0aW5ncy50b3BOYXYNCiAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnc2V0dGluZ3MvY2hhbmdlU2V0dGluZycsIHsNCiAgICAgIGtleTogJ3RvcE5hdicsDQogICAgICB2YWx1ZTogZmFsc2UNCiAgICB9KQ0KDQogICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICB9LCAxMDAwKTsNCiAgfSwNCiAgYmVmb3JlRGVzdHJveSgpIHsNCiAgICAvLyDmgaLlpI3pobbpg6jlr7zoiKrmoI/orr7nva4NCiAgICBpZiAodGhpcy5vcmlnaW5hbFRvcE5hdiAhPT0gdW5kZWZpbmVkKSB7DQogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnc2V0dGluZ3MvY2hhbmdlU2V0dGluZycsIHsNCiAgICAgICAga2V5OiAndG9wTmF2JywNCiAgICAgICAgdmFsdWU6IHRoaXMub3JpZ2luYWxUb3BOYXYNCiAgICAgIH0pDQogICAgfQ0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6LA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/dashboard", "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"page-header-content\">\r\n      <div class=\"avatar\">\r\n        <a-avatar size=\"large\" :src=\"currentUser.avatar\" />\r\n      </div>\r\n      <div class=\"content\">\r\n        <div class=\"content-title\">\r\n          {{ currentUser.name }}<span class=\"welcome-text\">，欢迎使用舆情监控系统</span>\r\n        </div>\r\n        <div>{{ currentUser.title }} | {{ currentUser.group }}</div>\r\n      </div>\r\n      <div class=\"extra-content\">\r\n        <div class=\"stat-item\">\r\n          <a-statistic title=\"今日热点\" :value=\"hotTopicsCount\" />\r\n        </div>\r\n        <div class=\"stat-item\">\r\n          <a-statistic title=\"负面信息\" :value=\"negativeCount\" />\r\n        </div>\r\n        <div class=\"stat-item\">\r\n          <a-statistic title=\"总监控量\" :value=\"totalMonitorCount\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div>\r\n      <a-row :gutter=\"24\">\r\n        <a-col :xl=\"16\" :lg=\"24\" :md=\"24\" :sm=\"24\" :xs=\"24\">\r\n          <a-card\r\n            :loading=\"loading\"\r\n            style=\"margin-bottom: 24px\"\r\n            :bordered=\"false\"\r\n            title=\"24小时传播趋势\"\r\n          >\r\n            <div style=\"height: 400px; position: relative;\">\r\n              <v-chart :forceFit=\"true\" height=\"400\" :data=\"trendData\" :scale=\"trendScale\">\r\n                <v-tooltip crosshairs></v-tooltip>\r\n                <v-axis dataKey=\"time\"></v-axis>\r\n                <v-axis dataKey=\"value\"></v-axis>\r\n                <v-legend></v-legend>\r\n                <v-line position=\"time*value\" color=\"type\" :size=\"2\"></v-line>\r\n                <v-point position=\"time*value\" color=\"type\" :size=\"4\" shape=\"circle\"></v-point>\r\n              </v-chart>\r\n            </div>\r\n          </a-card>\r\n\r\n          <a-row :gutter=\"24\">\r\n            <a-col :span=\"12\">\r\n              <a-card\r\n                :loading=\"loading\"\r\n                style=\"margin-bottom: 24px\"\r\n                :bordered=\"false\"\r\n                title=\"平台来源占比\"\r\n              >\r\n                <div style=\"height: 300px;\">\r\n                  <v-chart :forceFit=\"true\" height=\"300\" :data=\"platformData\" :scale=\"platformScale\">\r\n                    <v-tooltip></v-tooltip>\r\n                    <v-legend dataKey=\"type\"></v-legend>\r\n                    <v-coord type=\"theta\" radius=\"0.75\" innerRadius=\"0.6\"></v-coord>\r\n                    <v-pie position=\"percent\" color=\"type\" :label=\"labelConfig\"></v-pie>\r\n                  </v-chart>\r\n                </div>\r\n              </a-card>\r\n            </a-col>\r\n            <a-col :span=\"12\">\r\n              <a-card\r\n                :loading=\"loading\"\r\n                style=\"margin-bottom: 24px\"\r\n                :bordered=\"false\"\r\n                title=\"情感分布占比\"\r\n              >\r\n                <div style=\"height: 300px;\">\r\n                  <v-chart :forceFit=\"true\" height=\"300\" :data=\"sentimentData\" :scale=\"sentimentScale\">\r\n                    <v-tooltip></v-tooltip>\r\n                    <v-axis dataKey=\"time\"></v-axis>\r\n                    <v-axis dataKey=\"value\"></v-axis>\r\n                    <v-legend dataKey=\"type\"></v-legend>\r\n                    <v-area position=\"time*value\" color=\"type\" :opacity=\"0.6\"></v-area>\r\n                  </v-chart>\r\n                </div>\r\n              </a-card>\r\n            </a-col>\r\n          </a-row>\r\n\r\n          <a-card :loading=\"loading\" :bordered=\"false\">\r\n            <template slot=\"title\">\r\n              <i class=\"el-icon-hot\" style=\"color: #F5222D; margin-right: 8px;\"></i>\r\n              热门文章\r\n            </template>\r\n            <a-list>\r\n              <a-list-item :key=\"index\" v-for=\"(item, index) in hotArticles\">\r\n                <a-list-item-meta>\r\n                  <template slot=\"avatar\">\r\n                    <a-tag :color=\"item.tagColor\">{{ item.tag }}</a-tag>\r\n                  </template>\r\n                  <div slot=\"title\">\r\n                    <a :href=\"item.link\">{{ item.title }}</a>\r\n                  </div>\r\n                  <div slot=\"description\">\r\n                    <span>{{ item.source }}</span>\r\n                    <span style=\"float: right;\">{{ item.publishTime }}</span>\r\n                  </div>\r\n                </a-list-item-meta>\r\n              </a-list-item>\r\n            </a-list>\r\n          </a-card>\r\n        </a-col>\r\n        <a-col\r\n          style=\"padding: 0 12px\"\r\n          :xl=\"8\"\r\n          :lg=\"24\"\r\n          :md=\"24\"\r\n          :sm=\"24\"\r\n          :xs=\"24\"\r\n        >\r\n          <a-card\r\n            style=\"margin-bottom: 24px\"\r\n            :bordered=\"false\"\r\n          >\r\n            <template slot=\"title\">\r\n              <i class=\"el-icon-warning-outline\" style=\"color: #FA8C16; margin-right: 8px;\"></i>\r\n              预警方案设置\r\n            </template>\r\n            <div class=\"setting-buttons\">\r\n              <a-button type=\"primary\" icon=\"user\" style=\"margin-right: 8px;\">接收人设置</a-button>\r\n              <a-button type=\"primary\" icon=\"setting\" style=\"margin-right: 8px;\">预警设置</a-button>\r\n              <a-button type=\"primary\" icon=\"bell\" style=\"margin-right: 8px;\">关键词设置</a-button>\r\n              <a-button type=\"primary\" icon=\"search\" @click=\"goToSearch\">搜索测试</a-button>\r\n            </div>\r\n            <a-divider />\r\n            <div class=\"switch-buttons\">\r\n              <div style=\"margin-bottom: 16px; display: flex; justify-content: space-between; align-items: center;\">\r\n                <span>预警开关</span>\r\n                <a-switch checked></a-switch>\r\n              </div>\r\n              <div style=\"display: flex; justify-content: space-between;\">\r\n                <a-button>自动预警</a-button>\r\n                <a-button type=\"primary\">人工预警</a-button>\r\n              </div>\r\n            </div>\r\n          </a-card>\r\n          <a-card\r\n            style=\"margin-bottom: 24px\"\r\n            :bordered=\"false\"\r\n          >\r\n            <template slot=\"title\">\r\n              <i class=\"el-icon-document\" style=\"color: #52C41A; margin-right: 8px;\"></i>\r\n              报告模板\r\n            </template>\r\n            <a-list>\r\n              <a-list-item v-for=\"(item, index) in reportTemplates\" :key=\"index\">\r\n                <a-list-item-meta>\r\n                  <div slot=\"title\">{{ item.title }}</div>\r\n                  <div slot=\"description\">{{ item.createTime }}</div>\r\n                </a-list-item-meta>\r\n                <div>\r\n                  <a-button type=\"link\" icon=\"edit\"></a-button>\r\n                  <a-button type=\"link\" icon=\"copy\"></a-button>\r\n                  <a-button type=\"link\" icon=\"delete\"></a-button>\r\n                </div>\r\n              </a-list-item>\r\n            </a-list>\r\n          </a-card>\r\n          <a-card :loading=\"loading\" :bordered=\"false\">\r\n            <template slot=\"title\">\r\n              <i class=\"el-icon-warning\" style=\"color: #FF4D4F; margin-right: 8px;\"></i>\r\n              最新负面\r\n            </template>\r\n            <a-list>\r\n              <a-list-item v-for=\"(item, index) in negativeNews\" :key=\"index\">\r\n                <a-list-item-meta>\r\n                  <template slot=\"avatar\">\r\n                    <a-badge status=\"error\" />\r\n                  </template>\r\n                  <div slot=\"title\">\r\n                    <a :href=\"item.link\">{{ item.title }}</a>\r\n                  </div>\r\n                  <div slot=\"description\">{{ item.publishTime }}</div>\r\n                </a-list-item-meta>\r\n              </a-list-item>\r\n            </a-list>\r\n          </a-card>\r\n        </a-col>\r\n      </a-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Viser from 'viser-vue';\r\nimport {\r\n  Avatar,\r\n  Button,\r\n  Card,\r\n  Col,\r\n  List,\r\n  Row,\r\n  Statistic,\r\n  Tag,\r\n  Divider,\r\n  Switch,\r\n  Badge,\r\n} from \"ant-design-vue\";\r\nimport 'ant-design-vue/dist/antd.css';\r\nimport Vue from \"vue\";\r\n\r\nVue.use(Viser);\r\nVue.component(Avatar.name, Avatar);\r\nVue.component(Button.name, Button);\r\nVue.component(Card.name, Card);\r\nVue.component(Card.Grid.name, Card.Grid);\r\nVue.component(Card.Meta.name, Card.Meta);\r\nVue.component(Col.name, Col);\r\nVue.component(List.name, List);\r\nVue.component(List.Item.name, List.Item);\r\nVue.component(List.Item.Meta.name, List.Item.Meta);\r\nVue.component(Row.name, Row);\r\nVue.component(Statistic.name, Statistic);\r\nVue.component(Tag.name, Tag);\r\nVue.component(Divider.name, Divider);\r\nVue.component(Switch.name, Switch);\r\nVue.component(Badge.name, Badge);\r\n\r\nexport default {\r\n  name: \"DashBoard\",\r\n  methods: {\r\n    goToSearch() {\r\n      // 跳转到搜索结果页面，并传递测试搜索关键词\r\n      this.$router.push({\r\n        path: '/search-results',\r\n        query: {\r\n          q: '方太 厨电'\r\n        }\r\n      });\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      originalTopNav: undefined, // 存储原始的topNav状态\r\n      currentUser: {\r\n        avatar:\r\n          \"https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png\",\r\n        name: \"方太\",\r\n        userid: \"00000001\",\r\n        email: \"<EMAIL>\",\r\n        signature: \"舆情监控，实时把控\",\r\n        title: \"舆情分析师\",\r\n        group: \"舆情监控中心\",\r\n      },\r\n      // 统计数据\r\n      hotTopicsCount: 24,\r\n      negativeCount: 8,\r\n      totalMonitorCount: 1446,\r\n\r\n      // 24小时传播趋势数据\r\n      trendData: [\r\n        { time: \"00:00\", value: 300, type: \"总量\" },\r\n        { time: \"01:00\", value: 250, type: \"总量\" },\r\n        { time: \"02:00\", value: 200, type: \"总量\" },\r\n        { time: \"03:00\", value: 180, type: \"总量\" },\r\n        { time: \"04:00\", value: 150, type: \"总量\" },\r\n        { time: \"05:00\", value: 170, type: \"总量\" },\r\n        { time: \"06:00\", value: 220, type: \"总量\" },\r\n        { time: \"07:00\", value: 350, type: \"总量\" },\r\n        { time: \"08:00\", value: 500, type: \"总量\" },\r\n        { time: \"09:00\", value: 620, type: \"总量\" },\r\n        { time: \"10:00\", value: 550, type: \"总量\" },\r\n        { time: \"11:00\", value: 480, type: \"总量\" },\r\n        { time: \"12:00\", value: 400, type: \"总量\" },\r\n        { time: \"13:00\", value: 450, type: \"总量\" },\r\n        { time: \"14:00\", value: 500, type: \"总量\" },\r\n        { time: \"15:00\", value: 470, type: \"总量\" },\r\n        { time: \"16:00\", value: 460, type: \"总量\" },\r\n        { time: \"17:00\", value: 520, type: \"总量\" },\r\n        { time: \"18:00\", value: 580, type: \"总量\" },\r\n        { time: \"19:00\", value: 550, type: \"总量\" },\r\n        { time: \"20:00\", value: 500, type: \"总量\" },\r\n        { time: \"21:00\", value: 450, type: \"总量\" },\r\n        { time: \"22:00\", value: 400, type: \"总量\" },\r\n        { time: \"23:00\", value: 350, type: \"总量\" },\r\n\r\n        { time: \"00:00\", value: 100, type: \"微博\" },\r\n        { time: \"01:00\", value: 80, type: \"微博\" },\r\n        { time: \"02:00\", value: 60, type: \"微博\" },\r\n        { time: \"03:00\", value: 50, type: \"微博\" },\r\n        { time: \"04:00\", value: 40, type: \"微博\" },\r\n        { time: \"05:00\", value: 50, type: \"微博\" },\r\n        { time: \"06:00\", value: 70, type: \"微博\" },\r\n        { time: \"07:00\", value: 100, type: \"微博\" },\r\n        { time: \"08:00\", value: 150, type: \"微博\" },\r\n        { time: \"09:00\", value: 180, type: \"微博\" },\r\n        { time: \"10:00\", value: 160, type: \"微博\" },\r\n        { time: \"11:00\", value: 140, type: \"微博\" },\r\n        { time: \"12:00\", value: 120, type: \"微博\" },\r\n        { time: \"13:00\", value: 130, type: \"微博\" },\r\n        { time: \"14:00\", value: 150, type: \"微博\" },\r\n        { time: \"15:00\", value: 140, type: \"微博\" },\r\n        { time: \"16:00\", value: 130, type: \"微博\" },\r\n        { time: \"17:00\", value: 150, type: \"微博\" },\r\n        { time: \"18:00\", value: 170, type: \"微博\" },\r\n        { time: \"19:00\", value: 160, type: \"微博\" },\r\n        { time: \"20:00\", value: 150, type: \"微博\" },\r\n        { time: \"21:00\", value: 130, type: \"微博\" },\r\n        { time: \"22:00\", value: 120, type: \"微博\" },\r\n        { time: \"23:00\", value: 100, type: \"微博\" },\r\n\r\n        { time: \"00:00\", value: 80, type: \"视频\" },\r\n        { time: \"01:00\", value: 70, type: \"视频\" },\r\n        { time: \"02:00\", value: 60, type: \"视频\" },\r\n        { time: \"03:00\", value: 50, type: \"视频\" },\r\n        { time: \"04:00\", value: 40, type: \"视频\" },\r\n        { time: \"05:00\", value: 50, type: \"视频\" },\r\n        { time: \"06:00\", value: 60, type: \"视频\" },\r\n        { time: \"07:00\", value: 80, type: \"视频\" },\r\n        { time: \"08:00\", value: 100, type: \"视频\" },\r\n        { time: \"09:00\", value: 120, type: \"视频\" },\r\n        { time: \"10:00\", value: 110, type: \"视频\" },\r\n        { time: \"11:00\", value: 100, type: \"视频\" },\r\n        { time: \"12:00\", value: 90, type: \"视频\" },\r\n        { time: \"13:00\", value: 100, type: \"视频\" },\r\n        { time: \"14:00\", value: 110, type: \"视频\" },\r\n        { time: \"15:00\", value: 100, type: \"视频\" },\r\n        { time: \"16:00\", value: 90, type: \"视频\" },\r\n        { time: \"17:00\", value: 100, type: \"视频\" },\r\n        { time: \"18:00\", value: 110, type: \"视频\" },\r\n        { time: \"19:00\", value: 100, type: \"视频\" },\r\n        { time: \"20:00\", value: 90, type: \"视频\" },\r\n        { time: \"21:00\", value: 80, type: \"视频\" },\r\n        { time: \"22:00\", value: 70, type: \"视频\" },\r\n        { time: \"23:00\", value: 60, type: \"视频\" },\r\n      ],\r\n      trendScale: [\r\n        {\r\n          dataKey: 'value',\r\n          min: 0,\r\n        },\r\n      ],\r\n\r\n      // 平台来源占比数据\r\n      platformData: [\r\n        { type: '微博', percent: 31.2 },\r\n        { type: '视频', percent: 17.9 },\r\n        { type: '头条号', percent: 15.3 },\r\n        { type: 'APP', percent: 12.7 },\r\n        { type: '微信', percent: 9.8 },\r\n        { type: '其他', percent: 13.1 },\r\n      ],\r\n      platformScale: [\r\n        {\r\n          dataKey: 'percent',\r\n          min: 0,\r\n          formatter: '.0%',\r\n        },\r\n      ],\r\n      labelConfig: {\r\n        offset: -20,\r\n        textStyle: {\r\n          fill: '#000',\r\n          fontSize: 12,\r\n          fontWeight: 'bold',\r\n        },\r\n        formatter: (text, item) => {\r\n          return `${item.point.type}: ${item.point.percent}%`;\r\n        },\r\n      },\r\n\r\n      // 情感分布占比数据\r\n      sentimentData: [\r\n        { time: '00:00', value: 300, type: '中性' },\r\n        { time: '02:00', value: 280, type: '中性' },\r\n        { time: '04:00', value: 250, type: '中性' },\r\n        { time: '06:00', value: 260, type: '中性' },\r\n        { time: '08:00', value: 280, type: '中性' },\r\n        { time: '10:00', value: 300, type: '中性' },\r\n        { time: '12:00', value: 280, type: '中性' },\r\n        { time: '14:00', value: 250, type: '中性' },\r\n        { time: '16:00', value: 260, type: '中性' },\r\n        { time: '18:00', value: 280, type: '中性' },\r\n        { time: '20:00', value: 300, type: '中性' },\r\n        { time: '22:00', value: 280, type: '中性' },\r\n\r\n        { time: '00:00', value: 100, type: '正面' },\r\n        { time: '02:00', value: 120, type: '正面' },\r\n        { time: '04:00', value: 140, type: '正面' },\r\n        { time: '06:00', value: 130, type: '正面' },\r\n        { time: '08:00', value: 120, type: '正面' },\r\n        { time: '10:00', value: 100, type: '正面' },\r\n        { time: '12:00', value: 120, type: '正面' },\r\n        { time: '14:00', value: 140, type: '正面' },\r\n        { time: '16:00', value: 130, type: '正面' },\r\n        { time: '18:00', value: 120, type: '正面' },\r\n        { time: '20:00', value: 100, type: '正面' },\r\n        { time: '22:00', value: 120, type: '正面' },\r\n\r\n        { time: '00:00', value: 50, type: '负面' },\r\n        { time: '02:00', value: 60, type: '负面' },\r\n        { time: '04:00', value: 70, type: '负面' },\r\n        { time: '06:00', value: 65, type: '负面' },\r\n        { time: '08:00', value: 60, type: '负面' },\r\n        { time: '10:00', value: 50, type: '负面' },\r\n        { time: '12:00', value: 60, type: '负面' },\r\n        { time: '14:00', value: 70, type: '负面' },\r\n        { time: '16:00', value: 65, type: '负面' },\r\n        { time: '18:00', value: 60, type: '负面' },\r\n        { time: '20:00', value: 50, type: '负面' },\r\n        { time: '22:00', value: 60, type: '负面' },\r\n      ],\r\n      sentimentScale: [\r\n        {\r\n          dataKey: 'value',\r\n          min: 0,\r\n        },\r\n      ],\r\n\r\n      // 热门文章列表\r\n      hotArticles: [\r\n        {\r\n          id: 1,\r\n          title: '方太热水器(Fotile)官方旗舰店 方太热水器(Fotile)-111 方太热水器(Fotile) 400.6808.655...',\r\n          tag: '热门',\r\n          tagColor: '#f50',\r\n          source: '电商平台',\r\n          publishTime: '2025-04-29 20:24:00',\r\n          link: 'https://example.com/article/1',\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '近地铺30年地暖口碑，靠老字二全新设计住人。#成都二手房 #地暖电源',\r\n          tag: '热门',\r\n          tagColor: '#f50',\r\n          source: '视频平台',\r\n          publishTime: '2025-04-29 15:15:16',\r\n          link: 'https://example.com/article/2',\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '现在这个社会要靠脑袋吃饭，可能是一辈子的事，开玩笑 我一个实体人 更应该有品质。',\r\n          tag: '热门',\r\n          tagColor: '#f50',\r\n          source: '视频平台',\r\n          publishTime: '2025-04-29 14:29:09',\r\n          link: 'https://example.com/article/3',\r\n        },\r\n        {\r\n          id: 4,\r\n          title: '（无标题）S2 V b ## b ### 限定2人方太全球首创一代燃气灶全球首发 03 ALOQAV 0 13 图A-5986 59...',\r\n          tag: '热门',\r\n          tagColor: '#f50',\r\n          source: '视频平台',\r\n          publishTime: '2025-04-29 14:19:23',\r\n          link: 'https://example.com/article/4',\r\n        },\r\n        {\r\n          id: 5,\r\n          title: '你于过这事吗? 来看合了#方太官网 #南粤 #新桥口大厨的字写得 #同城姐妹的朋友看过来 #电影',\r\n          tag: '热门',\r\n          tagColor: '#f50',\r\n          source: '视频平台',\r\n          publishTime: '2025-04-29 12:48:04',\r\n          link: 'https://example.com/article/5',\r\n        },\r\n      ],\r\n\r\n      // 报告模板列表\r\n      reportTemplates: [\r\n        {\r\n          id: 1,\r\n          title: '舆情-周报表',\r\n          createTime: '2019-11-16 18:02:00',\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '舆情-月报表',\r\n          createTime: '2019-11-18 18:06:52',\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '舆情-季度表',\r\n          createTime: '2021-09-12 10:15:00',\r\n        },\r\n      ],\r\n\r\n      // 最新负面信息\r\n      negativeNews: [\r\n        {\r\n          id: 1,\r\n          title: '方太热水器出现质量问题，多名用户投诉无人处理',\r\n          publishTime: '2025-04-29 19:03:00',\r\n          link: 'https://example.com/negative/1',\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '方太厨电安装电话无人接听，客户投诉服务态度差',\r\n          publishTime: '2025-04-29 18:22:43',\r\n          link: 'https://example.com/negative/2',\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '航空女友偷拍日记被礼，网友后，我在床上看到了电视一幕的她',\r\n          publishTime: '2025-04-29 17:48:45',\r\n          link: 'https://example.com/negative/3',\r\n        },\r\n        {\r\n          id: 4,\r\n          title: '某品牌大型抽油烟机噪音问题引发用户不满',\r\n          publishTime: '2025-04-29 15:15:16',\r\n          link: 'https://example.com/negative/4',\r\n        },\r\n        {\r\n          id: 5,\r\n          title: '家电售后服务调查：多品牌服务质量参差不齐',\r\n          publishTime: '2025-04-29 12:26:04',\r\n          link: 'https://example.com/negative/5',\r\n        },\r\n      ],\r\n\r\n      loading: true\r\n    };\r\n  },\r\n\r\n  mounted() {\r\n    // 隐藏顶部导航栏\r\n    this.originalTopNav = this.$store.state.settings.topNav\r\n    this.$store.dispatch('settings/changeSetting', {\r\n      key: 'topNav',\r\n      value: false\r\n    })\r\n\r\n    setTimeout(() => {\r\n      this.loading = false;\r\n    }, 1000);\r\n  },\r\n  beforeDestroy() {\r\n    // 恢复顶部导航栏设置\r\n    if (this.originalTopNav !== undefined) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'topNav',\r\n        value: this.originalTopNav\r\n      })\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n  <style lang=\"less\" scoped>\r\n@import \"./Workplace.less\";\r\n\r\n\r\n\r\n.page-header-content {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.setting-buttons {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.switch-buttons {\r\n  margin-top: 16px;\r\n}\r\n\r\n.ant-list-item {\r\n  transition: all 0.3s;\r\n\r\n  &:hover {\r\n    background-color: rgba(24, 144, 255, 0.05);\r\n  }\r\n}\r\n\r\n.ant-card {\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  transition: all 0.3s;\r\n\r\n  &:hover {\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  }\r\n}\r\n\r\n.ant-tag {\r\n  margin-right: 0;\r\n}\r\n\r\n.ant-list-item-meta-title {\r\n  margin-bottom: 4px;\r\n\r\n  a {\r\n    color: rgba(0, 0, 0, 0.85);\r\n\r\n    &:hover {\r\n      color: #1890ff;\r\n    }\r\n  }\r\n}\r\n\r\n.ant-list-item-meta-description {\r\n  color: rgba(0, 0, 0, 0.45);\r\n}\r\n\r\n.mobile {\r\n  .more-info {\r\n    border: 0;\r\n    padding-top: 16px;\r\n    margin: 16px 0 16px;\r\n  }\r\n\r\n  .headerContent .title .welcome-text {\r\n    display: none;\r\n  }\r\n}\r\n</style>\r\n"]}]}