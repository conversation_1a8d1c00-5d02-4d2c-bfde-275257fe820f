{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\table.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\table.js", "mtime": 1749104422704}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_block", "_interopRequireDefault", "require", "_container", "TableCell", "exports", "_Block", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "key", "value", "cellOffset", "parent", "children", "indexOf", "format", "name", "blotName", "domNode", "setAttribute", "_superPropGet2", "row", "rowOffset", "table", "create", "node", "tableId", "formats", "hasAttribute", "getAttribute", "undefined", "Block", "_defineProperty2", "TableRow", "_Container", "checkMerge", "next", "head", "thisHead", "thisTail", "tail", "nextHead", "nextTail", "optimize", "context", "_this", "for<PERSON>ach", "child", "childFormats", "nextFormats", "splitAfter", "prev", "Container", "TableBody", "_Container2", "TableContainer", "_Container3", "balanceCells", "_this2", "rows", "descendants", "maxColumns", "reduce", "max", "Math", "length", "Array", "fill", "blot", "scroll", "append<PERSON><PERSON><PERSON>", "cells", "column", "map", "at", "deleteColumn", "index", "_this$descendant", "descendant", "_this$descendant2", "_slicedToArray2", "body", "cell", "remove", "insertColumn", "_this3", "_this$descendant3", "_this$descendant4", "ref", "insertBefore", "insertRow", "_this4", "_this$descendant5", "_this$descendant6", "id", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requiredC<PERSON><PERSON>", "random", "toString", "slice", "concat"], "sources": ["../../src/formats/table.ts"], "sourcesContent": ["import type { LinkedList } from 'parchment';\nimport Block from '../blots/block.js';\nimport Container from '../blots/container.js';\n\nclass TableCell extends Block {\n  static blotName = 'table';\n  static tagName = 'TD';\n\n  static create(value: string) {\n    const node = super.create() as HTMLElement;\n    if (value) {\n      node.setAttribute('data-row', value);\n    } else {\n      node.setAttribute('data-row', tableId());\n    }\n    return node;\n  }\n\n  static formats(domNode: HTMLElement) {\n    if (domNode.hasAttribute('data-row')) {\n      return domNode.getAttribute('data-row');\n    }\n    return undefined;\n  }\n\n  next: this | null;\n\n  cellOffset() {\n    if (this.parent) {\n      return this.parent.children.indexOf(this);\n    }\n    return -1;\n  }\n\n  format(name: string, value: string) {\n    if (name === TableCell.blotName && value) {\n      this.domNode.setAttribute('data-row', value);\n    } else {\n      super.format(name, value);\n    }\n  }\n\n  row(): TableRow {\n    return this.parent as TableRow;\n  }\n\n  rowOffset() {\n    if (this.row()) {\n      return this.row().rowOffset();\n    }\n    return -1;\n  }\n\n  table() {\n    return this.row() && this.row().table();\n  }\n}\n\nclass TableRow extends Container {\n  static blotName = 'table-row';\n  static tagName = 'TR';\n\n  children: LinkedList<TableCell>;\n  next: this | null;\n\n  checkMerge() {\n    // @ts-expect-error\n    if (super.checkMerge() && this.next.children.head != null) {\n      // @ts-expect-error\n      const thisHead = this.children.head.formats();\n      // @ts-expect-error\n      const thisTail = this.children.tail.formats();\n      // @ts-expect-error\n      const nextHead = this.next.children.head.formats();\n      // @ts-expect-error\n      const nextTail = this.next.children.tail.formats();\n      return (\n        thisHead.table === thisTail.table &&\n        thisHead.table === nextHead.table &&\n        thisHead.table === nextTail.table\n      );\n    }\n    return false;\n  }\n\n  optimize(context: { [key: string]: any }) {\n    super.optimize(context);\n    this.children.forEach((child) => {\n      if (child.next == null) return;\n      const childFormats = child.formats();\n      const nextFormats = child.next.formats();\n      if (childFormats.table !== nextFormats.table) {\n        const next = this.splitAfter(child);\n        if (next) {\n          // @ts-expect-error TODO: parameters of optimize() should be a optional\n          next.optimize();\n        }\n        // We might be able to merge with prev now\n        if (this.prev) {\n          // @ts-expect-error TODO: parameters of optimize() should be a optional\n          this.prev.optimize();\n        }\n      }\n    });\n  }\n\n  rowOffset() {\n    if (this.parent) {\n      return this.parent.children.indexOf(this);\n    }\n    return -1;\n  }\n\n  table() {\n    return this.parent && this.parent.parent;\n  }\n}\n\nclass TableBody extends Container {\n  static blotName = 'table-body';\n  static tagName = 'TBODY';\n\n  children: LinkedList<TableRow>;\n}\n\nclass TableContainer extends Container {\n  static blotName = 'table-container';\n  static tagName = 'TABLE';\n\n  children: LinkedList<TableBody>;\n\n  balanceCells() {\n    const rows = this.descendants(TableRow);\n    const maxColumns = rows.reduce((max, row) => {\n      return Math.max(row.children.length, max);\n    }, 0);\n    rows.forEach((row) => {\n      new Array(maxColumns - row.children.length).fill(0).forEach(() => {\n        let value;\n        if (row.children.head != null) {\n          value = TableCell.formats(row.children.head.domNode);\n        }\n        const blot = this.scroll.create(TableCell.blotName, value);\n        row.appendChild(blot);\n        // @ts-expect-error TODO: parameters of optimize() should be a optional\n        blot.optimize(); // Add break blot\n      });\n    });\n  }\n\n  cells(column: number) {\n    return this.rows().map((row) => row.children.at(column));\n  }\n\n  deleteColumn(index: number) {\n    // @ts-expect-error\n    const [body] = this.descendant(TableBody) as TableBody[];\n    if (body == null || body.children.head == null) return;\n    body.children.forEach((row) => {\n      const cell = row.children.at(index);\n      if (cell != null) {\n        cell.remove();\n      }\n    });\n  }\n\n  insertColumn(index: number) {\n    // @ts-expect-error\n    const [body] = this.descendant(TableBody) as TableBody[];\n    if (body == null || body.children.head == null) return;\n    body.children.forEach((row) => {\n      const ref = row.children.at(index);\n      // @ts-expect-error\n      const value = TableCell.formats(row.children.head.domNode);\n      const cell = this.scroll.create(TableCell.blotName, value);\n      row.insertBefore(cell, ref);\n    });\n  }\n\n  insertRow(index: number) {\n    // @ts-expect-error\n    const [body] = this.descendant(TableBody) as TableBody[];\n    if (body == null || body.children.head == null) return;\n    const id = tableId();\n    const row = this.scroll.create(TableRow.blotName) as TableRow;\n    body.children.head.children.forEach(() => {\n      const cell = this.scroll.create(TableCell.blotName, id);\n      row.appendChild(cell);\n    });\n    const ref = body.children.at(index);\n    body.insertBefore(row, ref);\n  }\n\n  rows() {\n    const body = this.children.head;\n    if (body == null) return [];\n    return body.children.map((row) => row);\n  }\n}\n\nTableContainer.allowedChildren = [TableBody];\nTableBody.requiredContainer = TableContainer;\n\nTableBody.allowedChildren = [TableRow];\nTableRow.requiredContainer = TableBody;\n\nTableRow.allowedChildren = [TableCell];\nTableCell.requiredContainer = TableRow;\n\nfunction tableId() {\n  const id = Math.random().toString(36).slice(2, 6);\n  return `row-${id}`;\n}\n\nexport { TableCell, TableRow, TableBody, TableContainer, tableId };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAF,sBAAA,CAAAC,OAAA;AAA6C,IAEvCE,SAAS,GAAAC,OAAA,CAAAD,SAAA,0BAAAE,MAAA;EAAA,SAAAF,UAAA;IAAA,IAAAG,gBAAA,CAAAC,OAAA,QAAAJ,SAAA;IAAA,WAAAK,WAAA,CAAAD,OAAA,QAAAJ,SAAA,EAAAM,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAJ,SAAA,EAAAE,MAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAJ,SAAA;IAAAS,GAAA;IAAAC,KAAA,EAuBb,SAAAC,UAAUA,CAAA,EAAG;MACX,IAAI,IAAI,CAACC,MAAM,EAAE;QACf,OAAO,IAAI,CAACA,MAAM,CAACC,QAAQ,CAACC,OAAO,CAAC,IAAI,CAAC;MAC3C;MACA,OAAO,CAAC,CAAC;IACX;EAAA;IAAAL,GAAA;IAAAC,KAAA,EAEA,SAAAK,MAAMA,CAACC,IAAY,EAAEN,KAAa,EAAE;MAClC,IAAIM,IAAI,KAAKhB,SAAS,CAACiB,QAAQ,IAAIP,KAAK,EAAE;QACxC,IAAI,CAACQ,OAAO,CAACC,YAAY,CAAC,UAAU,EAAET,KAAK,CAAC;MAC9C,CAAC,MAAM;QACL,IAAAU,cAAA,CAAAhB,OAAA,EAAAJ,SAAA,sBAAagB,IAAI,EAAEN,KAAK;MAC1B;IACF;EAAA;IAAAD,GAAA;IAAAC,KAAA,EAEA,SAAAW,GAAGA,CAAA,EAAa;MACd,OAAO,IAAI,CAACT,MAAM;IACpB;EAAA;IAAAH,GAAA;IAAAC,KAAA,EAEA,SAAAY,SAASA,CAAA,EAAG;MACV,IAAI,IAAI,CAACD,GAAG,CAAC,CAAC,EAAE;QACd,OAAO,IAAI,CAACA,GAAG,CAAC,CAAC,CAACC,SAAS,CAAC,CAAC;MAC/B;MACA,OAAO,CAAC,CAAC;IACX;EAAA;IAAAb,GAAA;IAAAC,KAAA,EAEA,SAAAa,KAAKA,CAAA,EAAG;MACN,OAAO,IAAI,CAACF,GAAG,CAAC,CAAC,IAAI,IAAI,CAACA,GAAG,CAAC,CAAC,CAACE,KAAK,CAAC,CAAC;IACzC;EAAA;IAAAd,GAAA;IAAAC,KAAA,EA/CA,SAAOc,MAAMA,CAACd,KAAa,EAAE;MAC3B,IAAMe,IAAI,OAAAL,cAAA,CAAAhB,OAAA,EAAAJ,SAAA,wBAAgC;MAC1C,IAAIU,KAAK,EAAE;QACTe,IAAI,CAACN,YAAY,CAAC,UAAU,EAAET,KAAK,CAAC;MACtC,CAAC,MAAM;QACLe,IAAI,CAACN,YAAY,CAAC,UAAU,EAAEO,OAAO,CAAC,CAAC,CAAC;MAC1C;MACA,OAAOD,IAAI;IACb;EAAA;IAAAhB,GAAA;IAAAC,KAAA,EAEA,SAAOiB,OAAOA,CAACT,OAAoB,EAAE;MACnC,IAAIA,OAAO,CAACU,YAAY,CAAC,UAAU,CAAC,EAAE;QACpC,OAAOV,OAAO,CAACW,YAAY,CAAC,UAAU,CAAC;MACzC;MACA,OAAOC,SAAS;IAClB;EAAA;AAAA,EAnBsBC,cAAK;AAAA,IAAAC,gBAAA,CAAA5B,OAAA,EAAvBJ,SAAS,cACK,OAAO;AAAA,IAAAgC,gBAAA,CAAA5B,OAAA,EADrBJ,SAAS,aAEI,IAAI;AAAA,IAoDjBiC,QAAQ,GAAAhC,OAAA,CAAAgC,QAAA,0BAAAC,UAAA;EAAA,SAAAD,SAAA;IAAA,IAAA9B,gBAAA,CAAAC,OAAA,QAAA6B,QAAA;IAAA,WAAA5B,WAAA,CAAAD,OAAA,QAAA6B,QAAA,EAAA3B,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAA6B,QAAA,EAAAC,UAAA;EAAA,WAAA1B,aAAA,CAAAJ,OAAA,EAAA6B,QAAA;IAAAxB,GAAA;IAAAC,KAAA,EAOZ,SAAAyB,UAAUA,CAAA,EAAG;MACX;MACA,IAAI,IAAAf,cAAA,CAAAhB,OAAA,EAAA6B,QAAA,gCAAsB,IAAI,CAACG,IAAI,CAACvB,QAAQ,CAACwB,IAAI,IAAI,IAAI,EAAE;QACzD;QACA,IAAMC,QAAQ,GAAG,IAAI,CAACzB,QAAQ,CAACwB,IAAI,CAACV,OAAO,CAAC,CAAC;QAC7C;QACA,IAAMY,QAAQ,GAAG,IAAI,CAAC1B,QAAQ,CAAC2B,IAAI,CAACb,OAAO,CAAC,CAAC;QAC7C;QACA,IAAMc,QAAQ,GAAG,IAAI,CAACL,IAAI,CAACvB,QAAQ,CAACwB,IAAI,CAACV,OAAO,CAAC,CAAC;QAClD;QACA,IAAMe,QAAQ,GAAG,IAAI,CAACN,IAAI,CAACvB,QAAQ,CAAC2B,IAAI,CAACb,OAAO,CAAC,CAAC;QAClD,OACEW,QAAQ,CAACf,KAAK,KAAKgB,QAAQ,CAAChB,KAAK,IACjCe,QAAQ,CAACf,KAAK,KAAKkB,QAAQ,CAAClB,KAAK,IACjCe,QAAQ,CAACf,KAAK,KAAKmB,QAAQ,CAACnB,KAAK;MAErC;MACA,OAAO,KAAK;IACd;EAAA;IAAAd,GAAA;IAAAC,KAAA,EAEA,SAAAiC,QAAQA,CAACC,OAA+B,EAAE;MAAA,IAAAC,KAAA;MACxC,IAAAzB,cAAA,CAAAhB,OAAA,EAAA6B,QAAA,wBAAeW,OAAO;MACtB,IAAI,CAAC/B,QAAQ,CAACiC,OAAO,CAAE,UAAAC,KAAK,EAAK;QAC/B,IAAIA,KAAK,CAACX,IAAI,IAAI,IAAI,EAAE;QACxB,IAAMY,YAAY,GAAGD,KAAK,CAACpB,OAAO,CAAC,CAAC;QACpC,IAAMsB,WAAW,GAAGF,KAAK,CAACX,IAAI,CAACT,OAAO,CAAC,CAAC;QACxC,IAAIqB,YAAY,CAACzB,KAAK,KAAK0B,WAAW,CAAC1B,KAAK,EAAE;UAC5C,IAAMa,IAAI,GAAGS,KAAI,CAACK,UAAU,CAACH,KAAK,CAAC;UACnC,IAAIX,IAAI,EAAE;YACR;YACAA,IAAI,CAACO,QAAQ,CAAC,CAAC;UACjB;UACA;UACA,IAAIE,KAAI,CAACM,IAAI,EAAE;YACb;YACAN,KAAI,CAACM,IAAI,CAACR,QAAQ,CAAC,CAAC;UACtB;QACF;MACF,CAAC,CAAC;IACJ;EAAA;IAAAlC,GAAA;IAAAC,KAAA,EAEA,SAAAY,SAASA,CAAA,EAAG;MACV,IAAI,IAAI,CAACV,MAAM,EAAE;QACf,OAAO,IAAI,CAACA,MAAM,CAACC,QAAQ,CAACC,OAAO,CAAC,IAAI,CAAC;MAC3C;MACA,OAAO,CAAC,CAAC;IACX;EAAA;IAAAL,GAAA;IAAAC,KAAA,EAEA,SAAAa,KAAKA,CAAA,EAAG;MACN,OAAO,IAAI,CAACX,MAAM,IAAI,IAAI,CAACA,MAAM,CAACA,MAAM;IAC1C;EAAA;AAAA,EAzDqBwC,kBAAS;AAAA,IAAApB,gBAAA,CAAA5B,OAAA,EAA1B6B,QAAQ,cACM,WAAW;AAAA,IAAAD,gBAAA,CAAA5B,OAAA,EADzB6B,QAAQ,aAEK,IAAI;AAAA,IA0DjBoB,SAAS,GAAApD,OAAA,CAAAoD,SAAA,0BAAAC,WAAA;EAAA,SAAAD,UAAA;IAAA,IAAAlD,gBAAA,CAAAC,OAAA,QAAAiD,SAAA;IAAA,WAAAhD,WAAA,CAAAD,OAAA,QAAAiD,SAAA,EAAA/C,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAiD,SAAA,EAAAC,WAAA;EAAA,WAAA9C,aAAA,CAAAJ,OAAA,EAAAiD,SAAA;AAAA,EAASD,kBAAS;AAAA,IAAApB,gBAAA,CAAA5B,OAAA,EAA3BiD,SAAS,cACK,YAAY;AAAA,IAAArB,gBAAA,CAAA5B,OAAA,EAD1BiD,SAAS,aAEI,OAAO;AAAA,IAKpBE,cAAc,GAAAtD,OAAA,CAAAsD,cAAA,0BAAAC,WAAA;EAAA,SAAAD,eAAA;IAAA,IAAApD,gBAAA,CAAAC,OAAA,QAAAmD,cAAA;IAAA,WAAAlD,WAAA,CAAAD,OAAA,QAAAmD,cAAA,EAAAjD,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAmD,cAAA,EAAAC,WAAA;EAAA,WAAAhD,aAAA,CAAAJ,OAAA,EAAAmD,cAAA;IAAA9C,GAAA;IAAAC,KAAA,EAMlB,SAAA+C,YAAYA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACb,IAAMC,IAAI,GAAG,IAAI,CAACC,WAAW,CAAC3B,QAAQ,CAAC;MACvC,IAAM4B,UAAU,GAAGF,IAAI,CAACG,MAAM,CAAC,UAACC,GAAG,EAAE1C,GAAG,EAAK;QAC3C,OAAO2C,IAAI,CAACD,GAAG,CAAC1C,GAAG,CAACR,QAAQ,CAACoD,MAAM,EAAEF,GAAG,CAAC;MAC3C,CAAC,EAAE,CAAC,CAAC;MACLJ,IAAI,CAACb,OAAO,CAAE,UAAAzB,GAAG,EAAK;QACpB,IAAI6C,KAAK,CAACL,UAAU,GAAGxC,GAAG,CAACR,QAAQ,CAACoD,MAAM,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC,CAACrB,OAAO,CAAC,YAAM;UAChE,IAAIpC,KAAK;UACT,IAAIW,GAAG,CAACR,QAAQ,CAACwB,IAAI,IAAI,IAAI,EAAE;YAC7B3B,KAAK,GAAGV,SAAS,CAAC2B,OAAO,CAACN,GAAG,CAACR,QAAQ,CAACwB,IAAI,CAACnB,OAAO,CAAC;UACtD;UACA,IAAMkD,IAAI,GAAGV,MAAI,CAACW,MAAM,CAAC7C,MAAM,CAACxB,SAAS,CAACiB,QAAQ,EAAEP,KAAK,CAAC;UAC1DW,GAAG,CAACiD,WAAW,CAACF,IAAI,CAAC;UACrB;UACAA,IAAI,CAACzB,QAAQ,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EAAA;IAAAlC,GAAA;IAAAC,KAAA,EAEA,SAAA6D,KAAKA,CAACC,MAAc,EAAE;MACpB,OAAO,IAAI,CAACb,IAAI,CAAC,CAAC,CAACc,GAAG,CAAE,UAAApD,GAAG;QAAA,OAAKA,GAAG,CAACR,QAAQ,CAAC6D,EAAE,CAACF,MAAM,CAAC;MAAA,EAAC;IAC1D;EAAA;IAAA/D,GAAA;IAAAC,KAAA,EAEA,SAAAiE,YAAYA,CAACC,KAAa,EAAE;MAC1B;MACA,IAAAC,gBAAA,GAAe,IAAI,CAACC,UAAU,CAACzB,SAAS,CAAgB;QAAA0B,iBAAA,OAAAC,eAAA,CAAA5E,OAAA,EAAAyE,gBAAA;QAAjDI,IAAI,GAAAF,iBAAA;MACX,IAAIE,IAAI,IAAI,IAAI,IAAIA,IAAI,CAACpE,QAAQ,CAACwB,IAAI,IAAI,IAAI,EAAE;MAChD4C,IAAI,CAACpE,QAAQ,CAACiC,OAAO,CAAE,UAAAzB,GAAG,EAAK;QAC7B,IAAM6D,IAAI,GAAG7D,GAAG,CAACR,QAAQ,CAAC6D,EAAE,CAACE,KAAK,CAAC;QACnC,IAAIM,IAAI,IAAI,IAAI,EAAE;UAChBA,IAAI,CAACC,MAAM,CAAC,CAAC;QACf;MACF,CAAC,CAAC;IACJ;EAAA;IAAA1E,GAAA;IAAAC,KAAA,EAEA,SAAA0E,YAAYA,CAACR,KAAa,EAAE;MAAA,IAAAS,MAAA;MAC1B;MACA,IAAAC,iBAAA,GAAe,IAAI,CAACR,UAAU,CAACzB,SAAS,CAAgB;QAAAkC,iBAAA,OAAAP,eAAA,CAAA5E,OAAA,EAAAkF,iBAAA;QAAjDL,IAAI,GAAAM,iBAAA;MACX,IAAIN,IAAI,IAAI,IAAI,IAAIA,IAAI,CAACpE,QAAQ,CAACwB,IAAI,IAAI,IAAI,EAAE;MAChD4C,IAAI,CAACpE,QAAQ,CAACiC,OAAO,CAAE,UAAAzB,GAAG,EAAK;QAC7B,IAAMmE,GAAG,GAAGnE,GAAG,CAACR,QAAQ,CAAC6D,EAAE,CAACE,KAAK,CAAC;QAClC;QACA,IAAMlE,KAAK,GAAGV,SAAS,CAAC2B,OAAO,CAACN,GAAG,CAACR,QAAQ,CAACwB,IAAI,CAACnB,OAAO,CAAC;QAC1D,IAAMgE,IAAI,GAAGG,MAAI,CAAChB,MAAM,CAAC7C,MAAM,CAACxB,SAAS,CAACiB,QAAQ,EAAEP,KAAK,CAAC;QAC1DW,GAAG,CAACoE,YAAY,CAACP,IAAI,EAAEM,GAAG,CAAC;MAC7B,CAAC,CAAC;IACJ;EAAA;IAAA/E,GAAA;IAAAC,KAAA,EAEA,SAAAgF,SAASA,CAACd,KAAa,EAAE;MAAA,IAAAe,MAAA;MACvB;MACA,IAAAC,iBAAA,GAAe,IAAI,CAACd,UAAU,CAACzB,SAAS,CAAgB;QAAAwC,iBAAA,OAAAb,eAAA,CAAA5E,OAAA,EAAAwF,iBAAA;QAAjDX,IAAI,GAAAY,iBAAA;MACX,IAAIZ,IAAI,IAAI,IAAI,IAAIA,IAAI,CAACpE,QAAQ,CAACwB,IAAI,IAAI,IAAI,EAAE;MAChD,IAAMyD,EAAE,GAAGpE,OAAO,CAAC,CAAC;MACpB,IAAML,GAAG,GAAG,IAAI,CAACgD,MAAM,CAAC7C,MAAM,CAACS,QAAQ,CAAChB,QAAQ,CAAa;MAC7DgE,IAAI,CAACpE,QAAQ,CAACwB,IAAI,CAACxB,QAAQ,CAACiC,OAAO,CAAC,YAAM;QACxC,IAAMoC,IAAI,GAAGS,MAAI,CAACtB,MAAM,CAAC7C,MAAM,CAACxB,SAAS,CAACiB,QAAQ,EAAE6E,EAAE,CAAC;QACvDzE,GAAG,CAACiD,WAAW,CAACY,IAAI,CAAC;MACvB,CAAC,CAAC;MACF,IAAMM,GAAG,GAAGP,IAAI,CAACpE,QAAQ,CAAC6D,EAAE,CAACE,KAAK,CAAC;MACnCK,IAAI,CAACQ,YAAY,CAACpE,GAAG,EAAEmE,GAAG,CAAC;IAC7B;EAAA;IAAA/E,GAAA;IAAAC,KAAA,EAEA,SAAAiD,IAAIA,CAAA,EAAG;MACL,IAAMsB,IAAI,GAAG,IAAI,CAACpE,QAAQ,CAACwB,IAAI;MAC/B,IAAI4C,IAAI,IAAI,IAAI,EAAE,OAAO,EAAE;MAC3B,OAAOA,IAAI,CAACpE,QAAQ,CAAC4D,GAAG,CAAE,UAAApD,GAAG;QAAA,OAAKA,GAAG;MAAA,EAAC;IACxC;EAAA;AAAA,EAxE2B+B,kBAAS;AAAA,IAAApB,gBAAA,CAAA5B,OAAA,EAAhCmD,cAAc,cACA,iBAAiB;AAAA,IAAAvB,gBAAA,CAAA5B,OAAA,EAD/BmD,cAAc,aAED,OAAO;AAyE1BA,cAAc,CAACwC,eAAe,GAAG,CAAC1C,SAAS,CAAC;AAC5CA,SAAS,CAAC2C,iBAAiB,GAAGzC,cAAc;AAE5CF,SAAS,CAAC0C,eAAe,GAAG,CAAC9D,QAAQ,CAAC;AACtCA,QAAQ,CAAC+D,iBAAiB,GAAG3C,SAAS;AAEtCpB,QAAQ,CAAC8D,eAAe,GAAG,CAAC/F,SAAS,CAAC;AACtCA,SAAS,CAACgG,iBAAiB,GAAG/D,QAAQ;AAEtC,SAASP,OAAOA,CAAA,EAAG;EACjB,IAAMoE,EAAE,GAAG9B,IAAI,CAACiC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACjD,cAAAC,MAAA,CAAcN,EAAG;AACnB", "ignoreList": []}]}