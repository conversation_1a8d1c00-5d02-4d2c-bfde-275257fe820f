{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\DictTag\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\DictTag\\index.vue", "mtime": 1749104047618}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/DictTag", "sourcesContent": ["<template>\r\n  <div>\r\n    <template v-for=\"(item, index) in options\">\r\n      <template v-if=\"values.includes(item.value)\">\r\n        <span\r\n          v-if=\"(item.raw.listClass == 'default' || item.raw.listClass == '') && (item.raw.cssClass == '' || item.raw.cssClass == null)\"\r\n          :key=\"item.value\"\r\n          :index=\"index\"\r\n          :class=\"item.raw.cssClass\"\r\n          >{{ item.label + ' ' }}</span\r\n        >\r\n        <el-tag\r\n          v-else\r\n          :disable-transitions=\"true\"\r\n          :key=\"item.value\"\r\n          :index=\"index\"\r\n          :type=\"item.raw.listClass == 'primary' ? '' : item.raw.listClass\"\r\n          :class=\"item.raw.cssClass\"\r\n        >\r\n          {{ item.label + ' ' }}\r\n        </el-tag>\r\n      </template>\r\n    </template>\r\n    <template v-if=\"unmatch && showValue\">\r\n      {{ unmatchArray | handleArray }}\r\n    </template>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"DictTag\",\r\n  props: {\r\n    options: {\r\n      type: Array,\r\n      default: null,\r\n    },\r\n    value: [Number, String, Array],\r\n    // 当未找到匹配的数据时，显示value\r\n    showValue: {\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    separator: {\r\n      type: String,\r\n      default: \",\"\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      unmatchArray: [], // 记录未匹配的项\r\n    }\r\n  },\r\n  computed: {\r\n    values() {\r\n      if (this.value === null || typeof this.value === 'undefined' || this.value === '') return []\r\n      return Array.isArray(this.value) ? this.value.map(item => '' + item) : String(this.value).split(this.separator)\r\n    },\r\n    unmatch() {\r\n      this.unmatchArray = []\r\n      // 没有value不显示\r\n      if (this.value === null || typeof this.value === 'undefined' || this.value === '' || this.options.length === 0) return false\r\n      // 传入值为数组\r\n      let unmatch = false // 添加一个标志来判断是否有未匹配项\r\n      this.values.forEach(item => {\r\n        if (!this.options.some(v => v.value === item)) {\r\n          this.unmatchArray.push(item)\r\n          unmatch = true // 如果有未匹配项，将标志设置为true\r\n        }\r\n      })\r\n      return unmatch // 返回标志的值\r\n    },\r\n\r\n  },\r\n  filters: {\r\n    handleArray(array) {\r\n      if (array.length === 0) return '';\r\n      return array.reduce((pre, cur) => {\r\n        return pre + ' ' + cur;\r\n      })\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style scoped>\r\n.el-tag + .el-tag {\r\n  margin-left: 10px;\r\n}\r\n</style>\r\n"]}]}