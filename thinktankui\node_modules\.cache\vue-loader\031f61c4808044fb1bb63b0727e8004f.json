{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\report-center\\index.vue?vue&type=style&index=0&id=572a4832&scoped=true&lang=css", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\report-center\\index.vue", "mtime": 1749104047642}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749104419119}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749104421224}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749104419872}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYXBwLWNvbnRhaW5lciB7DQogIGhlaWdodDogMTAwJTsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCn0NCg0KLnBhZ2UtY29udGFpbmVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAgaGVpZ2h0OiAxMDAlOw0KfQ0KDQovKiDlt6bkvqflr7zoiKrmoI/moLflvI8gKi8NCi5sZWZ0LXNpZGViYXIgew0KICB3aWR0aDogMjAwcHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIGJvcmRlci1yaWdodDogMXB4IHNvbGlkICNlNmU2ZTY7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIGZsZXgtc2hyaW5rOiAwOw0KICB0cmFuc2l0aW9uOiB3aWR0aCAwLjNzOw0KfQ0KDQovKiDmipjlj6DnirbmgIHnmoTkvqfovrnmoI8gKi8NCi5sZWZ0LXNpZGViYXIuY29sbGFwc2VkIHsNCiAgd2lkdGg6IDY0cHg7DQp9DQoNCi5sZWZ0LXNpZGViYXIuY29sbGFwc2VkIC5zaWRlYmFyLXNlYXJjaCwNCi5sZWZ0LXNpZGViYXIuY29sbGFwc2VkIC5lbC1tZW51LWl0ZW0gc3BhbiwNCi5sZWZ0LXNpZGViYXIuY29sbGFwc2VkIC5lbC1zdWJtZW51X190aXRsZSBzcGFuIHsNCiAgZGlzcGxheTogbm9uZTsNCn0NCg0KLmxlZnQtc2lkZWJhci5jb2xsYXBzZWQgLm5ldy1zY2hlbWUtYnRuIHsNCiAgcGFkZGluZzogOHB4IDA7DQogIGZvbnQtc2l6ZTogMDsNCn0NCg0KLmxlZnQtc2lkZWJhci5jb2xsYXBzZWQgLm5ldy1zY2hlbWUtYnRuIGkgew0KICBmb250LXNpemU6IDE2cHg7DQogIG1hcmdpbjogMDsNCn0NCg0KLnNpZGViYXItaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgcGFkZGluZzogMTBweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7DQp9DQoNCi5uZXctc2NoZW1lLWJ0biB7DQogIGZsZXg6IDE7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgcGFkZGluZzogOHB4IDEwcHg7DQp9DQoNCi5zaWRlYmFyLWJ0biB7DQogIHdpZHRoOiAzMHB4Ow0KICBoZWlnaHQ6IDMwcHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBtYXJnaW4tbGVmdDogNXB4Ow0KICBjdXJzb3I6IHBvaW50ZXI7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KfQ0KDQouc2lkZWJhci1zZWFyY2ggew0KICBwYWRkaW5nOiAxMHB4Ow0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDsNCn0NCg0KLnNpZGViYXItbWVudSB7DQogIGZsZXg6IDE7DQogIG92ZXJmbG93LXk6IGF1dG87DQp9DQoNCi5zaWRlYmFyLW1lbnUtbGlzdCB7DQogIGJvcmRlci1yaWdodDogbm9uZTsNCn0NCg0KLmFjdGl2ZS1tZW51LWl0ZW0gew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZWNmNWZmICFpbXBvcnRhbnQ7DQogIGNvbG9yOiAjNDA5RUZGICFpbXBvcnRhbnQ7DQp9DQoNCi8qIOWPs+S+p+WGheWuueWMuuagt+W8jyAqLw0KLnJpZ2h0LWNvbnRlbnQgew0KICBmbGV4OiAxOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KICBwYWRkaW5nOiAyMHB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KfQ0KDQovKiDopobnm5ZFbGVtZW50IFVJ55qE5LiA5Lqb6buY6K6k5qC35byPICovDQo6OnYtZGVlcCAuZWwtbWVudS1pdGVtLCA6OnYtZGVlcCAuZWwtc3VibWVudV9fdGl0bGUgew0KICBoZWlnaHQ6IDQwcHg7DQogIGxpbmUtaGVpZ2h0OiA0MHB4Ow0KICBmb250LXNpemU6IDE0cHg7DQp9DQoNCjo6di1kZWVwIC5lbC1zdWJtZW51IC5lbC1tZW51LWl0ZW0gew0KICBoZWlnaHQ6IDM2cHg7DQogIGxpbmUtaGVpZ2h0OiAzNnB4Ow0KICBwYWRkaW5nOiAwIDIwcHggMCA0MHB4Ow0KfQ0KDQovKiDmiqXlkYrkuK3lv4PmoLflvI8gKi8NCi5yZXBvcnQtY29udGFpbmVyIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgcGFkZGluZzogMjBweDsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBoZWlnaHQ6IDEwMCU7DQogIG92ZXJmbG93LXk6IGF1dG87DQp9DQoNCi5yZXBvcnQtaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KfQ0KDQoucmVwb3J0LWhlYWRlciAudGl0bGUgew0KICBmb250LXNpemU6IDE4cHg7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KfQ0KDQoucmVwb3J0LWhlYWRlciAuYWN0aW9ucyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGdhcDogMTBweDsNCn0NCg0KLmZpbHRlci1vcHRpb25zIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTsNCiAgcGFkZGluZzogMTVweDsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KfQ0KDQouZmlsdGVyLXJvdyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLmZpbHRlci1pdGVtIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLmZpbHRlci1idXR0b25zIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZ2FwOiAxMHB4Ow0KICBtYXJnaW4tbGVmdDogYXV0bzsNCn0NCg0KLnRhYi1jb250YWluZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIGJvcmRlcjogMXB4IHNvbGlkICNkY2RmZTY7DQogIHdpZHRoOiBmaXQtY29udGVudDsNCiAgbWFyZ2luLWxlZnQ6IGF1dG87DQp9DQoNCi50YWItaXRlbSB7DQogIHBhZGRpbmc6IDhweCAyMHB4Ow0KICBjdXJzb3I6IHBvaW50ZXI7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBmb250LXNpemU6IDE0cHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgYm9yZGVyLXJpZ2h0OiAxcHggc29saWQgI2RjZGZlNjsNCiAgbWluLXdpZHRoOiAxMDBweDsNCn0NCg0KLnRhYi1pdGVtOmxhc3QtY2hpbGQgew0KICBib3JkZXItcmlnaHQ6IG5vbmU7DQp9DQoNCi50YWItaXRlbS5hY3RpdmUgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjNDA5RUZGOw0KICBjb2xvcjogI2ZmZjsNCn0NCg0KLmZpbHRlci1pdGVtIHNwYW4gew0KICBtYXJnaW4tcmlnaHQ6IDEwcHg7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBmb250LXNpemU6IDE0cHg7DQp9DQoNCi5yZXBvcnQtdGFibGUgew0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIG1pbi1oZWlnaHQ6IDMwMHB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjZWJlZWY1Ow0KICBib3JkZXItcmFkaXVzOiA0cHg7DQp9DQoNCi5yZXBvcnQtdGFibGUgOjp2LWRlZXAgLmVsLXRhYmxlIHRoIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTsNCiAgY29sb3I6ICM2MDYyNjY7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLnJlcG9ydC10YWJsZSA6OnYtZGVlcCAuZWwtdGFibGUgdGQgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQp9DQoNCi5lbXB0eS1kYXRhIHsNCiAgcG9zaXRpb246IGFic29sdXRlOw0KICB0b3A6IDUwJTsNCiAgbGVmdDogNTAlOw0KICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSgtNTAlLCAtNTAlKTsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0KDQouZW1wdHktZGF0YSAuZW1wdHktaWNvbiB7DQogIGZvbnQtc2l6ZTogNjBweDsNCiAgY29sb3I6ICNjMGM0Y2M7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQp9DQoNCi5lbXB0eS1kYXRhIHAgew0KICBjb2xvcjogIzkwOTM5OTsNCiAgZm9udC1zaXplOiAxNHB4Ow0KfQ0KDQouZGVsZXRlLWJ0biB7DQogIGNvbG9yOiAjZjU2YzZjOw0KfQ0KDQoucGFnaW5hdGlvbi1jb250YWluZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgbWFyZ2luLXRvcDogMjBweDsNCn0NCg0KLyog5qih5p2/566h55CG5qC35byPICovDQoudGVtcGxhdGUtaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICBwYWRkaW5nLWJvdHRvbTogMTBweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlYmVlZjU7DQp9DQoNCi50ZW1wbGF0ZS10aXRsZSB7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogIGNvbG9yOiAjMzAzMTMzOw0KfQ0KDQoudGVtcGxhdGUtYWN0aW9ucyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGdhcDogMTBweDsNCn0NCg0KLnRlbXBsYXRlLXRhYi1jb250YWluZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIGJvcmRlcjogMXB4IHNvbGlkICNkY2RmZTY7DQogIHdpZHRoOiBmaXQtY29udGVudDsNCn0NCg0KLnRlbXBsYXRlLXRhYiB7DQogIHBhZGRpbmc6IDhweCAyMHB4Ow0KICBjdXJzb3I6IHBvaW50ZXI7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBmb250LXNpemU6IDE0cHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgYm9yZGVyLXJpZ2h0OiAxcHggc29saWQgI2RjZGZlNjsNCiAgbWluLXdpZHRoOiAxMDBweDsNCn0NCg0KLnRlbXBsYXRlLXRhYjpsYXN0LWNoaWxkIHsNCiAgYm9yZGVyLXJpZ2h0OiBub25lOw0KfQ0KDQoudGVtcGxhdGUtdGFiLmFjdGl2ZSB7DQogIGJhY2tncm91bmQtY29sb3I6ICM0MDlFRkY7DQogIGNvbG9yOiAjZmZmOw0KfQ0KDQoudGVtcGxhdGUtdGFibGUgew0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KfQ0KDQouY3JlYXRlLXRlbXBsYXRlLWJ0biB7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQp9DQoNCi5jcmVhdGUtdGVtcGxhdGUtYnRuIC5lbC1idXR0b24gew0KICBib3JkZXItY29sb3I6ICNmNTZjNmM7DQogIGNvbG9yOiAjZjU2YzZjOw0KfQ0KDQouY3JlYXRlLXRlbXBsYXRlLWJ0biAuZWwtYnV0dG9uOmhvdmVyIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZlZjBmMDsNCn0NCg0KLmJhY2stYnV0dG9uIHsNCiAgdGV4dC1hbGlnbjogcmlnaHQ7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQp9DQoNCi5iYWNrLWJ1dHRvbiAuZWwtYnV0dG9uIHsNCiAgY29sb3I6ICM0MDlFRkY7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgcGFkZGluZzogMDsNCn0NCg0KLmJhY2stYnV0dG9uIC5lbC1idXR0b246aG92ZXIgew0KICBjb2xvcjogIzY2YjFmZjsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+cA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/report-center", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"page-container\">\r\n      <!-- 左侧导航栏 -->\r\n      <div class=\"left-sidebar\" :class=\"{ 'collapsed': sidebarCollapsed }\">\r\n        <div class=\"sidebar-header\">\r\n          <el-button type=\"warning\" class=\"new-scheme-btn\" @click=\"createNewScheme\">\r\n            <i class=\"el-icon-plus\"></i> 新建方案\r\n          </el-button>\r\n          <div class=\"sidebar-btn\" @click=\"toggleSidebar\">\r\n            <i class=\"el-icon-s-fold\"></i>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"sidebar-search\">\r\n          <el-input\r\n            v-model=\"sidebarSearchText\"\r\n            placeholder=\"搜索\"\r\n            prefix-icon=\"el-icon-search\"\r\n            size=\"small\"\r\n            @input=\"searchSidebar\"\r\n          ></el-input>\r\n        </div>\r\n\r\n        <div class=\"sidebar-menu\">\r\n          <el-menu\r\n            :default-active=\"activeMenuItem\"\r\n            class=\"sidebar-menu-list\"\r\n            @select=\"handleMenuSelect\"\r\n          >\r\n            <template v-for=\"(item, index) in menuCategories\">\r\n              <!-- 使用唯一的key -->\r\n              <el-menu-item\r\n                v-if=\"item.isItem\"\r\n                :key=\"'item-' + item.name\"\r\n                :index=\"item.name\"\r\n                :class=\"{ 'active-menu-item': activeMenuItem === item.name }\"\r\n              >\r\n                <span>{{ item.name }}</span>\r\n              </el-menu-item>\r\n\r\n              <!-- 如果是子菜单 -->\r\n              <el-submenu\r\n                v-else\r\n                :key=\"'submenu-' + item.name\"\r\n                :index=\"item.name\"\r\n              >\r\n                <template slot=\"title\">\r\n                  <span>{{ item.name }}({{ item.count }})</span>\r\n                </template>\r\n                <!-- 子菜单项 -->\r\n                <el-menu-item\r\n                  v-for=\"child in item.children\"\r\n                  :key=\"child.name\"\r\n                  :index=\"child.name\"\r\n                >\r\n                  {{ child.name }}\r\n                </el-menu-item>\r\n              </el-submenu>\r\n            </template>\r\n          </el-menu>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧内容区 -->\r\n      <div class=\"right-content\">\r\n        <div class=\"report-container\">\r\n          <div class=\"report-header\">\r\n            <div class=\"title\">方案报告</div>\r\n            <div class=\"actions\">\r\n              <el-button type=\"primary\" size=\"small\" icon=\"el-icon-edit\" @click=\"editReport\">模板管理</el-button>\r\n              <el-button type=\"primary\" size=\"small\" icon=\"el-icon-document\" @click=\"exportReport\">定时任务管理</el-button>\r\n              <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"createReport\">新建报告</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 报告列表视图 -->\r\n          <div v-if=\"currentView === 'report'\">\r\n            <div class=\"filter-options\">\r\n              <div class=\"filter-row\">\r\n                <div class=\"filter-item\">\r\n                  <span>时间范围：</span>\r\n                  <el-date-picker\r\n                    v-model=\"dateRange\"\r\n                    type=\"daterange\"\r\n                    range-separator=\"-\"\r\n                    start-placeholder=\"开始日期\"\r\n                    end-placeholder=\"结束日期\"\r\n                    size=\"small\"\r\n                    style=\"width: 240px;\"\r\n                  ></el-date-picker>\r\n                </div>\r\n                <div class=\"filter-item\">\r\n                  <el-select v-model=\"filterType\" placeholder=\"全部\" size=\"small\" style=\"width: 100px;\">\r\n                    <el-option label=\"全部\" value=\"all\"></el-option>\r\n                    <el-option label=\"名称\" value=\"name\"></el-option>\r\n                    <el-option label=\"类型\" value=\"type\"></el-option>\r\n                  </el-select>\r\n                  <el-input\r\n                    v-model=\"searchKeyword\"\r\n                    placeholder=\"请输入内容\"\r\n                    size=\"small\"\r\n                    style=\"width: 200px; margin-left: 5px;\"\r\n                  ></el-input>\r\n                </div>\r\n                <div class=\"tab-container\">\r\n                  <div\r\n                    class=\"tab-item\"\r\n                    :class=\"{ 'active': activeTab === 'normal' }\"\r\n                    @click=\"switchTab('normal')\"\r\n                  >\r\n                    普通报告\r\n                  </div>\r\n                  <div\r\n                    class=\"tab-item\"\r\n                    :class=\"{ 'active': activeTab === 'competitor' }\"\r\n                    @click=\"switchTab('competitor')\"\r\n                  >\r\n                    竞对报告\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"report-table\">\r\n              <el-table\r\n                :data=\"reportList\"\r\n                style=\"width: 100%\"\r\n                border\r\n                stripe\r\n                :header-cell-style=\"{background:'#f5f7fa', color:'#606266'}\"\r\n              >\r\n                <el-table-column\r\n                  prop=\"name\"\r\n                  label=\"配置名称\"\r\n                  width=\"180\"\r\n                ></el-table-column>\r\n                <el-table-column\r\n                  prop=\"createTime\"\r\n                  label=\"创建时间\"\r\n                  width=\"180\"\r\n                ></el-table-column>\r\n                <el-table-column\r\n                  prop=\"type\"\r\n                  label=\"配置类型\"\r\n                  width=\"120\"\r\n                ></el-table-column>\r\n                <el-table-column\r\n                  prop=\"status\"\r\n                  label=\"状态\"\r\n                  width=\"100\"\r\n                ></el-table-column>\r\n                <el-table-column\r\n                  prop=\"reportStatus\"\r\n                  label=\"报告状态\"\r\n                  width=\"120\"\r\n                ></el-table-column>\r\n                <el-table-column\r\n                  label=\"操作\"\r\n                  align=\"center\"\r\n                >\r\n                  <template slot-scope=\"scope\">\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      icon=\"el-icon-view\"\r\n                      @click=\"viewReport(scope.row)\"\r\n                    >查看</el-button>\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      icon=\"el-icon-edit\"\r\n                      @click=\"editReportItem(scope.row)\"\r\n                    >编辑</el-button>\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      icon=\"el-icon-delete\"\r\n                      class=\"delete-btn\"\r\n                      @click=\"deleteReport(scope.row)\"\r\n                    >删除</el-button>\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n\r\n              <!-- 空数据显示 -->\r\n              <div v-if=\"reportList.length === 0\" class=\"empty-data\">\r\n                <i class=\"el-icon-data-analysis empty-icon\"></i>\r\n                <p>暂无数据</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 模板管理视图 -->\r\n          <div v-if=\"currentView === 'template'\">\r\n            <div class=\"back-button\">\r\n              <el-button type=\"text\" icon=\"el-icon-arrow-left\" @click=\"goBack\">返回</el-button>\r\n            </div>\r\n            <div class=\"template-header\">\r\n              <div class=\"template-title\">\r\n                <i class=\"el-icon-document\"></i> 报告模板\r\n              </div>\r\n              <div class=\"template-actions\">\r\n                <div class=\"template-tab-container\">\r\n                  <div\r\n                    class=\"template-tab\"\r\n                    :class=\"{ 'active': templateType === 'normal' }\"\r\n                    @click=\"switchTemplateType('normal')\"\r\n                  >\r\n                    普通模板\r\n                  </div>\r\n                  <div\r\n                    class=\"template-tab\"\r\n                    :class=\"{ 'active': templateType === 'competitor' }\"\r\n                    @click=\"switchTemplateType('competitor')\"\r\n                  >\r\n                    竞对模板\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"create-template-btn\">\r\n              <el-button type=\"primary\" plain @click=\"createProductTemplate\">\r\n                <i class=\"el-icon-plus\"></i> 创建产品模板\r\n              </el-button>\r\n            </div>\r\n\r\n            <div class=\"template-table\">\r\n              <el-table\r\n                :data=\"templateList\"\r\n                style=\"width: 100%\"\r\n                border\r\n                stripe\r\n                :header-cell-style=\"{background:'#f5f7fa', color:'#606266'}\"\r\n              >\r\n                <el-table-column\r\n                  prop=\"name\"\r\n                  label=\"模板名称\"\r\n                  width=\"180\"\r\n                ></el-table-column>\r\n                <el-table-column\r\n                  prop=\"createTime\"\r\n                  label=\"创建时间\"\r\n                  width=\"180\"\r\n                ></el-table-column>\r\n                <el-table-column\r\n                  label=\"操作\"\r\n                  align=\"center\"\r\n                >\r\n                  <template slot-scope=\"scope\">\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      icon=\"el-icon-view\"\r\n                      @click=\"viewTemplate(scope.row)\"\r\n                    >查看</el-button>\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      icon=\"el-icon-edit\"\r\n                      @click=\"editTemplate(scope.row)\"\r\n                    >编辑</el-button>\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      icon=\"el-icon-delete\"\r\n                      class=\"delete-btn\"\r\n                      @click=\"deleteTemplate(scope.row)\"\r\n                    >删除</el-button>\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"pagination-container\">\r\n            <el-pagination\r\n              background\r\n              layout=\"prev, pager, next\"\r\n              :total=\"total\"\r\n              :current-page.sync=\"currentPage\"\r\n              :page-size=\"pageSize\"\r\n              @current-change=\"handleCurrentChange\"\r\n            ></el-pagination>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"ReportList\",\r\n  data() {\r\n    return {\r\n      dateRange: [],\r\n      filterType: 'all',\r\n      searchKeyword: '',\r\n      reportList: [],\r\n      total: 0,\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      activeTab: 'normal', // 当前激活的选项卡：normal-普通报告，competitor-竞对报告\r\n      currentView: 'report', // 当前视图：report-报告列表，template-模板管理\r\n      templateType: 'normal', // 当前模板类型：normal-普通模板，competitor-竞对模板\r\n      // 模板列表数据\r\n      templateList: [\r\n        { name: '品牌-热议话题', createTime: '2019-11-18 14:02:08', operation: '' },\r\n        { name: '品牌-舆论', createTime: '2019-11-18 14:06:52', operation: '' },\r\n        { name: '品牌-竞品', createTime: '2021-04-07 15:15:00', operation: '' }\r\n      ],\r\n      // 侧边栏数据\r\n      sidebarCollapsed: false,\r\n      sidebarSearchText: '',\r\n      activeMenuItem: '方太',\r\n      menuCategories: [\r\n        { name: '总监', count: 1, children: [] },\r\n        { name: '品牌', count: 1, children: [] },\r\n        { name: '方太', count: 0, isItem: true },\r\n        { name: '人物', count: 0, children: [] },\r\n        { name: '机构', count: 0, children: [] },\r\n        { name: '产品', count: 0, children: [] },\r\n        { name: '事件', count: 0, children: [] },\r\n        { name: '话题', count: 0, children: [] }\r\n      ]\r\n    };\r\n  },\r\n  methods: {\r\n    // 侧边栏相关方法\r\n    toggleSidebar() {\r\n      this.sidebarCollapsed = !this.sidebarCollapsed;\r\n    },\r\n    handleMenuSelect(index) {\r\n      this.activeMenuItem = index;\r\n      // 这里可以添加切换菜单项后的逻辑，如重新获取数据等\r\n      this.fetchReportList();\r\n    },\r\n    createNewScheme() {\r\n      // 新建方案的逻辑\r\n      this.$message({\r\n        message: '新建方案功能待实现',\r\n        type: 'info'\r\n      });\r\n    },\r\n    searchSidebar() {\r\n      // 侧边栏搜索逻辑\r\n      console.log('搜索关键词：', this.sidebarSearchText);\r\n      // 实现搜索逻辑\r\n    },\r\n    // 模板管理\r\n    editReport() {\r\n      this.currentView = 'template';\r\n      this.$message.success(\"切换到模板管理\");\r\n    },\r\n    // 导出报告\r\n    exportReport() {\r\n      this.$message.success(\"定时任务管理\");\r\n    },\r\n    // 创建新报告\r\n    createReport() {\r\n      this.$message.success(\"新建报告\");\r\n    },\r\n    // 查看报告\r\n    viewReport(row) {\r\n      this.$message.success(`查看报告: ${row.name}`);\r\n    },\r\n    // 编辑报告项\r\n    editReportItem(row) {\r\n      this.$message.success(`编辑报告: ${row.name}`);\r\n    },\r\n    // 删除报告\r\n    deleteReport(row) {\r\n      this.$confirm(`确认删除报告 \"${row.name}\"?`, \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.$message.success(`删除报告: ${row.name}`);\r\n      }).catch(() => {\r\n        this.$message.info(\"已取消删除\");\r\n      });\r\n    },\r\n    // 处理页码变化\r\n    handleCurrentChange(val) {\r\n      this.currentPage = val;\r\n      this.fetchReportList();\r\n    },\r\n    // 获取报告列表\r\n    fetchReportList() {\r\n      // 实际应用中这里需要调用接口获取数据\r\n      // 这里模拟空数据\r\n      this.reportList = [];\r\n      this.total = 0;\r\n    },\r\n    // 查询按钮点击事件\r\n    handleQuery() {\r\n      this.$message.success(\"执行查询操作\");\r\n      this.fetchReportList();\r\n    },\r\n    // 重置按钮点击事件\r\n    handleReset() {\r\n      this.dateRange = [];\r\n      this.filterType = 'all';\r\n      this.searchKeyword = '';\r\n      this.$message.success(\"重置筛选条件\");\r\n    },\r\n    // 切换选项卡\r\n    switchTab(tab) {\r\n      this.activeTab = tab;\r\n      if (tab === 'normal') {\r\n        this.handleQuery();\r\n      } else {\r\n        this.handleReset();\r\n      }\r\n    },\r\n    // 创建模板\r\n    createTemplate() {\r\n      this.$message.success(\"创建新模板\");\r\n    },\r\n    // 创建产品模板\r\n    createProductTemplate() {\r\n      this.$message.success(\"创建产品模板\");\r\n    },\r\n    // 查看模板\r\n    viewTemplate(row) {\r\n      this.$message.success(`查看模板: ${row.name}`);\r\n    },\r\n    // 编辑模板\r\n    editTemplate(row) {\r\n      this.$message.success(`编辑模板: ${row.name}`);\r\n    },\r\n    // 删除模板\r\n    deleteTemplate(row) {\r\n      this.$confirm(`确认删除模板 \"${row.name}\"?`, \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.$message.success(`删除模板: ${row.name}`);\r\n      }).catch(() => {\r\n        this.$message.info(\"已取消删除\");\r\n      });\r\n    },\r\n    // 返回按钮点击事件\r\n    goBack() {\r\n      this.currentView = 'report';\r\n      this.$message.success(\"返回报告列表\");\r\n    },\r\n    // 切换模板类型\r\n    switchTemplateType(type) {\r\n      this.templateType = type;\r\n      this.$message.success(`切换到${type === 'normal' ? '普通模板' : '竞对模板'}`);\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchReportList();\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.page-container {\r\n  display: flex;\r\n  height: 100%;\r\n}\r\n\r\n/* 左侧导航栏样式 */\r\n.left-sidebar {\r\n  width: 200px;\r\n  background-color: #fff;\r\n  border-right: 1px solid #e6e6e6;\r\n  display: flex;\r\n  flex-direction: column;\r\n  flex-shrink: 0;\r\n  transition: width 0.3s;\r\n}\r\n\r\n/* 折叠状态的侧边栏 */\r\n.left-sidebar.collapsed {\r\n  width: 64px;\r\n}\r\n\r\n.left-sidebar.collapsed .sidebar-search,\r\n.left-sidebar.collapsed .el-menu-item span,\r\n.left-sidebar.collapsed .el-submenu__title span {\r\n  display: none;\r\n}\r\n\r\n.left-sidebar.collapsed .new-scheme-btn {\r\n  padding: 8px 0;\r\n  font-size: 0;\r\n}\r\n\r\n.left-sidebar.collapsed .new-scheme-btn i {\r\n  font-size: 16px;\r\n  margin: 0;\r\n}\r\n\r\n.sidebar-header {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.new-scheme-btn {\r\n  flex: 1;\r\n  font-size: 12px;\r\n  padding: 8px 10px;\r\n}\r\n\r\n.sidebar-btn {\r\n  width: 30px;\r\n  height: 30px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-left: 5px;\r\n  cursor: pointer;\r\n  color: #909399;\r\n}\r\n\r\n.sidebar-search {\r\n  padding: 10px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.sidebar-menu {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n}\r\n\r\n.sidebar-menu-list {\r\n  border-right: none;\r\n}\r\n\r\n.active-menu-item {\r\n  background-color: #ecf5ff !important;\r\n  color: #409EFF !important;\r\n}\r\n\r\n/* 右侧内容区样式 */\r\n.right-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 覆盖Element UI的一些默认样式 */\r\n::v-deep .el-menu-item, ::v-deep .el-submenu__title {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  font-size: 14px;\r\n}\r\n\r\n::v-deep .el-submenu .el-menu-item {\r\n  height: 36px;\r\n  line-height: 36px;\r\n  padding: 0 20px 0 40px;\r\n}\r\n\r\n/* 报告中心样式 */\r\n.report-container {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n  height: 100%;\r\n  overflow-y: auto;\r\n}\r\n\r\n.report-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.report-header .title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.report-header .actions {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.filter-options {\r\n  margin-bottom: 20px;\r\n  background-color: #f5f7fa;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.filter-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.filter-item {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.filter-buttons {\r\n  display: flex;\r\n  gap: 10px;\r\n  margin-left: auto;\r\n}\r\n\r\n.tab-container {\r\n  display: flex;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  border: 1px solid #dcdfe6;\r\n  width: fit-content;\r\n  margin-left: auto;\r\n}\r\n\r\n.tab-item {\r\n  padding: 8px 20px;\r\n  cursor: pointer;\r\n  background-color: #fff;\r\n  color: #606266;\r\n  font-size: 14px;\r\n  text-align: center;\r\n  border-right: 1px solid #dcdfe6;\r\n  min-width: 100px;\r\n}\r\n\r\n.tab-item:last-child {\r\n  border-right: none;\r\n}\r\n\r\n.tab-item.active {\r\n  background-color: #409EFF;\r\n  color: #fff;\r\n}\r\n\r\n.filter-item span {\r\n  margin-right: 10px;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.report-table {\r\n  margin-bottom: 20px;\r\n  position: relative;\r\n  min-height: 300px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n}\r\n\r\n.report-table ::v-deep .el-table th {\r\n  background-color: #f5f7fa;\r\n  color: #606266;\r\n  font-weight: 500;\r\n  text-align: center;\r\n}\r\n\r\n.report-table ::v-deep .el-table td {\r\n  text-align: center;\r\n}\r\n\r\n.empty-data {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  text-align: center;\r\n}\r\n\r\n.empty-data .empty-icon {\r\n  font-size: 60px;\r\n  color: #c0c4cc;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.empty-data p {\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.delete-btn {\r\n  color: #f56c6c;\r\n}\r\n\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 20px;\r\n}\r\n\r\n/* 模板管理样式 */\r\n.template-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.template-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.template-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.template-tab-container {\r\n  display: flex;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  border: 1px solid #dcdfe6;\r\n  width: fit-content;\r\n}\r\n\r\n.template-tab {\r\n  padding: 8px 20px;\r\n  cursor: pointer;\r\n  background-color: #fff;\r\n  color: #606266;\r\n  font-size: 14px;\r\n  text-align: center;\r\n  border-right: 1px solid #dcdfe6;\r\n  min-width: 100px;\r\n}\r\n\r\n.template-tab:last-child {\r\n  border-right: none;\r\n}\r\n\r\n.template-tab.active {\r\n  background-color: #409EFF;\r\n  color: #fff;\r\n}\r\n\r\n.template-table {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.create-template-btn {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.create-template-btn .el-button {\r\n  border-color: #f56c6c;\r\n  color: #f56c6c;\r\n}\r\n\r\n.create-template-btn .el-button:hover {\r\n  background-color: #fef0f0;\r\n}\r\n\r\n.back-button {\r\n  text-align: right;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.back-button .el-button {\r\n  color: #409EFF;\r\n  font-size: 14px;\r\n  padding: 0;\r\n}\r\n\r\n.back-button .el-button:hover {\r\n  color: #66b1ff;\r\n}\r\n</style>\r\n"]}]}