{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\layout\\index.vue?vue&type=style&index=0&id=13877386&lang=scss&scoped=true", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\layout\\index.vue", "mtime": 1749104047629}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749104419119}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749104421224}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749104419872}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749104418479}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgpAaW1wb3J0ICJ+QC9hc3NldHMvc3R5bGVzL21peGluLnNjc3MiOwpAaW1wb3J0ICJ+QC9hc3NldHMvc3R5bGVzL3ZhcmlhYmxlcy5zY3NzIjsKCi5hcHAtd3JhcHBlciB7CiAgQGluY2x1ZGUgY2xlYXJmaXg7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIGhlaWdodDogMTAwJTsKICB3aWR0aDogMTAwJTsKCiAgJi5tb2JpbGUub3BlblNpZGViYXIgewogICAgcG9zaXRpb246IGZpeGVkOwogICAgdG9wOiAwOwogIH0KfQoKLmRyYXdlci1iZyB7CiAgYmFja2dyb3VuZDogIzAwMDsKICBvcGFjaXR5OiAwLjM7CiAgd2lkdGg6IDEwMCU7CiAgdG9wOiAwOwogIGhlaWdodDogMTAwJTsKICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgei1pbmRleDogOTk5Owp9CgouZml4ZWQtaGVhZGVyIHsKICBwb3NpdGlvbjogZml4ZWQ7CiAgdG9wOiAwOwogIHJpZ2h0OiAwOwogIHotaW5kZXg6IDk7CiAgd2lkdGg6IGNhbGMoMTAwJSAtICN7JGJhc2Utc2lkZWJhci13aWR0aH0pOwogIHRyYW5zaXRpb246IHdpZHRoIDAuMjhzOwp9CgouaGlkZVNpZGViYXIgLmZpeGVkLWhlYWRlciB7CiAgd2lkdGg6IGNhbGMoMTAwJSAtIDU0cHgpOwp9Cgouc2lkZWJhckhpZGUgLmZpeGVkLWhlYWRlciB7CiAgd2lkdGg6IDEwMCU7Cn0KCi5tb2JpbGUgLmZpeGVkLWhlYWRlciB7CiAgd2lkdGg6IDEwMCU7Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout", "sourcesContent": ["<template>\r\n  <div :class=\"classObj\" class=\"app-wrapper\" :style=\"{'--current-color': theme}\">\r\n    <div v-if=\"device==='mobile'&&sidebar.opened\" class=\"drawer-bg\" @click=\"handleClickOutside\"/>\r\n    <sidebar v-if=\"!sidebar.hide\" class=\"sidebar-container\"/>\r\n    <div :class=\"{hasTagsView:needTagsView,sidebarHide:sidebar.hide}\" class=\"main-container\">\r\n      <div :class=\"{'fixed-header':fixedHeader}\">\r\n        <navbar/>\r\n        <tags-view v-if=\"needTagsView\"/>\r\n      </div>\r\n      <app-main/>\r\n      <right-panel>\r\n        <settings/>\r\n      </right-panel>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport RightPanel from '@/components/RightPanel'\r\nimport { AppMain, Navbar, Settings, Sidebar, TagsView } from './components'\r\nimport ResizeMixin from './mixin/ResizeHandler'\r\nimport { mapState } from 'vuex'\r\nimport variables from '@/assets/styles/variables.scss'\r\n\r\nexport default {\r\n  name: 'Layout',\r\n  components: {\r\n    AppMain,\r\n    Navbar,\r\n    RightPanel,\r\n    Settings,\r\n    Sidebar,\r\n    TagsView\r\n  },\r\n  mixins: [ResizeMixin],\r\n  computed: {\r\n    ...mapState({\r\n      theme: state => state.settings.theme,\r\n      sideTheme: state => state.settings.sideTheme,\r\n      sidebar: state => state.app.sidebar,\r\n      device: state => state.app.device,\r\n      needTagsView: state => state.settings.tagsView,\r\n      fixedHeader: state => state.settings.fixedHeader\r\n    }),\r\n    classObj() {\r\n      return {\r\n        hideSidebar: !this.sidebar.opened,\r\n        openSidebar: this.sidebar.opened,\r\n        withoutAnimation: this.sidebar.withoutAnimation,\r\n        mobile: this.device === 'mobile'\r\n      }\r\n    },\r\n    variables() {\r\n      return variables;\r\n    }\r\n  },\r\n  methods: {\r\n    handleClickOutside() {\r\n      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  @import \"~@/assets/styles/mixin.scss\";\r\n  @import \"~@/assets/styles/variables.scss\";\r\n\r\n  .app-wrapper {\r\n    @include clearfix;\r\n    position: relative;\r\n    height: 100%;\r\n    width: 100%;\r\n\r\n    &.mobile.openSidebar {\r\n      position: fixed;\r\n      top: 0;\r\n    }\r\n  }\r\n\r\n  .drawer-bg {\r\n    background: #000;\r\n    opacity: 0.3;\r\n    width: 100%;\r\n    top: 0;\r\n    height: 100%;\r\n    position: absolute;\r\n    z-index: 999;\r\n  }\r\n\r\n  .fixed-header {\r\n    position: fixed;\r\n    top: 0;\r\n    right: 0;\r\n    z-index: 9;\r\n    width: calc(100% - #{$base-sidebar-width});\r\n    transition: width 0.28s;\r\n  }\r\n\r\n  .hideSidebar .fixed-header {\r\n    width: calc(100% - 54px);\r\n  }\r\n\r\n  .sidebarHide .fixed-header {\r\n    width: 100%;\r\n  }\r\n\r\n  .mobile .fixed-header {\r\n    width: 100%;\r\n  }\r\n</style>\r\n"]}]}