{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\source-monitoring\\index.vue?vue&type=style&index=0&id=53d7ffda&lang=scss&scoped=true", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\source-monitoring\\index.vue", "mtime": 1749104047642}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749104419119}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749104421224}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749104419872}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749104418479}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyIA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/source-monitoring", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 页面头部 -->\r\n    <div class=\"page-header\">\r\n      <h2>信源监测</h2>\r\n    </div>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <div class=\"main-content\">\r\n      <!-- 标签页导航 -->\r\n      <el-tabs v-model=\"activeTab\" class=\"source-tabs\">\r\n        <el-tab-pane label=\"信源人员\" name=\"personnel\">\r\n          <div class=\"tab-content\">\r\n            <!-- 搜索和筛选区域 -->\r\n            <div class=\"filter-section\">\r\n              <div class=\"filter-row\">\r\n                <div class=\"filter-left\">\r\n                  <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\">接收人设置</el-button>\r\n                  <el-button size=\"small\" icon=\"el-icon-warning\">预警设置</el-button>\r\n                  <el-button size=\"small\" icon=\"el-icon-key\">关键词设置</el-button>\r\n                  <el-button size=\"small\" icon=\"el-icon-star-off\">信源设置</el-button>\r\n                </div>\r\n                <div class=\"filter-right\">\r\n                  <span class=\"status-text\">预警开关</span>\r\n                  <el-switch v-model=\"monitorStatus\" active-color=\"#13ce66\"></el-switch>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"search-row\">\r\n                <div class=\"search-left\">\r\n                  <el-button size=\"small\" icon=\"el-icon-refresh\">刷新</el-button>\r\n                  <el-button size=\"small\" icon=\"el-icon-download\">下载</el-button>\r\n                </div>\r\n                <div class=\"search-right\">\r\n                  <span class=\"total-text\">共0条记录</span>\r\n                  <el-select v-model=\"pageSize\" size=\"small\" style=\"width: 80px; margin: 0 10px;\">\r\n                    <el-option label=\"7条\" value=\"7\"></el-option>\r\n                    <el-option label=\"10条\" value=\"10\"></el-option>\r\n                    <el-option label=\"20条\" value=\"20\"></el-option>\r\n                  </el-select>\r\n                  <el-date-picker\r\n                    v-model=\"dateRange\"\r\n                    type=\"datetimerange\"\r\n                    range-separator=\"~\"\r\n                    start-placeholder=\"2023/04/23 00:00:00\"\r\n                    end-placeholder=\"2023/04/25 20:47:46\"\r\n                    size=\"small\"\r\n                    style=\"width: 350px; margin-right: 10px;\"\r\n                  ></el-date-picker>\r\n                  <el-select v-model=\"sortBy\" size=\"small\" style=\"width: 100px; margin-right: 10px;\">\r\n                    <el-option label=\"全部\" value=\"all\"></el-option>\r\n                  </el-select>\r\n                  <el-input\r\n                    v-model=\"searchKeyword\"\r\n                    placeholder=\"请输入关键词搜索\"\r\n                    size=\"small\"\r\n                    style=\"width: 200px;\"\r\n                    suffix-icon=\"el-icon-search\"\r\n                  ></el-input>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 表格区域 -->\r\n            <div class=\"table-section\">\r\n              <el-table\r\n                :data=\"tableData\"\r\n                style=\"width: 100%\"\r\n                height=\"400\"\r\n                empty-text=\"暂无数据\"\r\n              >\r\n                <el-table-column prop=\"media\" label=\"媒体\" width=\"120\"></el-table-column>\r\n                <el-table-column prop=\"platform\" label=\"平台\" width=\"100\"></el-table-column>\r\n                <el-table-column prop=\"publisher\" label=\"发布人\" width=\"120\"></el-table-column>\r\n                <el-table-column prop=\"publishTime\" label=\"发布时间\" width=\"180\"></el-table-column>\r\n                <el-table-column prop=\"content\" label=\"内容\" min-width=\"200\"></el-table-column>\r\n                <el-table-column prop=\"readCount\" label=\"阅读量\" width=\"100\"></el-table-column>\r\n                <el-table-column label=\"操作\" width=\"120\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-button type=\"text\" size=\"small\">查看</el-button>\r\n                    <el-button type=\"text\" size=\"small\">编辑</el-button>\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n            </div>\r\n          </div>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"信源媒体\" name=\"media\">\r\n          <div class=\"tab-content\">\r\n            <div class=\"empty-state\">\r\n              <p>暂无数据</p>\r\n            </div>\r\n          </div>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"媒体监控\" name=\"monitoring\">\r\n          <div class=\"tab-content\">\r\n            <div class=\"empty-state\">\r\n              <p>暂无数据</p>\r\n            </div>\r\n          </div>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"媒体反馈\" name=\"feedback\">\r\n          <div class=\"tab-content\">\r\n            <div class=\"empty-state\">\r\n              <p>暂无数据</p>\r\n            </div>\r\n          </div>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'SourceMonitoring',\r\n  data() {\r\n    return {\r\n      activeTab: 'personnel',\r\n      monitorStatus: true,\r\n      pageSize: '7',\r\n      dateRange: [],\r\n      sortBy: 'all',\r\n      searchKeyword: '',\r\n      tableData: [] // 暂无数据，所以为空数组\r\n    }\r\n  },\r\n  methods: {\r\n    // 这里可以添加各种方法\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  padding: 20px;\r\n  background-color: #f0f2f5;\r\n  min-height: calc(100vh - 84px);\r\n}\r\n\r\n.page-header {\r\n  margin-bottom: 20px;\r\n\r\n  h2 {\r\n    margin: 0;\r\n    font-size: 20px;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n.main-content {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\r\n  min-height: calc(100vh - 150px);\r\n}\r\n\r\n.source-tabs {\r\n  padding: 20px;\r\n\r\n  .tab-content {\r\n    margin-top: 20px;\r\n  }\r\n}\r\n\r\n.filter-section {\r\n  margin-bottom: 20px;\r\n\r\n  .filter-row {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 15px;\r\n\r\n    .filter-left {\r\n      display: flex;\r\n      gap: 10px;\r\n    }\r\n\r\n    .filter-right {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 10px;\r\n\r\n      .status-text {\r\n        font-size: 14px;\r\n        color: #606266;\r\n      }\r\n    }\r\n  }\r\n\r\n  .search-row {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n\r\n    .search-left {\r\n      display: flex;\r\n      gap: 10px;\r\n    }\r\n\r\n    .search-right {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .total-text {\r\n        font-size: 14px;\r\n        color: #606266;\r\n        margin-right: 15px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.table-section {\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n}\r\n\r\n.empty-state {\r\n  text-align: center;\r\n  padding: 60px 0;\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n</style>\r\n"]}]}