{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\my-issues\\index.vue?vue&type=style&index=0&id=02dd1d94&lang=scss&scoped=true", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\my-issues\\index.vue", "mtime": 1749104047642}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749104419119}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749104421224}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749104419872}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749104418479}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0NA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/my-issues", "sourcesContent": ["<template>\r\n  <div class=\"my-issues\">\r\n    <!-- 左侧导航栏 -->\r\n    <div class=\"left-sidebar\">\r\n      <div class=\"sidebar-header\">\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\">新建方案</el-button>\r\n      </div>\r\n\r\n      <div class=\"sidebar-search\">\r\n        <el-input\r\n          v-model=\"searchText\"\r\n          placeholder=\"搜索方案\"\r\n          size=\"small\"\r\n          prefix-icon=\"el-icon-search\">\r\n        </el-input>\r\n      </div>\r\n\r\n      <div class=\"sidebar-menu\">\r\n        <div class=\"menu-section\">\r\n          <div class=\"section-title\">已有方案</div>\r\n          <el-menu\r\n            :default-active=\"activeMenuItem\"\r\n            class=\"sidebar-menu-list\"\r\n            @select=\"handleMenuSelect\">\r\n            <el-menu-item index=\"首页\">\r\n              <i class=\"el-icon-s-home\"></i>\r\n              <span>首页</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"总监(1)\">\r\n              <i class=\"el-icon-s-custom\"></i>\r\n              <span>总监(1)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"品牌(1)\">\r\n              <i class=\"el-icon-s-goods\"></i>\r\n              <span>品牌(1)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"方太\" class=\"active-item\">\r\n              <i class=\"el-icon-star-off\"></i>\r\n              <span>方太</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"人物(0)\">\r\n              <i class=\"el-icon-user\"></i>\r\n              <span>人物(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"机构(0)\">\r\n              <i class=\"el-icon-office-building\"></i>\r\n              <span>机构(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"产品(0)\">\r\n              <i class=\"el-icon-goods\"></i>\r\n              <span>产品(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"事件(0)\">\r\n              <i class=\"el-icon-warning\"></i>\r\n              <span>事件(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"话题(0)\">\r\n              <i class=\"el-icon-chat-dot-round\"></i>\r\n              <span>话题(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"舆情总览\">\r\n              <i class=\"el-icon-monitor\"></i>\r\n              <span>舆情总览</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"发送预警\">\r\n              <i class=\"el-icon-message\"></i>\r\n              <span>发送预警</span>\r\n            </el-menu-item>\r\n          </el-menu>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 右侧内容区 -->\r\n    <div class=\"right-content\">\r\n      <!-- 主要内容区域 -->\r\n      <div class=\"main-content\">\r\n        <div class=\"issues-header\">\r\n          <h2>我的问题</h2>\r\n        </div>\r\n\r\n        <div class=\"issues-list\">\r\n          <div\r\n            v-for=\"(issue, index) in issuesList\"\r\n            :key=\"index\"\r\n            class=\"issue-item\"\r\n            :class=\"issue.type\">\r\n            <div class=\"issue-indicator\"></div>\r\n            <div class=\"issue-content\">\r\n              <div class=\"issue-title\">{{ issue.title }}</div>\r\n              <div class=\"issue-description\" v-if=\"issue.description\">{{ issue.description }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'MyIssues',\r\n  data() {\r\n    return {\r\n      activeMenuItem: '方太',\r\n      searchText: '',\r\n      originalTopNav: undefined, // 存储原始的topNav状态\r\n      issuesList: [\r\n        {\r\n          type: 'negative',\r\n          title: '为什么会被误解为不好？',\r\n          description: '网民对某些产品或服务产生负面印象，可能是由于信息传播不当或误解造成的。'\r\n        },\r\n        {\r\n          type: 'neutral',\r\n          title: '在网络上的口碑怎么样？',\r\n          description: ''\r\n        },\r\n        {\r\n          type: 'positive',\r\n          title: '古今中外都有哪些，比如：今天的网络舆情中，哪些是正面的？哪些是负面的？哪些是中性的？如：古代、现代、国内、国外等。',\r\n          description: ''\r\n        },\r\n        {\r\n          type: 'negative',\r\n          title: '网络舆情的影响因素有哪些？',\r\n          description: ''\r\n        },\r\n        {\r\n          type: 'positive',\r\n          title: '什么是网络舆情？',\r\n          description: ''\r\n        },\r\n        {\r\n          type: 'negative',\r\n          title: '什么是网络舆情？',\r\n          description: ''\r\n        },\r\n        {\r\n          type: 'positive',\r\n          title: '古今中外都有哪些，比如：今天的网络舆情中，哪些是正面的？哪些是负面的？哪些是中性的？如：古代、现代、国内、国外等。',\r\n          description: ''\r\n        },\r\n        {\r\n          type: 'negative',\r\n          title: '古今中外都有哪些，比如：今天的网络舆情中，哪些是正面的？哪些是负面的？哪些是中性的？如：古代、现代、国内、国外等。',\r\n          description: ''\r\n        },\r\n        {\r\n          type: 'positive',\r\n          title: '网络舆情的影响因素有哪些？',\r\n          description: ''\r\n        },\r\n        {\r\n          type: 'negative',\r\n          title: '为什么网络舆情监测很重要？',\r\n          description: '网络舆情监测对企业品牌管理和危机预警具有重要意义。'\r\n        },\r\n        {\r\n          type: 'positive',\r\n          title: '为什么网络舆情监测很重要？',\r\n          description: '网络舆情监测对企业品牌管理和危机预警具有重要意义。'\r\n        },\r\n        {\r\n          type: 'negative',\r\n          title: '中国网络舆情监测的发展历程是什么？',\r\n          description: '从早期的人工监测到现在的智能化监测系统，经历了技术革新的过程。'\r\n        },\r\n        {\r\n          type: 'positive',\r\n          title: '网络舆情监测的技术手段有哪些？',\r\n          description: '包括数据采集、情感分析、关键词监测、趋势预测等多种技术手段。'\r\n        },\r\n        {\r\n          type: 'negative',\r\n          title: '为什么会被误解为\"全球干旱\"，而不是下雨或者其他气象变化？',\r\n          description: '媒体报道的角度和用词选择可能会影响公众对气象事件的理解。'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  mounted() {\r\n    // 隐藏顶部导航栏\r\n    this.originalTopNav = this.$store.state.settings.topNav\r\n    this.$store.dispatch('settings/changeSetting', {\r\n      key: 'topNav',\r\n      value: false\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    // 恢复顶部导航栏设置\r\n    if (this.originalTopNav !== undefined) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'topNav',\r\n        value: this.originalTopNav\r\n      })\r\n    }\r\n  },\r\n  methods: {\r\n    handleMenuSelect(index) {\r\n      this.activeMenuItem = index\r\n      console.log('Menu selected:', index)\r\n\r\n      // 根据选择的菜单项进行路由跳转\r\n      if (index === '首页') {\r\n        this.$router.push('/index')\r\n      } else if (index === '舆情总览') {\r\n        this.$router.push('/opinion-overview')\r\n      } else if (index === '发送预警') {\r\n        // 这里可以触发发送预警的功能\r\n        console.log('发送预警功能')\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.my-issues {\r\n  display: flex;\r\n  height: 100vh;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.left-sidebar {\r\n  width: 280px;\r\n  background-color: #fff;\r\n  border-right: 1px solid #e8e8e8;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .sidebar-header {\r\n    padding: 16px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n\r\n  .sidebar-search {\r\n    padding: 16px;\r\n  }\r\n\r\n  .sidebar-menu {\r\n    flex: 1;\r\n    padding: 0 16px;\r\n\r\n    .section-title {\r\n      font-size: 14px;\r\n      color: #666;\r\n      margin-bottom: 8px;\r\n    }\r\n\r\n    .sidebar-menu-list {\r\n      border: none;\r\n\r\n      .el-menu-item {\r\n        height: 40px;\r\n        line-height: 40px;\r\n        margin-bottom: 4px;\r\n        border-radius: 4px;\r\n\r\n        &.active-item {\r\n          background-color: #e6f7ff;\r\n          color: #1890ff;\r\n        }\r\n\r\n        &:hover {\r\n          background-color: #f5f5f5;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.right-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #fff;\r\n\r\n  .main-content {\r\n    flex: 1;\r\n    padding: 20px 24px;\r\n    overflow-y: auto;\r\n  }\r\n}\r\n\r\n.issues-header {\r\n  margin-bottom: 16px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n\r\n  h2 {\r\n    margin: 0;\r\n    font-size: 18px;\r\n    font-weight: 500;\r\n    color: #333;\r\n  }\r\n}\r\n\r\n.issues-list {\r\n  .issue-item {\r\n    display: flex;\r\n    align-items: flex-start;\r\n    padding: 12px 0;\r\n    border-bottom: 1px solid #f0f0f0;\r\n    cursor: pointer;\r\n    transition: background-color 0.2s;\r\n\r\n    &:hover {\r\n      background-color: #f9f9f9;\r\n    }\r\n\r\n    &:last-child {\r\n      border-bottom: none;\r\n    }\r\n\r\n    .issue-indicator {\r\n      width: 6px;\r\n      height: 6px;\r\n      border-radius: 50%;\r\n      margin-right: 8px;\r\n      margin-top: 8px;\r\n      flex-shrink: 0;\r\n    }\r\n\r\n    &.positive .issue-indicator {\r\n      background-color: #52c41a;\r\n    }\r\n\r\n    &.negative .issue-indicator {\r\n      background-color: #ff4d4f;\r\n    }\r\n\r\n    &.neutral .issue-indicator {\r\n      background-color: #faad14;\r\n    }\r\n\r\n    .issue-content {\r\n      flex: 1;\r\n\r\n      .issue-title {\r\n        font-size: 14px;\r\n        color: #333;\r\n        line-height: 1.6;\r\n        margin-bottom: 4px;\r\n        font-weight: normal;\r\n        word-wrap: break-word;\r\n        word-break: break-all;\r\n      }\r\n\r\n      .issue-description {\r\n        font-size: 12px;\r\n        color: #999;\r\n        line-height: 1.5;\r\n        margin-top: 4px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}