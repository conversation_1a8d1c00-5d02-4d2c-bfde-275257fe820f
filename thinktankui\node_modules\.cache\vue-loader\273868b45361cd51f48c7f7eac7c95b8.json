{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\TopNav\\index.vue?vue&type=style&index=0&id=35f3a2c1&lang=scss", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\TopNav\\index.vue", "mtime": 1749104047621}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749104419119}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749104421224}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749104419872}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749104418479}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi50b3BtZW51LWNvbnRhaW5lci5lbC1tZW51LS1ob3Jpem9udGFsID4gLmVsLW1lbnUtaXRlbSB7DQogIGZsb2F0OiBsZWZ0Ow0KICBoZWlnaHQ6IDUwcHggIWltcG9ydGFudDsNCiAgbGluZS1oZWlnaHQ6IDUwcHggIWltcG9ydGFudDsNCiAgY29sb3I6ICM5OTkwOTMgIWltcG9ydGFudDsNCiAgcGFkZGluZzogMCA1cHggIWltcG9ydGFudDsNCiAgbWFyZ2luOiAwIDEwcHggIWltcG9ydGFudDsNCn0NCg0KLnRvcG1lbnUtY29udGFpbmVyLmVsLW1lbnUtLWhvcml6b250YWwgPiAuZWwtbWVudS1pdGVtLmlzLWFjdGl2ZSwgLmVsLW1lbnUtLWhvcml6b250YWwgPiAuZWwtc3VibWVudS5pcy1hY3RpdmUgLmVsLXN1Ym1lbnVfX3RpdGxlIHsNCiAgYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkICN7J3ZhcigtLXRoZW1lKSd9ICFpbXBvcnRhbnQ7DQogIGNvbG9yOiAjMzAzMTMzOw0KfQ0KDQovKiBzdWJtZW51IGl0ZW0gKi8NCi50b3BtZW51LWNvbnRhaW5lci5lbC1tZW51LS1ob3Jpem9udGFsID4gLmVsLXN1Ym1lbnUgLmVsLXN1Ym1lbnVfX3RpdGxlIHsNCiAgZmxvYXQ6IGxlZnQ7DQogIGhlaWdodDogNTBweCAhaW1wb3J0YW50Ow0KICBsaW5lLWhlaWdodDogNTBweCAhaW1wb3J0YW50Ow0KICBjb2xvcjogIzk5OTA5MyAhaW1wb3J0YW50Ow0KICBwYWRkaW5nOiAwIDVweCAhaW1wb3J0YW50Ow0KICBtYXJnaW46IDAgMTBweCAhaW1wb3J0YW50Ow0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/TopNav", "sourcesContent": ["<template>\r\n  <el-menu\r\n    :default-active=\"activeMenu\"\r\n    mode=\"horizontal\"\r\n    @select=\"handleSelect\"\r\n  >\r\n    <template v-for=\"(item, index) in topMenus\">\r\n      <el-menu-item :style=\"{'--theme': theme}\" :index=\"item.path\" :key=\"index\" v-if=\"index < visibleNumber\">\r\n        <svg-icon\r\n        v-if=\"item.meta && item.meta.icon && item.meta.icon !== '#'\"\r\n        :icon-class=\"item.meta.icon\"/>\r\n        {{ item.meta.title }}\r\n      </el-menu-item>\r\n    </template>\r\n\r\n    <!-- 顶部菜单超出数量折叠 -->\r\n    <el-submenu :style=\"{'--theme': theme}\" index=\"more\" :key=\"visibleNumber\" v-if=\"topMenus.length > visibleNumber\">\r\n      <template slot=\"title\">更多菜单</template>\r\n      <template v-for=\"(item, index) in topMenus\">\r\n        <el-menu-item\r\n          :index=\"item.path\"\r\n          :key=\"index\"\r\n          v-if=\"index >= visibleNumber\">\r\n          <svg-icon\r\n            v-if=\"item.meta && item.meta.icon && item.meta.icon !== '#'\"\r\n            :icon-class=\"item.meta.icon\"/>\r\n          {{ item.meta.title }}\r\n        </el-menu-item>\r\n      </template>\r\n    </el-submenu>\r\n  </el-menu>\r\n</template>\r\n\r\n<script>\r\nimport { constantRoutes } from \"@/router\";\r\nimport { isHttp } from \"@/utils/validate\";\r\n\r\n// 隐藏侧边栏路由\r\nconst hideList = ['/index', '/user/profile'];\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 顶部栏初始数\r\n      visibleNumber: 5,\r\n      // 当前激活菜单的 index\r\n      currentIndex: undefined\r\n    };\r\n  },\r\n  computed: {\r\n    theme() {\r\n      return this.$store.state.settings.theme;\r\n    },\r\n    // 顶部显示菜单\r\n    topMenus() {\r\n      let topMenus = [];\r\n      this.routers.map((menu) => {\r\n        if (menu.hidden !== true) {\r\n          // 兼容顶部栏一级菜单内部跳转\r\n          if (menu.path === \"/\") {\r\n            topMenus.push(menu.children[0]);\r\n          } else {\r\n            topMenus.push(menu);\r\n          }\r\n        }\r\n      });\r\n      return topMenus;\r\n    },\r\n    // 所有的路由信息\r\n    routers() {\r\n      return this.$store.state.permission.topbarRouters;\r\n    },\r\n    // 设置子路由\r\n    childrenMenus() {\r\n      var childrenMenus = [];\r\n      this.routers.map((router) => {\r\n        for (var item in router.children) {\r\n          if (router.children[item].parentPath === undefined) {\r\n            if(router.path === \"/\") {\r\n              router.children[item].path = \"/\" + router.children[item].path;\r\n            } else {\r\n              if(!isHttp(router.children[item].path)) {\r\n                router.children[item].path = router.path + \"/\" + router.children[item].path;\r\n              }\r\n            }\r\n            router.children[item].parentPath = router.path;\r\n          }\r\n          childrenMenus.push(router.children[item]);\r\n        }\r\n      });\r\n      return constantRoutes.concat(childrenMenus);\r\n    },\r\n    // 默认激活的菜单\r\n    activeMenu() {\r\n      const path = this.$route.path;\r\n      let activePath = path;\r\n      if (path !== undefined && path.lastIndexOf(\"/\") > 0 && hideList.indexOf(path) === -1) {\r\n        const tmpPath = path.substring(1, path.length);\r\n        if (!this.$route.meta.link) {\r\n          activePath = \"/\" + tmpPath.substring(0, tmpPath.indexOf(\"/\"));\r\n        }\r\n      } else if(!this.$route.children) {\r\n        activePath = path;\r\n        this.$store.dispatch('app/toggleSideBarHide', true);\r\n      }\r\n      this.activeRoutes(activePath);\r\n      return activePath;\r\n    },\r\n  },\r\n  beforeMount() {\r\n    window.addEventListener('resize', this.setVisibleNumber)\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.setVisibleNumber)\r\n  },\r\n  mounted() {\r\n    this.setVisibleNumber();\r\n  },\r\n  methods: {\r\n    // 根据宽度计算设置显示栏数\r\n    setVisibleNumber() {\r\n      const width = document.body.getBoundingClientRect().width / 3;\r\n      this.visibleNumber = parseInt(width / 85);\r\n    },\r\n    // 菜单选择事件\r\n    handleSelect(key, keyPath) {\r\n      this.currentIndex = key;\r\n      const route = this.routers.find(item => item.path === key);\r\n      if (isHttp(key)) {\r\n        // http(s):// 路径新窗口打开\r\n        window.open(key, \"_blank\");\r\n      } else if (!route || !route.children) {\r\n        // 没有子路由路径内部打开\r\n        const routeMenu = this.childrenMenus.find(item => item.path === key);\r\n        if (routeMenu && routeMenu.query) {\r\n          let query = JSON.parse(routeMenu.query);\r\n          this.$router.push({ path: key, query: query });\r\n        } else {\r\n          this.$router.push({ path: key });\r\n        }\r\n        this.$store.dispatch('app/toggleSideBarHide', true);\r\n      } else {\r\n        // 显示左侧联动菜单\r\n        this.activeRoutes(key);\r\n        this.$store.dispatch('app/toggleSideBarHide', false);\r\n      }\r\n    },\r\n    // 当前激活的路由\r\n    activeRoutes(key) {\r\n      var routes = [];\r\n      if (this.childrenMenus && this.childrenMenus.length > 0) {\r\n        this.childrenMenus.map((item) => {\r\n          if (key == item.parentPath || (key == \"index\" && \"\" == item.path)) {\r\n            routes.push(item);\r\n          }\r\n        });\r\n      }\r\n      if(routes.length > 0) {\r\n        this.$store.commit(\"SET_SIDEBAR_ROUTERS\", routes);\r\n      } else {\r\n        this.$store.dispatch('app/toggleSideBarHide', true);\r\n      }\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.topmenu-container.el-menu--horizontal > .el-menu-item {\r\n  float: left;\r\n  height: 50px !important;\r\n  line-height: 50px !important;\r\n  color: #999093 !important;\r\n  padding: 0 5px !important;\r\n  margin: 0 10px !important;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal > .el-menu-item.is-active, .el-menu--horizontal > .el-submenu.is-active .el-submenu__title {\r\n  border-bottom: 2px solid #{'var(--theme)'} !important;\r\n  color: #303133;\r\n}\r\n\r\n/* submenu item */\r\n.topmenu-container.el-menu--horizontal > .el-submenu .el-submenu__title {\r\n  float: left;\r\n  height: 50px !important;\r\n  line-height: 50px !important;\r\n  color: #999093 !important;\r\n  padding: 0 5px !important;\r\n  margin: 0 10px !important;\r\n}\r\n</style>\r\n"]}]}