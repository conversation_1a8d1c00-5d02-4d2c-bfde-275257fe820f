{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\ImagePreview\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\ImagePreview\\index.vue", "mtime": 1749104047621}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBpc0V4dGVybmFsIH0gZnJvbSAiQC91dGlscy92YWxpZGF0ZSI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkltYWdlUHJldmlldyIsDQogIHByb3BzOiB7DQogICAgc3JjOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAiIg0KICAgIH0sDQogICAgd2lkdGg6IHsNCiAgICAgIHR5cGU6IFtOdW1iZXIsIFN0cmluZ10sDQogICAgICBkZWZhdWx0OiAiIg0KICAgIH0sDQogICAgaGVpZ2h0OiB7DQogICAgICB0eXBlOiBbTnVtYmVyLCBTdHJpbmddLA0KICAgICAgZGVmYXVsdDogIiINCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgcmVhbFNyYygpIHsNCiAgICAgIGlmICghdGhpcy5zcmMpIHsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgbGV0IHJlYWxfc3JjID0gdGhpcy5zcmMuc3BsaXQoIiwiKVswXTsNCiAgICAgIGlmIChpc0V4dGVybmFsKHJlYWxfc3JjKSkgew0KICAgICAgICByZXR1cm4gcmVhbF9zcmM7DQogICAgICB9DQogICAgICByZXR1cm4gcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArIHJlYWxfc3JjOw0KICAgIH0sDQogICAgcmVhbFNyY0xpc3QoKSB7DQogICAgICBpZiAoIXRoaXMuc3JjKSB7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIGxldCByZWFsX3NyY19saXN0ID0gdGhpcy5zcmMuc3BsaXQoIiwiKTsNCiAgICAgIGxldCBzcmNMaXN0ID0gW107DQogICAgICByZWFsX3NyY19saXN0LmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgIGlmIChpc0V4dGVybmFsKGl0ZW0pKSB7DQogICAgICAgICAgcmV0dXJuIHNyY0xpc3QucHVzaChpdGVtKTsNCiAgICAgICAgfQ0KICAgICAgICByZXR1cm4gc3JjTGlzdC5wdXNoKHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyBpdGVtKTsNCiAgICAgIH0pOw0KICAgICAgcmV0dXJuIHNyY0xpc3Q7DQogICAgfSwNCiAgICByZWFsV2lkdGgoKSB7DQogICAgICByZXR1cm4gdHlwZW9mIHRoaXMud2lkdGggPT0gInN0cmluZyIgPyB0aGlzLndpZHRoIDogYCR7dGhpcy53aWR0aH1weGA7DQogICAgfSwNCiAgICByZWFsSGVpZ2h0KCkgew0KICAgICAgcmV0dXJuIHR5cGVvZiB0aGlzLmhlaWdodCA9PSAic3RyaW5nIiA/IHRoaXMuaGVpZ2h0IDogYCR7dGhpcy5oZWlnaHR9cHhgOw0KICAgIH0NCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;AAcA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/ImagePreview", "sourcesContent": ["<template>\r\n  <el-image\r\n    :src=\"`${realSrc}`\"\r\n    fit=\"cover\"\r\n    :style=\"`width:${realWidth};height:${realHeight};`\"\r\n    :preview-src-list=\"realSrcList\"\r\n  >\r\n    <div slot=\"error\" class=\"image-slot\">\r\n      <i class=\"el-icon-picture-outline\"></i>\r\n    </div>\r\n  </el-image>\r\n</template>\r\n\r\n<script>\r\nimport { isExternal } from \"@/utils/validate\";\r\n\r\nexport default {\r\n  name: \"ImagePreview\",\r\n  props: {\r\n    src: {\r\n      type: String,\r\n      default: \"\"\r\n    },\r\n    width: {\r\n      type: [Number, String],\r\n      default: \"\"\r\n    },\r\n    height: {\r\n      type: [Number, String],\r\n      default: \"\"\r\n    }\r\n  },\r\n  computed: {\r\n    realSrc() {\r\n      if (!this.src) {\r\n        return;\r\n      }\r\n      let real_src = this.src.split(\",\")[0];\r\n      if (isExternal(real_src)) {\r\n        return real_src;\r\n      }\r\n      return process.env.VUE_APP_BASE_API + real_src;\r\n    },\r\n    realSrcList() {\r\n      if (!this.src) {\r\n        return;\r\n      }\r\n      let real_src_list = this.src.split(\",\");\r\n      let srcList = [];\r\n      real_src_list.forEach(item => {\r\n        if (isExternal(item)) {\r\n          return srcList.push(item);\r\n        }\r\n        return srcList.push(process.env.VUE_APP_BASE_API + item);\r\n      });\r\n      return srcList;\r\n    },\r\n    realWidth() {\r\n      return typeof this.width == \"string\" ? this.width : `${this.width}px`;\r\n    },\r\n    realHeight() {\r\n      return typeof this.height == \"string\" ? this.height : `${this.height}px`;\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.el-image {\r\n  border-radius: 5px;\r\n  background-color: #ebeef5;\r\n  box-shadow: 0 0 5px 1px #ccc;\r\n  ::v-deep .el-image__inner {\r\n    transition: all 0.3s;\r\n    cursor: pointer;\r\n    &:hover {\r\n      transform: scale(1.2);\r\n    }\r\n  }\r\n  ::v-deep .image-slot {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    width: 100%;\r\n    height: 100%;\r\n    color: #909399;\r\n    font-size: 30px;\r\n  }\r\n}\r\n</style>\r\n"]}]}