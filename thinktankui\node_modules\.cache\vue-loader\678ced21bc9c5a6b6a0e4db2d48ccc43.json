{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\system\\user\\profile\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\system\\user\\profile\\index.vue", "mtime": 1749104047647}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgdXNlckF2YXRhciBmcm9tICIuL3VzZXJBdmF0YXIiOw0KaW1wb3J0IHVzZXJJbmZvIGZyb20gIi4vdXNlckluZm8iOw0KaW1wb3J0IHJlc2V0UHdkIGZyb20gIi4vcmVzZXRQd2QiOw0KaW1wb3J0IHsgZ2V0VXNlclByb2ZpbGUgfSBmcm9tICJAL2FwaS9zeXN0ZW0vdXNlciI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIlByb2ZpbGUiLA0KICBjb21wb25lbnRzOiB7IHVzZXJBdmF0YXIsIHVzZXJJbmZvLCByZXNldFB3ZCB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICB1c2VyOiB7fSwNCiAgICAgIHJvbGVHcm91cDoge30sDQogICAgICBwb3N0R3JvdXA6IHt9LA0KICAgICAgYWN0aXZlVGFiOiAidXNlcmluZm8iDQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldFVzZXIoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGdldFVzZXIoKSB7DQogICAgICBnZXRVc2VyUHJvZmlsZSgpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnVzZXIgPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB0aGlzLnJvbGVHcm91cCA9IHJlc3BvbnNlLnJvbGVHcm91cDsNCiAgICAgICAgdGhpcy5wb3N0R3JvdXAgPSByZXNwb25zZS5wb3N0R3JvdXA7DQogICAgICB9KTsNCiAgICB9DQogIH0NCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6DA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/profile", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"6\" :xs=\"24\">\r\n        <el-card class=\"box-card\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>个人信息</span>\r\n          </div>\r\n          <div>\r\n            <div class=\"text-center\">\r\n              <userAvatar />\r\n            </div>\r\n            <ul class=\"list-group list-group-striped\">\r\n              <li class=\"list-group-item\">\r\n                <svg-icon icon-class=\"user\" />用户名称\r\n                <div class=\"pull-right\">{{ user.userName }}</div>\r\n              </li>\r\n              <li class=\"list-group-item\">\r\n                <svg-icon icon-class=\"phone\" />手机号码\r\n                <div class=\"pull-right\">{{ user.phonenumber }}</div>\r\n              </li>\r\n              <li class=\"list-group-item\">\r\n                <svg-icon icon-class=\"email\" />用户邮箱\r\n                <div class=\"pull-right\">{{ user.email }}</div>\r\n              </li>\r\n              <li class=\"list-group-item\">\r\n                <svg-icon icon-class=\"tree\" />所属部门\r\n                <div class=\"pull-right\" v-if=\"user.dept\">{{ user.dept.deptName }} / {{ postGroup }}</div>\r\n              </li>\r\n              <li class=\"list-group-item\">\r\n                <svg-icon icon-class=\"peoples\" />所属角色\r\n                <div class=\"pull-right\">{{ roleGroup }}</div>\r\n              </li>\r\n              <li class=\"list-group-item\">\r\n                <svg-icon icon-class=\"date\" />创建日期\r\n                <div class=\"pull-right\">{{ user.createTime }}</div>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"18\" :xs=\"24\">\r\n        <el-card>\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>基本资料</span>\r\n          </div>\r\n          <el-tabs v-model=\"activeTab\">\r\n            <el-tab-pane label=\"基本资料\" name=\"userinfo\">\r\n              <userInfo :user=\"user\" />\r\n            </el-tab-pane>\r\n            <el-tab-pane label=\"修改密码\" name=\"resetPwd\">\r\n              <resetPwd />\r\n            </el-tab-pane>\r\n          </el-tabs>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport userAvatar from \"./userAvatar\";\r\nimport userInfo from \"./userInfo\";\r\nimport resetPwd from \"./resetPwd\";\r\nimport { getUserProfile } from \"@/api/system/user\";\r\n\r\nexport default {\r\n  name: \"Profile\",\r\n  components: { userAvatar, userInfo, resetPwd },\r\n  data() {\r\n    return {\r\n      user: {},\r\n      roleGroup: {},\r\n      postGroup: {},\r\n      activeTab: \"userinfo\"\r\n    };\r\n  },\r\n  created() {\r\n    this.getUser();\r\n  },\r\n  methods: {\r\n    getUser() {\r\n      getUserProfile().then(response => {\r\n        this.user = response.data;\r\n        this.roleGroup = response.roleGroup;\r\n        this.postGroup = response.postGroup;\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}