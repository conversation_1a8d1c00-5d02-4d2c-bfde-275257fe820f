{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\ImageUpload\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\ImageUpload\\index.vue", "mtime": 1749104047621}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/ImageUpload", "sourcesContent": ["<template>\r\n  <div class=\"component-upload-image\">\r\n    <el-upload\r\n      multiple\r\n      :action=\"uploadImgUrl\"\r\n      list-type=\"picture-card\"\r\n      :on-success=\"handleUploadSuccess\"\r\n      :before-upload=\"handleBeforeUpload\"\r\n      :limit=\"limit\"\r\n      :on-error=\"handleUploadError\"\r\n      :on-exceed=\"handleExceed\"\r\n      ref=\"imageUpload\"\r\n      :on-remove=\"handleDelete\"\r\n      :show-file-list=\"true\"\r\n      :headers=\"headers\"\r\n      :file-list=\"fileList\"\r\n      :on-preview=\"handlePictureCardPreview\"\r\n      :class=\"{hide: this.fileList.length >= this.limit}\"\r\n    >\r\n      <i class=\"el-icon-plus\"></i>\r\n    </el-upload>\r\n\r\n    <!-- 上传提示 -->\r\n    <div class=\"el-upload__tip\" slot=\"tip\" v-if=\"showTip\">\r\n      请上传\r\n      <template v-if=\"fileSize\"> 大小不超过 <b style=\"color: #f56c6c\">{{ fileSize }}MB</b> </template>\r\n      <template v-if=\"fileType\"> 格式为 <b style=\"color: #f56c6c\">{{ fileType.join(\"/\") }}</b> </template>\r\n      的文件\r\n    </div>\r\n\r\n    <el-dialog\r\n      :visible.sync=\"dialogVisible\"\r\n      title=\"预览\"\r\n      width=\"800\"\r\n      append-to-body\r\n    >\r\n      <img\r\n        :src=\"dialogImageUrl\"\r\n        style=\"display: block; max-width: 100%; margin: 0 auto\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getToken } from \"@/utils/auth\";\r\nimport { isExternal } from \"@/utils/validate\";\r\n\r\nexport default {\r\n  props: {\r\n    value: [String, Object, Array],\r\n    // 图片数量限制\r\n    limit: {\r\n      type: Number,\r\n      default: 5,\r\n    },\r\n    // 大小限制(MB)\r\n    fileSize: {\r\n       type: Number,\r\n      default: 5,\r\n    },\r\n    // 文件类型, 例如['png', 'jpg', 'jpeg']\r\n    fileType: {\r\n      type: Array,\r\n      default: () => [\"png\", \"jpg\", \"jpeg\"],\r\n    },\r\n    // 是否显示提示\r\n    isShowTip: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      number: 0,\r\n      uploadList: [],\r\n      dialogImageUrl: \"\",\r\n      dialogVisible: false,\r\n      hideUpload: false,\r\n      baseUrl: process.env.VUE_APP_BASE_API,\r\n      uploadImgUrl: process.env.VUE_APP_BASE_API + \"/common/upload\", // 上传的图片服务器地址\r\n      headers: {\r\n        Authorization: \"Bearer \" + getToken(),\r\n      },\r\n      fileList: []\r\n    };\r\n  },\r\n  watch: {\r\n    value: {\r\n      handler(val) {\r\n        if (val) {\r\n          // 首先将值转为数组\r\n          const list = Array.isArray(val) ? val : this.value.split(',');\r\n          // 然后将数组转为对象数组\r\n          this.fileList = list.map(item => {\r\n            if (typeof item === \"string\") {\r\n              if (item.indexOf(this.baseUrl) === -1 && !isExternal(item)) {\r\n                  item = { name: this.baseUrl + item, url: this.baseUrl + item };\r\n              } else {\r\n                  item = { name: item, url: item };\r\n              }\r\n            }\r\n            return item;\r\n          });\r\n        } else {\r\n          this.fileList = [];\r\n          return [];\r\n        }\r\n      },\r\n      deep: true,\r\n      immediate: true\r\n    }\r\n  },\r\n  computed: {\r\n    // 是否显示提示\r\n    showTip() {\r\n      return this.isShowTip && (this.fileType || this.fileSize);\r\n    },\r\n  },\r\n  methods: {\r\n    // 上传前loading加载\r\n    handleBeforeUpload(file) {\r\n      let isImg = false;\r\n      if (this.fileType.length) {\r\n        let fileExtension = \"\";\r\n        if (file.name.lastIndexOf(\".\") > -1) {\r\n          fileExtension = file.name.slice(file.name.lastIndexOf(\".\") + 1);\r\n        }\r\n        isImg = this.fileType.some(type => {\r\n          if (file.type.indexOf(type) > -1) return true;\r\n          if (fileExtension && fileExtension.indexOf(type) > -1) return true;\r\n          return false;\r\n        });\r\n      } else {\r\n        isImg = file.type.indexOf(\"image\") > -1;\r\n      }\r\n\r\n      if (!isImg) {\r\n        this.$modal.msgError(`文件格式不正确，请上传${this.fileType.join(\"/\")}图片格式文件!`);\r\n        return false;\r\n      }\r\n      if (file.name.includes(',')) {\r\n        this.$modal.msgError('文件名不正确，不能包含英文逗号!');\r\n        return false;\r\n      }\r\n      if (this.fileSize) {\r\n        const isLt = file.size / 1024 / 1024 < this.fileSize;\r\n        if (!isLt) {\r\n          this.$modal.msgError(`上传头像图片大小不能超过 ${this.fileSize} MB!`);\r\n          return false;\r\n        }\r\n      }\r\n      this.$modal.loading(\"正在上传图片，请稍候...\");\r\n      this.number++;\r\n    },\r\n    // 文件个数超出\r\n    handleExceed() {\r\n      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`);\r\n    },\r\n    // 上传成功回调\r\n    handleUploadSuccess(res, file) {\r\n      if (res.code === 200) {\r\n        this.uploadList.push({ name: res.fileName, url: res.fileName });\r\n        this.uploadedSuccessfully();\r\n      } else {\r\n        this.number--;\r\n        this.$modal.closeLoading();\r\n        this.$modal.msgError(res.msg);\r\n        this.$refs.imageUpload.handleRemove(file);\r\n        this.uploadedSuccessfully();\r\n      }\r\n    },\r\n    // 删除图片\r\n    handleDelete(file) {\r\n      const findex = this.fileList.map(f => f.name).indexOf(file.name);\r\n      if (findex > -1) {\r\n        this.fileList.splice(findex, 1);\r\n        this.$emit(\"input\", this.listToString(this.fileList));\r\n      }\r\n    },\r\n    // 上传失败\r\n    handleUploadError() {\r\n      this.$modal.msgError(\"上传图片失败，请重试\");\r\n      this.$modal.closeLoading();\r\n    },\r\n    // 上传结束处理\r\n    uploadedSuccessfully() {\r\n      if (this.number > 0 && this.uploadList.length === this.number) {\r\n        this.fileList = this.fileList.concat(this.uploadList);\r\n        this.uploadList = [];\r\n        this.number = 0;\r\n        this.$emit(\"input\", this.listToString(this.fileList));\r\n        this.$modal.closeLoading();\r\n      }\r\n    },\r\n    // 预览\r\n    handlePictureCardPreview(file) {\r\n      this.dialogImageUrl = file.url;\r\n      this.dialogVisible = true;\r\n    },\r\n    // 对象转成指定字符串分隔\r\n    listToString(list, separator) {\r\n      let strs = \"\";\r\n      separator = separator || \",\";\r\n      for (let i in list) {\r\n        if (list[i].url) {\r\n          strs += list[i].url.replace(this.baseUrl, \"\") + separator;\r\n        }\r\n      }\r\n      return strs != '' ? strs.substr(0, strs.length - 1) : '';\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n// .el-upload--picture-card 控制加号部分\r\n::v-deep.hide .el-upload--picture-card {\r\n    display: none;\r\n}\r\n// 去掉动画效果\r\n::v-deep .el-list-enter-active,\r\n::v-deep .el-list-leave-active {\r\n    transition: all 0s;\r\n}\r\n\r\n::v-deep .el-list-enter, .el-list-leave-active {\r\n  opacity: 0;\r\n  transform: translateY(0);\r\n}\r\n</style>\r\n\r\n"]}]}