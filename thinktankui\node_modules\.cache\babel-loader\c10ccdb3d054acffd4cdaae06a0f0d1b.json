{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\font.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\font.js", "mtime": 1749104421624}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLkZvbnRTdHlsZSA9IGV4cG9ydHMuRm9udENsYXNzID0gdm9pZCAwOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLmV4ZWMuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5yZXBsYWNlLmpzIik7CnZhciBfY2xhc3NDYWxsQ2hlY2syID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvY2xhc3NDYWxsQ2hlY2suanMiKSk7CnZhciBfY3JlYXRlQ2xhc3MyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvY3JlYXRlQ2xhc3MuanMiKSk7CnZhciBfY2FsbFN1cGVyMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovdGhpbmt0YW5rL3RoaW5rdGFua3VpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2NhbGxTdXBlci5qcyIpKTsKdmFyIF9zdXBlclByb3BHZXQyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvc3VwZXJQcm9wR2V0LmpzIikpOwp2YXIgX2luaGVyaXRzMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovdGhpbmt0YW5rL3RoaW5rdGFua3VpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2luaGVyaXRzLmpzIikpOwp2YXIgX3BhcmNobWVudCA9IHJlcXVpcmUoInBhcmNobWVudCIpOwp2YXIgY29uZmlnID0gewogIHNjb3BlOiBfcGFyY2htZW50LlNjb3BlLklOTElORSwKICB3aGl0ZWxpc3Q6IFsnc2VyaWYnLCAnbW9ub3NwYWNlJ10KfTsKdmFyIEZvbnRDbGFzcyA9IGV4cG9ydHMuRm9udENsYXNzID0gbmV3IF9wYXJjaG1lbnQuQ2xhc3NBdHRyaWJ1dG9yKCdmb250JywgJ3FsLWZvbnQnLCBjb25maWcpOwp2YXIgRm9udFN0eWxlQXR0cmlidXRvciA9IC8qI19fUFVSRV9fKi9mdW5jdGlvbiAoX1N0eWxlQXR0cmlidXRvcikgewogIGZ1bmN0aW9uIEZvbnRTdHlsZUF0dHJpYnV0b3IoKSB7CiAgICAoMCwgX2NsYXNzQ2FsbENoZWNrMi5kZWZhdWx0KSh0aGlzLCBGb250U3R5bGVBdHRyaWJ1dG9yKTsKICAgIHJldHVybiAoMCwgX2NhbGxTdXBlcjIuZGVmYXVsdCkodGhpcywgRm9udFN0eWxlQXR0cmlidXRvciwgYXJndW1lbnRzKTsKICB9CiAgKDAsIF9pbmhlcml0czIuZGVmYXVsdCkoRm9udFN0eWxlQXR0cmlidXRvciwgX1N0eWxlQXR0cmlidXRvcik7CiAgcmV0dXJuICgwLCBfY3JlYXRlQ2xhc3MyLmRlZmF1bHQpKEZvbnRTdHlsZUF0dHJpYnV0b3IsIFt7CiAgICBrZXk6ICJ2YWx1ZSIsCiAgICB2YWx1ZTogZnVuY3Rpb24gdmFsdWUobm9kZSkgewogICAgICByZXR1cm4gKDAsIF9zdXBlclByb3BHZXQyLmRlZmF1bHQpKEZvbnRTdHlsZUF0dHJpYnV0b3IsICJ2YWx1ZSIsIHRoaXMsIDMpKFtub2RlXSkucmVwbGFjZSgvWyInXS9nLCAnJyk7CiAgICB9CiAgfV0pOwp9KF9wYXJjaG1lbnQuU3R5bGVBdHRyaWJ1dG9yKTsKdmFyIEZvbnRTdHlsZSA9IGV4cG9ydHMuRm9udFN0eWxlID0gbmV3IEZvbnRTdHlsZUF0dHJpYnV0b3IoJ2ZvbnQnLCAnZm9udC1mYW1pbHknLCBjb25maWcpOw=="}, {"version": 3, "names": ["_parchment", "require", "config", "scope", "<PERSON><PERSON>", "INLINE", "whitelist", "FontClass", "exports", "ClassAttributor", "FontStyleAttributor", "_StyleAttributor", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "key", "value", "node", "_superPropGet2", "replace", "StyleAttributor", "FontStyle"], "sources": ["../../src/formats/font.ts"], "sourcesContent": ["import { ClassAttributor, Scope, StyleAttributor } from 'parchment';\n\nconst config = {\n  scope: Scope.INLINE,\n  whitelist: ['serif', 'monospace'],\n};\n\nconst FontClass = new ClassAttributor('font', 'ql-font', config);\n\nclass FontStyleAttributor extends StyleAttributor {\n  value(node: HTMLElement) {\n    return super.value(node).replace(/[\"']/g, '');\n  }\n}\n\nconst FontStyle = new FontStyleAttributor('font', 'font-family', config);\n\nexport { FontStyle, FontClass };\n"], "mappings": ";;;;;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAEA,IAAMC,MAAM,GAAG;EACbC,KAAK,EAAEC,gBAAK,CAACC,MAAM;EACnBC,SAAS,EAAE,CAAC,OAAO,EAAE,WAAW;AAClC,CAAC;AAED,IAAMC,SAAS,GAAAC,OAAA,CAAAD,SAAA,GAAG,IAAIE,0BAAe,CAAC,MAAM,EAAE,SAAS,EAAEP,MAAM,CAAC;AAAA,IAE1DQ,mBAAmB,0BAAAC,gBAAA;EAAA,SAAAD,oBAAA;IAAA,IAAAE,gBAAA,CAAAC,OAAA,QAAAH,mBAAA;IAAA,WAAAI,WAAA,CAAAD,OAAA,QAAAH,mBAAA,EAAAK,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAH,mBAAA,EAAAC,gBAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAH,mBAAA;IAAAQ,GAAA;IAAAC,KAAA,EACvB,SAAAA,KAAKA,CAACC,IAAiB,EAAE;MACvB,OAAO,IAAAC,cAAA,CAAAR,OAAA,EAAAH,mBAAA,qBAAYU,IAAI,GAAEE,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;IAC/C;EAAA;AAAA,EAHgCC,0BAAe;AAMjD,IAAMC,SAAS,GAAAhB,OAAA,CAAAgB,SAAA,GAAG,IAAId,mBAAmB,CAAC,MAAM,EAAE,aAAa,EAAER,MAAM,CAAC", "ignoreList": []}]}