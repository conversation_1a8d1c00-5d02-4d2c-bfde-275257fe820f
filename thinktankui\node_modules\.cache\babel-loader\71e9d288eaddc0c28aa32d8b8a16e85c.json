{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\account\\user-management.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\account\\user-management.vue", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "userList", "userId", "userName", "phoneNumber", "email", "password", "maxVisits", "role", "department", "status", "total", "currentPage", "pageSize", "dialogVisible", "dialogTitle", "userForm", "userFormRules", "required", "message", "trigger", "min", "max", "pattern", "type", "methods", "handleCurrentChange", "val", "handleAdd", "handleEdit", "row", "JSON", "parse", "stringify", "handleDelete", "_this", "$confirm", "concat", "confirmButtonText", "cancelButtonText", "then", "filter", "item", "length", "$message", "success", "catch", "info", "handleStatusChange", "submitForm", "_this2", "$refs", "validate", "valid", "index", "findIndex", "splice", "push"], "sources": ["src/views/account/user-management.vue"], "sourcesContent": ["<template>\r\n  <div class=\"user-management-container\">\r\n    <div class=\"user-management-header\">\r\n      <div class=\"title\">用户管理</div>\r\n      <div class=\"actions\">\r\n        <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"handleAdd\">添加用户</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"user-management-content\">\r\n      <el-table\r\n        :data=\"userList\"\r\n        style=\"width: 100%\"\r\n        border\r\n        stripe\r\n        :header-cell-style=\"{background:'#f5f7fa', color:'#606266'}\"\r\n      >\r\n        <el-table-column\r\n          prop=\"userId\"\r\n          label=\"用户ID\"\r\n          width=\"100\"\r\n          align=\"center\"\r\n        />\r\n        <el-table-column\r\n          prop=\"userName\"\r\n          label=\"用户名称\"\r\n          width=\"150\"\r\n          align=\"center\"\r\n        />\r\n        <el-table-column\r\n          prop=\"phoneNumber\"\r\n          label=\"手机号\"\r\n          width=\"150\"\r\n          align=\"center\"\r\n        />\r\n        <el-table-column\r\n          prop=\"email\"\r\n          label=\"邮箱\"\r\n          width=\"180\"\r\n          align=\"center\"\r\n        />\r\n        <el-table-column\r\n          prop=\"maxVisits\"\r\n          label=\"访问权限\"\r\n          width=\"100\"\r\n          align=\"center\"\r\n        />\r\n        <el-table-column\r\n          prop=\"status\"\r\n          label=\"状态\"\r\n          width=\"100\"\r\n          align=\"center\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <el-switch\r\n              v-model=\"scope.row.status\"\r\n              active-color=\"#13ce66\"\r\n              inactive-color=\"#ff4949\"\r\n              :active-value=\"1\"\r\n              :inactive-value=\"0\"\r\n              @change=\"handleStatusChange(scope.row)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"操作\"\r\n          align=\"center\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"handleEdit(scope.row)\"\r\n            >编辑</el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-delete\"\r\n              class=\"delete-btn\"\r\n              @click=\"handleDelete(scope.row)\"\r\n            >删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          background\r\n          layout=\"prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          :current-page.sync=\"currentPage\"\r\n          :page-size=\"pageSize\"\r\n          @current-change=\"handleCurrentChange\"\r\n        />\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 添加用户对话框 -->\r\n    <el-dialog title=\"添加用户\" :visible.sync=\"dialogVisible\" width=\"500px\" center class=\"user-dialog\">\r\n      <el-form ref=\"userForm\" :model=\"userForm\" :rules=\"userFormRules\" label-width=\"100px\">\r\n        <el-form-item label=\"用户名称\" prop=\"userName\">\r\n          <div class=\"required-mark\">*</div>\r\n          <el-input v-model=\"userForm.userName\" placeholder=\"请输入用户名/手机号/邮箱等登录名\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号码\" prop=\"phoneNumber\">\r\n          <div class=\"required-mark\">*</div>\r\n          <el-input v-model=\"userForm.phoneNumber\" placeholder=\"输入手机号码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱\" prop=\"email\">\r\n          <div class=\"required-mark\">*</div>\r\n          <el-input v-model=\"userForm.email\" placeholder=\"输入邮箱\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"密码\" prop=\"password\">\r\n          <div class=\"required-mark\">*</div>\r\n          <el-input v-model=\"userForm.password\" type=\"password\" placeholder=\"8-16 密码必须同时包含数字、大小写字母和符号\" show-password />\r\n        </el-form-item>\r\n        <el-form-item label=\"最大访问量\" prop=\"maxVisits\">\r\n          <el-input v-model=\"userForm.maxVisits\" placeholder=\"0\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\">\r\n          <el-switch\r\n            v-model=\"userForm.status\"\r\n            active-color=\"#13ce66\"\r\n            inactive-color=\"#ff4949\"\r\n            :active-value=\"1\"\r\n            :inactive-value=\"0\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\" class=\"confirm-btn\">确定</el-button>\r\n        <el-button @click=\"dialogVisible = false\" class=\"cancel-btn\">取消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"UserManagement\",\r\n  data() {\r\n    return {\r\n      // 用户列表\r\n      userList: [\r\n        {\r\n          userId: 1,\r\n          userName: \"admin\",\r\n          phoneNumber: \"13800138000\",\r\n          email: \"<EMAIL>\",\r\n          password: \"******\",\r\n          maxVisits: 1000,\r\n          role: \"管理员\",\r\n          department: \"技术部\",\r\n          status: 1\r\n        },\r\n        {\r\n          userId: 2,\r\n          userName: \"user1\",\r\n          phoneNumber: \"13800138001\",\r\n          email: \"<EMAIL>\",\r\n          password: \"******\",\r\n          maxVisits: 500,\r\n          role: \"普通用户\",\r\n          department: \"市场部\",\r\n          status: 1\r\n        },\r\n        {\r\n          userId: 3,\r\n          userName: \"user2\",\r\n          phoneNumber: \"13800138002\",\r\n          email: \"<EMAIL>\",\r\n          password: \"******\",\r\n          maxVisits: 300,\r\n          role: \"普通用户\",\r\n          department: \"销售部\",\r\n          status: 0\r\n        }\r\n      ],\r\n      // 分页相关\r\n      total: 3,\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      // 对话框相关\r\n      dialogVisible: false,\r\n      dialogTitle: \"添加用户\",\r\n      userForm: {\r\n        userId: null,\r\n        userName: \"\",\r\n        phoneNumber: \"\",\r\n        email: \"\",\r\n        password: \"\",\r\n        maxVisits: 0,\r\n        role: \"\",\r\n        department: \"\",\r\n        status: 1\r\n      },\r\n      userFormRules: {\r\n        userName: [\r\n          { required: true, message: \"请输入用户名称\", trigger: \"blur\" },\r\n          { min: 3, max: 20, message: \"长度在 3 到 20 个字符\", trigger: \"blur\" }\r\n        ],\r\n        phoneNumber: [\r\n          { required: true, message: \"请输入手机号码\", trigger: \"blur\" },\r\n          { pattern: /^1[3-9]\\d{9}$/, message: \"请输入正确的手机号码\", trigger: \"blur\" }\r\n        ],\r\n        email: [\r\n          { required: true, message: \"请输入邮箱\", trigger: \"blur\" },\r\n          { type: \"email\", message: \"请输入正确的邮箱地址\", trigger: \"blur\" }\r\n        ],\r\n        password: [\r\n          { required: true, message: \"请输入密码\", trigger: \"blur\" },\r\n          { min: 8, max: 16, message: \"长度在 8 到 16 个字符\", trigger: \"blur\" },\r\n          {\r\n            pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[^\\da-zA-Z]).{8,16}$/,\r\n            message: \"密码必须同时包含数字、大小写字母和符号\",\r\n            trigger: \"blur\"\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  methods: {\r\n    // 处理页码变化\r\n    handleCurrentChange(val) {\r\n      this.currentPage = val;\r\n      // 实际应用中这里需要调用接口获取对应页的数据\r\n    },\r\n    // 添加用户\r\n    handleAdd() {\r\n      this.dialogTitle = \"添加用户\";\r\n      this.userForm = {\r\n        userId: null,\r\n        userName: \"\",\r\n        phoneNumber: \"\",\r\n        email: \"\",\r\n        password: \"\",\r\n        maxVisits: 0,\r\n        role: \"\",\r\n        department: \"\",\r\n        status: 1\r\n      };\r\n      this.dialogVisible = true;\r\n    },\r\n    // 编辑用户\r\n    handleEdit(row) {\r\n      this.dialogTitle = \"编辑用户\";\r\n      this.userForm = JSON.parse(JSON.stringify(row));\r\n      this.dialogVisible = true;\r\n    },\r\n    // 删除用户\r\n    handleDelete(row) {\r\n      this.$confirm(`确定要删除用户\"${row.userName}\"吗？`, \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        // 实际应用中这里需要调用接口删除用户\r\n        this.userList = this.userList.filter(item => item.userId !== row.userId);\r\n        this.total = this.userList.length;\r\n        this.$message.success(\"删除成功\");\r\n      }).catch(() => {\r\n        this.$message.info(\"已取消删除\");\r\n      });\r\n    },\r\n    // 修改用户状态\r\n    handleStatusChange(row) {\r\n      // 实际应用中这里需要调用接口修改用户状态\r\n      this.$message.success(`用户\"${row.userName}\"状态已${row.status === 1 ? '启用' : '禁用'}`);\r\n    },\r\n    // 提交表单\r\n    submitForm() {\r\n      this.$refs.userForm.validate(valid => {\r\n        if (valid) {\r\n          if (this.userForm.userId) {\r\n            // 编辑用户\r\n            const index = this.userList.findIndex(item => item.userId === this.userForm.userId);\r\n            if (index !== -1) {\r\n              this.userList.splice(index, 1, this.userForm);\r\n              this.$message.success(\"修改成功\");\r\n            }\r\n          } else {\r\n            // 添加用户\r\n            this.userForm.userId = this.userList.length + 1;\r\n            this.userList.push(this.userForm);\r\n            this.total = this.userList.length;\r\n            this.$message.success(\"添加成功\");\r\n          }\r\n          this.dialogVisible = false;\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.user-management-container {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.user-management-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n\r\n  .title {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n.user-management-content {\r\n  .el-table {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .delete-btn {\r\n    color: #f56c6c;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 20px;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: center;\r\n\r\n  .confirm-btn {\r\n    background-color: #409EFF;\r\n    border-color: #409EFF;\r\n    margin-right: 10px;\r\n  }\r\n\r\n  .cancel-btn {\r\n    color: #606266;\r\n    background-color: #fff;\r\n    border-color: #dcdfe6;\r\n  }\r\n}\r\n\r\n.user-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e4e7ed;\r\n\r\n    .el-dialog__title {\r\n      font-size: 16px;\r\n      font-weight: 500;\r\n    }\r\n\r\n    .el-dialog__headerbtn {\r\n      top: 15px;\r\n    }\r\n  }\r\n\r\n  .el-dialog__body {\r\n    padding: 20px;\r\n  }\r\n\r\n  .el-form-item {\r\n    position: relative;\r\n    margin-bottom: 20px;\r\n\r\n    .required-mark {\r\n      position: absolute;\r\n      left: -10px;\r\n      top: 10px;\r\n      color: #f56c6c;\r\n      font-size: 14px;\r\n    }\r\n\r\n    .el-form-item__label {\r\n      color: #606266;\r\n      font-weight: normal;\r\n    }\r\n\r\n    .el-input__inner {\r\n      border-radius: 3px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCA2IA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,QAAA,GACA;QACAC,MAAA;QACAC,QAAA;QACAC,WAAA;QACAC,KAAA;QACAC,QAAA;QACAC,SAAA;QACAC,IAAA;QACAC,UAAA;QACAC,MAAA;MACA,GACA;QACAR,MAAA;QACAC,QAAA;QACAC,WAAA;QACAC,KAAA;QACAC,QAAA;QACAC,SAAA;QACAC,IAAA;QACAC,UAAA;QACAC,MAAA;MACA,GACA;QACAR,MAAA;QACAC,QAAA;QACAC,WAAA;QACAC,KAAA;QACAC,QAAA;QACAC,SAAA;QACAC,IAAA;QACAC,UAAA;QACAC,MAAA;MACA,EACA;MACA;MACAC,KAAA;MACAC,WAAA;MACAC,QAAA;MACA;MACAC,aAAA;MACAC,WAAA;MACAC,QAAA;QACAd,MAAA;QACAC,QAAA;QACAC,WAAA;QACAC,KAAA;QACAC,QAAA;QACAC,SAAA;QACAC,IAAA;QACAC,UAAA;QACAC,MAAA;MACA;MACAO,aAAA;QACAd,QAAA,GACA;UAAAe,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAhB,WAAA,GACA;UAAAc,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAG,OAAA;UAAAJ,OAAA;UAAAC,OAAA;QAAA,EACA;QACAf,KAAA,GACA;UAAAa,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAI,IAAA;UAAAL,OAAA;UAAAC,OAAA;QAAA,EACA;QACAd,QAAA,GACA;UAAAY,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,GACA;UACAG,OAAA;UACAJ,OAAA;UACAC,OAAA;QACA;MAEA;IACA;EACA;EACAK,OAAA;IACA;IACAC,mBAAA,WAAAA,oBAAAC,GAAA;MACA,KAAAf,WAAA,GAAAe,GAAA;MACA;IACA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA,KAAAb,WAAA;MACA,KAAAC,QAAA;QACAd,MAAA;QACAC,QAAA;QACAC,WAAA;QACAC,KAAA;QACAC,QAAA;QACAC,SAAA;QACAC,IAAA;QACAC,UAAA;QACAC,MAAA;MACA;MACA,KAAAI,aAAA;IACA;IACA;IACAe,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAAf,WAAA;MACA,KAAAC,QAAA,GAAAe,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAH,GAAA;MACA,KAAAhB,aAAA;IACA;IACA;IACAoB,YAAA,WAAAA,aAAAJ,GAAA;MAAA,IAAAK,KAAA;MACA,KAAAC,QAAA,gDAAAC,MAAA,CAAAP,GAAA,CAAA3B,QAAA;QACAmC,iBAAA;QACAC,gBAAA;QACAf,IAAA;MACA,GAAAgB,IAAA;QACA;QACAL,KAAA,CAAAlC,QAAA,GAAAkC,KAAA,CAAAlC,QAAA,CAAAwC,MAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAxC,MAAA,KAAA4B,GAAA,CAAA5B,MAAA;QAAA;QACAiC,KAAA,CAAAxB,KAAA,GAAAwB,KAAA,CAAAlC,QAAA,CAAA0C,MAAA;QACAR,KAAA,CAAAS,QAAA,CAAAC,OAAA;MACA,GAAAC,KAAA;QACAX,KAAA,CAAAS,QAAA,CAAAG,IAAA;MACA;IACA;IACA;IACAC,kBAAA,WAAAA,mBAAAlB,GAAA;MACA;MACA,KAAAc,QAAA,CAAAC,OAAA,kBAAAR,MAAA,CAAAP,GAAA,CAAA3B,QAAA,0BAAAkC,MAAA,CAAAP,GAAA,CAAApB,MAAA;IACA;IACA;IACAuC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAAnC,QAAA,CAAAoC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAlC,QAAA,CAAAd,MAAA;YACA;YACA,IAAAoD,KAAA,GAAAJ,MAAA,CAAAjD,QAAA,CAAAsD,SAAA,WAAAb,IAAA;cAAA,OAAAA,IAAA,CAAAxC,MAAA,KAAAgD,MAAA,CAAAlC,QAAA,CAAAd,MAAA;YAAA;YACA,IAAAoD,KAAA;cACAJ,MAAA,CAAAjD,QAAA,CAAAuD,MAAA,CAAAF,KAAA,KAAAJ,MAAA,CAAAlC,QAAA;cACAkC,MAAA,CAAAN,QAAA,CAAAC,OAAA;YACA;UACA;YACA;YACAK,MAAA,CAAAlC,QAAA,CAAAd,MAAA,GAAAgD,MAAA,CAAAjD,QAAA,CAAA0C,MAAA;YACAO,MAAA,CAAAjD,QAAA,CAAAwD,IAAA,CAAAP,MAAA,CAAAlC,QAAA;YACAkC,MAAA,CAAAvC,KAAA,GAAAuC,MAAA,CAAAjD,QAAA,CAAA0C,MAAA;YACAO,MAAA,CAAAN,QAAA,CAAAC,OAAA;UACA;UACAK,MAAA,CAAApC,aAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}