{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\hot-events\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\hot-events\\index.vue", "mtime": 1749104047640}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "activeTab", "hotEventsList", "title", "heat", "methods", "handleTabClick", "tab", "console", "log"], "sources": ["src/views/hot-events/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"hot-events-container\">\r\n    <!-- 顶部标签页 -->\r\n    <div class=\"tabs-header\">\r\n      <el-tabs v-model=\"activeTab\" @tab-click=\"handleTabClick\">\r\n        <el-tab-pane label=\"热点地图\" name=\"map\"></el-tab-pane>\r\n        <el-tab-pane label=\"三方热点\" name=\"third-party\"></el-tab-pane>\r\n        <el-tab-pane label=\"七日热点\" name=\"seven-days\"></el-tab-pane>\r\n        <el-tab-pane label=\"平台\" name=\"platform\"></el-tab-pane>\r\n        <el-tab-pane label=\"主题\" name=\"theme\"></el-tab-pane>\r\n      </el-tabs>\r\n    </div>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <div class=\"main-content\">\r\n      <!-- 左侧地图区域 -->\r\n      <div class=\"map-section\">\r\n        <div class=\"map-container\">\r\n          <div class=\"china-map\">\r\n            <div class=\"map-placeholder\">\r\n              <i class=\"el-icon-location\"></i>\r\n              <p>中国热点地图</p>\r\n              <p class=\"map-note\">地图功能开发中...</p>\r\n            </div>\r\n          </div>\r\n          <!-- 地图图例 -->\r\n          <div class=\"map-legend\">\r\n            <div class=\"legend-item\">\r\n              <span class=\"legend-color\" style=\"background: #8B0000;\"></span>\r\n              <span>≥ 500000</span>\r\n            </div>\r\n            <div class=\"legend-item\">\r\n              <span class=\"legend-color\" style=\"background: #CD5C5C;\"></span>\r\n              <span>100000 - 500000</span>\r\n            </div>\r\n            <div class=\"legend-item\">\r\n              <span class=\"legend-color\" style=\"background: #F08080;\"></span>\r\n              <span>50000 - 100000</span>\r\n            </div>\r\n            <div class=\"legend-item\">\r\n              <span class=\"legend-color\" style=\"background: #FFA07A;\"></span>\r\n              <span>10000 - 50000</span>\r\n            </div>\r\n            <div class=\"legend-item\">\r\n              <span class=\"legend-color\" style=\"background: #FFE4E1;\"></span>\r\n              <span>0 - 10000</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧热点列表 -->\r\n      <div class=\"hotlist-section\">\r\n        <div class=\"hotlist-header\">\r\n          <h3>昨日热点事件TOP10</h3>\r\n        </div>\r\n        <div class=\"hotlist-content\">\r\n          <div\r\n            v-for=\"(item, index) in hotEventsList\"\r\n            :key=\"index\"\r\n            class=\"hot-item\"\r\n            :class=\"{ 'top-three': index < 3 }\"\r\n          >\r\n            <div class=\"rank-number\">{{ index + 1 }}</div>\r\n            <div class=\"event-content\">\r\n              <div class=\"event-title\">{{ item.title }}</div>\r\n              <div class=\"event-heat\">{{ item.heat }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 底部详情区域 -->\r\n    <div class=\"details-section\">\r\n      <div class=\"detail-card\">\r\n        <div class=\"card-header\">\r\n          <h4>全国热点</h4>\r\n        </div>\r\n        <div class=\"card-content\">\r\n          <div class=\"event-item\">\r\n            <div class=\"event-icon\">热</div>\r\n            <div class=\"event-info\">\r\n              <div class=\"event-title\">商务部回应欧盟对华人造板反倾销立案调查：中方对此表示强烈不满和坚决反对</div>\r\n              <div class=\"event-time\">2023-04-29</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"event-description\">\r\n            针对欧盟对华人造板反倾销立案调查，中方表示强烈不满和坚决反对。商务部表示，中国人造板产业发展健康，出口产品质量优良，价格公平合理。\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"detail-card\">\r\n        <div class=\"card-header\">\r\n          <h4>北京市热点</h4>\r\n          <el-button type=\"text\" size=\"small\">查看详情</el-button>\r\n        </div>\r\n        <div class=\"card-content\">\r\n          <div class=\"event-item\">\r\n            <div class=\"event-icon\">京</div>\r\n            <div class=\"event-info\">\r\n              <div class=\"event-title\">北京海淀法院：\"老赖\"被限，法院强制执行，追回欠款</div>\r\n              <div class=\"event-time\">2023-04-29</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"event-description\">\r\n            北京海淀法院近日成功执行一起债务纠纷案件，通过限制被执行人消费等强制措施，成功追回欠款，维护了当事人合法权益。\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'HotEvents',\r\n  data() {\r\n    return {\r\n      activeTab: 'map',\r\n      hotEventsList: [\r\n        { title: '我军在台海进行实战化演练，展现维护国家主权决心', heat: '20196' },\r\n        { title: '马云现身，何时归来？', heat: '19640' },\r\n        { title: '全国疫情（2023年4月29日）', heat: '12840' },\r\n        { title: '俄军打击乌克兰目标，乌方称遭受重大损失', heat: '11480' },\r\n        { title: '今天一定要重点关注的', heat: '11100' },\r\n        { title: '到底是什么原因呢！', heat: '9950' },\r\n        { title: '一条微博引发上千万网友热议', heat: '9740' },\r\n        { title: '新，俄乌冲突最新进展：乌克兰称击退俄军一次攻击', heat: '9410' },\r\n        { title: '让我们共同关注这个事件，事关每个人', heat: '8550' },\r\n        { title: '国际军事专家分析当前局势，称局势仍在发展', heat: '7980' }\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    handleTabClick(tab) {\r\n      console.log('切换标签页:', tab.name)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.hot-events-container {\r\n  padding: 20px;\r\n  background: #f5f5f5;\r\n  min-height: 100vh;\r\n}\r\n\r\n.tabs-header {\r\n  background: white;\r\n  border-radius: 8px;\r\n  padding: 0 20px;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n}\r\n\r\n.main-content {\r\n  display: flex;\r\n  gap: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.map-section {\r\n  flex: 1;\r\n  background: white;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n}\r\n\r\n.map-container {\r\n  position: relative;\r\n  height: 400px;\r\n}\r\n\r\n.china-map {\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  border-radius: 4px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 2px dashed #ddd;\r\n}\r\n\r\n.map-placeholder {\r\n  text-align: center;\r\n  color: #666;\r\n  font-size: 16px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.map-placeholder i {\r\n  font-size: 48px;\r\n  color: #409EFF;\r\n  margin-bottom: 10px;\r\n  display: block;\r\n}\r\n\r\n.map-placeholder p {\r\n  margin: 5px 0;\r\n}\r\n\r\n.map-note {\r\n  font-size: 14px;\r\n  color: #999;\r\n}\r\n\r\n.map-legend {\r\n  position: absolute;\r\n  bottom: 20px;\r\n  left: 20px;\r\n  background: rgba(255,255,255,0.9);\r\n  padding: 10px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n}\r\n\r\n.legend-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.legend-color {\r\n  width: 16px;\r\n  height: 12px;\r\n  margin-right: 8px;\r\n  border-radius: 2px;\r\n}\r\n\r\n.hotlist-section {\r\n  width: 350px;\r\n  background: white;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n}\r\n\r\n.hotlist-header h3 {\r\n  margin: 0 0 20px 0;\r\n  color: #333;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.hot-item {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  padding: 12px 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.hot-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.rank-number {\r\n  width: 24px;\r\n  height: 24px;\r\n  background: #f0f0f0;\r\n  color: #666;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n  margin-right: 12px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.top-three .rank-number {\r\n  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);\r\n  color: white;\r\n}\r\n\r\n.event-content {\r\n  flex: 1;\r\n}\r\n\r\n.event-title {\r\n  font-size: 13px;\r\n  color: #333;\r\n  line-height: 1.4;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.event-heat {\r\n  font-size: 12px;\r\n  color: #ff6b6b;\r\n  font-weight: bold;\r\n}\r\n\r\n.details-section {\r\n  display: flex;\r\n  gap: 20px;\r\n}\r\n\r\n.detail-card {\r\n  flex: 1;\r\n  background: white;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.card-header h4 {\r\n  margin: 0;\r\n  color: #333;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.event-item {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.event-icon {\r\n  width: 32px;\r\n  height: 32px;\r\n  background: #ff6b6b;\r\n  color: white;\r\n  border-radius: 4px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n  margin-right: 12px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.event-info {\r\n  flex: 1;\r\n}\r\n\r\n.event-info .event-title {\r\n  font-size: 14px;\r\n  color: #333;\r\n  font-weight: 500;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.event-time {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.event-description {\r\n  font-size: 13px;\r\n  color: #666;\r\n  line-height: 1.5;\r\n  margin-top: 10px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAoHA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,aAAA,GACA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,IAAA;MAAA;IAEA;EACA;EACAC,OAAA;IACAC,cAAA,WAAAA,eAAAC,GAAA;MACAC,OAAA,CAAAC,GAAA,WAAAF,GAAA,CAAAR,IAAA;IACA;EACA;AACA", "ignoreList": []}]}