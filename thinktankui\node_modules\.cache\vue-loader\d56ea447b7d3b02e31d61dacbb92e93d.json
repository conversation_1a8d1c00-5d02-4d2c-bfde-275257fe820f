{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\Pagination\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\Pagination\\index.vue", "mtime": 1749104047621}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAkBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Pagination", "sourcesContent": ["<template>\r\n  <div :class=\"{'hidden':hidden}\" class=\"pagination-container\">\r\n    <el-pagination\r\n      :background=\"background\"\r\n      :current-page.sync=\"currentPage\"\r\n      :page-size.sync=\"pageSize\"\r\n      :layout=\"layout\"\r\n      :page-sizes=\"pageSizes\"\r\n      :pager-count=\"pagerCount\"\r\n      :total=\"total\"\r\n      v-bind=\"$attrs\"\r\n      @size-change=\"handleSizeChange\"\r\n      @current-change=\"handleCurrentChange\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { scrollTo } from '@/utils/scroll-to'\r\n\r\nexport default {\r\n  name: 'Pagination',\r\n  props: {\r\n    total: {\r\n      required: true,\r\n      type: Number\r\n    },\r\n    page: {\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    limit: {\r\n      type: Number,\r\n      default: 20\r\n    },\r\n    pageSizes: {\r\n      type: Array,\r\n      default() {\r\n        return [10, 20, 30, 50]\r\n      }\r\n    },\r\n    // 移动端页码按钮的数量端默认值5\r\n    pagerCount: {\r\n      type: Number,\r\n      default: document.body.clientWidth < 992 ? 5 : 7\r\n    },\r\n    layout: {\r\n      type: String,\r\n      default: 'total, sizes, prev, pager, next, jumper'\r\n    },\r\n    background: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    autoScroll: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    hidden: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n    };\r\n  },\r\n  computed: {\r\n    currentPage: {\r\n      get() {\r\n        return this.page\r\n      },\r\n      set(val) {\r\n        this.$emit('update:page', val)\r\n      }\r\n    },\r\n    pageSize: {\r\n      get() {\r\n        return this.limit\r\n      },\r\n      set(val) {\r\n        this.$emit('update:limit', val)\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    handleSizeChange(val) {\r\n      if (this.currentPage * val > this.total) {\r\n        this.currentPage = 1\r\n      }\r\n      this.$emit('pagination', { page: this.currentPage, limit: val })\r\n      if (this.autoScroll) {\r\n        scrollTo(0, 800)\r\n      }\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.$emit('pagination', { page: val, limit: this.pageSize })\r\n      if (this.autoScroll) {\r\n        scrollTo(0, 800)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.pagination-container {\r\n  background: #fff;\r\n}\r\n.pagination-container.hidden {\r\n  display: none;\r\n}\r\n</style>\r\n"]}]}