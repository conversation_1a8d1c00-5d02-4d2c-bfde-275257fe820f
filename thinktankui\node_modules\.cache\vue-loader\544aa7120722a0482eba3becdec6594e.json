{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\warning-center\\index.vue?vue&type=template&id=3dd5307a&scoped=true", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\warning-center\\index.vue", "mtime": 1749104047651}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749104421286}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}