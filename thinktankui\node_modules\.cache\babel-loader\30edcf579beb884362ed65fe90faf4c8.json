{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\script.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\script.js", "mtime": 1749104422572}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_inline", "_interopRequireDefault", "require", "<PERSON><PERSON><PERSON>", "_Inline", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "key", "value", "create", "document", "createElement", "_superPropGet2", "formats", "domNode", "tagName", "undefined", "Inline", "_defineProperty2", "_default", "exports"], "sources": ["../../src/formats/script.ts"], "sourcesContent": ["import Inline from '../blots/inline.js';\n\nclass Script extends Inline {\n  static blotName = 'script';\n  static tagName = ['SUB', 'SUP'];\n\n  static create(value: 'super' | 'sub' | (string & {})) {\n    if (value === 'super') {\n      return document.createElement('sup');\n    }\n    if (value === 'sub') {\n      return document.createElement('sub');\n    }\n    return super.create(value);\n  }\n\n  static formats(domNode: HTMLElement) {\n    if (domNode.tagName === 'SUB') return 'sub';\n    if (domNode.tagName === 'SUP') return 'super';\n    return undefined;\n  }\n}\n\nexport default Script;\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAuC,IAEjCC,MAAM,0BAAAC,OAAA;EAAA,SAAAD,OAAA;IAAA,IAAAE,gBAAA,CAAAC,OAAA,QAAAH,MAAA;IAAA,WAAAI,WAAA,CAAAD,OAAA,QAAAH,MAAA,EAAAK,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAH,MAAA,EAAAC,OAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAH,MAAA;IAAAQ,GAAA;IAAAC,KAAA,EAIV,SAAOC,MAAMA,CAACD,KAAsC,EAAE;MACpD,IAAIA,KAAK,KAAK,OAAO,EAAE;QACrB,OAAOE,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACtC;MACA,IAAIH,KAAK,KAAK,KAAK,EAAE;QACnB,OAAOE,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACtC;MACA,WAAAC,cAAA,CAAAV,OAAA,EAAAH,MAAA,sBAAoBS,KAAK;IAC3B;EAAA;IAAAD,GAAA;IAAAC,KAAA,EAEA,SAAOK,OAAOA,CAACC,OAAoB,EAAE;MACnC,IAAIA,OAAO,CAACC,OAAO,KAAK,KAAK,EAAE,OAAO,KAAK;MAC3C,IAAID,OAAO,CAACC,OAAO,KAAK,KAAK,EAAE,OAAO,OAAO;MAC7C,OAAOC,SAAS;IAClB;EAAA;AAAA,EAlBmBC,eAAM;AAAA,IAAAC,gBAAA,CAAAhB,OAAA,EAArBH,MAAM,cACQ,QAAQ;AAAA,IAAAmB,gBAAA,CAAAhB,OAAA,EADtBH,MAAM,aAEO,CAAC,KAAK,EAAE,KAAK,CAAC;AAAA,IAAAoB,QAAA,GAAAC,OAAA,CAAAlB,OAAA,GAmBlBH,MAAM", "ignoreList": []}]}