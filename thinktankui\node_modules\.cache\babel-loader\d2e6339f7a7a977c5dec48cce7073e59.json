{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\core\\module.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\core\\module.js", "mtime": 1749104422206}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfY3JlYXRlQ2xhc3MyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvY3JlYXRlQ2xhc3MuanMiKSk7CnZhciBfY2xhc3NDYWxsQ2hlY2syID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvY2xhc3NDYWxsQ2hlY2suanMiKSk7CnZhciBfZGVmaW5lUHJvcGVydHkyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZGVmaW5lUHJvcGVydHkuanMiKSk7CnZhciBNb2R1bGUgPSAvKiNfX1BVUkVfXyovKDAsIF9jcmVhdGVDbGFzczIuZGVmYXVsdCkoZnVuY3Rpb24gTW9kdWxlKHF1aWxsKSB7CiAgKDAsIF9jbGFzc0NhbGxDaGVjazIuZGVmYXVsdCkodGhpcywgTW9kdWxlKTsKICB2YXIgb3B0aW9ucyA9IGFyZ3VtZW50cy5sZW5ndGggPiAxICYmIGFyZ3VtZW50c1sxXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzFdIDoge307CiAgdGhpcy5xdWlsbCA9IHF1aWxsOwogIHRoaXMub3B0aW9ucyA9IG9wdGlvbnM7Cn0pOwooMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KShNb2R1bGUsICJERUZBVUxUUyIsIHt9KTsKdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gTW9kdWxlOw=="}, {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "_createClass2", "default", "quill", "_classCallCheck2", "options", "arguments", "length", "undefined", "_defineProperty2", "_default", "exports"], "sources": ["../../src/core/module.ts"], "sourcesContent": ["import type Quill from './quill.js';\n\nabstract class Module<T extends {} = {}> {\n  static DEFAULTS = {};\n\n  constructor(\n    public quill: Quill,\n    protected options: Partial<T> = {},\n  ) {}\n}\n\nexport default Module;\n"], "mappings": ";;;;;;;;;;IAEeA,MAAM,oBAAAC,aAAA,CAAAC,OAAA,EAGnB,SAAAF,OACSG,KAAY,EAEnB;EAAA,IAAAC,gBAAA,CAAAF,OAAA,QAAAF,MAAA;EAAA,IADUK,OAAmB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAAA,KAD3BH,KAAY,GAAZA,KAAY;EAAA,KACTE,OAAmB,GAAnBA,OAAmB;AAC5B;AAAA,IAAAI,gBAAA,CAAAP,OAAA,EANUF,MAAM,cACD,CAAC,CAAC;AAAA,IAAAU,QAAA,GAAAC,OAAA,CAAAT,OAAA,GAQPF,MAAM", "ignoreList": []}]}