{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\Editor\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\Editor\\index.vue", "mtime": 1749104047618}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAoBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Editor", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-upload\r\n      :action=\"uploadUrl\"\r\n      :before-upload=\"handleBeforeUpload\"\r\n      :on-success=\"handleUploadSuccess\"\r\n      :on-error=\"handleUploadError\"\r\n      name=\"file\"\r\n      :show-file-list=\"false\"\r\n      :headers=\"headers\"\r\n      style=\"display: none\"\r\n      ref=\"upload\"\r\n      v-if=\"this.type == 'url'\"\r\n    >\r\n    </el-upload>\r\n    <div class=\"editor\" ref=\"editor\" :style=\"styles\"></div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Quill from \"quill\";\r\nimport \"quill/dist/quill.core.css\";\r\nimport \"quill/dist/quill.snow.css\";\r\nimport \"quill/dist/quill.bubble.css\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"Editor\",\r\n  props: {\r\n    /* 编辑器的内容 */\r\n    value: {\r\n      type: String,\r\n      default: \"\",\r\n    },\r\n    /* 高度 */\r\n    height: {\r\n      type: Number,\r\n      default: null,\r\n    },\r\n    /* 最小高度 */\r\n    minHeight: {\r\n      type: Number,\r\n      default: null,\r\n    },\r\n    /* 只读 */\r\n    readOnly: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    /* 上传文件大小限制(MB) */\r\n    fileSize: {\r\n      type: Number,\r\n      default: 5,\r\n    },\r\n    /* 类型（base64格式、url格式） */\r\n    type: {\r\n      type: String,\r\n      default: \"url\",\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/common/upload\", // 上传的图片服务器地址\r\n      headers: {\r\n        Authorization: \"Bearer \" + getToken()\r\n      },\r\n      Quill: null,\r\n      currentValue: \"\",\r\n      options: {\r\n        theme: \"snow\",\r\n        bounds: document.body,\r\n        debug: \"warn\",\r\n        modules: {\r\n          // 工具栏配置\r\n          toolbar: [\r\n            [\"bold\", \"italic\", \"underline\", \"strike\"],       // 加粗 斜体 下划线 删除线\r\n            [\"blockquote\", \"code-block\"],                    // 引用  代码块\r\n            [{ list: \"ordered\" }, { list: \"bullet\" }],       // 有序、无序列表\r\n            [{ indent: \"-1\" }, { indent: \"+1\" }],            // 缩进\r\n            [{ size: [\"small\", false, \"large\", \"huge\"] }],   // 字体大小\r\n            [{ header: [1, 2, 3, 4, 5, 6, false] }],         // 标题\r\n            [{ color: [] }, { background: [] }],             // 字体颜色、字体背景颜色\r\n            [{ align: [] }],                                 // 对齐方式\r\n            [\"clean\"],                                       // 清除文本格式\r\n            [\"link\", \"image\", \"video\"]                       // 链接、图片、视频\r\n          ],\r\n        },\r\n        placeholder: \"请输入内容\",\r\n        readOnly: this.readOnly,\r\n      },\r\n    };\r\n  },\r\n  computed: {\r\n    styles() {\r\n      let style = {};\r\n      if (this.minHeight) {\r\n        style.minHeight = `${this.minHeight}px`;\r\n      }\r\n      if (this.height) {\r\n        style.height = `${this.height}px`;\r\n      }\r\n      return style;\r\n    },\r\n  },\r\n  watch: {\r\n    value: {\r\n      handler(val) {\r\n        if (val !== this.currentValue) {\r\n          this.currentValue = val === null ? \"\" : val;\r\n          if (this.Quill) {\r\n            this.Quill.clipboard.dangerouslyPasteHTML(this.currentValue);\r\n          }\r\n        }\r\n      },\r\n      immediate: true,\r\n    },\r\n  },\r\n  mounted() {\r\n    this.init();\r\n  },\r\n  beforeDestroy() {\r\n    this.Quill = null;\r\n  },\r\n  methods: {\r\n    init() {\r\n      const editor = this.$refs.editor;\r\n      this.Quill = new Quill(editor, this.options);\r\n      // 如果设置了上传地址则自定义图片上传事件\r\n      if (this.type == 'url') {\r\n        let toolbar = this.Quill.getModule(\"toolbar\");\r\n        toolbar.addHandler(\"image\", (value) => {\r\n          if (value) {\r\n            this.$refs.upload.$children[0].$refs.input.click();\r\n          } else {\r\n            this.quill.format(\"image\", false);\r\n          }\r\n        });\r\n      }\r\n      this.Quill.clipboard.dangerouslyPasteHTML(this.currentValue);\r\n      this.Quill.on(\"text-change\", (delta, oldDelta, source) => {\r\n        const html = this.$refs.editor.children[0].innerHTML;\r\n        const text = this.Quill.getText();\r\n        const quill = this.Quill;\r\n        this.currentValue = html;\r\n        this.$emit(\"input\", html);\r\n        this.$emit(\"on-change\", { html, text, quill });\r\n      });\r\n      this.Quill.on(\"text-change\", (delta, oldDelta, source) => {\r\n        this.$emit(\"on-text-change\", delta, oldDelta, source);\r\n      });\r\n      this.Quill.on(\"selection-change\", (range, oldRange, source) => {\r\n        this.$emit(\"on-selection-change\", range, oldRange, source);\r\n      });\r\n      this.Quill.on(\"editor-change\", (eventName, ...args) => {\r\n        this.$emit(\"on-editor-change\", eventName, ...args);\r\n      });\r\n    },\r\n    // 上传前校检格式和大小\r\n    handleBeforeUpload(file) {\r\n      const type = [\"image/jpeg\", \"image/jpg\", \"image/png\", \"image/svg\"];\r\n      const isJPG = type.includes(file.type);\r\n      // 检验文件格式\r\n      if (!isJPG) {\r\n        this.$message.error(`图片格式错误!`);\r\n        return false;\r\n      }\r\n      // 校检文件大小\r\n      if (this.fileSize) {\r\n        const isLt = file.size / 1024 / 1024 < this.fileSize;\r\n        if (!isLt) {\r\n          this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`);\r\n          return false;\r\n        }\r\n      }\r\n      return true;\r\n    },\r\n    handleUploadSuccess(res, file) {\r\n      // 如果上传成功\r\n      if (res.code == 200) {\r\n        // 获取富文本组件实例\r\n        let quill = this.Quill;\r\n        // 获取光标所在位置\r\n        let length = quill.getSelection().index;\r\n        // 插入图片  res.url为服务器返回的图片地址\r\n        quill.insertEmbed(length, \"image\", process.env.VUE_APP_BASE_API + res.fileName);\r\n        // 调整光标到最后\r\n        quill.setSelection(length + 1);\r\n      } else {\r\n        this.$message.error(\"图片插入失败\");\r\n      }\r\n    },\r\n    handleUploadError() {\r\n      this.$message.error(\"图片插入失败\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.editor, .ql-toolbar {\r\n  white-space: pre-wrap !important;\r\n  line-height: normal !important;\r\n}\r\n.quill-img {\r\n  display: none;\r\n}\r\n.ql-snow .ql-tooltip[data-mode=\"link\"]::before {\r\n  content: \"请输入链接地址:\";\r\n}\r\n.ql-snow .ql-tooltip.ql-editing a.ql-action::after {\r\n  border-right: 0px;\r\n  content: \"保存\";\r\n  padding-right: 0px;\r\n}\r\n.ql-snow .ql-tooltip[data-mode=\"video\"]::before {\r\n  content: \"请输入视频地址:\";\r\n}\r\n.ql-snow .ql-picker.ql-size .ql-picker-label::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item::before {\r\n  content: \"14px\";\r\n}\r\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"small\"]::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"small\"]::before {\r\n  content: \"10px\";\r\n}\r\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"large\"]::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"large\"]::before {\r\n  content: \"18px\";\r\n}\r\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"huge\"]::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"huge\"]::before {\r\n  content: \"32px\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item::before {\r\n  content: \"文本\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"1\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"1\"]::before {\r\n  content: \"标题1\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"2\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"2\"]::before {\r\n  content: \"标题2\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"3\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"3\"]::before {\r\n  content: \"标题3\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"4\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"4\"]::before {\r\n  content: \"标题4\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"5\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"5\"]::before {\r\n  content: \"标题5\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"6\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"6\"]::before {\r\n  content: \"标题6\";\r\n}\r\n.ql-snow .ql-picker.ql-font .ql-picker-label::before,\r\n.ql-snow .ql-picker.ql-font .ql-picker-item::before {\r\n  content: \"标准字体\";\r\n}\r\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"serif\"]::before,\r\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"serif\"]::before {\r\n  content: \"衬线字体\";\r\n}\r\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"monospace\"]::before,\r\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"monospace\"]::before {\r\n  content: \"等宽字体\";\r\n}\r\n</style>\r\n"]}]}