{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\plugins\\auth.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\plugins\\auth.js", "mtime": 1749104047629}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749104418875}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_store", "_interopRequireDefault", "require", "authPermission", "permission", "all_permission", "permissions", "store", "getters", "length", "some", "v", "authRole", "role", "super_admin", "roles", "_default", "exports", "default", "<PERSON><PERSON><PERSON><PERSON>", "hasPermiOr", "item", "hasPermiAnd", "every", "hasRole", "hasRoleOr", "hasRoleAnd"], "sources": ["D:/thinktank/thinktankui/src/plugins/auth.js"], "sourcesContent": ["import store from '@/store'\r\n\r\nfunction authPermission(permission) {\r\n  const all_permission = \"*:*:*\";\r\n  const permissions = store.getters && store.getters.permissions\r\n  if (permission && permission.length > 0) {\r\n    return permissions.some(v => {\r\n      return all_permission === v || v === permission\r\n    })\r\n  } else {\r\n    return false\r\n  }\r\n}\r\n\r\nfunction authRole(role) {\r\n  const super_admin = \"admin\";\r\n  const roles = store.getters && store.getters.roles\r\n  if (role && role.length > 0) {\r\n    return roles.some(v => {\r\n      return super_admin === v || v === role\r\n    })\r\n  } else {\r\n    return false\r\n  }\r\n}\r\n\r\nexport default {\r\n  // 验证用户是否具备某权限\r\n  hasPermi(permission) {\r\n    return authPermission(permission);\r\n  },\r\n  // 验证用户是否含有指定权限，只需包含其中一个\r\n  hasPermiOr(permissions) {\r\n    return permissions.some(item => {\r\n      return authPermission(item)\r\n    })\r\n  },\r\n  // 验证用户是否含有指定权限，必须全部拥有\r\n  hasPermiAnd(permissions) {\r\n    return permissions.every(item => {\r\n      return authPermission(item)\r\n    })\r\n  },\r\n  // 验证用户是否具备某角色\r\n  hasRole(role) {\r\n    return authRole(role);\r\n  },\r\n  // 验证用户是否含有指定角色，只需包含其中一个\r\n  hasRoleOr(roles) {\r\n    return roles.some(item => {\r\n      return authRole(item)\r\n    })\r\n  },\r\n  // 验证用户是否含有指定角色，必须全部拥有\r\n  hasRoleAnd(roles) {\r\n    return roles.every(item => {\r\n      return authRole(item)\r\n    })\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,SAASC,cAAcA,CAACC,UAAU,EAAE;EAClC,IAAMC,cAAc,GAAG,OAAO;EAC9B,IAAMC,WAAW,GAAGC,cAAK,CAACC,OAAO,IAAID,cAAK,CAACC,OAAO,CAACF,WAAW;EAC9D,IAAIF,UAAU,IAAIA,UAAU,CAACK,MAAM,GAAG,CAAC,EAAE;IACvC,OAAOH,WAAW,CAACI,IAAI,CAAC,UAAAC,CAAC,EAAI;MAC3B,OAAON,cAAc,KAAKM,CAAC,IAAIA,CAAC,KAAKP,UAAU;IACjD,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,OAAO,KAAK;EACd;AACF;AAEA,SAASQ,QAAQA,CAACC,IAAI,EAAE;EACtB,IAAMC,WAAW,GAAG,OAAO;EAC3B,IAAMC,KAAK,GAAGR,cAAK,CAACC,OAAO,IAAID,cAAK,CAACC,OAAO,CAACO,KAAK;EAClD,IAAIF,IAAI,IAAIA,IAAI,CAACJ,MAAM,GAAG,CAAC,EAAE;IAC3B,OAAOM,KAAK,CAACL,IAAI,CAAC,UAAAC,CAAC,EAAI;MACrB,OAAOG,WAAW,KAAKH,CAAC,IAAIA,CAAC,KAAKE,IAAI;IACxC,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,OAAO,KAAK;EACd;AACF;AAAC,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEc;EACb;EACAC,QAAQ,WAARA,QAAQA,CAACf,UAAU,EAAE;IACnB,OAAOD,cAAc,CAACC,UAAU,CAAC;EACnC,CAAC;EACD;EACAgB,UAAU,WAAVA,UAAUA,CAACd,WAAW,EAAE;IACtB,OAAOA,WAAW,CAACI,IAAI,CAAC,UAAAW,IAAI,EAAI;MAC9B,OAAOlB,cAAc,CAACkB,IAAI,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC;EACD;EACAC,WAAW,WAAXA,WAAWA,CAAChB,WAAW,EAAE;IACvB,OAAOA,WAAW,CAACiB,KAAK,CAAC,UAAAF,IAAI,EAAI;MAC/B,OAAOlB,cAAc,CAACkB,IAAI,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC;EACD;EACAG,OAAO,WAAPA,OAAOA,CAACX,IAAI,EAAE;IACZ,OAAOD,QAAQ,CAACC,IAAI,CAAC;EACvB,CAAC;EACD;EACAY,SAAS,WAATA,SAASA,CAACV,KAAK,EAAE;IACf,OAAOA,KAAK,CAACL,IAAI,CAAC,UAAAW,IAAI,EAAI;MACxB,OAAOT,QAAQ,CAACS,IAAI,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC;EACD;EACAK,UAAU,WAAVA,UAAUA,CAACX,KAAK,EAAE;IAChB,OAAOA,KAAK,CAACQ,KAAK,CAAC,UAAAF,IAAI,EAAI;MACzB,OAAOT,QAAQ,CAACS,IAAI,CAAC;IACvB,CAAC,CAAC;EACJ;AACF,CAAC", "ignoreList": []}]}