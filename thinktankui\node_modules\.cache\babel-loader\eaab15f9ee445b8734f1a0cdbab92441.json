{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\core\\utils\\createRegistryWithFormats.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\core\\utils\\createRegistryWithFormats.js", "mtime": 1749104421388}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_parchment", "require", "MAX_REGISTER_ITERATIONS", "CORE_FORMATS", "createRegistryWithFormats", "formats", "sourceRegistry", "debug", "registry", "Registry", "for<PERSON>ach", "name", "coreBlot", "query", "register", "format", "error", "concat", "iterations", "_format$requiredConta", "requiredC<PERSON><PERSON>", "_default", "exports", "default"], "sources": ["../../../src/core/utils/createRegistryWithFormats.ts"], "sourcesContent": ["import { Registry } from 'parchment';\n\nconst MAX_REGISTER_ITERATIONS = 100;\nconst CORE_FORMATS = ['block', 'break', 'cursor', 'inline', 'scroll', 'text'];\n\nconst createRegistryWithFormats = (\n  formats: string[],\n  sourceRegistry: Registry,\n  debug: { error: (errorMessage: string) => void },\n) => {\n  const registry = new Registry();\n  CORE_FORMATS.forEach((name) => {\n    const coreBlot = sourceRegistry.query(name);\n    if (coreBlot) registry.register(coreBlot);\n  });\n\n  formats.forEach((name) => {\n    let format = sourceRegistry.query(name);\n    if (!format) {\n      debug.error(\n        `Cannot register \"${name}\" specified in \"formats\" config. Are you sure it was registered?`,\n      );\n    }\n    let iterations = 0;\n    while (format) {\n      registry.register(format);\n      format = 'blotName' in format ? format.requiredContainer ?? null : null;\n\n      iterations += 1;\n      if (iterations > MAX_REGISTER_ITERATIONS) {\n        debug.error(\n          `Cycle detected in registering blot requiredContainer: \"${name}\"`,\n        );\n        break;\n      }\n    }\n  });\n\n  return registry;\n};\n\nexport default createRegistryWithFormats;\n"], "mappings": ";;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAEA,IAAMC,uBAAuB,GAAG,GAAG;AACnC,IAAMC,YAAY,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC;AAE7E,IAAMC,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAC7BC,OAAiB,EACjBC,cAAwB,EACxBC,KAAgD,EAC7C;EACH,IAAMC,QAAQ,GAAG,IAAIC,mBAAQ,CAAC,CAAC;EAC/BN,YAAY,CAACO,OAAO,CAAE,UAAAC,IAAI,EAAK;IAC7B,IAAMC,QAAQ,GAAGN,cAAc,CAACO,KAAK,CAACF,IAAI,CAAC;IAC3C,IAAIC,QAAQ,EAAEJ,QAAQ,CAACM,QAAQ,CAACF,QAAQ,CAAC;EAC3C,CAAC,CAAC;EAEFP,OAAO,CAACK,OAAO,CAAE,UAAAC,IAAI,EAAK;IACxB,IAAII,MAAM,GAAGT,cAAc,CAACO,KAAK,CAACF,IAAI,CAAC;IACvC,IAAI,CAACI,MAAM,EAAE;MACXR,KAAK,CAACS,KAAK,sBAAAC,MAAA,CACWN,IAAK,wEAC3B,CAAC;IACH;IACA,IAAIO,UAAU,GAAG,CAAC;IAClB,OAAOH,MAAM,EAAE;MAAA,IAAAI,qBAAA;MACbX,QAAQ,CAACM,QAAQ,CAACC,MAAM,CAAC;MACzBA,MAAM,GAAG,UAAU,IAAIA,MAAM,IAAAI,qBAAA,GAAGJ,MAAM,CAACK,iBAAiB,cAAAD,qBAAA,cAAAA,qBAAA,GAAI,IAAI,GAAG,IAAI;MAEvED,UAAU,IAAI,CAAC;MACf,IAAIA,UAAU,GAAGhB,uBAAuB,EAAE;QACxCK,KAAK,CAACS,KAAK,4DAAAC,MAAA,CACiDN,IAAK,OACjE,CAAC;QACD;MACF;IACF;EACF,CAAC,CAAC;EAEF,OAAOH,QAAQ;AACjB,CAAC;AAAA,IAAAa,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEcnB,yBAAyB", "ignoreList": []}]}