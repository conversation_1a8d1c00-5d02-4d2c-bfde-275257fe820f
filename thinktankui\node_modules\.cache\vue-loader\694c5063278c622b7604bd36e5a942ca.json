{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\monitor\\cache\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\monitor\\cache\\index.vue", "mtime": 1749104047642}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRDYWNoZSB9IGZyb20gIkAvYXBpL21vbml0b3IvY2FjaGUiOw0KaW1wb3J0ICogYXMgZWNoYXJ0cyBmcm9tICJlY2hhcnRzIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiQ2FjaGUiLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDnu5/orqHlkb3ku6Tkv6Hmga8NCiAgICAgIGNvbW1hbmRzdGF0czogbnVsbCwNCiAgICAgIC8vIOS9v+eUqOWGheWtmA0KICAgICAgdXNlZG1lbW9yeTogbnVsbCwNCiAgICAgIC8vIGNhY2hl5L+h5oGvDQogICAgICBjYWNoZTogW10NCiAgICB9DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRMaXN0KCk7DQogICAgdGhpcy5vcGVuTG9hZGluZygpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOafpee8k+WtmOivouS/oeaBryAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICBnZXRDYWNoZSgpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIHRoaXMuY2FjaGUgPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB0aGlzLiRtb2RhbC5jbG9zZUxvYWRpbmcoKTsNCg0KICAgICAgICB0aGlzLmNvbW1hbmRzdGF0cyA9IGVjaGFydHMuaW5pdCh0aGlzLiRyZWZzLmNvbW1hbmRzdGF0cywgIm1hY2Fyb25zIik7DQogICAgICAgIHRoaXMuY29tbWFuZHN0YXRzLnNldE9wdGlvbih7DQogICAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgICAgdHJpZ2dlcjogIml0ZW0iLA0KICAgICAgICAgICAgZm9ybWF0dGVyOiAie2F9IDxici8+e2J9IDoge2N9ICh7ZH0lKSIsDQogICAgICAgICAgfSwNCiAgICAgICAgICBzZXJpZXM6IFsNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgbmFtZTogIuWRveS7pCIsDQogICAgICAgICAgICAgIHR5cGU6ICJwaWUiLA0KICAgICAgICAgICAgICByb3NlVHlwZTogInJhZGl1cyIsDQogICAgICAgICAgICAgIHJhZGl1czogWzE1LCA5NV0sDQogICAgICAgICAgICAgIGNlbnRlcjogWyI1MCUiLCAiMzglIl0sDQogICAgICAgICAgICAgIGRhdGE6IHJlc3BvbnNlLmRhdGEuY29tbWFuZFN0YXRzLA0KICAgICAgICAgICAgICBhbmltYXRpb25FYXNpbmc6ICJjdWJpY0luT3V0IiwNCiAgICAgICAgICAgICAgYW5pbWF0aW9uRHVyYXRpb246IDEwMDAsDQogICAgICAgICAgICB9DQogICAgICAgICAgXQ0KICAgICAgICB9KTsNCiAgICAgICAgdGhpcy51c2VkbWVtb3J5ID0gZWNoYXJ0cy5pbml0KHRoaXMuJHJlZnMudXNlZG1lbW9yeSwgIm1hY2Fyb25zIik7DQogICAgICAgIHRoaXMudXNlZG1lbW9yeS5zZXRPcHRpb24oew0KICAgICAgICAgIHRvb2x0aXA6IHsNCiAgICAgICAgICAgIGZvcm1hdHRlcjogIntifSA8YnIvPnthfSA6ICIgKyB0aGlzLmNhY2hlLmluZm8udXNlZF9tZW1vcnlfaHVtYW4sDQogICAgICAgICAgfSwNCiAgICAgICAgICBzZXJpZXM6IFsNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgbmFtZTogIuWzsOWAvCIsDQogICAgICAgICAgICAgIHR5cGU6ICJnYXVnZSIsDQogICAgICAgICAgICAgIG1pbjogMCwNCiAgICAgICAgICAgICAgbWF4OiAxMDAwLA0KICAgICAgICAgICAgICBkZXRhaWw6IHsNCiAgICAgICAgICAgICAgICBmb3JtYXR0ZXI6IHRoaXMuY2FjaGUuaW5mby51c2VkX21lbW9yeV9odW1hbiwNCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgZGF0YTogWw0KICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgIHZhbHVlOiBwYXJzZUZsb2F0KHRoaXMuY2FjaGUuaW5mby51c2VkX21lbW9yeV9odW1hbiksDQogICAgICAgICAgICAgICAgICBuYW1lOiAi5YaF5a2Y5raI6ICXIiwNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIF0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICBdDQogICAgICAgIH0pOw0KICAgICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigicmVzaXplIiwgKCkgPT4gew0KICAgICAgICAgIHRoaXMuY29tbWFuZHN0YXRzLnJlc2l6ZSgpOw0KICAgICAgICAgIHRoaXMudXNlZG1lbW9yeS5yZXNpemUoKTsNCiAgICAgICAgfSk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOaJk+W8gOWKoOi9veWxgg0KICAgIG9wZW5Mb2FkaW5nKCkgew0KICAgICAgdGhpcy4kbW9kYWwubG9hZGluZygi5q2j5Zyo5Yqg6L2957yT5a2Y55uR5o6n5pWw5o2u77yM6K+356iN5YCZ77yBIik7DQogICAgfQ0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/monitor/cache", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <el-col :span=\"24\" class=\"card-box\">\r\n        <el-card>\r\n          <div slot=\"header\"><span><i class=\"el-icon-monitor\"></i> 基本信息</span></div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <table cellspacing=\"0\" style=\"width: 100%\">\r\n              <tbody>\r\n                <tr>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">Redis版本</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.redis_version }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">运行模式</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.redis_mode == \"standalone\" ? \"单机\" : \"集群\" }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">端口</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.tcp_port }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">客户端数</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.connected_clients }}</div></td>\r\n                </tr>\r\n                <tr>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">运行时间(天)</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.uptime_in_days }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">使用内存</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.used_memory_human }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">使用CPU</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ parseFloat(cache.info.used_cpu_user_children).toFixed(2) }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">内存配置</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.maxmemory_human }}</div></td>\r\n                </tr>\r\n                <tr>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">AOF是否开启</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.aof_enabled == \"0\" ? \"否\" : \"是\" }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">RDB是否成功</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.rdb_last_bgsave_status }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">Key数量</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.dbSize\">{{ cache.dbSize }} </div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">网络入口/出口</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.instantaneous_input_kbps }}kps/{{cache.info.instantaneous_output_kbps}}kps</div></td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\" class=\"card-box\">\r\n        <el-card>\r\n          <div slot=\"header\"><span><i class=\"el-icon-pie-chart\"></i> 命令统计</span></div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <div ref=\"commandstats\" style=\"height: 420px\" />\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\" class=\"card-box\">\r\n        <el-card>\r\n          <div slot=\"header\"><span><i class=\"el-icon-odometer\"></i> 内存信息</span></div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <div ref=\"usedmemory\" style=\"height: 420px\" />\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCache } from \"@/api/monitor/cache\";\r\nimport * as echarts from \"echarts\";\r\n\r\nexport default {\r\n  name: \"Cache\",\r\n  data() {\r\n    return {\r\n      // 统计命令信息\r\n      commandstats: null,\r\n      // 使用内存\r\n      usedmemory: null,\r\n      // cache信息\r\n      cache: []\r\n    }\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.openLoading();\r\n  },\r\n  methods: {\r\n    /** 查缓存询信息 */\r\n    getList() {\r\n      getCache().then((response) => {\r\n        this.cache = response.data;\r\n        this.$modal.closeLoading();\r\n\r\n        this.commandstats = echarts.init(this.$refs.commandstats, \"macarons\");\r\n        this.commandstats.setOption({\r\n          tooltip: {\r\n            trigger: \"item\",\r\n            formatter: \"{a} <br/>{b} : {c} ({d}%)\",\r\n          },\r\n          series: [\r\n            {\r\n              name: \"命令\",\r\n              type: \"pie\",\r\n              roseType: \"radius\",\r\n              radius: [15, 95],\r\n              center: [\"50%\", \"38%\"],\r\n              data: response.data.commandStats,\r\n              animationEasing: \"cubicInOut\",\r\n              animationDuration: 1000,\r\n            }\r\n          ]\r\n        });\r\n        this.usedmemory = echarts.init(this.$refs.usedmemory, \"macarons\");\r\n        this.usedmemory.setOption({\r\n          tooltip: {\r\n            formatter: \"{b} <br/>{a} : \" + this.cache.info.used_memory_human,\r\n          },\r\n          series: [\r\n            {\r\n              name: \"峰值\",\r\n              type: \"gauge\",\r\n              min: 0,\r\n              max: 1000,\r\n              detail: {\r\n                formatter: this.cache.info.used_memory_human,\r\n              },\r\n              data: [\r\n                {\r\n                  value: parseFloat(this.cache.info.used_memory_human),\r\n                  name: \"内存消耗\",\r\n                }\r\n              ]\r\n            }\r\n          ]\r\n        });\r\n        window.addEventListener(\"resize\", () => {\r\n          this.commandstats.resize();\r\n          this.usedmemory.resize();\r\n        });\r\n      });\r\n    },\r\n    // 打开加载层\r\n    openLoading() {\r\n      this.$modal.loading(\"正在加载缓存监控数据，请稍候！\");\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}