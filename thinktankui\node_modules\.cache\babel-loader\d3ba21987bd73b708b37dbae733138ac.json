{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\tool\\gen\\editTable.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\tool\\gen\\editTable.vue", "mtime": 1749104047651}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_gen", "require", "_type", "_menu", "_basicInfoForm", "_interopRequireDefault", "_genInfoForm", "_sortablejs", "name", "components", "basicInfoForm", "genInfoForm", "data", "activeName", "tableHeight", "document", "documentElement", "scrollHeight", "tables", "columns", "dictOptions", "menus", "info", "created", "_this", "tableId", "$route", "params", "getGenTable", "then", "res", "rows", "getDictOptionselect", "response", "getMenuTreeselect", "handleTree", "methods", "submitForm", "_this2", "basicForm", "$refs", "basicInfo", "genForm", "genInfo", "Promise", "all", "map", "getFormPromise", "validateResult", "every", "item", "genTable", "Object", "assign", "model", "treeCode", "treeName", "treeParentCode", "parentMenuId", "updateGenTable", "$modal", "msgSuccess", "msg", "code", "close", "msgError", "form", "resolve", "validate", "obj", "path", "query", "t", "Date", "now", "pageNum", "$tab", "closeOpenPage", "mounted", "_this3", "el", "dragTable", "$el", "querySelectorAll", "sortable", "Sortable", "create", "handle", "onEnd", "evt", "targetRow", "splice", "oldIndex", "newIndex", "index", "sort", "parseInt"], "sources": ["src/views/tool/gen/editTable.vue"], "sourcesContent": ["<template>\r\n  <el-card>\r\n    <el-tabs v-model=\"activeName\">\r\n      <el-tab-pane label=\"基本信息\" name=\"basic\">\r\n        <basic-info-form ref=\"basicInfo\" :info=\"info\" />\r\n      </el-tab-pane>\r\n      <el-tab-pane label=\"字段信息\" name=\"columnInfo\">\r\n        <el-table ref=\"dragTable\" :data=\"columns\" row-key=\"columnId\" :max-height=\"tableHeight\">\r\n          <el-table-column label=\"序号\" type=\"index\" min-width=\"5%\" class-name=\"allowDrag\" />\r\n          <el-table-column\r\n            label=\"字段列名\"\r\n            prop=\"columnName\"\r\n            min-width=\"10%\"\r\n            :show-overflow-tooltip=\"true\"\r\n          />\r\n          <el-table-column label=\"字段描述\" min-width=\"10%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.columnComment\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"物理类型\"\r\n            prop=\"columnType\"\r\n            min-width=\"10%\"\r\n            :show-overflow-tooltip=\"true\"\r\n          />\r\n          <el-table-column label=\"Python类型\" min-width=\"11%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.pythonType\">\r\n                <el-option label=\"str\" value=\"str\" />\r\n                <el-option label=\"int\" value=\"int\" />\r\n                <el-option label=\"float\" value=\"float\" />\r\n                <el-option label=\"Decimal\" value=\"Decimal\" />\r\n                <el-option label=\"date\" value=\"date\" />\r\n                <el-option label=\"time\" value=\"time\" />\r\n                <el-option label=\"datetime\" value=\"datetime\" />\r\n                <el-option label=\"bytes\" value=\"bytes\" />\r\n                <el-option label=\"dict\" value=\"dict\" />\r\n                <el-option label=\"list\" value=\"list\" />\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"Python属性\" min-width=\"10%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.pythonField\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column label=\"插入\" min-width=\"5%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox true-label=\"1\" false-label=\"0\" v-model=\"scope.row.isInsert\"></el-checkbox>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"编辑\" min-width=\"5%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox true-label=\"1\" false-label=\"0\" v-model=\"scope.row.isEdit\"></el-checkbox>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"列表\" min-width=\"5%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox true-label=\"1\" false-label=\"0\" v-model=\"scope.row.isList\"></el-checkbox>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"查询\" min-width=\"5%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox true-label=\"1\" false-label=\"0\" v-model=\"scope.row.isQuery\"></el-checkbox>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"查询方式\" min-width=\"10%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.queryType\">\r\n                <el-option label=\"=\" value=\"EQ\" />\r\n                <el-option label=\"!=\" value=\"NE\" />\r\n                <el-option label=\">\" value=\"GT\" />\r\n                <el-option label=\">=\" value=\"GTE\" />\r\n                <el-option label=\"<\" value=\"LT\" />\r\n                <el-option label=\"<=\" value=\"LTE\" />\r\n                <el-option label=\"LIKE\" value=\"LIKE\" />\r\n                <el-option label=\"BETWEEN\" value=\"BETWEEN\" />\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"必填\" min-width=\"5%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox true-label=\"1\" false-label=\"0\" v-model=\"scope.row.isRequired\"></el-checkbox>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"唯一\" min-width=\"5%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox true-label=\"1\" false-label=\"0\" v-model=\"scope.row.isUnique\"></el-checkbox>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"显示类型\" min-width=\"12%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.htmlType\">\r\n                <el-option label=\"文本框\" value=\"input\" />\r\n                <el-option label=\"文本域\" value=\"textarea\" />\r\n                <el-option label=\"下拉框\" value=\"select\" />\r\n                <el-option label=\"单选框\" value=\"radio\" />\r\n                <el-option label=\"复选框\" value=\"checkbox\" />\r\n                <el-option label=\"日期控件\" value=\"datetime\" />\r\n                <el-option label=\"图片上传\" value=\"imageUpload\" />\r\n                <el-option label=\"文件上传\" value=\"fileUpload\" />\r\n                <el-option label=\"富文本控件\" value=\"editor\" />\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"字典类型\" min-width=\"12%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.dictType\" clearable filterable placeholder=\"请选择\">\r\n                <el-option\r\n                  v-for=\"dict in dictOptions\"\r\n                  :key=\"dict.dictType\"\r\n                  :label=\"dict.dictName\"\r\n                  :value=\"dict.dictType\">\r\n                  <span style=\"float: left\">{{ dict.dictName }}</span>\r\n                  <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ dict.dictType }}</span>\r\n              </el-option>\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </el-tab-pane>\r\n      <el-tab-pane label=\"生成信息\" name=\"genInfo\">\r\n        <gen-info-form ref=\"genInfo\" :info=\"info\" :tables=\"tables\" :menus=\"menus\"/>\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n    <el-form label-width=\"100px\">\r\n      <el-form-item style=\"text-align: center;margin-left:-100px;margin-top:10px;\">\r\n        <el-button type=\"primary\" @click=\"submitForm()\">提交</el-button>\r\n        <el-button @click=\"close()\">返回</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </el-card>\r\n</template>\r\n\r\n<script>\r\nimport { getGenTable, updateGenTable } from \"@/api/tool/gen\";\r\nimport { optionselect as getDictOptionselect } from \"@/api/system/dict/type\";\r\nimport { listMenu as getMenuTreeselect } from \"@/api/system/menu\";\r\nimport basicInfoForm from \"./basicInfoForm\";\r\nimport genInfoForm from \"./genInfoForm\";\r\nimport Sortable from 'sortablejs'\r\n\r\nexport default {\r\n  name: \"GenEdit\",\r\n  components: {\r\n    basicInfoForm,\r\n    genInfoForm\r\n  },\r\n  data() {\r\n    return {\r\n      // 选中选项卡的 name\r\n      activeName: \"columnInfo\",\r\n      // 表格的高度\r\n      tableHeight: document.documentElement.scrollHeight - 245 + \"px\",\r\n      // 表信息\r\n      tables: [],\r\n      // 表列信息\r\n      columns: [],\r\n      // 字典信息\r\n      dictOptions: [],\r\n      // 菜单信息\r\n      menus: [],\r\n      // 表详细信息\r\n      info: {}\r\n    };\r\n  },\r\n  created() {\r\n    const tableId = this.$route.params && this.$route.params.tableId;\r\n    if (tableId) {\r\n      // 获取表详细信息\r\n      getGenTable(tableId).then(res => {\r\n        this.columns = res.data.rows;\r\n        this.info = res.data.info;\r\n        this.tables = res.data.tables;\r\n      });\r\n      /** 查询字典下拉列表 */\r\n      getDictOptionselect().then(response => {\r\n        this.dictOptions = response.data;\r\n      });\r\n      /** 查询菜单下拉列表 */\r\n      getMenuTreeselect().then(response => {\r\n        this.menus = this.handleTree(response.data, \"menuId\");\r\n      });\r\n    }\r\n  },\r\n  methods: {\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      const basicForm = this.$refs.basicInfo.$refs.basicInfoForm;\r\n      const genForm = this.$refs.genInfo.$refs.genInfoForm;\r\n      Promise.all([basicForm, genForm].map(this.getFormPromise)).then(res => {\r\n        const validateResult = res.every(item => !!item);\r\n        if (validateResult) {\r\n          const genTable = Object.assign({}, basicForm.model, genForm.model);\r\n          genTable.columns = this.columns;\r\n          genTable.params = {\r\n            treeCode: genTable.treeCode,\r\n            treeName: genTable.treeName,\r\n            treeParentCode: genTable.treeParentCode,\r\n            parentMenuId: genTable.parentMenuId\r\n          };\r\n          updateGenTable(genTable).then(res => {\r\n            this.$modal.msgSuccess(res.msg);\r\n            if (res.code === 200) {\r\n              this.close();\r\n            }\r\n          });\r\n        } else {\r\n          this.$modal.msgError(\"表单校验未通过，请重新检查提交内容\");\r\n        }\r\n      });\r\n    },\r\n    getFormPromise(form) {\r\n      return new Promise(resolve => {\r\n        form.validate(res => {\r\n          resolve(res);\r\n        });\r\n      });\r\n    },\r\n    /** 关闭按钮 */\r\n    close() {\r\n      const obj = { path: \"/tool/gen\", query: { t: Date.now(), pageNum: this.$route.query.pageNum } };\r\n      this.$tab.closeOpenPage(obj);\r\n    }\r\n  },\r\n  mounted() {\r\n    const el = this.$refs.dragTable.$el.querySelectorAll(\".el-table__body-wrapper > table > tbody\")[0];\r\n    const sortable = Sortable.create(el, {\r\n      handle: \".allowDrag\",\r\n      onEnd: evt => {\r\n        const targetRow = this.columns.splice(evt.oldIndex, 1)[0];\r\n        this.columns.splice(evt.newIndex, 0, targetRow);\r\n        for (let index in this.columns) {\r\n          this.columns[index].sort = parseInt(index) + 1;\r\n        }\r\n      }\r\n    });\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;AAyIA,IAAAA,IAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,cAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,YAAA,GAAAD,sBAAA,CAAAJ,OAAA;AACA,IAAAM,WAAA,GAAAF,sBAAA,CAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAO,IAAA;EACAC,UAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,WAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,UAAA;MACA;MACAC,WAAA,EAAAC,QAAA,CAAAC,eAAA,CAAAC,YAAA;MACA;MACAC,MAAA;MACA;MACAC,OAAA;MACA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,IAAAC,OAAA,QAAAC,MAAA,CAAAC,MAAA,SAAAD,MAAA,CAAAC,MAAA,CAAAF,OAAA;IACA,IAAAA,OAAA;MACA;MACA,IAAAG,gBAAA,EAAAH,OAAA,EAAAI,IAAA,WAAAC,GAAA;QACAN,KAAA,CAAAL,OAAA,GAAAW,GAAA,CAAAlB,IAAA,CAAAmB,IAAA;QACAP,KAAA,CAAAF,IAAA,GAAAQ,GAAA,CAAAlB,IAAA,CAAAU,IAAA;QACAE,KAAA,CAAAN,MAAA,GAAAY,GAAA,CAAAlB,IAAA,CAAAM,MAAA;MACA;MACA;MACA,IAAAc,kBAAA,IAAAH,IAAA,WAAAI,QAAA;QACAT,KAAA,CAAAJ,WAAA,GAAAa,QAAA,CAAArB,IAAA;MACA;MACA;MACA,IAAAsB,cAAA,IAAAL,IAAA,WAAAI,QAAA;QACAT,KAAA,CAAAH,KAAA,GAAAG,KAAA,CAAAW,UAAA,CAAAF,QAAA,CAAArB,IAAA;MACA;IACA;EACA;EACAwB,OAAA;IACA,WACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,SAAA,QAAAC,KAAA,CAAAC,SAAA,CAAAD,KAAA,CAAA9B,aAAA;MACA,IAAAgC,OAAA,QAAAF,KAAA,CAAAG,OAAA,CAAAH,KAAA,CAAA7B,WAAA;MACAiC,OAAA,CAAAC,GAAA,EAAAN,SAAA,EAAAG,OAAA,EAAAI,GAAA,MAAAC,cAAA,GAAAlB,IAAA,WAAAC,GAAA;QACA,IAAAkB,cAAA,GAAAlB,GAAA,CAAAmB,KAAA,WAAAC,IAAA;UAAA,SAAAA,IAAA;QAAA;QACA,IAAAF,cAAA;UACA,IAAAG,QAAA,GAAAC,MAAA,CAAAC,MAAA,KAAAd,SAAA,CAAAe,KAAA,EAAAZ,OAAA,CAAAY,KAAA;UACAH,QAAA,CAAAhC,OAAA,GAAAmB,MAAA,CAAAnB,OAAA;UACAgC,QAAA,CAAAxB,MAAA;YACA4B,QAAA,EAAAJ,QAAA,CAAAI,QAAA;YACAC,QAAA,EAAAL,QAAA,CAAAK,QAAA;YACAC,cAAA,EAAAN,QAAA,CAAAM,cAAA;YACAC,YAAA,EAAAP,QAAA,CAAAO;UACA;UACA,IAAAC,mBAAA,EAAAR,QAAA,EAAAtB,IAAA,WAAAC,GAAA;YACAQ,MAAA,CAAAsB,MAAA,CAAAC,UAAA,CAAA/B,GAAA,CAAAgC,GAAA;YACA,IAAAhC,GAAA,CAAAiC,IAAA;cACAzB,MAAA,CAAA0B,KAAA;YACA;UACA;QACA;UACA1B,MAAA,CAAAsB,MAAA,CAAAK,QAAA;QACA;MACA;IACA;IACAlB,cAAA,WAAAA,eAAAmB,IAAA;MACA,WAAAtB,OAAA,WAAAuB,OAAA;QACAD,IAAA,CAAAE,QAAA,WAAAtC,GAAA;UACAqC,OAAA,CAAArC,GAAA;QACA;MACA;IACA;IACA,WACAkC,KAAA,WAAAA,MAAA;MACA,IAAAK,GAAA;QAAAC,IAAA;QAAAC,KAAA;UAAAC,CAAA,EAAAC,IAAA,CAAAC,GAAA;UAAAC,OAAA,OAAAjD,MAAA,CAAA6C,KAAA,CAAAI;QAAA;MAAA;MACA,KAAAC,IAAA,CAAAC,aAAA,CAAAR,GAAA;IACA;EACA;EACAS,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA,IAAAC,EAAA,QAAAxC,KAAA,CAAAyC,SAAA,CAAAC,GAAA,CAAAC,gBAAA;IACA,IAAAC,QAAA,GAAAC,mBAAA,CAAAC,MAAA,CAAAN,EAAA;MACAO,MAAA;MACAC,KAAA,WAAAA,MAAAC,GAAA;QACA,IAAAC,SAAA,GAAAX,MAAA,CAAA5D,OAAA,CAAAwE,MAAA,CAAAF,GAAA,CAAAG,QAAA;QACAb,MAAA,CAAA5D,OAAA,CAAAwE,MAAA,CAAAF,GAAA,CAAAI,QAAA,KAAAH,SAAA;QACA,SAAAI,KAAA,IAAAf,MAAA,CAAA5D,OAAA;UACA4D,MAAA,CAAA5D,OAAA,CAAA2E,KAAA,EAAAC,IAAA,GAAAC,QAAA,CAAAF,KAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}