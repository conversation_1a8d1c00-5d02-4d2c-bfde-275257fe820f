{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\DictTag\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\DictTag\\index.vue", "mtime": 1749104047618}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "props", "options", "type", "Array", "default", "value", "Number", "String", "showValue", "Boolean", "separator", "data", "unmatch<PERSON><PERSON>y", "computed", "values", "isArray", "map", "item", "split", "unmatch", "_this", "length", "for<PERSON>ach", "some", "v", "push", "filters", "handleArray", "array", "reduce", "pre", "cur"], "sources": ["src/components/DictTag/index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <template v-for=\"(item, index) in options\">\r\n      <template v-if=\"values.includes(item.value)\">\r\n        <span\r\n          v-if=\"(item.raw.listClass == 'default' || item.raw.listClass == '') && (item.raw.cssClass == '' || item.raw.cssClass == null)\"\r\n          :key=\"item.value\"\r\n          :index=\"index\"\r\n          :class=\"item.raw.cssClass\"\r\n          >{{ item.label + ' ' }}</span\r\n        >\r\n        <el-tag\r\n          v-else\r\n          :disable-transitions=\"true\"\r\n          :key=\"item.value\"\r\n          :index=\"index\"\r\n          :type=\"item.raw.listClass == 'primary' ? '' : item.raw.listClass\"\r\n          :class=\"item.raw.cssClass\"\r\n        >\r\n          {{ item.label + ' ' }}\r\n        </el-tag>\r\n      </template>\r\n    </template>\r\n    <template v-if=\"unmatch && showValue\">\r\n      {{ unmatchArray | handleArray }}\r\n    </template>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"DictTag\",\r\n  props: {\r\n    options: {\r\n      type: Array,\r\n      default: null,\r\n    },\r\n    value: [Number, String, Array],\r\n    // 当未找到匹配的数据时，显示value\r\n    showValue: {\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    separator: {\r\n      type: String,\r\n      default: \",\"\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      unmatchArray: [], // 记录未匹配的项\r\n    }\r\n  },\r\n  computed: {\r\n    values() {\r\n      if (this.value === null || typeof this.value === 'undefined' || this.value === '') return []\r\n      return Array.isArray(this.value) ? this.value.map(item => '' + item) : String(this.value).split(this.separator)\r\n    },\r\n    unmatch() {\r\n      this.unmatchArray = []\r\n      // 没有value不显示\r\n      if (this.value === null || typeof this.value === 'undefined' || this.value === '' || this.options.length === 0) return false\r\n      // 传入值为数组\r\n      let unmatch = false // 添加一个标志来判断是否有未匹配项\r\n      this.values.forEach(item => {\r\n        if (!this.options.some(v => v.value === item)) {\r\n          this.unmatchArray.push(item)\r\n          unmatch = true // 如果有未匹配项，将标志设置为true\r\n        }\r\n      })\r\n      return unmatch // 返回标志的值\r\n    },\r\n\r\n  },\r\n  filters: {\r\n    handleArray(array) {\r\n      if (array.length === 0) return '';\r\n      return array.reduce((pre, cur) => {\r\n        return pre + ' ' + cur;\r\n      })\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style scoped>\r\n.el-tag + .el-tag {\r\n  margin-left: 10px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCA8BA;EACAA,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA;IACA;IACAC,KAAA,GAAAC,MAAA,EAAAC,MAAA,EAAAJ,KAAA;IACA;IACAK,SAAA;MACAN,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAM,SAAA;MACAR,IAAA,EAAAK,MAAA;MACAH,OAAA;IACA;EACA;EACAO,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA;IACA;EACA;EACAC,QAAA;IACAC,MAAA,WAAAA,OAAA;MACA,SAAAT,KAAA,yBAAAA,KAAA,yBAAAA,KAAA;MACA,OAAAF,KAAA,CAAAY,OAAA,MAAAV,KAAA,SAAAA,KAAA,CAAAW,GAAA,WAAAC,IAAA;QAAA,YAAAA,IAAA;MAAA,KAAAV,MAAA,MAAAF,KAAA,EAAAa,KAAA,MAAAR,SAAA;IACA;IACAS,OAAA,WAAAA,QAAA;MAAA,IAAAC,KAAA;MACA,KAAAR,YAAA;MACA;MACA,SAAAP,KAAA,yBAAAA,KAAA,yBAAAA,KAAA,gBAAAJ,OAAA,CAAAoB,MAAA;MACA;MACA,IAAAF,OAAA;MACA,KAAAL,MAAA,CAAAQ,OAAA,WAAAL,IAAA;QACA,KAAAG,KAAA,CAAAnB,OAAA,CAAAsB,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAnB,KAAA,KAAAY,IAAA;QAAA;UACAG,KAAA,CAAAR,YAAA,CAAAa,IAAA,CAAAR,IAAA;UACAE,OAAA;QACA;MACA;MACA,OAAAA,OAAA;IACA;EAEA;EACAO,OAAA;IACAC,WAAA,WAAAA,YAAAC,KAAA;MACA,IAAAA,KAAA,CAAAP,MAAA;MACA,OAAAO,KAAA,CAAAC,MAAA,WAAAC,GAAA,EAAAC,GAAA;QACA,OAAAD,GAAA,SAAAC,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}