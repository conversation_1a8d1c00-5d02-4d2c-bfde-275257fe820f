{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\RightToolbar\\index.vue?vue&type=style&index=0&id=38ed449c&lang=scss&scoped=true", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\RightToolbar\\index.vue", "mtime": 1749104047621}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749104419119}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749104421224}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749104419872}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749104418479}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KOjp2LWRlZXAgLmVsLXRyYW5zZmVyX19idXR0b24gew0KICBib3JkZXItcmFkaXVzOiA1MCU7DQogIHBhZGRpbmc6IDEycHg7DQogIGRpc3BsYXk6IGJsb2NrOw0KICBtYXJnaW4tbGVmdDogMHB4Ow0KfQ0KOjp2LWRlZXAgLmVsLXRyYW5zZmVyX19idXR0b246Zmlyc3QtY2hpbGQgew0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/RightToolbar", "sourcesContent": ["<template>\r\n  <div class=\"top-right-btn\" :style=\"style\">\r\n    <el-row>\r\n      <el-tooltip class=\"item\" effect=\"dark\" :content=\"showSearch ? '隐藏搜索' : '显示搜索'\" placement=\"top\" v-if=\"search\">\r\n        <el-button size=\"mini\" circle icon=\"el-icon-search\" @click=\"toggleSearch()\" />\r\n      </el-tooltip>\r\n      <el-tooltip class=\"item\" effect=\"dark\" content=\"刷新\" placement=\"top\">\r\n        <el-button size=\"mini\" circle icon=\"el-icon-refresh\" @click=\"refresh()\" />\r\n      </el-tooltip>\r\n      <el-tooltip class=\"item\" effect=\"dark\" content=\"显隐列\" placement=\"top\" v-if=\"columns\">\r\n        <el-button size=\"mini\" circle icon=\"el-icon-menu\" @click=\"showColumn()\" v-if=\"showColumnsType == 'transfer'\"/>\r\n        <el-dropdown trigger=\"click\" :hide-on-click=\"false\" style=\"padding-left: 12px\" v-if=\"showColumnsType == 'checkbox'\">\r\n          <el-button size=\"mini\" circle icon=\"el-icon-menu\" />\r\n          <el-dropdown-menu slot=\"dropdown\">\r\n            <template v-for=\"item in columns\">\r\n              <el-dropdown-item :key=\"item.key\">\r\n                <el-checkbox :checked=\"item.visible\" @change=\"checkboxChange($event, item.label)\" :label=\"item.label\" />\r\n              </el-dropdown-item>\r\n            </template>\r\n          </el-dropdown-menu>\r\n        </el-dropdown>\r\n      </el-tooltip>\r\n    </el-row>\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" append-to-body>\r\n      <el-transfer\r\n        :titles=\"['显示', '隐藏']\"\r\n        v-model=\"value\"\r\n        :data=\"columns\"\r\n        @change=\"dataChange\"\r\n      ></el-transfer>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: \"RightToolbar\",\r\n  data() {\r\n    return {\r\n      // 显隐数据\r\n      value: [],\r\n      // 弹出层标题\r\n      title: \"显示/隐藏\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n    };\r\n  },\r\n  props: {\r\n    /* 是否显示检索条件 */\r\n    showSearch: {\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    /* 显隐列信息 */\r\n    columns: {\r\n      type: Array,\r\n    },\r\n    /* 是否显示检索图标 */\r\n    search: {\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    /* 显隐列类型（transfer穿梭框、checkbox复选框） */\r\n    showColumnsType: {\r\n      type: String,\r\n      default: \"checkbox\",\r\n    },\r\n    /* 右外边距 */\r\n    gutter: {\r\n      type: Number,\r\n      default: 10,\r\n    },\r\n  },\r\n  computed: {\r\n    style() {\r\n      const ret = {};\r\n      if (this.gutter) {\r\n        ret.marginRight = `${this.gutter / 2}px`;\r\n      }\r\n      return ret;\r\n    }\r\n  },\r\n  created() {\r\n    if (this.showColumnsType == 'transfer') {\r\n      // 显隐列初始默认隐藏列\r\n      for (let item in this.columns) {\r\n        if (this.columns[item].visible === false) {\r\n          this.value.push(parseInt(item));\r\n        }\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    // 搜索\r\n    toggleSearch() {\r\n      this.$emit(\"update:showSearch\", !this.showSearch);\r\n    },\r\n    // 刷新\r\n    refresh() {\r\n      this.$emit(\"queryTable\");\r\n    },\r\n    // 右侧列表元素变化\r\n    dataChange(data) {\r\n      for (let item in this.columns) {\r\n        const key = this.columns[item].key;\r\n        this.columns[item].visible = !data.includes(key);\r\n      }\r\n    },\r\n    // 打开显隐列dialog\r\n    showColumn() {\r\n      this.open = true;\r\n    },\r\n    // 勾选\r\n    checkboxChange(event, label) {\r\n      this.columns.filter(item => item.label == label)[0].visible = event;\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n::v-deep .el-transfer__button {\r\n  border-radius: 50%;\r\n  padding: 12px;\r\n  display: block;\r\n  margin-left: 0px;\r\n}\r\n::v-deep .el-transfer__button:first-child {\r\n  margin-bottom: 10px;\r\n}\r\n</style>\r\n"]}]}