{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\core\\utils\\scrollRectIntoView.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\core\\utils\\scrollRectIntoView.js", "mtime": 1749104422607}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["getParentElement", "element", "parentElement", "getRootNode", "host", "getElementRect", "rect", "getBoundingClientRect", "scaleX", "Math", "abs", "width", "offsetWidth", "scaleY", "height", "offsetHeight", "top", "right", "left", "clientWidth", "bottom", "clientHeight", "paddingValueToInt", "value", "number", "parseInt", "Number", "isNaN", "getScrollDistance", "targetStart", "targetEnd", "scrollStart", "scrollEnd", "scrollPaddingStart", "scrollPaddingEnd", "scrollRectIntoView", "root", "targetRect", "document", "ownerDocument", "current", "_window$visualViewpor", "_window$visualViewpor2", "_window$visualViewpor3", "_window$visualViewpor4", "isDocumentBody", "body", "bounding", "window", "visualViewport", "documentElement", "style", "getComputedStyle", "scrollDistanceX", "scrollPaddingLeft", "scrollPaddingRight", "scrollDistanceY", "scrollPaddingTop", "scrollPaddingBottom", "_document$defaultView", "defaultView", "scrollBy", "_current", "scrollLeft", "scrollTop", "scrolledLeft", "scrolledTop", "position", "_default", "exports", "default"], "sources": ["../../../src/core/utils/scrollRectIntoView.ts"], "sourcesContent": ["export type Rect = {\n  top: number;\n  right: number;\n  bottom: number;\n  left: number;\n};\n\nconst getParentElement = (element: Node): Element | null =>\n  element.parentElement || (element.getRootNode() as ShadowRoot).host || null;\n\nconst getElementRect = (element: Element): Rect => {\n  const rect = element.getBoundingClientRect();\n  const scaleX =\n    ('offsetWidth' in element &&\n      Math.abs(rect.width) / (element as HTMLElement).offsetWidth) ||\n    1;\n  const scaleY =\n    ('offsetHeight' in element &&\n      Math.abs(rect.height) / (element as HTMLElement).offsetHeight) ||\n    1;\n  return {\n    top: rect.top,\n    right: rect.left + element.clientWidth * scaleX,\n    bottom: rect.top + element.clientHeight * scaleY,\n    left: rect.left,\n  };\n};\n\nconst paddingValueToInt = (value: string) => {\n  const number = parseInt(value, 10);\n  return Number.isNaN(number) ? 0 : number;\n};\n\n// Follow the steps described in https://www.w3.org/TR/cssom-view-1/#element-scrolling-members,\n// assuming that the scroll option is set to 'nearest'.\nconst getScrollDistance = (\n  targetStart: number,\n  targetEnd: number,\n  scrollStart: number,\n  scrollEnd: number,\n  scrollPaddingStart: number,\n  scrollPaddingEnd: number,\n) => {\n  if (targetStart < scrollStart && targetEnd > scrollEnd) {\n    return 0;\n  }\n\n  if (targetStart < scrollStart) {\n    return -(scrollStart - targetStart + scrollPaddingStart);\n  }\n\n  if (targetEnd > scrollEnd) {\n    return targetEnd - targetStart > scrollEnd - scrollStart\n      ? targetStart + scrollPaddingStart - scrollStart\n      : targetEnd - scrollEnd + scrollPaddingEnd;\n  }\n  return 0;\n};\n\nconst scrollRectIntoView = (root: HTMLElement, targetRect: Rect) => {\n  const document = root.ownerDocument;\n\n  let rect = targetRect;\n\n  let current: Element | null = root;\n  while (current) {\n    const isDocumentBody: boolean = current === document.body;\n    const bounding = isDocumentBody\n      ? {\n          top: 0,\n          right:\n            window.visualViewport?.width ??\n            document.documentElement.clientWidth,\n          bottom:\n            window.visualViewport?.height ??\n            document.documentElement.clientHeight,\n          left: 0,\n        }\n      : getElementRect(current);\n\n    const style = getComputedStyle(current);\n    const scrollDistanceX = getScrollDistance(\n      rect.left,\n      rect.right,\n      bounding.left,\n      bounding.right,\n      paddingValueToInt(style.scrollPaddingLeft),\n      paddingValueToInt(style.scrollPaddingRight),\n    );\n    const scrollDistanceY = getScrollDistance(\n      rect.top,\n      rect.bottom,\n      bounding.top,\n      bounding.bottom,\n      paddingValueToInt(style.scrollPaddingTop),\n      paddingValueToInt(style.scrollPaddingBottom),\n    );\n    if (scrollDistanceX || scrollDistanceY) {\n      if (isDocumentBody) {\n        document.defaultView?.scrollBy(scrollDistanceX, scrollDistanceY);\n      } else {\n        const { scrollLeft, scrollTop } = current;\n        if (scrollDistanceY) {\n          current.scrollTop += scrollDistanceY;\n        }\n        if (scrollDistanceX) {\n          current.scrollLeft += scrollDistanceX;\n        }\n        const scrolledLeft = current.scrollLeft - scrollLeft;\n        const scrolledTop = current.scrollTop - scrollTop;\n        rect = {\n          left: rect.left - scrolledLeft,\n          top: rect.top - scrolledTop,\n          right: rect.right - scrolledLeft,\n          bottom: rect.bottom - scrolledTop,\n        };\n      }\n    }\n\n    current =\n      isDocumentBody || style.position === 'fixed'\n        ? null\n        : getParentElement(current);\n  }\n};\n\nexport default scrollRectIntoView;\n"], "mappings": ";;;;;;;AAOA,IAAMA,gBAAgB,GAAI,SAApBA,gBAAgBA,CAAIC,OAAa;EAAA,OACrCA,OAAO,CAACC,aAAa,IAAKD,OAAO,CAACE,WAAW,CAAC,CAAC,CAAgBC,IAAI,IAAI,IAAI;AAAA;AAE7E,IAAMC,cAAc,GAAI,SAAlBA,cAAcA,CAAIJ,OAAgB,EAAW;EACjD,IAAMK,IAAI,GAAGL,OAAO,CAACM,qBAAqB,CAAC,CAAC;EAC5C,IAAMC,MAAM,GACT,aAAa,IAAIP,OAAO,IACvBQ,IAAI,CAACC,GAAG,CAACJ,IAAI,CAACK,KAAK,CAAC,GAAIV,OAAO,CAAiBW,WAAW,IAC7D,CAAC;EACH,IAAMC,MAAM,GACT,cAAc,IAAIZ,OAAO,IACxBQ,IAAI,CAACC,GAAG,CAACJ,IAAI,CAACQ,MAAM,CAAC,GAAIb,OAAO,CAAiBc,YAAY,IAC/D,CAAC;EACH,OAAO;IACLC,GAAG,EAAEV,IAAI,CAACU,GAAG;IACbC,KAAK,EAAEX,IAAI,CAACY,IAAI,GAAGjB,OAAO,CAACkB,WAAW,GAAGX,MAAM;IAC/CY,MAAM,EAAEd,IAAI,CAACU,GAAG,GAAGf,OAAO,CAACoB,YAAY,GAAGR,MAAM;IAChDK,IAAI,EAAEZ,IAAI,CAACY;EACb,CAAC;AACH,CAAC;AAED,IAAMI,iBAAiB,GAAI,SAArBA,iBAAiBA,CAAIC,KAAa,EAAK;EAC3C,IAAMC,MAAM,GAAGC,QAAQ,CAACF,KAAK,EAAE,EAAE,CAAC;EAClC,OAAOG,MAAM,CAACC,KAAK,CAACH,MAAM,CAAC,GAAG,CAAC,GAAGA,MAAM;AAC1C,CAAC;;AAED;AACA;AACA,IAAMI,iBAAiB,GAAG,SAApBA,iBAAiBA,CACrBC,WAAmB,EACnBC,SAAiB,EACjBC,WAAmB,EACnBC,SAAiB,EACjBC,kBAA0B,EAC1BC,gBAAwB,EACrB;EACH,IAAIL,WAAW,GAAGE,WAAW,IAAID,SAAS,GAAGE,SAAS,EAAE;IACtD,OAAO,CAAC;EACV;EAEA,IAAIH,WAAW,GAAGE,WAAW,EAAE;IAC7B,OAAO,EAAEA,WAAW,GAAGF,WAAW,GAAGI,kBAAkB,CAAC;EAC1D;EAEA,IAAIH,SAAS,GAAGE,SAAS,EAAE;IACzB,OAAOF,SAAS,GAAGD,WAAW,GAAGG,SAAS,GAAGD,WAAW,GACpDF,WAAW,GAAGI,kBAAkB,GAAGF,WAAW,GAC9CD,SAAS,GAAGE,SAAS,GAAGE,gBAAgB;EAC9C;EACA,OAAO,CAAC;AACV,CAAC;AAED,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIC,IAAiB,EAAEC,UAAgB,EAAK;EAClE,IAAMC,QAAQ,GAAGF,IAAI,CAACG,aAAa;EAEnC,IAAIjC,IAAI,GAAG+B,UAAU;EAErB,IAAIG,OAAuB,GAAGJ,IAAI;EAClC,OAAOI,OAAO,EAAE;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACd,IAAMC,cAAuB,GAAGL,OAAO,KAAKF,QAAQ,CAACQ,IAAI;IACzD,IAAMC,QAAQ,GAAGF,cAAc,GAC3B;MACE7B,GAAG,EAAE,CAAC;MACNC,KAAK,GAAAwB,qBAAA,IAAAC,sBAAA,GACHM,MAAM,CAACC,cAAc,cAAAP,sBAAA,uBAArBA,sBAAA,CAAuB/B,KAAK,cAAA8B,qBAAA,cAAAA,qBAAA,GAC5BH,QAAQ,CAACY,eAAe,CAAC/B,WAAW;MACtCC,MAAM,GAAAuB,sBAAA,IAAAC,sBAAA,GACJI,MAAM,CAACC,cAAc,cAAAL,sBAAA,uBAArBA,sBAAA,CAAuB9B,MAAM,cAAA6B,sBAAA,cAAAA,sBAAA,GAC7BL,QAAQ,CAACY,eAAe,CAAC7B,YAAY;MACvCH,IAAI,EAAE;IACR,CAAC,GACDb,cAAc,CAACmC,OAAO,CAAC;IAE3B,IAAMW,KAAK,GAAGC,gBAAgB,CAACZ,OAAO,CAAC;IACvC,IAAMa,eAAe,GAAGzB,iBAAiB,CACvCtB,IAAI,CAACY,IAAI,EACTZ,IAAI,CAACW,KAAK,EACV8B,QAAQ,CAAC7B,IAAI,EACb6B,QAAQ,CAAC9B,KAAK,EACdK,iBAAiB,CAAC6B,KAAK,CAACG,iBAAiB,CAAC,EAC1ChC,iBAAiB,CAAC6B,KAAK,CAACI,kBAAkB,CAC5C,CAAC;IACD,IAAMC,eAAe,GAAG5B,iBAAiB,CACvCtB,IAAI,CAACU,GAAG,EACRV,IAAI,CAACc,MAAM,EACX2B,QAAQ,CAAC/B,GAAG,EACZ+B,QAAQ,CAAC3B,MAAM,EACfE,iBAAiB,CAAC6B,KAAK,CAACM,gBAAgB,CAAC,EACzCnC,iBAAiB,CAAC6B,KAAK,CAACO,mBAAmB,CAC7C,CAAC;IACD,IAAIL,eAAe,IAAIG,eAAe,EAAE;MACtC,IAAIX,cAAc,EAAE;QAAA,IAAAc,qBAAA;QAClB,CAAAA,qBAAA,GAAArB,QAAQ,CAACsB,WAAW,cAAAD,qBAAA,eAApBA,qBAAA,CAAsBE,QAAQ,CAACR,eAAe,EAAEG,eAAe,CAAC;MAClE,CAAC,MAAM;QACL,IAAAM,QAAA,GAAkCtB,OAAO;UAAjCuB,UAAU,GAAAD,QAAA,CAAVC,UAAU;UAAEC,SAAA,GAAAF,QAAA,CAAAE,SAAA;QACpB,IAAIR,eAAe,EAAE;UACnBhB,OAAO,CAACwB,SAAS,IAAIR,eAAe;QACtC;QACA,IAAIH,eAAe,EAAE;UACnBb,OAAO,CAACuB,UAAU,IAAIV,eAAe;QACvC;QACA,IAAMY,YAAY,GAAGzB,OAAO,CAACuB,UAAU,GAAGA,UAAU;QACpD,IAAMG,WAAW,GAAG1B,OAAO,CAACwB,SAAS,GAAGA,SAAS;QACjD1D,IAAI,GAAG;UACLY,IAAI,EAAEZ,IAAI,CAACY,IAAI,GAAG+C,YAAY;UAC9BjD,GAAG,EAAEV,IAAI,CAACU,GAAG,GAAGkD,WAAW;UAC3BjD,KAAK,EAAEX,IAAI,CAACW,KAAK,GAAGgD,YAAY;UAChC7C,MAAM,EAAEd,IAAI,CAACc,MAAM,GAAG8C;QACxB,CAAC;MACH;IACF;IAEA1B,OAAO,GACLK,cAAc,IAAIM,KAAK,CAACgB,QAAQ,KAAK,OAAO,GACxC,IAAI,GACJnE,gBAAgB,CAACwC,OAAO,CAAC;EACjC;AACF,CAAC;AAAA,IAAA4B,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEcnC,kBAAkB", "ignoreList": []}]}