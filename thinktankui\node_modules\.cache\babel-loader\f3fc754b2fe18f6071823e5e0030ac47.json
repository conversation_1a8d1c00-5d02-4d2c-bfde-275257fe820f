{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\code.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\code.js", "mtime": 1749104421067}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_block", "_interopRequireDefault", "require", "_break", "_cursor", "_inline", "_text", "_interopRequireWildcard", "_container", "_quill", "CodeBlockContainer", "exports", "_Container", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "key", "value", "code", "index", "length", "children", "map", "child", "domNode", "innerText", "join", "slice", "html", "concat", "escapeText", "create", "_superPropGet2", "setAttribute", "Container", "CodeBlock", "_Block", "register", "<PERSON><PERSON><PERSON>", "Block", "_defineProperty2", "Code", "_Inline", "Inline", "blotName", "tagName", "className", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TextBlot", "Break", "<PERSON><PERSON><PERSON>", "requiredC<PERSON><PERSON>"], "sources": ["../../src/formats/code.ts"], "sourcesContent": ["import Block from '../blots/block.js';\nimport Break from '../blots/break.js';\nimport Cursor from '../blots/cursor.js';\nimport Inline from '../blots/inline.js';\nimport TextBlot, { escapeText } from '../blots/text.js';\nimport Container from '../blots/container.js';\nimport Quill from '../core/quill.js';\n\nclass CodeBlockContainer extends Container {\n  static create(value: string) {\n    const domNode = super.create(value) as Element;\n    domNode.setAttribute('spellcheck', 'false');\n    return domNode;\n  }\n\n  code(index: number, length: number) {\n    return (\n      this.children\n        // @ts-expect-error\n        .map((child) => (child.length() <= 1 ? '' : child.domNode.innerText))\n        .join('\\n')\n        .slice(index, index + length)\n    );\n  }\n\n  html(index: number, length: number) {\n    // `\\n`s are needed in order to support empty lines at the beginning and the end.\n    // https://html.spec.whatwg.org/multipage/syntax.html#element-restrictions\n    return `<pre>\\n${escapeText(this.code(index, length))}\\n</pre>`;\n  }\n}\n\nclass CodeBlock extends Block {\n  static TAB = '  ';\n\n  static register() {\n    Quill.register(CodeBlockContainer);\n  }\n}\n\nclass Code extends Inline {}\nCode.blotName = 'code';\nCode.tagName = 'CODE';\n\nCodeBlock.blotName = 'code-block';\nCodeBlock.className = 'ql-code-block';\nCodeBlock.tagName = 'DIV';\nCodeBlockContainer.blotName = 'code-block-container';\nCodeBlockContainer.className = 'ql-code-block-container';\nCodeBlockContainer.tagName = 'DIV';\n\nCodeBlockContainer.allowedChildren = [CodeBlock];\n\nCodeBlock.allowedChildren = [TextBlot, Break, Cursor];\nCodeBlock.requiredContainer = CodeBlockContainer;\n\nexport { Code, CodeBlockContainer, CodeBlock as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,OAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,OAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,KAAA,GAAAC,uBAAA,CAAAL,OAAA;AACA,IAAAM,UAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,MAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAoC,IAE9BQ,kBAAkB,GAAAC,OAAA,CAAAD,kBAAA,0BAAAE,UAAA;EAAA,SAAAF,mBAAA;IAAA,IAAAG,gBAAA,CAAAC,OAAA,QAAAJ,kBAAA;IAAA,WAAAK,WAAA,CAAAD,OAAA,QAAAJ,kBAAA,EAAAM,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAJ,kBAAA,EAAAE,UAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAJ,kBAAA;IAAAS,GAAA;IAAAC,KAAA,EAOtB,SAAAC,IAAIA,CAACC,KAAa,EAAEC,MAAc,EAAE;MAClC,OACE,IAAI,CAACC;MACH;MAAA,CACCC,GAAG,CAAE,UAAAC,KAAK;QAAA,OAAMA,KAAK,CAACH,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAGG,KAAK,CAACC,OAAO,CAACC,SAAU;MAAA,EAAC,CACpEC,IAAI,CAAC,IAAI,CAAC,CACVC,KAAK,CAACR,KAAK,EAAEA,KAAK,GAAGC,MAAM,CAAC;IAEnC;EAAA;IAAAJ,GAAA;IAAAC,KAAA,EAEA,SAAAW,IAAIA,CAACT,KAAa,EAAEC,MAAc,EAAE;MAClC;MACA;MACA,iBAAAS,MAAA,CAAiB,IAAAC,gBAAU,EAAC,IAAI,CAACZ,IAAI,CAACC,KAAK,EAAEC,MAAM,CAAC,CAAE;IACxD;EAAA;IAAAJ,GAAA;IAAAC,KAAA,EApBA,SAAOc,MAAMA,CAACd,KAAa,EAAE;MAC3B,IAAMO,OAAO,OAAAQ,cAAA,CAAArB,OAAA,EAAAJ,kBAAA,sBAAgBU,KAAK,EAAY;MAC9CO,OAAO,CAACS,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC;MAC3C,OAAOT,OAAO;IAChB;EAAA;AAAA,EAL+BU,kBAAS;AAAA,IAwBpCC,SAAS,GAAA3B,OAAA,CAAAG,OAAA,0BAAAyB,MAAA;EAAA,SAAAD,UAAA;IAAA,IAAAzB,gBAAA,CAAAC,OAAA,QAAAwB,SAAA;IAAA,WAAAvB,WAAA,CAAAD,OAAA,QAAAwB,SAAA,EAAAtB,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAwB,SAAA,EAAAC,MAAA;EAAA,WAAArB,aAAA,CAAAJ,OAAA,EAAAwB,SAAA;IAAAnB,GAAA;IAAAC,KAAA,EAGb,SAAOoB,QAAQA,CAAA,EAAG;MAChBC,cAAK,CAACD,QAAQ,CAAC9B,kBAAkB,CAAC;IACpC;EAAA;AAAA,EALsBgC,cAAK;AAAA,IAAAC,gBAAA,CAAA7B,OAAA,EAAvBwB,SAAS,SACA,IAAI;AAAA,IAObM,IAAI,GAAAjC,OAAA,CAAAiC,IAAA,0BAAAC,OAAA;EAAA,SAAAD,KAAA;IAAA,IAAA/B,gBAAA,CAAAC,OAAA,QAAA8B,IAAA;IAAA,WAAA7B,WAAA,CAAAD,OAAA,QAAA8B,IAAA,EAAA5B,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAA8B,IAAA,EAAAC,OAAA;EAAA,WAAA3B,aAAA,CAAAJ,OAAA,EAAA8B,IAAA;AAAA,EAASE,eAAM;AACzBF,IAAI,CAACG,QAAQ,GAAG,MAAM;AACtBH,IAAI,CAACI,OAAO,GAAG,MAAM;AAErBV,SAAS,CAACS,QAAQ,GAAG,YAAY;AACjCT,SAAS,CAACW,SAAS,GAAG,eAAe;AACrCX,SAAS,CAACU,OAAO,GAAG,KAAK;AACzBtC,kBAAkB,CAACqC,QAAQ,GAAG,sBAAsB;AACpDrC,kBAAkB,CAACuC,SAAS,GAAG,yBAAyB;AACxDvC,kBAAkB,CAACsC,OAAO,GAAG,KAAK;AAElCtC,kBAAkB,CAACwC,eAAe,GAAG,CAACZ,SAAS,CAAC;AAEhDA,SAAS,CAACY,eAAe,GAAG,CAACC,aAAQ,EAAEC,cAAK,EAAEC,eAAM,CAAC;AACrDf,SAAS,CAACgB,iBAAiB,GAAG5C,kBAAkB", "ignoreList": []}]}