{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\Editor\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\Editor\\index.vue", "mtime": 1749104047618}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_quill", "_interopRequireDefault", "require", "_auth", "name", "props", "value", "type", "String", "default", "height", "Number", "minHeight", "readOnly", "Boolean", "fileSize", "data", "uploadUrl", "process", "env", "VUE_APP_BASE_API", "headers", "Authorization", "getToken", "<PERSON><PERSON><PERSON>", "currentValue", "options", "theme", "bounds", "document", "body", "debug", "modules", "toolbar", "list", "indent", "size", "header", "color", "background", "align", "placeholder", "computed", "styles", "style", "concat", "watch", "handler", "val", "clipboard", "dangerouslyPasteHTML", "immediate", "mounted", "init", "<PERSON><PERSON><PERSON><PERSON>", "methods", "_this", "editor", "$refs", "getModule", "add<PERSON><PERSON><PERSON>", "upload", "$children", "input", "click", "quill", "format", "on", "delta", "<PERSON><PERSON><PERSON><PERSON>", "source", "html", "children", "innerHTML", "text", "getText", "$emit", "range", "oldRange", "eventName", "_len", "arguments", "length", "args", "Array", "_key", "apply", "handleBeforeUpload", "file", "isJPG", "includes", "$message", "error", "isLt", "handleUploadSuccess", "res", "code", "getSelection", "index", "insertEmbed", "fileName", "setSelection", "handleUploadError"], "sources": ["src/components/Editor/index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-upload\r\n      :action=\"uploadUrl\"\r\n      :before-upload=\"handleBeforeUpload\"\r\n      :on-success=\"handleUploadSuccess\"\r\n      :on-error=\"handleUploadError\"\r\n      name=\"file\"\r\n      :show-file-list=\"false\"\r\n      :headers=\"headers\"\r\n      style=\"display: none\"\r\n      ref=\"upload\"\r\n      v-if=\"this.type == 'url'\"\r\n    >\r\n    </el-upload>\r\n    <div class=\"editor\" ref=\"editor\" :style=\"styles\"></div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Quill from \"quill\";\r\nimport \"quill/dist/quill.core.css\";\r\nimport \"quill/dist/quill.snow.css\";\r\nimport \"quill/dist/quill.bubble.css\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"Editor\",\r\n  props: {\r\n    /* 编辑器的内容 */\r\n    value: {\r\n      type: String,\r\n      default: \"\",\r\n    },\r\n    /* 高度 */\r\n    height: {\r\n      type: Number,\r\n      default: null,\r\n    },\r\n    /* 最小高度 */\r\n    minHeight: {\r\n      type: Number,\r\n      default: null,\r\n    },\r\n    /* 只读 */\r\n    readOnly: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    /* 上传文件大小限制(MB) */\r\n    fileSize: {\r\n      type: Number,\r\n      default: 5,\r\n    },\r\n    /* 类型（base64格式、url格式） */\r\n    type: {\r\n      type: String,\r\n      default: \"url\",\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/common/upload\", // 上传的图片服务器地址\r\n      headers: {\r\n        Authorization: \"Bearer \" + getToken()\r\n      },\r\n      Quill: null,\r\n      currentValue: \"\",\r\n      options: {\r\n        theme: \"snow\",\r\n        bounds: document.body,\r\n        debug: \"warn\",\r\n        modules: {\r\n          // 工具栏配置\r\n          toolbar: [\r\n            [\"bold\", \"italic\", \"underline\", \"strike\"],       // 加粗 斜体 下划线 删除线\r\n            [\"blockquote\", \"code-block\"],                    // 引用  代码块\r\n            [{ list: \"ordered\" }, { list: \"bullet\" }],       // 有序、无序列表\r\n            [{ indent: \"-1\" }, { indent: \"+1\" }],            // 缩进\r\n            [{ size: [\"small\", false, \"large\", \"huge\"] }],   // 字体大小\r\n            [{ header: [1, 2, 3, 4, 5, 6, false] }],         // 标题\r\n            [{ color: [] }, { background: [] }],             // 字体颜色、字体背景颜色\r\n            [{ align: [] }],                                 // 对齐方式\r\n            [\"clean\"],                                       // 清除文本格式\r\n            [\"link\", \"image\", \"video\"]                       // 链接、图片、视频\r\n          ],\r\n        },\r\n        placeholder: \"请输入内容\",\r\n        readOnly: this.readOnly,\r\n      },\r\n    };\r\n  },\r\n  computed: {\r\n    styles() {\r\n      let style = {};\r\n      if (this.minHeight) {\r\n        style.minHeight = `${this.minHeight}px`;\r\n      }\r\n      if (this.height) {\r\n        style.height = `${this.height}px`;\r\n      }\r\n      return style;\r\n    },\r\n  },\r\n  watch: {\r\n    value: {\r\n      handler(val) {\r\n        if (val !== this.currentValue) {\r\n          this.currentValue = val === null ? \"\" : val;\r\n          if (this.Quill) {\r\n            this.Quill.clipboard.dangerouslyPasteHTML(this.currentValue);\r\n          }\r\n        }\r\n      },\r\n      immediate: true,\r\n    },\r\n  },\r\n  mounted() {\r\n    this.init();\r\n  },\r\n  beforeDestroy() {\r\n    this.Quill = null;\r\n  },\r\n  methods: {\r\n    init() {\r\n      const editor = this.$refs.editor;\r\n      this.Quill = new Quill(editor, this.options);\r\n      // 如果设置了上传地址则自定义图片上传事件\r\n      if (this.type == 'url') {\r\n        let toolbar = this.Quill.getModule(\"toolbar\");\r\n        toolbar.addHandler(\"image\", (value) => {\r\n          if (value) {\r\n            this.$refs.upload.$children[0].$refs.input.click();\r\n          } else {\r\n            this.quill.format(\"image\", false);\r\n          }\r\n        });\r\n      }\r\n      this.Quill.clipboard.dangerouslyPasteHTML(this.currentValue);\r\n      this.Quill.on(\"text-change\", (delta, oldDelta, source) => {\r\n        const html = this.$refs.editor.children[0].innerHTML;\r\n        const text = this.Quill.getText();\r\n        const quill = this.Quill;\r\n        this.currentValue = html;\r\n        this.$emit(\"input\", html);\r\n        this.$emit(\"on-change\", { html, text, quill });\r\n      });\r\n      this.Quill.on(\"text-change\", (delta, oldDelta, source) => {\r\n        this.$emit(\"on-text-change\", delta, oldDelta, source);\r\n      });\r\n      this.Quill.on(\"selection-change\", (range, oldRange, source) => {\r\n        this.$emit(\"on-selection-change\", range, oldRange, source);\r\n      });\r\n      this.Quill.on(\"editor-change\", (eventName, ...args) => {\r\n        this.$emit(\"on-editor-change\", eventName, ...args);\r\n      });\r\n    },\r\n    // 上传前校检格式和大小\r\n    handleBeforeUpload(file) {\r\n      const type = [\"image/jpeg\", \"image/jpg\", \"image/png\", \"image/svg\"];\r\n      const isJPG = type.includes(file.type);\r\n      // 检验文件格式\r\n      if (!isJPG) {\r\n        this.$message.error(`图片格式错误!`);\r\n        return false;\r\n      }\r\n      // 校检文件大小\r\n      if (this.fileSize) {\r\n        const isLt = file.size / 1024 / 1024 < this.fileSize;\r\n        if (!isLt) {\r\n          this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`);\r\n          return false;\r\n        }\r\n      }\r\n      return true;\r\n    },\r\n    handleUploadSuccess(res, file) {\r\n      // 如果上传成功\r\n      if (res.code == 200) {\r\n        // 获取富文本组件实例\r\n        let quill = this.Quill;\r\n        // 获取光标所在位置\r\n        let length = quill.getSelection().index;\r\n        // 插入图片  res.url为服务器返回的图片地址\r\n        quill.insertEmbed(length, \"image\", process.env.VUE_APP_BASE_API + res.fileName);\r\n        // 调整光标到最后\r\n        quill.setSelection(length + 1);\r\n      } else {\r\n        this.$message.error(\"图片插入失败\");\r\n      }\r\n    },\r\n    handleUploadError() {\r\n      this.$message.error(\"图片插入失败\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.editor, .ql-toolbar {\r\n  white-space: pre-wrap !important;\r\n  line-height: normal !important;\r\n}\r\n.quill-img {\r\n  display: none;\r\n}\r\n.ql-snow .ql-tooltip[data-mode=\"link\"]::before {\r\n  content: \"请输入链接地址:\";\r\n}\r\n.ql-snow .ql-tooltip.ql-editing a.ql-action::after {\r\n  border-right: 0px;\r\n  content: \"保存\";\r\n  padding-right: 0px;\r\n}\r\n.ql-snow .ql-tooltip[data-mode=\"video\"]::before {\r\n  content: \"请输入视频地址:\";\r\n}\r\n.ql-snow .ql-picker.ql-size .ql-picker-label::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item::before {\r\n  content: \"14px\";\r\n}\r\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"small\"]::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"small\"]::before {\r\n  content: \"10px\";\r\n}\r\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"large\"]::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"large\"]::before {\r\n  content: \"18px\";\r\n}\r\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"huge\"]::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"huge\"]::before {\r\n  content: \"32px\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item::before {\r\n  content: \"文本\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"1\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"1\"]::before {\r\n  content: \"标题1\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"2\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"2\"]::before {\r\n  content: \"标题2\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"3\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"3\"]::before {\r\n  content: \"标题3\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"4\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"4\"]::before {\r\n  content: \"标题4\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"5\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"5\"]::before {\r\n  content: \"标题5\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"6\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"6\"]::before {\r\n  content: \"标题6\";\r\n}\r\n.ql-snow .ql-picker.ql-font .ql-picker-label::before,\r\n.ql-snow .ql-picker.ql-font .ql-picker-item::before {\r\n  content: \"标准字体\";\r\n}\r\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"serif\"]::before,\r\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"serif\"]::before {\r\n  content: \"衬线字体\";\r\n}\r\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"monospace\"]::before,\r\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"monospace\"]::before {\r\n  content: \"等宽字体\";\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;AAoBA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,KAAA;IACA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACA;IACAC,MAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACA;IACAG,SAAA;MACAL,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACA;IACAI,QAAA;MACAN,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACA;IACAM,QAAA;MACAR,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACA;IACAF,IAAA;MACAA,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAO,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MAAA;MACAC,OAAA;QACAC,aAAA,kBAAAC,cAAA;MACA;MACAC,KAAA;MACAC,YAAA;MACAC,OAAA;QACAC,KAAA;QACAC,MAAA,EAAAC,QAAA,CAAAC,IAAA;QACAC,KAAA;QACAC,OAAA;UACA;UACAC,OAAA,GACA;UAAA;UACA;UAAA;UACA;YAAAC,IAAA;UAAA;YAAAA,IAAA;UAAA;UAAA;UACA;YAAAC,MAAA;UAAA;YAAAA,MAAA;UAAA;UAAA;UACA;YAAAC,IAAA;UAAA;UAAA;UACA;YAAAC,MAAA;UAAA;UAAA;UACA;YAAAC,KAAA;UAAA;YAAAC,UAAA;UAAA;UAAA;UACA;YAAAC,KAAA;UAAA;UAAA;UACA;UAAA;UACA;UAAA;QAEA;QACAC,WAAA;QACA5B,QAAA,OAAAA;MACA;IACA;EACA;EACA6B,QAAA;IACAC,MAAA,WAAAA,OAAA;MACA,IAAAC,KAAA;MACA,SAAAhC,SAAA;QACAgC,KAAA,CAAAhC,SAAA,MAAAiC,MAAA,MAAAjC,SAAA;MACA;MACA,SAAAF,MAAA;QACAkC,KAAA,CAAAlC,MAAA,MAAAmC,MAAA,MAAAnC,MAAA;MACA;MACA,OAAAkC,KAAA;IACA;EACA;EACAE,KAAA;IACAxC,KAAA;MACAyC,OAAA,WAAAA,QAAAC,GAAA;QACA,IAAAA,GAAA,UAAAvB,YAAA;UACA,KAAAA,YAAA,GAAAuB,GAAA,iBAAAA,GAAA;UACA,SAAAxB,KAAA;YACA,KAAAA,KAAA,CAAAyB,SAAA,CAAAC,oBAAA,MAAAzB,YAAA;UACA;QACA;MACA;MACA0B,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,KAAA9B,KAAA;EACA;EACA+B,OAAA;IACAF,IAAA,WAAAA,KAAA;MAAA,IAAAG,KAAA;MACA,IAAAC,MAAA,QAAAC,KAAA,CAAAD,MAAA;MACA,KAAAjC,KAAA,OAAAA,cAAA,CAAAiC,MAAA,OAAA/B,OAAA;MACA;MACA,SAAAnB,IAAA;QACA,IAAA0B,OAAA,QAAAT,KAAA,CAAAmC,SAAA;QACA1B,OAAA,CAAA2B,UAAA,oBAAAtD,KAAA;UACA,IAAAA,KAAA;YACAkD,KAAA,CAAAE,KAAA,CAAAG,MAAA,CAAAC,SAAA,IAAAJ,KAAA,CAAAK,KAAA,CAAAC,KAAA;UACA;YACAR,KAAA,CAAAS,KAAA,CAAAC,MAAA;UACA;QACA;MACA;MACA,KAAA1C,KAAA,CAAAyB,SAAA,CAAAC,oBAAA,MAAAzB,YAAA;MACA,KAAAD,KAAA,CAAA2C,EAAA,0BAAAC,KAAA,EAAAC,QAAA,EAAAC,MAAA;QACA,IAAAC,IAAA,GAAAf,KAAA,CAAAE,KAAA,CAAAD,MAAA,CAAAe,QAAA,IAAAC,SAAA;QACA,IAAAC,IAAA,GAAAlB,KAAA,CAAAhC,KAAA,CAAAmD,OAAA;QACA,IAAAV,KAAA,GAAAT,KAAA,CAAAhC,KAAA;QACAgC,KAAA,CAAA/B,YAAA,GAAA8C,IAAA;QACAf,KAAA,CAAAoB,KAAA,UAAAL,IAAA;QACAf,KAAA,CAAAoB,KAAA;UAAAL,IAAA,EAAAA,IAAA;UAAAG,IAAA,EAAAA,IAAA;UAAAT,KAAA,EAAAA;QAAA;MACA;MACA,KAAAzC,KAAA,CAAA2C,EAAA,0BAAAC,KAAA,EAAAC,QAAA,EAAAC,MAAA;QACAd,KAAA,CAAAoB,KAAA,mBAAAR,KAAA,EAAAC,QAAA,EAAAC,MAAA;MACA;MACA,KAAA9C,KAAA,CAAA2C,EAAA,+BAAAU,KAAA,EAAAC,QAAA,EAAAR,MAAA;QACAd,KAAA,CAAAoB,KAAA,wBAAAC,KAAA,EAAAC,QAAA,EAAAR,MAAA;MACA;MACA,KAAA9C,KAAA,CAAA2C,EAAA,4BAAAY,SAAA;QAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;UAAAF,IAAA,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;QAAA;QACA7B,KAAA,CAAAoB,KAAA,CAAAU,KAAA,CAAA9B,KAAA,uBAAAuB,SAAA,EAAAlC,MAAA,CAAAsC,IAAA;MACA;IACA;IACA;IACAI,kBAAA,WAAAA,mBAAAC,IAAA;MACA,IAAAjF,IAAA;MACA,IAAAkF,KAAA,GAAAlF,IAAA,CAAAmF,QAAA,CAAAF,IAAA,CAAAjF,IAAA;MACA;MACA,KAAAkF,KAAA;QACA,KAAAE,QAAA,CAAAC,KAAA;QACA;MACA;MACA;MACA,SAAA7E,QAAA;QACA,IAAA8E,IAAA,GAAAL,IAAA,CAAApD,IAAA,sBAAArB,QAAA;QACA,KAAA8E,IAAA;UACA,KAAAF,QAAA,CAAAC,KAAA,iEAAA/C,MAAA,MAAA9B,QAAA;UACA;QACA;MACA;MACA;IACA;IACA+E,mBAAA,WAAAA,oBAAAC,GAAA,EAAAP,IAAA;MACA;MACA,IAAAO,GAAA,CAAAC,IAAA;QACA;QACA,IAAA/B,KAAA,QAAAzC,KAAA;QACA;QACA,IAAA0D,MAAA,GAAAjB,KAAA,CAAAgC,YAAA,GAAAC,KAAA;QACA;QACAjC,KAAA,CAAAkC,WAAA,CAAAjB,MAAA,WAAAhE,OAAA,CAAAC,GAAA,CAAAC,gBAAA,GAAA2E,GAAA,CAAAK,QAAA;QACA;QACAnC,KAAA,CAAAoC,YAAA,CAAAnB,MAAA;MACA;QACA,KAAAS,QAAA,CAAAC,KAAA;MACA;IACA;IACAU,iBAAA,WAAAA,kBAAA;MACA,KAAAX,QAAA,CAAAC,KAAA;IACA;EACA;AACA", "ignoreList": []}]}