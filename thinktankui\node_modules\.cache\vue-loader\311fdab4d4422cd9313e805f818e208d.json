{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\account\\index.vue?vue&type=style&index=0&id=7427b2c6&lang=scss&scoped=true", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\account\\index.vue", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmFwcC1jb250YWluZXIgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1Ow0KICBtaW4taGVpZ2h0OiAxMDB2aDsNCiAgcGFkZGluZzogMjBweDsNCn0NCg0KLnBhZ2UtbGF5b3V0IHsNCiAgZGlzcGxheTogZmxleDsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBib3gtc2hhZG93OiAwIDFweCA0cHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KfQ0KDQoubGVmdC1zaWRlYmFyIHsNCiAgd2lkdGg6IDIwMHB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICBib3JkZXItcmlnaHQ6IDFweCBzb2xpZCAjZTZlNmU2Ow0KfQ0KDQoudXNlci1pbmZvIHsNCiAgcGFkZGluZzogMjBweDsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U2ZTZlNjsNCg0KICAuYXZhdGFyIHsNCiAgICB3aWR0aDogNjBweDsNCiAgICBoZWlnaHQ6IDYwcHg7DQogICAgbWFyZ2luOiAwIGF1dG8gMTBweDsNCiAgICBib3JkZXItcmFkaXVzOiA1MCU7DQogICAgb3ZlcmZsb3c6IGhpZGRlbjsNCg0KICAgIGltZyB7DQogICAgICB3aWR0aDogMTAwJTsNCiAgICAgIGhlaWdodDogMTAwJTsNCiAgICAgIG9iamVjdC1maXQ6IGNvdmVyOw0KICAgIH0NCiAgfQ0KDQogIC51c2VyLWlkIHsNCiAgICBmb250LXNpemU6IDE2cHg7DQogICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgbWFyZ2luLWJvdHRvbTogNXB4Ow0KICB9DQoNCiAgLnJlZ2lzdGVyLWRhdGUgew0KICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICBjb2xvcjogIzk5OTsNCiAgfQ0KfQ0KDQouc2lkZWJhci1tZW51IHsNCiAgLnNpZGViYXItbWVudS1saXN0IHsNCiAgICBib3JkZXItcmlnaHQ6IG5vbmU7DQogIH0NCg0KICAuZWwtbWVudS1pdGVtIHsNCiAgICBoZWlnaHQ6IDUwcHg7DQogICAgbGluZS1oZWlnaHQ6IDUwcHg7DQoNCiAgICBpIHsNCiAgICAgIG1hcmdpbi1yaWdodDogNXB4Ow0KICAgICAgY29sb3I6ICM2NjY7DQogICAgfQ0KICB9DQoNCiAgLmVsLW1lbnUtaXRlbS5pcy1hY3RpdmUgew0KICAgIGJhY2tncm91bmQtY29sb3I6ICNmMGY5ZWI7DQogICAgY29sb3I6ICM2N2MyM2E7DQoNCiAgICBpIHsNCiAgICAgIGNvbG9yOiAjNjdjMjNhOw0KICAgIH0NCiAgfQ0KfQ0KDQouY29udGVudCB7DQogIGZsZXg6IDE7DQogIHBhZGRpbmc6IDIwcHg7DQp9DQoNCi5hY2NvdW50LWNvbnRhaW5lciB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQp9DQoNCi5hY2NvdW50LWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgcGFkZGluZy1ib3R0b206IDIwcHg7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZWVlOw0KDQogIC50aXRsZSB7DQogICAgZm9udC1zaXplOiAxOHB4Ow0KICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICB9DQp9DQoNCi5hY2NvdW50LWluZm8gew0KICAuaW5mby1pdGVtIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIG1hcmdpbi1ib3R0b206IDIwcHg7DQogICAgbGluZS1oZWlnaHQ6IDI0cHg7DQoNCiAgICAubGFiZWwgew0KICAgICAgd2lkdGg6IDEyMHB4Ow0KICAgICAgY29sb3I6ICM2NjY7DQogICAgICB0ZXh0LWFsaWduOiByaWdodDsNCiAgICAgIHBhZGRpbmctcmlnaHQ6IDEwcHg7DQogICAgfQ0KDQogICAgLnZhbHVlIHsNCiAgICAgIGZsZXg6IDE7DQogICAgICBjb2xvcjogIzMzMzsNCiAgICB9DQogIH0NCn0NCg0KLyog57O757uf6K6+572u5qC35byPICovDQouc2V0dGluZ3MtY29udGFpbmVyIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCn0NCg0KLnNldHRpbmdzLWhlYWRlciB7DQogIHBhZGRpbmctYm90dG9tOiAxNXB4Ow0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KDQogIC50aXRsZSB7DQogICAgZm9udC1zaXplOiAxOHB4Ow0KICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICB9DQp9DQoNCi5zZXR0aW5ncy1jb250ZW50IHsNCiAgZGlzcGxheTogZmxleDsNCn0NCg0KLnNldHRpbmdzLXNpZGViYXIgew0KICB3aWR0aDogMTIwcHg7DQogIGJvcmRlci1yaWdodDogMXB4IHNvbGlkICNlZWU7DQogIHBhZGRpbmctcmlnaHQ6IDEwcHg7DQp9DQoNCi5zZXR0aW5ncy1tZW51IHsNCiAgLm1lbnUtaXRlbSB7DQogICAgcGFkZGluZzogMTBweCAwOw0KICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgY29sb3I6ICM2NjY7DQogICAgZm9udC1zaXplOiAxNHB4Ow0KDQogICAgaSB7DQogICAgICBtYXJnaW4tcmlnaHQ6IDhweDsNCiAgICAgIGNvbG9yOiAjMTg5MGZmOw0KICAgIH0NCg0KICAgICYuYWN0aXZlIHsNCiAgICAgIGNvbG9yOiAjMTg5MGZmOw0KICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgfQ0KDQogICAgJjpob3ZlciB7DQogICAgICBjb2xvcjogIzE4OTBmZjsNCiAgICB9DQogIH0NCn0NCg0KLnNldHRpbmdzLW1haW4gew0KICBmbGV4OiAxOw0KICBwYWRkaW5nLWxlZnQ6IDIwcHg7DQp9DQoNCi5zZXR0aW5ncy1zZWN0aW9uIHsNCiAgLnNlY3Rpb24tdGl0bGUgew0KICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgIG1hcmdpbjogMTVweCAwIDEwcHg7DQogICAgY29sb3I6ICMzMzM7DQogICAgZm9udC1zaXplOiAxNHB4Ow0KICB9DQp9DQoNCi5vcHRpb25zLWdyb3VwIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC13cmFwOiB3cmFwOw0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KDQogIC5lbC1jaGVja2JveCB7DQogICAgbWFyZ2luLXJpZ2h0OiAxNXB4Ow0KICAgIG1hcmdpbi1ib3R0b206IDEwcHg7DQogICAgbWluLXdpZHRoOiA4MHB4Ow0KICB9DQp9DQoNCi5kb3dubG9hZC1zZXR0aW5ncyB7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQoNCiAgLnNldHRpbmctaXRlbSB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICAgIGZvbnQtc2l6ZTogMTRweDsNCg0KICAgIC5sYWJlbCB7DQogICAgICB3aWR0aDogODBweDsNCiAgICAgIGNvbG9yOiAjNjY2Ow0KICAgIH0NCg0KICAgIC52YWx1ZSB7DQogICAgICBmbGV4OiAxOw0KICAgICAgY29sb3I6ICMzMzM7DQogICAgfQ0KICB9DQp9DQoNCi5zYXZlLWJ1dHRvbiB7DQogIG1hcmdpbi10b3A6IDIwcHg7DQoNCiAgLmVsLWJ1dHRvbiB7DQogICAgYmFja2dyb3VuZC1jb2xvcjogIzE4OTBmZjsNCiAgICBib3JkZXItY29sb3I6ICMxODkwZmY7DQogICAgcGFkZGluZzogOHB4IDIwcHg7DQogIH0NCn0NCg0KLyog5oiR55qE5LiL6L295qC35byPICovDQouZG93bmxvYWQtY29udGFpbmVyIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCn0NCg0KLmRvd25sb2FkLWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgcGFkZGluZy1ib3R0b206IDE1cHg7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQoNCiAgLnRpdGxlIHsNCiAgICBmb250LXNpemU6IDE4cHg7DQogICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogIH0NCg0KICAuYWN0aW9ucyB7DQogICAgLmVsLWJ1dHRvbiB7DQogICAgICBtYXJnaW4tbGVmdDogMTBweDsNCiAgICB9DQogIH0NCn0NCg0KLmRvd25sb2FkLWNvbnRlbnQgew0KICAuZWwtdGFibGUgew0KICAgIG1hcmdpbi1ib3R0b206IDIwcHg7DQogIH0NCg0KICAuZG93bmxvYWQtc3RhdHVzIHsNCiAgICBjb2xvcjogIzY3YzIzYTsNCiAgfQ0KfQ0KDQoucGFnaW5hdGlvbi1jb250YWluZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgbWFyZ2luLXRvcDogMjBweDsNCg0KICBzcGFuIHsNCiAgICBtYXJnaW46IDAgNXB4Ow0KICB9DQoNCiAgLmp1bXAtcGFnZS1pbnB1dCB7DQogICAgd2lkdGg6IDUwcHg7DQogICAgbWFyZ2luOiAwIDVweDsNCiAgfQ0KfQ0KDQovKiDkv67mlLnlr4bnoIHooajljZXmoLflvI8gKi8NCi5wYXNzd29yZC1mb3JtIHsNCiAgbWF4LXdpZHRoOiA1MDBweDsNCiAgbWFyZ2luOiAyMHB4IDA7DQoNCiAgLmZvcm0tZ3JvdXAgew0KICAgIG1hcmdpbi1ib3R0b206IDIwcHg7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KDQogICAgLmZvcm0tbGFiZWwgew0KICAgICAgd2lkdGg6IDEwMHB4Ow0KICAgICAgdGV4dC1hbGlnbjogcmlnaHQ7DQogICAgICBtYXJnaW4tcmlnaHQ6IDE1cHg7DQogICAgICBjb2xvcjogIzYwNjI2NjsNCiAgICB9DQoNCiAgICAuZWwtaW5wdXQgew0KICAgICAgd2lkdGg6IDMwMHB4Ow0KICAgIH0NCiAgfQ0KDQogIC5mb3JtLWFjdGlvbnMgew0KICAgIG1hcmdpbi10b3A6IDMwcHg7DQogICAgcGFkZGluZy1sZWZ0OiAxMTVweDsNCg0KICAgIC5lbC1idXR0b24gew0KICAgICAgd2lkdGg6IDEwMHB4Ow0KICAgIH0NCiAgfQ0KfQ0KDQovKiDmiJHnmoTmlLbol4/moLflvI8gKi8NCi5mYXZvcml0ZS1jb250YWluZXIgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KfQ0KDQouZmF2b3JpdGUtaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBwYWRkaW5nLWJvdHRvbTogMTVweDsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlZWU7DQoNCiAgLnRpdGxlIHsNCiAgICBmb250LXNpemU6IDE4cHg7DQogICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogIH0NCn0NCg0KLmZhdm9yaXRlLXRvb2xiYXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQoNCiAgLnRvb2xiYXItbGVmdCB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KDQogICAgLmVsLWNoZWNrYm94IHsNCiAgICAgIG1hcmdpbi1yaWdodDogMTVweDsNCiAgICB9DQoNCiAgICAuZWwtYnV0dG9uIHsNCiAgICAgIG1hcmdpbi1yaWdodDogMTBweDsNCiAgICB9DQogIH0NCg0KICAudG9vbGJhci1yaWdodCB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KDQogICAgLmRhdGUtZmlsdGVyIHsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgbWFyZ2luLXJpZ2h0OiAxNXB4Ow0KDQogICAgICAuZGF0ZS1zZXBhcmF0b3Igew0KICAgICAgICBtYXJnaW46IDAgNXB4Ow0KICAgICAgfQ0KICAgIH0NCg0KICAgIC5zZWFyY2gtYm94IHsNCiAgICAgIG1hcmdpbi1yaWdodDogMTVweDsNCiAgICB9DQogIH0NCn0NCg0KLmZhdm9yaXRlLWNvbnRlbnQgew0KICBtYXJnaW4tdG9wOiAyMHB4Ow0KDQogIC5mYXZvcml0ZS1saXN0IHsNCiAgICBib3JkZXI6IDFweCBzb2xpZCAjZWJlZWY1Ow0KICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCg0KICAgIC5mYXZvcml0ZS1pdGVtIHsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBwYWRkaW5nOiAxNXB4Ow0KICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlYmVlZjU7DQoNCiAgICAgICY6bGFzdC1jaGlsZCB7DQogICAgICAgIGJvcmRlci1ib3R0b206IG5vbmU7DQogICAgICB9DQoNCiAgICAgIC5pdGVtLWNoZWNrYm94IHsNCiAgICAgICAgbWFyZ2luLXJpZ2h0OiAxNXB4Ow0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgfQ0KDQogICAgICAuaXRlbS1jb250ZW50IHsNCiAgICAgICAgZmxleDogMTsNCg0KICAgICAgICAuaXRlbS10aXRsZSB7DQogICAgICAgICAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KDQogICAgICAgICAgLnRpdGxlLXRhZyB7DQogICAgICAgICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogICAgICAgICAgICBwYWRkaW5nOiAycHggNnB4Ow0KICAgICAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgICAgICAgY29sb3I6ICNmZmY7DQogICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjU2YzZjOw0KICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogMnB4Ow0KICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLnRpdGxlLXRleHQgew0KICAgICAgICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgICAgICAgIGNvbG9yOiAjMzAzMTMzOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIC5pdGVtLWluZm8gew0KICAgICAgICAgIGZvbnQtc2l6ZTogMTNweDsNCiAgICAgICAgICBjb2xvcjogIzkwOTM5OTsNCg0KICAgICAgICAgIC5pbmZvLXRpbWUgew0KICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiAxNXB4Ow0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAuaXRlbS1hY3Rpb25zIHsNCiAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgICAgbWFyZ2luLWxlZnQ6IDE1cHg7DQoNCiAgICAgICAgLmNhbmNlbC1mYXZvcml0ZS1idG4gew0KICAgICAgICAgIGNvbG9yOiAjZjU2YzZjOw0KDQogICAgICAgICAgJjpob3ZlciB7DQogICAgICAgICAgICBjb2xvcjogI2ZmN2M3YzsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCg0KICAuZmF2b3JpdGUtcGFnaW5hdGlvbiB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBtYXJnaW4tdG9wOiAyMHB4Ow0KICAgIHBhZGRpbmc6IDEwcHggMDsNCg0KICAgIC5wYWdpbmF0aW9uLWluZm8gew0KICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgY29sb3I6ICM2MDYyNjY7DQoNCiAgICAgIHNwYW4gew0KICAgICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgICAgY29sb3I6ICMzMDMxMzM7DQogICAgICB9DQogICAgfQ0KDQogICAgLnBhZ2luYXRpb24tY29udHJvbHMgew0KICAgICAgZmxleDogMTsNCiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICB9DQoNCiAgICAucGFnaW5hdGlvbi1qdW1wIHsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KDQogICAgICBzcGFuIHsNCiAgICAgICAgbWFyZ2luOiAwIDVweDsNCiAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICBjb2xvcjogIzYwNjI2NjsNCiAgICAgIH0NCg0KICAgICAgLmp1bXAtcGFnZS1pbnB1dCB7DQogICAgICAgIHdpZHRoOiA1MHB4Ow0KICAgICAgICBtYXJnaW46IDAgNXB4Ow0KICAgICAgfQ0KDQogICAgICAuZWwtYnV0dG9uIHsNCiAgICAgICAgbWFyZ2luLWxlZnQ6IDVweDsNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg0KLyog5oiR55qE6IGU57O75Lq65qC35byPICovDQouY29udGFjdC1jb250YWluZXIgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICBwYWRkaW5nOiAyMHB4Ow0KICBib3JkZXItcmFkaXVzOiA0cHg7DQp9DQoNCi5jb250YWN0LWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCg0KICAudGl0bGUgew0KICAgIGZvbnQtc2l6ZTogMThweDsNCiAgICBmb250LXdlaWdodDogYm9sZDsNCiAgfQ0KDQogIC5jb250YWN0LXRhYnMgew0KICAgIC5lbC1yYWRpby1ncm91cCB7DQogICAgICAuZWwtcmFkaW8tYnV0dG9uIHsNCiAgICAgICAgbWFyZ2luLXJpZ2h0OiAtMXB4Ow0KDQogICAgICAgICY6Zmlyc3QtY2hpbGQgLmVsLXJhZGlvLWJ1dHRvbl9faW5uZXIgew0KICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDRweCAwIDAgNHB4Ow0KICAgICAgICB9DQoNCiAgICAgICAgJjpsYXN0LWNoaWxkIC5lbC1yYWRpby1idXR0b25fX2lubmVyIHsNCiAgICAgICAgICBib3JkZXItcmFkaXVzOiAwIDRweCA0cHggMDsNCiAgICAgICAgfQ0KDQogICAgICAgIC5lbC1yYWRpby1idXR0b25fX2lubmVyIHsNCiAgICAgICAgICBwYWRkaW5nOiA4cHggMTVweDsNCiAgICAgICAgICBmb250LXNpemU6IDEzcHg7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg0KLmNvbnRhY3QtdG9vbGJhciB7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQp9DQoNCi5jb250YWN0LWNvbnRlbnQgew0KICAuZWwtdGFibGUgew0KICAgIG1hcmdpbi1ib3R0b206IDIwcHg7DQogIH0NCg0KICAuZGVsZXRlLWJ0biB7DQogICAgY29sb3I6ICNmNTZjNmM7DQogIH0NCn0NCg0KLyog57Sg5p2Q5bqT5qC35byPICovDQoubWF0ZXJpYWwtY29udGFpbmVyIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCn0NCg0KLm1hdGVyaWFsLWhlYWRlciB7DQogIHBhZGRpbmctYm90dG9tOiAxNXB4Ow0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KDQogIC50aXRsZSB7DQogICAgZm9udC1zaXplOiAxOHB4Ow0KICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICB9DQp9DQoNCi5tYXRlcmlhbC1jb250ZW50IHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC13cmFwOiB3cmFwOw0KDQogIC5tYXRlcmlhbC1hZGQtYm94IHsNCiAgICB3aWR0aDogMjAwcHg7DQogICAgaGVpZ2h0OiAxNTBweDsNCiAgICBib3JkZXI6IDFweCBkYXNoZWQgI2Q5ZDlkOTsNCiAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgY3Vyc29yOiBwb2ludGVyOw0KICAgIG1hcmdpbi1yaWdodDogMjBweDsNCiAgICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KDQogICAgJjpob3ZlciB7DQogICAgICBib3JkZXItY29sb3I6ICMxODkwZmY7DQoNCiAgICAgIC5hZGQtaWNvbiwgLmFkZC10ZXh0IHsNCiAgICAgICAgY29sb3I6ICMxODkwZmY7DQogICAgICB9DQogICAgfQ0KDQogICAgLmFkZC1pY29uIHsNCiAgICAgIGZvbnQtc2l6ZTogMzBweDsNCiAgICAgIGNvbG9yOiAjOGM4YzhjOw0KICAgICAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgICB9DQoNCiAgICAuYWRkLXRleHQgew0KICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgY29sb3I6ICM4YzhjOGM7DQogICAgfQ0KICB9DQp9DQoNCi5tYXRlcmlhbC1kaWFsb2cgew0KICAuZWwtZGlhbG9nX19oZWFkZXIgew0KICAgIHBhZGRpbmc6IDE1cHg7DQogICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZjBmMGYwOw0KICB9DQoNCiAgLm1hdGVyaWFsLWZvcm0gew0KICAgIHBhZGRpbmc6IDIwcHggMDsNCg0KICAgIC5mb3JtLWl0ZW0gew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KDQogICAgICAuZm9ybS1sYWJlbCB7DQogICAgICAgIHdpZHRoOiA4MHB4Ow0KICAgICAgICB0ZXh0LWFsaWduOiByaWdodDsNCiAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMHB4Ow0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC5kaWFsb2ctZm9vdGVyIHsNCiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQoNCiAgICAuZWwtYnV0dG9uIHsNCiAgICAgIHdpZHRoOiA4MHB4Ow0KICAgIH0NCiAgfQ0KfQ0KDQovKiDmm7TmlrDor7TmmI7moLflvI8gKi8NCi51cGRhdGVzLXNlY3Rpb24gew0KICAudXBkYXRlcy1oZWFkZXIgew0KICAgIG1hcmdpbi1ib3R0b206IDIwcHg7DQoNCiAgICAudmVyc2lvbi10aXRsZSB7DQogICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgIG1hcmdpbi1ib3R0b206IDVweDsNCiAgICB9DQoNCiAgICAudmVyc2lvbi1kYXRlIHsNCiAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgIGNvbG9yOiAjOTk5Ow0KICAgIH0NCiAgfQ0KDQogIC51cGRhdGVzLWNvbnRlbnQgew0KICAgIGRpc3BsYXk6IGZsZXg7DQoNCiAgICAudmVyc2lvbi1saXN0IHsNCiAgICAgIHdpZHRoOiAxMDBweDsNCiAgICAgIGJvcmRlci1yaWdodDogMXB4IHNvbGlkICNlZWU7DQogICAgICBwYWRkaW5nLXJpZ2h0OiAxNXB4Ow0KICAgICAgbWFyZ2luLXJpZ2h0OiAyMHB4Ow0KDQogICAgICAudmVyc2lvbi1pdGVtIHsNCiAgICAgICAgcGFkZGluZzogOHB4IDA7DQogICAgICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICAgICAgY29sb3I6ICM2NjY7DQogICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDVweDsNCg0KICAgICAgICAmOmhvdmVyIHsNCiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KICAgICAgICB9DQoNCiAgICAgICAgJi5hY3RpdmUgew0KICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlNmY3ZmY7DQogICAgICAgICAgY29sb3I6ICMxODkwZmY7DQogICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQoNCiAgICAudXBkYXRlLWRldGFpbHMgew0KICAgICAgZmxleDogMTsNCg0KICAgICAgLnVwZGF0ZS1ub3RlcyB7DQogICAgICAgIC5ub3RlLWl0ZW0gew0KICAgICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgICAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgICAgICAgICBsaW5lLWhlaWdodDogMS42Ow0KDQogICAgICAgICAgLm5vdGUtbnVtYmVyIHsNCiAgICAgICAgICAgIG1hcmdpbi1yaWdodDogNXB4Ow0KICAgICAgICAgICAgY29sb3I6ICM2NjY7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLm5vdGUtY29udGVudCB7DQogICAgICAgICAgICBmbGV4OiAxOw0KDQogICAgICAgICAgICBiIHsNCiAgICAgICAgICAgICAgY29sb3I6ICMxODkwZmY7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4tCA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/account", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"page-layout\">\r\n      <!-- 左侧导航栏 -->\r\n      <div class=\"left-sidebar\">\r\n        <div class=\"user-info\">\r\n          <div class=\"avatar\">\r\n            <img src=\"@/assets/images/profile.jpg\" alt=\"用户头像\">\r\n          </div>\r\n          <div class=\"user-id\">***********</div>\r\n          <div class=\"register-date\">2023-04-28注册</div>\r\n        </div>\r\n\r\n        <div class=\"sidebar-menu\">\r\n          <el-menu\r\n            :default-active=\"activeMenuItem\"\r\n            class=\"sidebar-menu-list\"\r\n            @select=\"handleMenuSelect\"\r\n          >\r\n            <el-menu-item index=\"account\">\r\n              <i class=\"el-icon-user\"></i>\r\n              <span>我的账号</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"favorite\">\r\n              <i class=\"el-icon-star-on\"></i>\r\n              <span>我的收藏</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"download\">\r\n              <i class=\"el-icon-download\"></i>\r\n              <span>我的下载</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"contact\">\r\n              <i class=\"el-icon-notebook-1\"></i>\r\n              <span>我的联系人</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"material\">\r\n              <i class=\"el-icon-chat-line-round\"></i>\r\n              <span>素材库</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"privacy\">\r\n              <i class=\"el-icon-setting\"></i>\r\n              <span>系统设置</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"user-management\">\r\n              <i class=\"el-icon-user\"></i>\r\n              <span>用户管理</span>\r\n            </el-menu-item>\r\n          </el-menu>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧内容区 -->\r\n      <div class=\"content\">\r\n        <!-- 账户信息内容 -->\r\n        <div v-if=\"activeMenuItem === 'account'\" class=\"account-container\">\r\n          <div class=\"account-header\">\r\n            <div class=\"title\">我的账号</div>\r\n            <div class=\"actions\">\r\n              <el-button size=\"small\" @click=\"showAccountInfo\">账号安全</el-button>\r\n              <el-button type=\"primary\" size=\"small\" @click=\"showPasswordForm\">修改密码</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 账户信息内容 -->\r\n          <div v-if=\"!showPasswordChange\" class=\"account-info\">\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">用户账号：</div>\r\n              <div class=\"value\">***********</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">手机号码：</div>\r\n              <div class=\"value\">***********</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">创建时间：</div>\r\n              <div class=\"value\">2023-04-28</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">专业认证分类：</div>\r\n              <div class=\"value\">互联网+</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">方案：</div>\r\n              <div class=\"value\">【免费】+【限时】</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">总积分值：</div>\r\n              <div class=\"value\">2000</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">积分等级：</div>\r\n              <div class=\"value\">初级VIP</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">剩余可用积分：</div>\r\n              <div class=\"value\">【250积分 = 600次】</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">预计可用天数：</div>\r\n              <div class=\"value\">365</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">预计到期日：</div>\r\n              <div class=\"value\">365</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">剩余次数：</div>\r\n              <div class=\"value\">10000次</div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 修改密码表单 -->\r\n          <div v-if=\"showPasswordChange\" class=\"password-form\">\r\n            <div class=\"form-group\">\r\n              <div class=\"form-label\">旧密码：</div>\r\n              <el-input\r\n                v-model=\"passwordForm.oldPassword\"\r\n                type=\"password\"\r\n                placeholder=\"请输入旧密码\"\r\n                show-password\r\n              ></el-input>\r\n            </div>\r\n\r\n            <div class=\"form-group\">\r\n              <div class=\"form-label\">新密码：</div>\r\n              <el-input\r\n                v-model=\"passwordForm.newPassword\"\r\n                type=\"password\"\r\n                placeholder=\"8-16 密码必须同时包含数字、大小写字母和符号\"\r\n                show-password\r\n              ></el-input>\r\n            </div>\r\n\r\n            <div class=\"form-group\">\r\n              <div class=\"form-label\">确认新密码：</div>\r\n              <el-input\r\n                v-model=\"passwordForm.confirmPassword\"\r\n                type=\"password\"\r\n                placeholder=\"请再次输入新密码\"\r\n                show-password\r\n              ></el-input>\r\n            </div>\r\n\r\n            <div class=\"form-actions\">\r\n              <el-button type=\"primary\" @click=\"changePassword\">确认修改</el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 我的下载内容 -->\r\n        <div v-if=\"activeMenuItem === 'download'\" class=\"download-container\">\r\n          <div class=\"download-header\">\r\n            <div class=\"title\">我的下载</div>\r\n            <div class=\"actions\">\r\n              <el-button type=\"primary\" size=\"small\" @click=\"batchDownload\">批量下载</el-button>\r\n              <el-button type=\"danger\" size=\"small\" @click=\"batchDelete\">批量删除</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"download-content\">\r\n            <el-table\r\n              :data=\"downloadList\"\r\n              style=\"width: 100%\"\r\n              @selection-change=\"handleSelectionChange\">\r\n              <el-table-column\r\n                type=\"selection\"\r\n                width=\"55\">\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"name\"\r\n                label=\"名称\"\r\n                width=\"300\">\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"dataSize\"\r\n                label=\"数据量\"\r\n                width=\"100\">\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"createTime\"\r\n                label=\"生成时间\"\r\n                width=\"180\">\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"status\"\r\n                label=\"下载状态\"\r\n                width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <span class=\"download-status\">{{ scope.row.status }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                label=\"操作\"\r\n                width=\"120\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    icon=\"el-icon-download\"\r\n                    @click=\"handleDownload(scope.row)\">\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    icon=\"el-icon-delete\"\r\n                    @click=\"handleDelete(scope.row)\">\r\n                  </el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <div class=\"pagination-container\">\r\n              <span>共 {{ total }} 条记录</span>\r\n              <el-pagination\r\n                background\r\n                layout=\"prev, pager, next, jumper\"\r\n                :total=\"total\"\r\n                :current-page.sync=\"currentPage\"\r\n                :page-size=\"pageSize\"\r\n                @current-change=\"handleCurrentChange\">\r\n              </el-pagination>\r\n              <span>前往第</span>\r\n              <el-input\r\n                v-model=\"jumpPage\"\r\n                size=\"mini\"\r\n                class=\"jump-page-input\">\r\n              </el-input>\r\n              <span>页</span>\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"primary\"\r\n                @click=\"handleJumpPage\">\r\n                确定\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 我的收藏内容 -->\r\n        <div v-if=\"activeMenuItem === 'favorite'\" class=\"favorite-container\">\r\n          <div class=\"favorite-header\">\r\n            <div class=\"title\">我的收藏</div>\r\n          </div>\r\n\r\n          <div class=\"favorite-toolbar\">\r\n            <div class=\"toolbar-left\">\r\n              <el-checkbox v-model=\"selectAllFavorites\">全选</el-checkbox>\r\n              <el-button size=\"small\" type=\"danger\" :disabled=\"selectedFavorites.length === 0\" @click=\"batchCancelFavorite\">批量取消收藏</el-button>\r\n              <el-button size=\"small\" icon=\"el-icon-refresh\">刷新</el-button>\r\n            </div>\r\n\r\n            <div class=\"toolbar-right\">\r\n              <div class=\"date-filter\">\r\n                <el-date-picker\r\n                  v-model=\"favoriteStartDate\"\r\n                  type=\"date\"\r\n                  placeholder=\"开始日期\"\r\n                  size=\"small\"\r\n                  format=\"yyyy-MM-dd\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                  style=\"width: 130px;\"\r\n                ></el-date-picker>\r\n                <span class=\"date-separator\">-</span>\r\n                <el-date-picker\r\n                  v-model=\"favoriteEndDate\"\r\n                  type=\"date\"\r\n                  placeholder=\"结束日期\"\r\n                  size=\"small\"\r\n                  format=\"yyyy-MM-dd\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                  style=\"width: 130px;\"\r\n                ></el-date-picker>\r\n              </div>\r\n\r\n              <div class=\"search-box\">\r\n                <el-input\r\n                  v-model=\"favoriteSearchKeyword\"\r\n                  placeholder=\"搜索内容\"\r\n                  size=\"small\"\r\n                  prefix-icon=\"el-icon-search\"\r\n                  clearable\r\n                  style=\"width: 200px;\"\r\n                ></el-input>\r\n              </div>\r\n\r\n              <el-button type=\"primary\" size=\"small\" icon=\"el-icon-download\" @click=\"exportFavorites\">全部下载</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"favorite-content\">\r\n            <div class=\"favorite-list\">\r\n              <div v-for=\"item in favoriteList\" :key=\"item.id\" class=\"favorite-item\">\r\n                <div class=\"item-checkbox\">\r\n                  <el-checkbox v-model=\"item.selected\" @change=\"handleFavoriteSelect\"></el-checkbox>\r\n                </div>\r\n                <div class=\"item-content\">\r\n                  <div class=\"item-title\">\r\n                    <span class=\"title-tag\">{{ item.tag }}</span>\r\n                    <span class=\"title-text\">{{ item.title }}</span>\r\n                  </div>\r\n                  <div class=\"item-info\">\r\n                    <span class=\"info-time\">收藏时间: {{ item.time }}</span>\r\n                    <span class=\"info-source\">来源: {{ item.source }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"item-actions\">\r\n                  <el-button\r\n                    type=\"text\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-delete\"\r\n                    class=\"cancel-favorite-btn\"\r\n                    @click=\"cancelFavorite(item)\">\r\n                    取消收藏\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"favorite-pagination\">\r\n              <div class=\"pagination-info\">\r\n                共 <span>{{ favoritesTotal }}</span> 条记录\r\n              </div>\r\n              <div class=\"pagination-controls\">\r\n                <el-pagination\r\n                  background\r\n                  layout=\"prev, pager, next\"\r\n                  :total=\"favoritesTotal\"\r\n                  :current-page.sync=\"favoritesCurrentPage\"\r\n                  :page-size=\"favoritesPageSize\"\r\n                  @current-change=\"handleFavoritePageChange\">\r\n                </el-pagination>\r\n              </div>\r\n              <div class=\"pagination-jump\">\r\n                <span>前往</span>\r\n                <el-input\r\n                  v-model=\"favoritesJumpPage\"\r\n                  size=\"mini\"\r\n                  class=\"jump-page-input\">\r\n                </el-input>\r\n                <span>页</span>\r\n                <el-button\r\n                  size=\"mini\"\r\n                  type=\"primary\"\r\n                  @click=\"handleFavoriteJumpPage\">\r\n                  确定\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 素材库内容 -->\r\n        <div v-if=\"activeMenuItem === 'material'\" class=\"material-container\">\r\n          <div class=\"material-header\">\r\n            <div class=\"title\">素材库</div>\r\n          </div>\r\n\r\n          <div class=\"material-content\">\r\n            <div class=\"material-add-box\" @click=\"showAddMaterialDialog\">\r\n              <div class=\"add-icon\">\r\n                <i class=\"el-icon-plus\"></i>\r\n              </div>\r\n              <div class=\"add-text\">新建素材包</div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 新建素材对话框 -->\r\n          <el-dialog\r\n            title=\"新建素材包\"\r\n            :visible.sync=\"addMaterialDialogVisible\"\r\n            width=\"400px\"\r\n            center\r\n            :show-close=\"false\"\r\n            custom-class=\"material-dialog\"\r\n          >\r\n            <div class=\"material-form\">\r\n              <div class=\"form-item\">\r\n                <div class=\"form-label\">素材包名称：</div>\r\n                <el-input v-model=\"newMaterialName\" placeholder=\"请输入素材包名称\"></el-input>\r\n              </div>\r\n            </div>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n              <el-button type=\"primary\" @click=\"addMaterial\">确定</el-button>\r\n              <el-button @click=\"addMaterialDialogVisible = false\">取消</el-button>\r\n            </div>\r\n          </el-dialog>\r\n        </div>\r\n\r\n        <!-- 我的联系人内容 -->\r\n        <div v-if=\"activeMenuItem === 'contact'\" class=\"contact-container\">\r\n          <div class=\"contact-header\">\r\n            <div class=\"title\">我的联系人</div>\r\n            <div class=\"contact-tabs\">\r\n              <el-radio-group v-model=\"contactActiveTab\" size=\"small\">\r\n                <el-radio-button label=\"wechat\">微信</el-radio-button>\r\n                <el-radio-button label=\"sms\">短信</el-radio-button>\r\n                <el-radio-button label=\"email\">邮箱</el-radio-button>\r\n                <el-radio-button label=\"qwechat\">企微群</el-radio-button>\r\n                <el-radio-button label=\"dingtalk\">钉钉群</el-radio-button>\r\n                <el-radio-button label=\"feishu\">飞书群</el-radio-button>\r\n              </el-radio-group>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"contact-toolbar\">\r\n            <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"showAddContactDialog\">添加/导入联系人</el-button>\r\n          </div>\r\n\r\n          <div class=\"contact-content\">\r\n            <el-table\r\n              :data=\"contactList\"\r\n              style=\"width: 100%\"\r\n              border\r\n              stripe\r\n              :header-cell-style=\"{background:'#f5f7fa', color:'#606266'}\"\r\n            >\r\n              <el-table-column\r\n                label=\"头像\"\r\n                width=\"80\"\r\n                align=\"center\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <el-avatar :size=\"40\" icon=\"el-icon-user\"></el-avatar>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"name\"\r\n                label=\"姓名\"\r\n                width=\"120\"\r\n                align=\"center\"\r\n              />\r\n              <el-table-column\r\n                prop=\"createTime\"\r\n                label=\"创建/更新\"\r\n                width=\"180\"\r\n                align=\"center\"\r\n              />\r\n              <el-table-column\r\n                prop=\"location\"\r\n                label=\"位置/时间\"\r\n                width=\"180\"\r\n                align=\"center\"\r\n              />\r\n              <el-table-column\r\n                label=\"操作\"\r\n                align=\"center\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    icon=\"el-icon-edit\"\r\n                    @click=\"handleEditContact(scope.row)\"\r\n                  >编辑</el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    icon=\"el-icon-delete\"\r\n                    class=\"delete-btn\"\r\n                    @click=\"handleDeleteContact(scope.row)\"\r\n                  >删除</el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n\r\n          <!-- 添加联系人对话框 -->\r\n          <el-dialog title=\"添加联系人\" :visible.sync=\"addContactDialogVisible\" width=\"500px\" center>\r\n            <el-form ref=\"contactForm\" :model=\"contactForm\" :rules=\"contactFormRules\" label-width=\"80px\">\r\n              <el-form-item label=\"姓名\" prop=\"name\">\r\n                <el-input v-model=\"contactForm.name\" placeholder=\"请输入姓名\" />\r\n              </el-form-item>\r\n              <el-form-item label=\"手机号码\" prop=\"phone\">\r\n                <el-input v-model=\"contactForm.phone\" placeholder=\"请输入手机号码\" />\r\n              </el-form-item>\r\n              <el-form-item label=\"邮箱\" prop=\"email\">\r\n                <el-input v-model=\"contactForm.email\" placeholder=\"请输入邮箱\" />\r\n              </el-form-item>\r\n              <el-form-item label=\"位置\" prop=\"location\">\r\n                <el-input v-model=\"contactForm.location\" placeholder=\"请输入位置\" />\r\n              </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n              <el-button @click=\"addContactDialogVisible = false\">取消</el-button>\r\n              <el-button type=\"primary\" @click=\"submitContactForm\">确定</el-button>\r\n            </div>\r\n          </el-dialog>\r\n        </div>\r\n\r\n        <!-- 用户管理内容 -->\r\n        <div v-if=\"activeMenuItem === 'user-management'\">\r\n          <user-management />\r\n        </div>\r\n\r\n        <!-- 系统设置内容 -->\r\n        <div v-if=\"activeMenuItem === 'privacy'\" class=\"settings-container\">\r\n          <div class=\"settings-header\">\r\n            <div class=\"title\">系统设置</div>\r\n          </div>\r\n\r\n          <div class=\"settings-content\">\r\n            <!-- 左侧设置菜单 -->\r\n            <div class=\"settings-sidebar\">\r\n              <div class=\"settings-menu\">\r\n                <div :class=\"['menu-item', currentSettingsTab === 'basic' ? 'active' : '']\" @click=\"switchSettingsTab('basic')\">\r\n                  <i class=\"el-icon-setting\"></i>\r\n                  <span>基础设置</span>\r\n                </div>\r\n                <div :class=\"['menu-item', currentSettingsTab === 'updates' ? 'active' : '']\" @click=\"switchSettingsTab('updates')\">\r\n                  <i class=\"el-icon-document\"></i>\r\n                  <span>更新说明</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 右侧设置内容 -->\r\n            <div class=\"settings-main\">\r\n              <!-- 基础设置内容 -->\r\n              <div v-if=\"currentSettingsTab === 'basic'\" class=\"settings-section\">\r\n                <div class=\"section-title\">文章管理选项：</div>\r\n                <div class=\"options-group\">\r\n                  <el-checkbox v-model=\"settings.auto\">自动</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.manual\">手动</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.downloadImages\">下载图片</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.backup\">备份</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.realTimeSync\">实时同步</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.humanReview\">人工审核</el-checkbox>\r\n                </div>\r\n\r\n                <div class=\"options-group\">\r\n                  <el-checkbox v-model=\"settings.autoPublish\">自动发布</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.keywords\">关键词</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.highQuality\">高质量</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.multiPlatform\">多平台发布</el-checkbox>\r\n                </div>\r\n\r\n                <div class=\"options-group\">\r\n                  <el-checkbox v-model=\"settings.autoSave\">自动保存</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.batchProcess\">批量处理</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.imageProcess\">图片处理</el-checkbox>\r\n                </div>\r\n\r\n                <div class=\"section-title\">下载设置：</div>\r\n                <div class=\"download-settings\">\r\n                  <div class=\"setting-item\">\r\n                    <span class=\"label\">下载路径：</span>\r\n                    <span class=\"value\">自动生成文件夹</span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"section-title\">高级自动化设置：</div>\r\n                <div class=\"options-group\">\r\n                  <el-checkbox v-model=\"settings.autoOn\">开启</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.autoOff\">关闭</el-checkbox>\r\n                </div>\r\n\r\n                <div class=\"save-button\">\r\n                  <el-button type=\"primary\" @click=\"saveSettings\">保存</el-button>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 更新说明内容 -->\r\n              <div v-if=\"currentSettingsTab === 'updates'\" class=\"updates-section\">\r\n                <div class=\"updates-header\">\r\n                  <div class=\"version-title\">{{ selectedVersion.version }} 版本更新说明</div>\r\n                  <div class=\"version-date\">{{ selectedVersion.date }}</div>\r\n                </div>\r\n\r\n                <div class=\"updates-content\">\r\n                  <div class=\"version-list\">\r\n                    <div\r\n                      v-for=\"version in versionList\"\r\n                      :key=\"version.version\"\r\n                      :class=\"['version-item', selectedVersion.version === version.version ? 'active' : '']\"\r\n                      @click=\"selectVersion(version.version)\"\r\n                    >\r\n                      {{ version.version }}\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div class=\"update-details\">\r\n                    <div class=\"update-notes\">\r\n                      <div v-for=\"(note, index) in selectedVersion.notes\" :key=\"index\" class=\"note-item\">\r\n                        <div class=\"note-number\">{{ index + 1 }}. </div>\r\n                        <div class=\"note-content\" v-html=\"note\"></div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UserManagement from './user-management.vue';\r\n\r\nexport default {\r\n  name: \"AccountManagement\",\r\n  components: {\r\n    UserManagement\r\n  },\r\n  watch: {\r\n    selectAllFavorites(val) {\r\n      this.watchSelectAllFavorites(val);\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      activeMenuItem: 'account', // 默认选中我的账号\r\n      currentSettingsTab: 'basic', // 默认选中基础设置\r\n      // 用户信息\r\n      userInfo: {\r\n        userId: '***********',\r\n        phoneNumber: '***********',\r\n        registerDate: '2023-04-28',\r\n        category: '互联网+',\r\n        plan: '【免费】+【限时】',\r\n        totalPoints: '2000',\r\n        pointsLevel: '初级VIP',\r\n        availablePoints: '【250积分 = 600次】',\r\n        estimatedDays: '365',\r\n        expirationDate: '365',\r\n        remainingCount: '10000次'\r\n      },\r\n      // 系统设置\r\n      settings: {\r\n        auto: true,\r\n        manual: true,\r\n        downloadImages: true,\r\n        backup: true,\r\n        realTimeSync: false,\r\n        humanReview: true,\r\n        autoPublish: false,\r\n        keywords: true,\r\n        highQuality: false,\r\n        multiPlatform: true,\r\n        autoSave: true,\r\n        batchProcess: true,\r\n        imageProcess: true,\r\n        autoOn: true,\r\n        autoOff: false\r\n      },\r\n      // 下载列表\r\n      downloadList: [\r\n        {\r\n          id: 1,\r\n          name: '方案_20240428163743_1',\r\n          dataSize: '1323',\r\n          createTime: '2024-04-28 16:37:57',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '方案_20240428163743_1',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-28 16:37:57',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '方案_20240427173742_4',\r\n          dataSize: '1893',\r\n          createTime: '2024-04-27 17:37:42',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 4,\r\n          name: '方案_20240427173742_3',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-27 17:37:42',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 5,\r\n          name: '方案_20240427173742_2',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-27 17:37:42',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 6,\r\n          name: '方案_20240427173742_2',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-27 17:37:42',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 7,\r\n          name: '方案_20240427173742_1',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-27 17:37:42',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 8,\r\n          name: '台账_20240427173129_5',\r\n          dataSize: '1281',\r\n          createTime: '2024-04-27 17:31:29',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 9,\r\n          name: '台账_20240427173129_4',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-27 17:31:29',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 10,\r\n          name: '台账_20240427173129_3',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-27 17:31:29',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 11,\r\n          name: '台账_20240427173129_2',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-27 17:31:29',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 12,\r\n          name: '台账_20240427173129_1',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-27 17:31:29',\r\n          status: '已完成'\r\n        }\r\n      ],\r\n      // 分页相关\r\n      total: 12,\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      jumpPage: '',\r\n      // 选中的下载项\r\n      selectedDownloads: [],\r\n      // 版本列表\r\n      versionList: [\r\n        { version: '6.2.9', date: '2024.01.19' },\r\n        { version: '6.2.8', date: '2023.12.15' },\r\n        { version: '6.2.7', date: '2023.11.20' },\r\n        { version: '6.2.6', date: '2023.10.18' },\r\n        { version: '6.2.5', date: '2023.09.25' },\r\n        { version: '6.2.4', date: '2023.08.30' },\r\n        { version: '6.2.3', date: '2023.07.28' },\r\n        { version: '6.2.2', date: '2023.06.22' },\r\n        { version: '6.2.1', date: '2023.05.15' },\r\n        { version: '6.2.0', date: '2023.04.10' }\r\n      ],\r\n      // 当前选中的版本\r\n      selectedVersion: {\r\n        version: '6.2.9',\r\n        date: '2024.01.19',\r\n        notes: [\r\n          '1. 【新增功能】新增<b>个人中心</b>功能，用户可以查看和管理个人信息。',\r\n          '2. 【功能优化】优化了<b>搜索引擎</b>的性能，提高了搜索速度和准确性。',\r\n          '3. 【界面调整】调整了<b>首页布局</b>，使界面更加简洁美观。',\r\n          '4. 【问题修复】修复了在某些情况下<b>\"数据导出\"</b>功能失效的问题。',\r\n          '5. 【安全增强】增强了系统的<b>安全性</b>，提高了数据保护能力。'\r\n        ]\r\n      },\r\n      // 素材库相关\r\n      addMaterialDialogVisible: false,\r\n      newMaterialName: '',\r\n      materialList: [],\r\n      // 修改密码相关\r\n      showPasswordChange: false,\r\n      passwordForm: {\r\n        oldPassword: '',\r\n        newPassword: '',\r\n        confirmPassword: ''\r\n      },\r\n      // 我的收藏相关\r\n      selectAllFavorites: false,\r\n      selectedFavorites: [],\r\n      favoriteStartDate: '',\r\n      favoriteEndDate: '',\r\n      favoriteSearchKeyword: '',\r\n      favoriteList: [\r\n        {\r\n          id: 1,\r\n          selected: false,\r\n          tag: 'HOT',\r\n          title: '新产品市场分析报告',\r\n          time: '2023-04-29 20:37:51',\r\n          source: '市场调查部门 A 数据库'\r\n        },\r\n        {\r\n          id: 2,\r\n          selected: false,\r\n          tag: 'NEW',\r\n          title: '2024年第一季度行业趋势分析',\r\n          time: '2024-04-28 15:22:36',\r\n          source: '行业研究中心'\r\n        },\r\n        {\r\n          id: 3,\r\n          selected: false,\r\n          tag: 'HOT',\r\n          title: '竞品分析与市场定位策略',\r\n          time: '2024-04-27 09:15:42',\r\n          source: '战略规划部'\r\n        },\r\n        {\r\n          id: 4,\r\n          selected: false,\r\n          tag: 'TOP',\r\n          title: '用户行为数据分析报告',\r\n          time: '2024-04-26 14:30:18',\r\n          source: '数据分析部门'\r\n        },\r\n        {\r\n          id: 5,\r\n          selected: false,\r\n          tag: 'NEW',\r\n          title: '新媒体营销策略白皮书',\r\n          time: '2024-04-25 11:45:23',\r\n          source: '营销部门'\r\n        },\r\n        {\r\n          id: 6,\r\n          selected: false,\r\n          tag: 'HOT',\r\n          title: '产品迭代计划与路线图',\r\n          time: '2024-04-24 16:20:37',\r\n          source: '产品部门'\r\n        },\r\n        {\r\n          id: 7,\r\n          selected: false,\r\n          tag: 'TOP',\r\n          title: '行业政策解读与影响分析',\r\n          time: '2024-04-23 10:05:12',\r\n          source: '政策研究中心'\r\n        }\r\n      ],\r\n      favoritesTotal: 7,\r\n      favoritesCurrentPage: 1,\r\n      favoritesPageSize: 10,\r\n      favoritesJumpPage: '',\r\n\r\n      // 我的联系人相关\r\n      contactActiveTab: 'wechat', // 默认选中微信标签\r\n      contactList: [\r\n        {\r\n          id: 1,\r\n          name: '张三',\r\n          phone: '13800138001',\r\n          email: '<EMAIL>',\r\n          location: '北京市朝阳区',\r\n          createTime: '2024-04-28 10:30:45'\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '李四',\r\n          phone: '13800138002',\r\n          email: '<EMAIL>',\r\n          location: '上海市浦东新区',\r\n          createTime: '2024-04-27 15:20:36'\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '王五',\r\n          phone: '13800138003',\r\n          email: '<EMAIL>',\r\n          location: '广州市天河区',\r\n          createTime: '2024-04-26 09:15:22'\r\n        }\r\n      ],\r\n      addContactDialogVisible: false,\r\n      contactForm: {\r\n        id: null,\r\n        name: '',\r\n        phone: '',\r\n        email: '',\r\n        location: ''\r\n      },\r\n      contactFormRules: {\r\n        name: [\r\n          { required: true, message: '请输入姓名', trigger: 'blur' }\r\n        ],\r\n        phone: [\r\n          { required: true, message: '请输入手机号码', trigger: 'blur' },\r\n          { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }\r\n        ],\r\n        email: [\r\n          { required: true, message: '请输入邮箱', trigger: 'blur' },\r\n          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  methods: {\r\n    // 处理菜单项选择\r\n    handleMenuSelect(index) {\r\n      this.activeMenuItem = index;\r\n    },\r\n    // 显示账户信息\r\n    showAccountInfo() {\r\n      this.showPasswordChange = false;\r\n    },\r\n    // 显示修改密码表单\r\n    showPasswordForm() {\r\n      this.showPasswordChange = true;\r\n      this.passwordForm = {\r\n        oldPassword: '',\r\n        newPassword: '',\r\n        confirmPassword: ''\r\n      };\r\n    },\r\n    // 修改密码\r\n    changePassword() {\r\n      // 表单验证\r\n      if (!this.passwordForm.oldPassword) {\r\n        this.$message.warning('请输入旧密码');\r\n        return;\r\n      }\r\n      if (!this.passwordForm.newPassword) {\r\n        this.$message.warning('请输入新密码');\r\n        return;\r\n      }\r\n      if (!this.passwordForm.confirmPassword) {\r\n        this.$message.warning('请确认新密码');\r\n        return;\r\n      }\r\n      if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {\r\n        this.$message.warning('两次输入的新密码不一致');\r\n        return;\r\n      }\r\n\r\n      // 提交修改密码请求\r\n      this.$message.success('密码修改成功');\r\n      this.showPasswordChange = false;\r\n    },\r\n    // 切换设置选项卡\r\n    switchSettingsTab(tab) {\r\n      this.currentSettingsTab = tab;\r\n    },\r\n    // 保存设置\r\n    saveSettings() {\r\n      this.$message.success('设置已保存');\r\n    },\r\n    // 处理表格选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedDownloads = selection;\r\n    },\r\n    // 批量下载\r\n    batchDownload() {\r\n      if (this.selectedDownloads.length === 0) {\r\n        this.$message.warning('请选择要下载的文件');\r\n        return;\r\n      }\r\n      this.$message.success(`已开始下载${this.selectedDownloads.length}个文件`);\r\n    },\r\n    // 批量删除\r\n    batchDelete() {\r\n      if (this.selectedDownloads.length === 0) {\r\n        this.$message.warning('请选择要删除的文件');\r\n        return;\r\n      }\r\n      this.$confirm('确定要删除选中的文件吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$message.success(`已删除${this.selectedDownloads.length}个文件`);\r\n        // 实际应用中这里需要调用接口删除文件\r\n      }).catch(() => {\r\n        this.$message.info('已取消删除');\r\n      });\r\n    },\r\n    // 下载单个文件\r\n    handleDownload(row) {\r\n      this.$message.success(`开始下载: ${row.name}`);\r\n    },\r\n    // 删除单个文件\r\n    handleDelete(row) {\r\n      this.$confirm('确定要删除该文件吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$message.success(`已删除: ${row.name}`);\r\n        // 实际应用中这里需要调用接口删除文件\r\n      }).catch(() => {\r\n        this.$message.info('已取消删除');\r\n      });\r\n    },\r\n    // 处理页码变化\r\n    handleCurrentChange(val) {\r\n      this.currentPage = val;\r\n      // 实际应用中这里需要调用接口获取对应页的数据\r\n    },\r\n    // 处理跳转页面\r\n    handleJumpPage() {\r\n      if (!this.jumpPage) {\r\n        return;\r\n      }\r\n      const page = parseInt(this.jumpPage);\r\n      if (isNaN(page) || page < 1 || page > Math.ceil(this.total / this.pageSize)) {\r\n        this.$message.warning('请输入有效的页码');\r\n        return;\r\n      }\r\n      this.currentPage = page;\r\n      // 实际应用中这里需要调用接口获取对应页的数据\r\n    },\r\n    // 选择版本\r\n    selectVersion(version) {\r\n      const versionData = this.versionList.find(v => v.version === version);\r\n      if (versionData) {\r\n        // 根据版本号获取对应的更新说明\r\n        let notes = [];\r\n        if (version === '6.2.9') {\r\n          notes = [\r\n            '1. 【新增功能】新增<b>个人中心</b>功能，用户可以查看和管理个人信息。',\r\n            '2. 【功能优化】优化了<b>搜索引擎</b>的性能，提高了搜索速度和准确性。',\r\n            '3. 【界面调整】调整了<b>首页布局</b>，使界面更加简洁美观。',\r\n            '4. 【问题修复】修复了在某些情况下<b>\"数据导出\"</b>功能失效的问题。',\r\n            '5. 【安全增强】增强了系统的<b>安全性</b>，提高了数据保护能力。'\r\n          ];\r\n        } else if (version === '6.2.8') {\r\n          notes = [\r\n            '1. 【新增功能】新增<b>数据分析</b>模块，提供更全面的数据统计。',\r\n            '2. 【功能优化】优化了<b>文件上传</b>功能，支持更多文件格式。',\r\n            '3. 【问题修复】修复了部分用户<b>无法登录</b>的问题。'\r\n          ];\r\n        } else {\r\n          notes = [\r\n            '1. 【功能优化】优化系统性能，提升用户体验。',\r\n            '2. 【问题修复】修复已知问题，提高系统稳定性。'\r\n          ];\r\n        }\r\n\r\n        this.selectedVersion = {\r\n          version: versionData.version,\r\n          date: versionData.date,\r\n          notes: notes\r\n        };\r\n      }\r\n    },\r\n    // 显示添加素材对话框\r\n    showAddMaterialDialog() {\r\n      this.newMaterialName = '';\r\n      this.addMaterialDialogVisible = true;\r\n    },\r\n    // 添加素材\r\n    addMaterial() {\r\n      if (!this.newMaterialName.trim()) {\r\n        this.$message.warning('请输入素材名称');\r\n        return;\r\n      }\r\n\r\n      // 添加新素材\r\n      this.materialList.push({\r\n        id: Date.now(),\r\n        name: this.newMaterialName,\r\n        createTime: new Date().toLocaleString()\r\n      });\r\n\r\n      this.$message.success(`素材\"${this.newMaterialName}\"创建成功`);\r\n      this.addMaterialDialogVisible = false;\r\n    },\r\n\r\n    // 处理收藏项选择\r\n    handleFavoriteSelect() {\r\n      this.selectedFavorites = this.favoriteList.filter(item => item.selected);\r\n      // 检查是否全选\r\n      this.selectAllFavorites = this.selectedFavorites.length === this.favoriteList.length;\r\n    },\r\n\r\n    // 全选/取消全选收藏项\r\n    watchSelectAllFavorites(val) {\r\n      this.favoriteList.forEach(item => {\r\n        item.selected = val;\r\n      });\r\n      this.selectedFavorites = val ? [...this.favoriteList] : [];\r\n    },\r\n\r\n    // 批量取消收藏\r\n    batchCancelFavorite() {\r\n      if (this.selectedFavorites.length === 0) {\r\n        this.$message.warning('请选择要取消收藏的项');\r\n        return;\r\n      }\r\n\r\n      this.$confirm('确定要取消选中的收藏吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 实际应用中这里需要调用接口取消收藏\r\n        const selectedIds = this.selectedFavorites.map(item => item.id);\r\n        this.favoriteList = this.favoriteList.filter(item => !selectedIds.includes(item.id));\r\n        this.selectedFavorites = [];\r\n        this.selectAllFavorites = false;\r\n        this.$message.success('已取消收藏');\r\n      }).catch(() => {\r\n        this.$message.info('已取消操作');\r\n      });\r\n    },\r\n\r\n    // 取消单个收藏\r\n    cancelFavorite(item) {\r\n      this.$confirm('确定要取消收藏\"' + item.title + '\"吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 实际应用中这里需要调用接口取消收藏\r\n        this.favoriteList = this.favoriteList.filter(i => i.id !== item.id);\r\n        // 更新选中的收藏项\r\n        this.selectedFavorites = this.selectedFavorites.filter(i => i.id !== item.id);\r\n        // 更新总数\r\n        this.favoritesTotal = this.favoriteList.length;\r\n        this.$message.success('已取消收藏');\r\n      }).catch(() => {\r\n        this.$message.info('已取消操作');\r\n      });\r\n    },\r\n\r\n    // 显示添加联系人对话框\r\n    showAddContactDialog() {\r\n      this.contactForm = {\r\n        id: null,\r\n        name: '',\r\n        phone: '',\r\n        email: '',\r\n        location: ''\r\n      };\r\n      this.addContactDialogVisible = true;\r\n    },\r\n\r\n    // 提交联系人表单\r\n    submitContactForm() {\r\n      this.$refs.contactForm.validate(valid => {\r\n        if (valid) {\r\n          if (this.contactForm.id) {\r\n            // 编辑联系人\r\n            const index = this.contactList.findIndex(item => item.id === this.contactForm.id);\r\n            if (index !== -1) {\r\n              // 更新创建时间\r\n              this.contactForm.createTime = new Date().toLocaleString('zh-CN', {\r\n                year: 'numeric',\r\n                month: '2-digit',\r\n                day: '2-digit',\r\n                hour: '2-digit',\r\n                minute: '2-digit',\r\n                second: '2-digit',\r\n                hour12: false\r\n              }).replace(/\\//g, '-');\r\n              this.contactList.splice(index, 1, this.contactForm);\r\n              this.$message.success('联系人修改成功');\r\n            }\r\n          } else {\r\n            // 添加联系人\r\n            const newContact = {\r\n              ...this.contactForm,\r\n              id: this.contactList.length > 0 ? Math.max(...this.contactList.map(item => item.id)) + 1 : 1,\r\n              createTime: new Date().toLocaleString('zh-CN', {\r\n                year: 'numeric',\r\n                month: '2-digit',\r\n                day: '2-digit',\r\n                hour: '2-digit',\r\n                minute: '2-digit',\r\n                second: '2-digit',\r\n                hour12: false\r\n              }).replace(/\\//g, '-')\r\n            };\r\n            this.contactList.push(newContact);\r\n            this.$message.success('联系人添加成功');\r\n          }\r\n          this.addContactDialogVisible = false;\r\n        }\r\n      });\r\n    },\r\n\r\n    // 编辑联系人\r\n    handleEditContact(row) {\r\n      this.contactForm = JSON.parse(JSON.stringify(row));\r\n      this.addContactDialogVisible = true;\r\n    },\r\n\r\n    // 删除联系人\r\n    handleDeleteContact(row) {\r\n      this.$confirm(`确定要删除联系人\"${row.name}\"吗？`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 实际应用中这里需要调用接口删除联系人\r\n        this.contactList = this.contactList.filter(item => item.id !== row.id);\r\n        this.$message.success('联系人删除成功');\r\n      }).catch(() => {\r\n        this.$message.info('已取消删除');\r\n      });\r\n    },\r\n\r\n    // 导出收藏\r\n    exportFavorites() {\r\n      this.$message.success('开始导出收藏');\r\n      // 实际应用中这里需要调用接口导出收藏\r\n    },\r\n\r\n    // 处理收藏分页变化\r\n    handleFavoritePageChange(val) {\r\n      this.favoritesCurrentPage = val;\r\n      // 实际应用中这里需要调用接口获取对应页的数据\r\n    },\r\n\r\n    // 处理收藏跳转页面\r\n    handleFavoriteJumpPage() {\r\n      if (!this.favoritesJumpPage) {\r\n        return;\r\n      }\r\n\r\n      const page = parseInt(this.favoritesJumpPage);\r\n      if (isNaN(page) || page < 1 || page > Math.ceil(this.favoritesTotal / this.favoritesPageSize)) {\r\n        this.$message.warning('请输入有效的页码');\r\n        return;\r\n      }\r\n\r\n      this.favoritesCurrentPage = page;\r\n      // 实际应用中这里需要调用接口获取对应页的数据\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background-color: #f5f5f5;\r\n  min-height: 100vh;\r\n  padding: 20px;\r\n}\r\n\r\n.page-layout {\r\n  display: flex;\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.left-sidebar {\r\n  width: 200px;\r\n  background-color: #fff;\r\n  border-right: 1px solid #e6e6e6;\r\n}\r\n\r\n.user-info {\r\n  padding: 20px;\r\n  text-align: center;\r\n  border-bottom: 1px solid #e6e6e6;\r\n\r\n  .avatar {\r\n    width: 60px;\r\n    height: 60px;\r\n    margin: 0 auto 10px;\r\n    border-radius: 50%;\r\n    overflow: hidden;\r\n\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n      object-fit: cover;\r\n    }\r\n  }\r\n\r\n  .user-id {\r\n    font-size: 16px;\r\n    font-weight: bold;\r\n    margin-bottom: 5px;\r\n  }\r\n\r\n  .register-date {\r\n    font-size: 12px;\r\n    color: #999;\r\n  }\r\n}\r\n\r\n.sidebar-menu {\r\n  .sidebar-menu-list {\r\n    border-right: none;\r\n  }\r\n\r\n  .el-menu-item {\r\n    height: 50px;\r\n    line-height: 50px;\r\n\r\n    i {\r\n      margin-right: 5px;\r\n      color: #666;\r\n    }\r\n  }\r\n\r\n  .el-menu-item.is-active {\r\n    background-color: #f0f9eb;\r\n    color: #67c23a;\r\n\r\n    i {\r\n      color: #67c23a;\r\n    }\r\n  }\r\n}\r\n\r\n.content {\r\n  flex: 1;\r\n  padding: 20px;\r\n}\r\n\r\n.account-container {\r\n  background-color: #fff;\r\n}\r\n\r\n.account-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-bottom: 20px;\r\n  margin-bottom: 20px;\r\n  border-bottom: 1px solid #eee;\r\n\r\n  .title {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n.account-info {\r\n  .info-item {\r\n    display: flex;\r\n    margin-bottom: 20px;\r\n    line-height: 24px;\r\n\r\n    .label {\r\n      width: 120px;\r\n      color: #666;\r\n      text-align: right;\r\n      padding-right: 10px;\r\n    }\r\n\r\n    .value {\r\n      flex: 1;\r\n      color: #333;\r\n    }\r\n  }\r\n}\r\n\r\n/* 系统设置样式 */\r\n.settings-container {\r\n  background-color: #fff;\r\n}\r\n\r\n.settings-header {\r\n  padding-bottom: 15px;\r\n  margin-bottom: 15px;\r\n\r\n  .title {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n.settings-content {\r\n  display: flex;\r\n}\r\n\r\n.settings-sidebar {\r\n  width: 120px;\r\n  border-right: 1px solid #eee;\r\n  padding-right: 10px;\r\n}\r\n\r\n.settings-menu {\r\n  .menu-item {\r\n    padding: 10px 0;\r\n    cursor: pointer;\r\n    display: flex;\r\n    align-items: center;\r\n    color: #666;\r\n    font-size: 14px;\r\n\r\n    i {\r\n      margin-right: 8px;\r\n      color: #1890ff;\r\n    }\r\n\r\n    &.active {\r\n      color: #1890ff;\r\n      font-weight: bold;\r\n    }\r\n\r\n    &:hover {\r\n      color: #1890ff;\r\n    }\r\n  }\r\n}\r\n\r\n.settings-main {\r\n  flex: 1;\r\n  padding-left: 20px;\r\n}\r\n\r\n.settings-section {\r\n  .section-title {\r\n    font-weight: bold;\r\n    margin: 15px 0 10px;\r\n    color: #333;\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n.options-group {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin-bottom: 15px;\r\n\r\n  .el-checkbox {\r\n    margin-right: 15px;\r\n    margin-bottom: 10px;\r\n    min-width: 80px;\r\n  }\r\n}\r\n\r\n.download-settings {\r\n  margin-bottom: 15px;\r\n\r\n  .setting-item {\r\n    display: flex;\r\n    margin-bottom: 10px;\r\n    font-size: 14px;\r\n\r\n    .label {\r\n      width: 80px;\r\n      color: #666;\r\n    }\r\n\r\n    .value {\r\n      flex: 1;\r\n      color: #333;\r\n    }\r\n  }\r\n}\r\n\r\n.save-button {\r\n  margin-top: 20px;\r\n\r\n  .el-button {\r\n    background-color: #1890ff;\r\n    border-color: #1890ff;\r\n    padding: 8px 20px;\r\n  }\r\n}\r\n\r\n/* 我的下载样式 */\r\n.download-container {\r\n  background-color: #fff;\r\n}\r\n\r\n.download-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-bottom: 15px;\r\n  margin-bottom: 15px;\r\n\r\n  .title {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .actions {\r\n    .el-button {\r\n      margin-left: 10px;\r\n    }\r\n  }\r\n}\r\n\r\n.download-content {\r\n  .el-table {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .download-status {\r\n    color: #67c23a;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-top: 20px;\r\n\r\n  span {\r\n    margin: 0 5px;\r\n  }\r\n\r\n  .jump-page-input {\r\n    width: 50px;\r\n    margin: 0 5px;\r\n  }\r\n}\r\n\r\n/* 修改密码表单样式 */\r\n.password-form {\r\n  max-width: 500px;\r\n  margin: 20px 0;\r\n\r\n  .form-group {\r\n    margin-bottom: 20px;\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .form-label {\r\n      width: 100px;\r\n      text-align: right;\r\n      margin-right: 15px;\r\n      color: #606266;\r\n    }\r\n\r\n    .el-input {\r\n      width: 300px;\r\n    }\r\n  }\r\n\r\n  .form-actions {\r\n    margin-top: 30px;\r\n    padding-left: 115px;\r\n\r\n    .el-button {\r\n      width: 100px;\r\n    }\r\n  }\r\n}\r\n\r\n/* 我的收藏样式 */\r\n.favorite-container {\r\n  background-color: #fff;\r\n}\r\n\r\n.favorite-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-bottom: 15px;\r\n  margin-bottom: 15px;\r\n  border-bottom: 1px solid #eee;\r\n\r\n  .title {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n.favorite-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n\r\n  .toolbar-left {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .el-checkbox {\r\n      margin-right: 15px;\r\n    }\r\n\r\n    .el-button {\r\n      margin-right: 10px;\r\n    }\r\n  }\r\n\r\n  .toolbar-right {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .date-filter {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-right: 15px;\r\n\r\n      .date-separator {\r\n        margin: 0 5px;\r\n      }\r\n    }\r\n\r\n    .search-box {\r\n      margin-right: 15px;\r\n    }\r\n  }\r\n}\r\n\r\n.favorite-content {\r\n  margin-top: 20px;\r\n\r\n  .favorite-list {\r\n    border: 1px solid #ebeef5;\r\n    border-radius: 4px;\r\n\r\n    .favorite-item {\r\n      display: flex;\r\n      padding: 15px;\r\n      border-bottom: 1px solid #ebeef5;\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      .item-checkbox {\r\n        margin-right: 15px;\r\n        display: flex;\r\n        align-items: center;\r\n      }\r\n\r\n      .item-content {\r\n        flex: 1;\r\n\r\n        .item-title {\r\n          margin-bottom: 8px;\r\n\r\n          .title-tag {\r\n            display: inline-block;\r\n            padding: 2px 6px;\r\n            font-size: 12px;\r\n            color: #fff;\r\n            background-color: #f56c6c;\r\n            border-radius: 2px;\r\n            margin-right: 8px;\r\n          }\r\n\r\n          .title-text {\r\n            font-size: 16px;\r\n            font-weight: 500;\r\n            color: #303133;\r\n          }\r\n        }\r\n\r\n        .item-info {\r\n          font-size: 13px;\r\n          color: #909399;\r\n\r\n          .info-time {\r\n            margin-right: 15px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .item-actions {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-left: 15px;\r\n\r\n        .cancel-favorite-btn {\r\n          color: #f56c6c;\r\n\r\n          &:hover {\r\n            color: #ff7c7c;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .favorite-pagination {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-top: 20px;\r\n    padding: 10px 0;\r\n\r\n    .pagination-info {\r\n      font-size: 14px;\r\n      color: #606266;\r\n\r\n      span {\r\n        font-weight: bold;\r\n        color: #303133;\r\n      }\r\n    }\r\n\r\n    .pagination-controls {\r\n      flex: 1;\r\n      text-align: center;\r\n    }\r\n\r\n    .pagination-jump {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      span {\r\n        margin: 0 5px;\r\n        font-size: 14px;\r\n        color: #606266;\r\n      }\r\n\r\n      .jump-page-input {\r\n        width: 50px;\r\n        margin: 0 5px;\r\n      }\r\n\r\n      .el-button {\r\n        margin-left: 5px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 我的联系人样式 */\r\n.contact-container {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.contact-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n\r\n  .title {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .contact-tabs {\r\n    .el-radio-group {\r\n      .el-radio-button {\r\n        margin-right: -1px;\r\n\r\n        &:first-child .el-radio-button__inner {\r\n          border-radius: 4px 0 0 4px;\r\n        }\r\n\r\n        &:last-child .el-radio-button__inner {\r\n          border-radius: 0 4px 4px 0;\r\n        }\r\n\r\n        .el-radio-button__inner {\r\n          padding: 8px 15px;\r\n          font-size: 13px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.contact-toolbar {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.contact-content {\r\n  .el-table {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .delete-btn {\r\n    color: #f56c6c;\r\n  }\r\n}\r\n\r\n/* 素材库样式 */\r\n.material-container {\r\n  background-color: #fff;\r\n}\r\n\r\n.material-header {\r\n  padding-bottom: 15px;\r\n  margin-bottom: 15px;\r\n\r\n  .title {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n.material-content {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n\r\n  .material-add-box {\r\n    width: 200px;\r\n    height: 150px;\r\n    border: 1px dashed #d9d9d9;\r\n    border-radius: 4px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n    cursor: pointer;\r\n    margin-right: 20px;\r\n    margin-bottom: 20px;\r\n\r\n    &:hover {\r\n      border-color: #1890ff;\r\n\r\n      .add-icon, .add-text {\r\n        color: #1890ff;\r\n      }\r\n    }\r\n\r\n    .add-icon {\r\n      font-size: 30px;\r\n      color: #8c8c8c;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .add-text {\r\n      font-size: 14px;\r\n      color: #8c8c8c;\r\n    }\r\n  }\r\n}\r\n\r\n.material-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px;\r\n    text-align: center;\r\n    border-bottom: 1px solid #f0f0f0;\r\n  }\r\n\r\n  .material-form {\r\n    padding: 20px 0;\r\n\r\n    .form-item {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 15px;\r\n\r\n      .form-label {\r\n        width: 80px;\r\n        text-align: right;\r\n        margin-right: 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .dialog-footer {\r\n    text-align: center;\r\n\r\n    .el-button {\r\n      width: 80px;\r\n    }\r\n  }\r\n}\r\n\r\n/* 更新说明样式 */\r\n.updates-section {\r\n  .updates-header {\r\n    margin-bottom: 20px;\r\n\r\n    .version-title {\r\n      font-size: 16px;\r\n      font-weight: bold;\r\n      margin-bottom: 5px;\r\n    }\r\n\r\n    .version-date {\r\n      font-size: 12px;\r\n      color: #999;\r\n    }\r\n  }\r\n\r\n  .updates-content {\r\n    display: flex;\r\n\r\n    .version-list {\r\n      width: 100px;\r\n      border-right: 1px solid #eee;\r\n      padding-right: 15px;\r\n      margin-right: 20px;\r\n\r\n      .version-item {\r\n        padding: 8px 0;\r\n        cursor: pointer;\r\n        color: #666;\r\n        font-size: 14px;\r\n        text-align: center;\r\n        border-radius: 4px;\r\n        margin-bottom: 5px;\r\n\r\n        &:hover {\r\n          background-color: #f5f7fa;\r\n        }\r\n\r\n        &.active {\r\n          background-color: #e6f7ff;\r\n          color: #1890ff;\r\n          font-weight: bold;\r\n        }\r\n      }\r\n    }\r\n\r\n    .update-details {\r\n      flex: 1;\r\n\r\n      .update-notes {\r\n        .note-item {\r\n          display: flex;\r\n          margin-bottom: 10px;\r\n          line-height: 1.6;\r\n\r\n          .note-number {\r\n            margin-right: 5px;\r\n            color: #666;\r\n          }\r\n\r\n          .note-content {\r\n            flex: 1;\r\n\r\n            b {\r\n              color: #1890ff;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}