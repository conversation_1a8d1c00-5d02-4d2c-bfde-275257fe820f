{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\blots\\text.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\blots\\text.js", "mtime": 1749104422736}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_parchment", "require", "Text", "exports", "default", "_TextBlot", "_classCallCheck2", "_callSuper2", "arguments", "_inherits2", "_createClass2", "TextBlot", "escapeText", "text", "replace", "s", "entityMap"], "sources": ["../../src/blots/text.ts"], "sourcesContent": ["import { TextBlot } from 'parchment';\n\nclass Text extends TextBlot {}\n\nfunction escapeText(text: string) {\n  return text.replace(/[&<>\"']/g, (s) => {\n    // https://lodash.com/docs#escape\n    const entityMap: Record<string, string> = {\n      '&': '&amp;',\n      '<': '&lt;',\n      '>': '&gt;',\n      '\"': '&quot;',\n      \"'\": '&#39;',\n    };\n    return entityMap[s];\n  });\n}\n\nexport { Text as default, escapeText };\n"], "mappings": ";;;;;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAAoC,IAE9BC,IAAI,GAAAC,OAAA,CAAAC,OAAA,0BAAAC,SAAA;EAAA,SAAAH,KAAA;IAAA,IAAAI,gBAAA,CAAAF,OAAA,QAAAF,IAAA;IAAA,WAAAK,WAAA,CAAAH,OAAA,QAAAF,IAAA,EAAAM,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAL,OAAA,EAAAF,IAAA,EAAAG,SAAA;EAAA,WAAAK,aAAA,CAAAN,OAAA,EAAAF,IAAA;AAAA,EAASS,mBAAQ;AAE3B,SAASC,UAAUA,CAACC,IAAY,EAAE;EAChC,OAAOA,IAAI,CAACC,OAAO,CAAC,UAAU,EAAG,UAAAC,CAAC,EAAK;IACrC;IACA,IAAMC,SAAiC,GAAG;MACxC,GAAG,EAAE,OAAO;MACZ,GAAG,EAAE,MAAM;MACX,GAAG,EAAE,MAAM;MACX,GAAG,EAAE,QAAQ;MACb,GAAG,EAAE;IACP,CAAC;IACD,OAAOA,SAAS,CAACD,CAAC,CAAC;EACrB,CAAC,CAAC;AACJ", "ignoreList": []}]}