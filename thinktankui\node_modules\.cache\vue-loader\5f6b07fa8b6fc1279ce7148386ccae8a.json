{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\system\\user\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\system\\user\\index.vue", "mtime": 1749104047647}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0VXNlciwgZ2V0VXNlciwgZGVsVXNlciwgYWRkVXNlciwgdXBkYXRlVXNlciwgcmVzZXRVc2VyUHdkLCBjaGFuZ2VVc2VyU3RhdHVzLCBkZXB0VHJlZVNlbGVjdCB9IGZyb20gIkAvYXBpL3N5c3RlbS91c2VyIjsNCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsNCmltcG9ydCBUcmVlc2VsZWN0IGZyb20gIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0IjsNCmltcG9ydCAiQHJpb3BoYWUvdnVlLXRyZWVzZWxlY3QvZGlzdC92dWUtdHJlZXNlbGVjdC5jc3MiOw0KaW1wb3J0IHsgU3BsaXRwYW5lcywgUGFuZSB9IGZyb20gInNwbGl0cGFuZXMiOw0KaW1wb3J0ICJzcGxpdHBhbmVzL2Rpc3Qvc3BsaXRwYW5lcy5jc3MiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJVc2VyIiwNCiAgZGljdHM6IFsnc3lzX25vcm1hbF9kaXNhYmxlJywgJ3N5c191c2VyX3NleCddLA0KICBjb21wb25lbnRzOiB7IFRyZWVzZWxlY3QsIFNwbGl0cGFuZXMsIFBhbmUgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIOeUqOaIt+ihqOagvOaVsOaNrg0KICAgICAgdXNlckxpc3Q6IG51bGwsDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOaJgOaciemDqOmXqOagkemAiemhuQ0KICAgICAgZGVwdE9wdGlvbnM6IHVuZGVmaW5lZCwNCiAgICAgIC8vIOi/h+a7pOaOieW3suemgeeUqOmDqOmXqOagkemAiemhuQ0KICAgICAgZW5hYmxlZERlcHRPcHRpb25zOiB1bmRlZmluZWQsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g6YOo6Zeo5ZCN56ewDQogICAgICBkZXB0TmFtZTogdW5kZWZpbmVkLA0KICAgICAgLy8g6buY6K6k5a+G56CBDQogICAgICBpbml0UGFzc3dvcmQ6IHVuZGVmaW5lZCwNCiAgICAgIC8vIOaXpeacn+iMg+WbtA0KICAgICAgZGF0ZVJhbmdlOiBbXSwNCiAgICAgIC8vIOWyl+S9jemAiemhuQ0KICAgICAgcG9zdE9wdGlvbnM6IFtdLA0KICAgICAgLy8g6KeS6Imy6YCJ6aG5DQogICAgICByb2xlT3B0aW9uczogW10sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHt9LA0KICAgICAgZGVmYXVsdFByb3BzOiB7DQogICAgICAgIGNoaWxkcmVuOiAiY2hpbGRyZW4iLA0KICAgICAgICBsYWJlbDogImxhYmVsIg0KICAgICAgfSwNCiAgICAgIC8vIOeUqOaIt+WvvOWFpeWPguaVsA0KICAgICAgdXBsb2FkOiB7DQogICAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgu+8iOeUqOaIt+WvvOWFpe+8iQ0KICAgICAgICBvcGVuOiBmYWxzZSwNCiAgICAgICAgLy8g5by55Ye65bGC5qCH6aKY77yI55So5oi35a+85YWl77yJDQogICAgICAgIHRpdGxlOiAiIiwNCiAgICAgICAgLy8g5piv5ZCm56aB55So5LiK5LygDQogICAgICAgIGlzVXBsb2FkaW5nOiBmYWxzZSwNCiAgICAgICAgLy8g5piv5ZCm5pu05paw5bey57uP5a2Y5Zyo55qE55So5oi35pWw5o2uDQogICAgICAgIHVwZGF0ZVN1cHBvcnQ6IGZhbHNlLA0KICAgICAgICAvLyDorr7nva7kuIrkvKDnmoTor7fmsYLlpLTpg6gNCiAgICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiAiQmVhcmVyICIgKyBnZXRUb2tlbigpIH0sDQogICAgICAgIC8vIOS4iuS8oOeahOWcsOWdgA0KICAgICAgICB1cmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL3N5c3RlbS91c2VyL2ltcG9ydERhdGEiDQogICAgICB9LA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIHVzZXJOYW1lOiB1bmRlZmluZWQsDQogICAgICAgIHBob25lbnVtYmVyOiB1bmRlZmluZWQsDQogICAgICAgIHN0YXR1czogdW5kZWZpbmVkLA0KICAgICAgICBkZXB0SWQ6IHVuZGVmaW5lZA0KICAgICAgfSwNCiAgICAgIC8vIOWIl+S/oeaBrw0KICAgICAgY29sdW1uczogWw0KICAgICAgICB7IGtleTogMCwgbGFiZWw6IGDnlKjmiLfnvJblj7dgLCB2aXNpYmxlOiB0cnVlIH0sDQogICAgICAgIHsga2V5OiAxLCBsYWJlbDogYOeUqOaIt+WQjeensGAsIHZpc2libGU6IHRydWUgfSwNCiAgICAgICAgeyBrZXk6IDIsIGxhYmVsOiBg55So5oi35pi156ewYCwgdmlzaWJsZTogdHJ1ZSB9LA0KICAgICAgICB7IGtleTogMywgbGFiZWw6IGDpg6jpl6hgLCB2aXNpYmxlOiB0cnVlIH0sDQogICAgICAgIHsga2V5OiA0LCBsYWJlbDogYOaJi+acuuWPt+eggWAsIHZpc2libGU6IHRydWUgfSwNCiAgICAgICAgeyBrZXk6IDUsIGxhYmVsOiBg54q25oCBYCwgdmlzaWJsZTogdHJ1ZSB9LA0KICAgICAgICB7IGtleTogNiwgbGFiZWw6IGDliJvlu7rml7bpl7RgLCB2aXNpYmxlOiB0cnVlIH0NCiAgICAgIF0sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIHVzZXJOYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIueUqOaIt+WQjeensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICAgIHsgbWluOiAyLCBtYXg6IDIwLCBtZXNzYWdlOiAn55So5oi35ZCN56ew6ZW/5bqm5b+F6aG75LuL5LqOIDIg5ZKMIDIwIOS5i+mXtCcsIHRyaWdnZXI6ICdibHVyJyB9DQogICAgICAgIF0sDQogICAgICAgIG5pY2tOYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIueUqOaIt+aYteensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICAgIHBhc3N3b3JkOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIueUqOaIt+WvhueggeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICAgIHsgbWluOiA1LCBtYXg6IDIwLCBtZXNzYWdlOiAn55So5oi35a+G56CB6ZW/5bqm5b+F6aG75LuL5LqOIDUg5ZKMIDIwIOS5i+mXtCcsIHRyaWdnZXI6ICdibHVyJyB9LA0KICAgICAgICAgIHsgcGF0dGVybjogL15bXjw+Iid8XFxdKyQvLCBtZXNzYWdlOiAi5LiN6IO95YyF5ZCr6Z2e5rOV5a2X56ym77yaPCA+IFwiICcgXFxcIHwiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBlbWFpbDogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHR5cGU6ICJlbWFpbCIsDQogICAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWl5q2j56Gu55qE6YKu566x5Zyw5Z2AIiwNCiAgICAgICAgICAgIHRyaWdnZXI6IFsiYmx1ciIsICJjaGFuZ2UiXQ0KICAgICAgICAgIH0NCiAgICAgICAgXSwNCiAgICAgICAgcGhvbmVudW1iZXI6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICBwYXR0ZXJuOiAvXjFbM3w0fDV8Nnw3fDh8OV1bMC05XVxkezh9JC8sDQogICAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWl5q2j56Gu55qE5omL5py65Y+356CBIiwNCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIg0KICAgICAgICAgIH0NCiAgICAgICAgXQ0KICAgICAgfQ0KICAgIH07DQogIH0sDQogIHdhdGNoOiB7DQogICAgLy8g5qC55o2u5ZCN56ew562b6YCJ6YOo6Zeo5qCRDQogICAgZGVwdE5hbWUodmFsKSB7DQogICAgICB0aGlzLiRyZWZzLnRyZWUuZmlsdGVyKHZhbCk7DQogICAgfQ0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIHRoaXMuZ2V0RGVwdFRyZWUoKTsNCiAgICB0aGlzLmdldENvbmZpZ0tleSgic3lzLnVzZXIuaW5pdFBhc3N3b3JkIikudGhlbihyZXNwb25zZSA9PiB7DQogICAgICB0aGlzLmluaXRQYXNzd29yZCA9IHJlc3BvbnNlLm1zZzsNCiAgICB9KTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8qKiDmn6Xor6LnlKjmiLfliJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxpc3RVc2VyKHRoaXMuYWRkRGF0ZVJhbmdlKHRoaXMucXVlcnlQYXJhbXMsIHRoaXMuZGF0ZVJhbmdlKSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgdGhpcy51c2VyTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB9DQogICAgICApOw0KICAgIH0sDQogICAgLyoqIOafpeivoumDqOmXqOS4i+aLieagkee7k+aehCAqLw0KICAgIGdldERlcHRUcmVlKCkgew0KICAgICAgZGVwdFRyZWVTZWxlY3QoKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5kZXB0T3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMuZW5hYmxlZERlcHRPcHRpb25zID0gdGhpcy5maWx0ZXJEaXNhYmxlZERlcHQoSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShyZXNwb25zZS5kYXRhKSkpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDov4fmu6TnpoHnlKjnmoTpg6jpl6gNCiAgICBmaWx0ZXJEaXNhYmxlZERlcHQoZGVwdExpc3QpIHsNCiAgICAgIHJldHVybiBkZXB0TGlzdC5maWx0ZXIoZGVwdCA9PiB7DQogICAgICAgIGlmIChkZXB0LmRpc2FibGVkKSB7DQogICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICB9DQogICAgICAgIGlmIChkZXB0LmNoaWxkcmVuICYmIGRlcHQuY2hpbGRyZW4ubGVuZ3RoKSB7DQogICAgICAgICAgZGVwdC5jaGlsZHJlbiA9IHRoaXMuZmlsdGVyRGlzYWJsZWREZXB0KGRlcHQuY2hpbGRyZW4pOw0KICAgICAgICB9DQogICAgICAgIHJldHVybiB0cnVlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDnrZvpgInoioLngrkNCiAgICBmaWx0ZXJOb2RlKHZhbHVlLCBkYXRhKSB7DQogICAgICBpZiAoIXZhbHVlKSByZXR1cm4gdHJ1ZTsNCiAgICAgIHJldHVybiBkYXRhLmxhYmVsLmluZGV4T2YodmFsdWUpICE9PSAtMTsNCiAgICB9LA0KICAgIC8vIOiKgueCueWNleWHu+S6i+S7tg0KICAgIGhhbmRsZU5vZGVDbGljayhkYXRhKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmRlcHRJZCA9IGRhdGEuaWQ7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICAvLyDnlKjmiLfnirbmgIHkv67mlLkNCiAgICBoYW5kbGVTdGF0dXNDaGFuZ2Uocm93KSB7DQogICAgICBsZXQgdGV4dCA9IHJvdy5zdGF0dXMgPT09ICIwIiA/ICLlkK/nlKgiIDogIuWBnOeUqCI7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfnoa7orqTopoEiJyArIHRleHQgKyAnIiInICsgcm93LnVzZXJOYW1lICsgJyLnlKjmiLflkJfvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgew0KICAgICAgICByZXR1cm4gY2hhbmdlVXNlclN0YXR1cyhyb3cudXNlcklkLCByb3cuc3RhdHVzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKHRleHQgKyAi5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaChmdW5jdGlvbigpIHsNCiAgICAgICAgcm93LnN0YXR1cyA9IHJvdy5zdGF0dXMgPT09ICIwIiA/ICIxIiA6ICIwIjsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5Y+W5raI5oyJ6ZKuDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgfSwNCiAgICAvLyDooajljZXph43nva4NCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgdXNlcklkOiB1bmRlZmluZWQsDQogICAgICAgIGRlcHRJZDogdW5kZWZpbmVkLA0KICAgICAgICB1c2VyTmFtZTogdW5kZWZpbmVkLA0KICAgICAgICBuaWNrTmFtZTogdW5kZWZpbmVkLA0KICAgICAgICBwYXNzd29yZDogdW5kZWZpbmVkLA0KICAgICAgICBwaG9uZW51bWJlcjogdW5kZWZpbmVkLA0KICAgICAgICBlbWFpbDogdW5kZWZpbmVkLA0KICAgICAgICBzZXg6IHVuZGVmaW5lZCwNCiAgICAgICAgc3RhdHVzOiAiMCIsDQogICAgICAgIHJlbWFyazogdW5kZWZpbmVkLA0KICAgICAgICBwb3N0SWRzOiBbXSwNCiAgICAgICAgcm9sZUlkczogW10NCiAgICAgIH07DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLmRhdGVSYW5nZSA9IFtdOw0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5kZXB0SWQgPSB1bmRlZmluZWQ7DQogICAgICB0aGlzLiRyZWZzLnRyZWUuc2V0Q3VycmVudEtleShudWxsKTsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0udXNlcklkKTsNCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPSAxOw0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOw0KICAgIH0sDQogICAgLy8g5pu05aSa5pON5L2c6Kem5Y+RDQogICAgaGFuZGxlQ29tbWFuZChjb21tYW5kLCByb3cpIHsNCiAgICAgIHN3aXRjaCAoY29tbWFuZCkgew0KICAgICAgICBjYXNlICJoYW5kbGVSZXNldFB3ZCI6DQogICAgICAgICAgdGhpcy5oYW5kbGVSZXNldFB3ZChyb3cpOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICJoYW5kbGVBdXRoUm9sZSI6DQogICAgICAgICAgdGhpcy5oYW5kbGVBdXRoUm9sZShyb3cpOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBkZWZhdWx0Og0KICAgICAgICAgIGJyZWFrOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIGdldFVzZXIoKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5wb3N0T3B0aW9ucyA9IHJlc3BvbnNlLnBvc3RzOw0KICAgICAgICB0aGlzLnJvbGVPcHRpb25zID0gcmVzcG9uc2Uucm9sZXM7DQogICAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg55So5oi3IjsNCiAgICAgICAgdGhpcy5mb3JtLnBhc3N3b3JkID0gdGhpcy5pbml0UGFzc3dvcmQ7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICBjb25zdCB1c2VySWQgPSByb3cudXNlcklkIHx8IHRoaXMuaWRzOw0KICAgICAgZ2V0VXNlcih1c2VySWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB0aGlzLnBvc3RPcHRpb25zID0gcmVzcG9uc2UucG9zdHM7DQogICAgICAgIHRoaXMucm9sZU9wdGlvbnMgPSByZXNwb25zZS5yb2xlczsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgInBvc3RJZHMiLCByZXNwb25zZS5wb3N0SWRzKTsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgInJvbGVJZHMiLCByZXNwb25zZS5yb2xlSWRzKTsNCiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnnlKjmiLciOw0KICAgICAgICB0aGlzLmZvcm0ucGFzc3dvcmQgPSB1bmRlZmluZWQ7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDph43nva7lr4bnoIHmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVSZXNldFB3ZChyb3cpIHsNCiAgICAgIHRoaXMuJHByb21wdCgn6K+36L6T5YWlIicgKyByb3cudXNlck5hbWUgKyAnIueahOaWsOWvhueggScsICLmj5DnpLoiLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsDQogICAgICAgIGNsb3NlT25DbGlja01vZGFsOiBmYWxzZSwNCiAgICAgICAgaW5wdXRQYXR0ZXJuOiAvXi57NSwyMH0kLywNCiAgICAgICAgaW5wdXRFcnJvck1lc3NhZ2U6ICLnlKjmiLflr4bnoIHplb/luqblv4Xpobvku4vkuo4gNSDlkowgMjAg5LmL6Ze0IiwNCiAgICAgICAgaW5wdXRWYWxpZGF0b3I6ICh2YWx1ZSkgPT4gew0KICAgICAgICAgIGlmICgvPHw+fCJ8J3xcfHxcXC8udGVzdCh2YWx1ZSkpIHsNCiAgICAgICAgICAgIHJldHVybiAi5LiN6IO95YyF5ZCr6Z2e5rOV5a2X56ym77yaPCA+IFwiICcgXFxcIHwiDQogICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgfSkudGhlbigoeyB2YWx1ZSB9KSA9PiB7DQogICAgICAgICAgcmVzZXRVc2VyUHdkKHJvdy51c2VySWQsIHZhbHVlKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKn++8jOaWsOWvhueggeaYr++8miIgKyB2YWx1ZSk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8qKiDliIbphY3op5LoibLmk43kvZwgKi8NCiAgICBoYW5kbGVBdXRoUm9sZTogZnVuY3Rpb24ocm93KSB7DQogICAgICBjb25zdCB1c2VySWQgPSByb3cudXNlcklkOw0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goIi9zeXN0ZW0vdXNlci1hdXRoL3JvbGUvIiArIHVzZXJJZCk7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybTogZnVuY3Rpb24oKSB7DQogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBpZiAodGhpcy5mb3JtLnVzZXJJZCAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgIHVwZGF0ZVVzZXIodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBhZGRVc2VyKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnN0IHVzZXJJZHMgPSByb3cudXNlcklkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk55So5oi357yW5Y+35Li6IicgKyB1c2VySWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgew0KICAgICAgICByZXR1cm4gZGVsVXNlcih1c2VySWRzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgnc3lzdGVtL3VzZXIvZXhwb3J0Jywgew0KICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zDQogICAgICB9LCBgdXNlcl8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkNCiAgICB9LA0KICAgIC8qKiDlr7zlhaXmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVJbXBvcnQoKSB7DQogICAgICB0aGlzLnVwbG9hZC50aXRsZSA9ICLnlKjmiLflr7zlhaUiOw0KICAgICAgdGhpcy51cGxvYWQub3BlbiA9IHRydWU7DQogICAgfSwNCiAgICAvKiog5LiL6L295qih5p2/5pON5L2cICovDQogICAgaW1wb3J0VGVtcGxhdGUoKSB7DQogICAgICB0aGlzLmRvd25sb2FkKCdzeXN0ZW0vdXNlci9pbXBvcnRUZW1wbGF0ZScsIHsNCiAgICAgIH0sIGB1c2VyX3RlbXBsYXRlXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQ0KICAgIH0sDQogICAgLy8g5paH5Lu25LiK5Lyg5Lit5aSE55CGDQogICAgaGFuZGxlRmlsZVVwbG9hZFByb2dyZXNzKGV2ZW50LCBmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy51cGxvYWQuaXNVcGxvYWRpbmcgPSB0cnVlOw0KICAgIH0sDQogICAgLy8g5paH5Lu25LiK5Lyg5oiQ5Yqf5aSE55CGDQogICAgaGFuZGxlRmlsZVN1Y2Nlc3MocmVzcG9uc2UsIGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICB0aGlzLnVwbG9hZC5vcGVuID0gZmFsc2U7DQogICAgICB0aGlzLnVwbG9hZC5pc1VwbG9hZGluZyA9IGZhbHNlOw0KICAgICAgdGhpcy4kcmVmcy51cGxvYWQuY2xlYXJGaWxlcygpOw0KICAgICAgdGhpcy4kYWxlcnQoIjxkaXYgc3R5bGU9J292ZXJmbG93OiBhdXRvO292ZXJmbG93LXg6IGhpZGRlbjttYXgtaGVpZ2h0OiA3MHZoO3BhZGRpbmc6IDEwcHggMjBweCAwOyc+IiArIHJlc3BvbnNlLm1zZyArICI8L2Rpdj4iLCAi5a+85YWl57uT5p6cIiwgeyBkYW5nZXJvdXNseVVzZUhUTUxTdHJpbmc6IHRydWUgfSk7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIC8vIOaPkOS6pOS4iuS8oOaWh+S7tg0KICAgIHN1Ym1pdEZpbGVGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmcy51cGxvYWQuc3VibWl0KCk7DQogICAgfQ0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2VA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <splitpanes :horizontal=\"this.$store.getters.device === 'mobile'\" class=\"default-theme\">\r\n        <!--部门数据-->\r\n        <pane size=\"16\">\r\n          <div class=\"head-container\">\r\n            <el-input\r\n              v-model=\"deptName\"\r\n              placeholder=\"请输入部门名称\"\r\n              clearable\r\n              size=\"small\"\r\n              prefix-icon=\"el-icon-search\"\r\n              style=\"margin-bottom: 20px\"\r\n            />\r\n          </div>\r\n          <div class=\"head-container\">\r\n            <el-tree\r\n              :data=\"deptOptions\"\r\n              :props=\"defaultProps\"\r\n              :expand-on-click-node=\"false\"\r\n              :filter-node-method=\"filterNode\"\r\n              ref=\"tree\"\r\n              node-key=\"id\"\r\n              default-expand-all\r\n              highlight-current\r\n              @node-click=\"handleNodeClick\"\r\n            />\r\n          </div>\r\n        </pane>\r\n        <!--用户数据-->\r\n        <pane size=\"84\">\r\n          <el-col>\r\n            <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n              <el-form-item label=\"用户名称\" prop=\"userName\">\r\n                <el-input\r\n                  v-model=\"queryParams.userName\"\r\n                  placeholder=\"请输入用户名称\"\r\n                  clearable\r\n                  style=\"width: 240px\"\r\n                  @keyup.enter.native=\"handleQuery\"\r\n                />\r\n              </el-form-item>\r\n              <el-form-item label=\"手机号码\" prop=\"phonenumber\">\r\n                <el-input\r\n                  v-model=\"queryParams.phonenumber\"\r\n                  placeholder=\"请输入手机号码\"\r\n                  clearable\r\n                  style=\"width: 240px\"\r\n                  @keyup.enter.native=\"handleQuery\"\r\n                />\r\n              </el-form-item>\r\n              <el-form-item label=\"状态\" prop=\"status\">\r\n                <el-select\r\n                  v-model=\"queryParams.status\"\r\n                  placeholder=\"用户状态\"\r\n                  clearable\r\n                  style=\"width: 240px\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"dict in dict.type.sys_normal_disable\"\r\n                    :key=\"dict.value\"\r\n                    :label=\"dict.label\"\r\n                    :value=\"dict.value\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n              <el-form-item label=\"创建时间\">\r\n                <el-date-picker\r\n                  v-model=\"dateRange\"\r\n                  style=\"width: 240px\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                  type=\"daterange\"\r\n                  range-separator=\"-\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                ></el-date-picker>\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n                <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n              </el-form-item>\r\n            </el-form>\r\n\r\n            <el-row :gutter=\"10\" class=\"mb8\">\r\n              <el-col :span=\"1.5\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  plain\r\n                  icon=\"el-icon-plus\"\r\n                  size=\"mini\"\r\n                  @click=\"handleAdd\"\r\n                  v-hasPermi=\"['system:user:add']\"\r\n                >新增</el-button>\r\n              </el-col>\r\n              <el-col :span=\"1.5\">\r\n                <el-button\r\n                  type=\"success\"\r\n                  plain\r\n                  icon=\"el-icon-edit\"\r\n                  size=\"mini\"\r\n                  :disabled=\"single\"\r\n                  @click=\"handleUpdate\"\r\n                  v-hasPermi=\"['system:user:edit']\"\r\n                >修改</el-button>\r\n              </el-col>\r\n              <el-col :span=\"1.5\">\r\n                <el-button\r\n                  type=\"danger\"\r\n                  plain\r\n                  icon=\"el-icon-delete\"\r\n                  size=\"mini\"\r\n                  :disabled=\"multiple\"\r\n                  @click=\"handleDelete\"\r\n                  v-hasPermi=\"['system:user:remove']\"\r\n                >删除</el-button>\r\n              </el-col>\r\n              <el-col :span=\"1.5\">\r\n                <el-button\r\n                  type=\"info\"\r\n                  plain\r\n                  icon=\"el-icon-upload2\"\r\n                  size=\"mini\"\r\n                  @click=\"handleImport\"\r\n                  v-hasPermi=\"['system:user:import']\"\r\n                >导入</el-button>\r\n              </el-col>\r\n              <el-col :span=\"1.5\">\r\n                <el-button\r\n                  type=\"warning\"\r\n                  plain\r\n                  icon=\"el-icon-download\"\r\n                  size=\"mini\"\r\n                  @click=\"handleExport\"\r\n                  v-hasPermi=\"['system:user:export']\"\r\n                >导出</el-button>\r\n              </el-col>\r\n              <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\" :columns=\"columns\"></right-toolbar>\r\n            </el-row>\r\n\r\n            <el-table v-loading=\"loading\" :data=\"userList\" @selection-change=\"handleSelectionChange\">\r\n              <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\r\n              <el-table-column label=\"用户编号\" align=\"center\" key=\"userId\" prop=\"userId\" v-if=\"columns[0].visible\" />\r\n              <el-table-column label=\"用户名称\" align=\"center\" key=\"userName\" prop=\"userName\" v-if=\"columns[1].visible\" :show-overflow-tooltip=\"true\" />\r\n              <el-table-column label=\"用户昵称\" align=\"center\" key=\"nickName\" prop=\"nickName\" v-if=\"columns[2].visible\" :show-overflow-tooltip=\"true\" />\r\n              <el-table-column label=\"部门\" align=\"center\" key=\"deptName\" prop=\"dept.deptName\" v-if=\"columns[3].visible\" :show-overflow-tooltip=\"true\" />\r\n              <el-table-column label=\"手机号码\" align=\"center\" key=\"phonenumber\" prop=\"phonenumber\" v-if=\"columns[4].visible\" width=\"120\" />\r\n              <el-table-column label=\"状态\" align=\"center\" key=\"status\" v-if=\"columns[5].visible\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-switch\r\n                    v-model=\"scope.row.status\"\r\n                    active-value=\"0\"\r\n                    inactive-value=\"1\"\r\n                    @change=\"handleStatusChange(scope.row)\"\r\n                  ></el-switch>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" v-if=\"columns[6].visible\" width=\"160\">\r\n                <template slot-scope=\"scope\">\r\n                  <span>{{ parseTime(scope.row.createTime) }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                label=\"操作\"\r\n                align=\"center\"\r\n                width=\"160\"\r\n                class-name=\"small-padding fixed-width\"\r\n              >\r\n                <template slot-scope=\"scope\" v-if=\"scope.row.userId !== 1\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    icon=\"el-icon-edit\"\r\n                    @click=\"handleUpdate(scope.row)\"\r\n                    v-hasPermi=\"['system:user:edit']\"\r\n                  >修改</el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    icon=\"el-icon-delete\"\r\n                    @click=\"handleDelete(scope.row)\"\r\n                    v-hasPermi=\"['system:user:remove']\"\r\n                  >删除</el-button>\r\n                  <el-dropdown size=\"mini\" @command=\"(command) => handleCommand(command, scope.row)\" v-hasPermi=\"['system:user:resetPwd', 'system:user:edit']\">\r\n                    <el-button size=\"mini\" type=\"text\" icon=\"el-icon-d-arrow-right\">更多</el-button>\r\n                    <el-dropdown-menu slot=\"dropdown\">\r\n                      <el-dropdown-item command=\"handleResetPwd\" icon=\"el-icon-key\"\r\n                        v-hasPermi=\"['system:user:resetPwd']\">重置密码</el-dropdown-item>\r\n                      <el-dropdown-item command=\"handleAuthRole\" icon=\"el-icon-circle-check\"\r\n                        v-hasPermi=\"['system:user:edit']\">分配角色</el-dropdown-item>\r\n                    </el-dropdown-menu>\r\n                  </el-dropdown>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <pagination\r\n              v-show=\"total>0\"\r\n              :total=\"total\"\r\n              :page.sync=\"queryParams.pageNum\"\r\n              :limit.sync=\"queryParams.pageSize\"\r\n              @pagination=\"getList\"\r\n            />\r\n          </el-col>\r\n        </pane>\r\n      </splitpanes>\r\n    </el-row>\r\n\r\n    <!-- 添加或修改用户配置对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"用户昵称\" prop=\"nickName\">\r\n              <el-input v-model=\"form.nickName\" placeholder=\"请输入用户昵称\" maxlength=\"30\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"归属部门\" prop=\"deptId\">\r\n              <treeselect v-model=\"form.deptId\" :options=\"enabledDeptOptions\" :show-count=\"true\" placeholder=\"请选择归属部门\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"手机号码\" prop=\"phonenumber\">\r\n              <el-input v-model=\"form.phonenumber\" placeholder=\"请输入手机号码\" maxlength=\"11\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"邮箱\" prop=\"email\">\r\n              <el-input v-model=\"form.email\" placeholder=\"请输入邮箱\" maxlength=\"50\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item v-if=\"form.userId == undefined\" label=\"用户名称\" prop=\"userName\">\r\n              <el-input v-model=\"form.userName\" placeholder=\"请输入用户名称\" maxlength=\"30\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item v-if=\"form.userId == undefined\" label=\"用户密码\" prop=\"password\">\r\n              <el-input v-model=\"form.password\" placeholder=\"请输入用户密码\" type=\"password\" maxlength=\"20\" show-password/>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"用户性别\">\r\n              <el-select v-model=\"form.sex\" placeholder=\"请选择性别\">\r\n                <el-option\r\n                  v-for=\"dict in dict.type.sys_user_sex\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                ></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"状态\">\r\n              <el-radio-group v-model=\"form.status\">\r\n                <el-radio\r\n                  v-for=\"dict in dict.type.sys_normal_disable\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.value\"\r\n                >{{dict.label}}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"岗位\">\r\n              <el-select v-model=\"form.postIds\" multiple placeholder=\"请选择岗位\">\r\n                <el-option\r\n                  v-for=\"item in postOptions\"\r\n                  :key=\"item.postId\"\r\n                  :label=\"item.postName\"\r\n                  :value=\"item.postId\"\r\n                  :disabled=\"item.status == 1\"\r\n                ></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"角色\">\r\n              <el-select v-model=\"form.roleIds\" multiple placeholder=\"请选择角色\">\r\n                <el-option\r\n                  v-for=\"item in roleOptions\"\r\n                  :key=\"item.roleId\"\r\n                  :label=\"item.roleName\"\r\n                  :value=\"item.roleId\"\r\n                  :disabled=\"item.status == 1\"\r\n                ></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"备注\">\r\n              <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 用户导入对话框 -->\r\n    <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" append-to-body>\r\n      <el-upload\r\n        ref=\"upload\"\r\n        :limit=\"1\"\r\n        accept=\".xlsx, .xls\"\r\n        :headers=\"upload.headers\"\r\n        :action=\"upload.url + '?updateSupport=' + upload.updateSupport\"\r\n        :disabled=\"upload.isUploading\"\r\n        :on-progress=\"handleFileUploadProgress\"\r\n        :on-success=\"handleFileSuccess\"\r\n        :auto-upload=\"false\"\r\n        drag\r\n      >\r\n        <i class=\"el-icon-upload\"></i>\r\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\r\n        <div class=\"el-upload__tip text-center\" slot=\"tip\">\r\n          <div class=\"el-upload__tip\" slot=\"tip\">\r\n            <el-checkbox v-model=\"upload.updateSupport\" /> 是否更新已经存在的用户数据\r\n          </div>\r\n          <span>仅允许导入xls、xlsx格式文件。</span>\r\n          <el-link type=\"primary\" :underline=\"false\" style=\"font-size:12px;vertical-align: baseline;\" @click=\"importTemplate\">下载模板</el-link>\r\n        </div>\r\n      </el-upload>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\r\n        <el-button @click=\"upload.open = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listUser, getUser, delUser, addUser, updateUser, resetUserPwd, changeUserStatus, deptTreeSelect } from \"@/api/system/user\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\nimport { Splitpanes, Pane } from \"splitpanes\";\r\nimport \"splitpanes/dist/splitpanes.css\";\r\n\r\nexport default {\r\n  name: \"User\",\r\n  dicts: ['sys_normal_disable', 'sys_user_sex'],\r\n  components: { Treeselect, Splitpanes, Pane },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 用户表格数据\r\n      userList: null,\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 所有部门树选项\r\n      deptOptions: undefined,\r\n      // 过滤掉已禁用部门树选项\r\n      enabledDeptOptions: undefined,\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 部门名称\r\n      deptName: undefined,\r\n      // 默认密码\r\n      initPassword: undefined,\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 岗位选项\r\n      postOptions: [],\r\n      // 角色选项\r\n      roleOptions: [],\r\n      // 表单参数\r\n      form: {},\r\n      defaultProps: {\r\n        children: \"children\",\r\n        label: \"label\"\r\n      },\r\n      // 用户导入参数\r\n      upload: {\r\n        // 是否显示弹出层（用户导入）\r\n        open: false,\r\n        // 弹出层标题（用户导入）\r\n        title: \"\",\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 是否更新已经存在的用户数据\r\n        updateSupport: false,\r\n        // 设置上传的请求头部\r\n        headers: { Authorization: \"Bearer \" + getToken() },\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + \"/system/user/importData\"\r\n      },\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        userName: undefined,\r\n        phonenumber: undefined,\r\n        status: undefined,\r\n        deptId: undefined\r\n      },\r\n      // 列信息\r\n      columns: [\r\n        { key: 0, label: `用户编号`, visible: true },\r\n        { key: 1, label: `用户名称`, visible: true },\r\n        { key: 2, label: `用户昵称`, visible: true },\r\n        { key: 3, label: `部门`, visible: true },\r\n        { key: 4, label: `手机号码`, visible: true },\r\n        { key: 5, label: `状态`, visible: true },\r\n        { key: 6, label: `创建时间`, visible: true }\r\n      ],\r\n      // 表单校验\r\n      rules: {\r\n        userName: [\r\n          { required: true, message: \"用户名称不能为空\", trigger: \"blur\" },\r\n          { min: 2, max: 20, message: '用户名称长度必须介于 2 和 20 之间', trigger: 'blur' }\r\n        ],\r\n        nickName: [\r\n          { required: true, message: \"用户昵称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        password: [\r\n          { required: true, message: \"用户密码不能为空\", trigger: \"blur\" },\r\n          { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' },\r\n          { pattern: /^[^<>\"'|\\\\]+$/, message: \"不能包含非法字符：< > \\\" ' \\\\\\ |\", trigger: \"blur\" }\r\n        ],\r\n        email: [\r\n          {\r\n            type: \"email\",\r\n            message: \"请输入正确的邮箱地址\",\r\n            trigger: [\"blur\", \"change\"]\r\n          }\r\n        ],\r\n        phonenumber: [\r\n          {\r\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\r\n            message: \"请输入正确的手机号码\",\r\n            trigger: \"blur\"\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  watch: {\r\n    // 根据名称筛选部门树\r\n    deptName(val) {\r\n      this.$refs.tree.filter(val);\r\n    }\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getDeptTree();\r\n    this.getConfigKey(\"sys.user.initPassword\").then(response => {\r\n      this.initPassword = response.msg;\r\n    });\r\n  },\r\n  methods: {\r\n    /** 查询用户列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\r\n          this.userList = response.rows;\r\n          this.total = response.total;\r\n          this.loading = false;\r\n        }\r\n      );\r\n    },\r\n    /** 查询部门下拉树结构 */\r\n    getDeptTree() {\r\n      deptTreeSelect().then(response => {\r\n        this.deptOptions = response.data;\r\n        this.enabledDeptOptions = this.filterDisabledDept(JSON.parse(JSON.stringify(response.data)));\r\n      });\r\n    },\r\n    // 过滤禁用的部门\r\n    filterDisabledDept(deptList) {\r\n      return deptList.filter(dept => {\r\n        if (dept.disabled) {\r\n          return false;\r\n        }\r\n        if (dept.children && dept.children.length) {\r\n          dept.children = this.filterDisabledDept(dept.children);\r\n        }\r\n        return true;\r\n      });\r\n    },\r\n    // 筛选节点\r\n    filterNode(value, data) {\r\n      if (!value) return true;\r\n      return data.label.indexOf(value) !== -1;\r\n    },\r\n    // 节点单击事件\r\n    handleNodeClick(data) {\r\n      this.queryParams.deptId = data.id;\r\n      this.handleQuery();\r\n    },\r\n    // 用户状态修改\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\r\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.userName + '\"用户吗？').then(function() {\r\n        return changeUserStatus(row.userId, row.status);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function() {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        userId: undefined,\r\n        deptId: undefined,\r\n        userName: undefined,\r\n        nickName: undefined,\r\n        password: undefined,\r\n        phonenumber: undefined,\r\n        email: undefined,\r\n        sex: undefined,\r\n        status: \"0\",\r\n        remark: undefined,\r\n        postIds: [],\r\n        roleIds: []\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.queryParams.deptId = undefined;\r\n      this.$refs.tree.setCurrentKey(null);\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.userId);\r\n      this.single = selection.length != 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    // 更多操作触发\r\n    handleCommand(command, row) {\r\n      switch (command) {\r\n        case \"handleResetPwd\":\r\n          this.handleResetPwd(row);\r\n          break;\r\n        case \"handleAuthRole\":\r\n          this.handleAuthRole(row);\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      getUser().then(response => {\r\n        this.postOptions = response.posts;\r\n        this.roleOptions = response.roles;\r\n        this.open = true;\r\n        this.title = \"添加用户\";\r\n        this.form.password = this.initPassword;\r\n      });\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const userId = row.userId || this.ids;\r\n      getUser(userId).then(response => {\r\n        this.form = response.data;\r\n        this.postOptions = response.posts;\r\n        this.roleOptions = response.roles;\r\n        this.$set(this.form, \"postIds\", response.postIds);\r\n        this.$set(this.form, \"roleIds\", response.roleIds);\r\n        this.open = true;\r\n        this.title = \"修改用户\";\r\n        this.form.password = undefined;\r\n      });\r\n    },\r\n    /** 重置密码按钮操作 */\r\n    handleResetPwd(row) {\r\n      this.$prompt('请输入\"' + row.userName + '\"的新密码', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        closeOnClickModal: false,\r\n        inputPattern: /^.{5,20}$/,\r\n        inputErrorMessage: \"用户密码长度必须介于 5 和 20 之间\",\r\n        inputValidator: (value) => {\r\n          if (/<|>|\"|'|\\||\\\\/.test(value)) {\r\n            return \"不能包含非法字符：< > \\\" ' \\\\\\ |\"\r\n          }\r\n        },\r\n      }).then(({ value }) => {\r\n          resetUserPwd(row.userId, value).then(response => {\r\n            this.$modal.msgSuccess(\"修改成功，新密码是：\" + value);\r\n          });\r\n        }).catch(() => {});\r\n    },\r\n    /** 分配角色操作 */\r\n    handleAuthRole: function(row) {\r\n      const userId = row.userId;\r\n      this.$router.push(\"/system/user-auth/role/\" + userId);\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.userId != undefined) {\r\n            updateUser(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addUser(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const userIds = row.userId || this.ids;\r\n      this.$modal.confirm('是否确认删除用户编号为\"' + userIds + '\"的数据项？').then(function() {\r\n        return delUser(userIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/user/export', {\r\n        ...this.queryParams\r\n      }, `user_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 导入按钮操作 */\r\n    handleImport() {\r\n      this.upload.title = \"用户导入\";\r\n      this.upload.open = true;\r\n    },\r\n    /** 下载模板操作 */\r\n    importTemplate() {\r\n      this.download('system/user/importTemplate', {\r\n      }, `user_template_${new Date().getTime()}.xlsx`)\r\n    },\r\n    // 文件上传中处理\r\n    handleFileUploadProgress(event, file, fileList) {\r\n      this.upload.isUploading = true;\r\n    },\r\n    // 文件上传成功处理\r\n    handleFileSuccess(response, file, fileList) {\r\n      this.upload.open = false;\r\n      this.upload.isUploading = false;\r\n      this.$refs.upload.clearFiles();\r\n      this.$alert(\"<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>\" + response.msg + \"</div>\", \"导入结果\", { dangerouslyUseHTMLString: true });\r\n      this.getList();\r\n    },\r\n    // 提交上传文件\r\n    submitFileForm() {\r\n      this.$refs.upload.submit();\r\n    }\r\n  }\r\n};\r\n</script>"]}]}