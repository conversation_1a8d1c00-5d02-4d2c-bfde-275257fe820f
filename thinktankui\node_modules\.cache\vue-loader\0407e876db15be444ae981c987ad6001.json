{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\DictTag\\index.vue?vue&type=template&id=7e7e1b87&scoped=true", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\DictTag\\index.vue", "mtime": 1749104047618}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749104421286}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}