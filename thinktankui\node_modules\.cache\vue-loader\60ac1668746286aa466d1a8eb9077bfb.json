{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\spread-analysis\\index.vue?vue&type=style&index=0&id=d2304d62&lang=scss&scoped=true", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\spread-analysis\\index.vue", "mtime": 1749104047642}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749104419119}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749104421224}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749104419872}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749104418479}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5hcHAtY29udGFpbmVyIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2YwZjJmNTsNCiAgbWluLWhlaWdodDogMTAwdmg7DQogIHBhZGRpbmc6IDA7DQp9DQoNCi50b3AtbmF2LWNvbnRhaW5lciB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTZlNmU2Ow0KDQogIC50b3AtbWVudSB7DQogICAgYm9yZGVyLWJvdHRvbTogbm9uZTsNCg0KICAgIC5lbC1tZW51LWl0ZW0gew0KICAgICAgaGVpZ2h0OiA1MHB4Ow0KICAgICAgbGluZS1oZWlnaHQ6IDUwcHg7DQogICAgICBmb250LXNpemU6IDE0cHg7DQoNCiAgICAgICYuaXMtYWN0aXZlLCAmLmFjdGl2ZS1tZW51IHsNCiAgICAgICAgY29sb3I6ICMxODkwZmY7DQogICAgICAgIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjMTg5MGZmOw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0KDQouY29udGVudC1jb250YWluZXIgew0KICBwYWRkaW5nOiAyMHB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KfQ0KDQouY2hhcnQtY2FyZCB7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCg0KICAuY2hhcnQtaGVhZGVyIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIHBhZGRpbmc6IDEycHggMjBweDsNCiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2ViZWVmNTsNCg0KICAgIC5jaGFydC10aXRsZSB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQoNCiAgICAgIC5ibHVlLWxpbmUgew0KICAgICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogICAgICAgIHdpZHRoOiAzcHg7DQogICAgICAgIGhlaWdodDogMTZweDsNCiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzE4OTBmZjsNCiAgICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7DQogICAgICB9DQogICAgfQ0KDQogICAgLmNoYXJ0LXRhYnMgew0KICAgICAgZGlzcGxheTogZmxleDsNCg0KICAgICAgLnRhYi1idXR0b24gew0KICAgICAgICBtYXJnaW4tcmlnaHQ6IDEwcHg7DQoNCiAgICAgICAgJi5hY3RpdmUgew0KICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICMxODkwZmY7DQogICAgICAgICAgYm9yZGVyLWNvbG9yOiAjMTg5MGZmOw0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KDQogICAgLmNoYXJ0LWFjdGlvbnMgew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQoNCiAgICAgIGkgew0KICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgIGNvbG9yOiAjOTA5Mzk5Ow0KICAgICAgICBtYXJnaW4tbGVmdDogMTVweDsNCiAgICAgICAgY3Vyc29yOiBwb2ludGVyOw0KDQogICAgICAgICY6aG92ZXIgew0KICAgICAgICAgIGNvbG9yOiAjMTg5MGZmOw0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLmNoYXJ0LWNvbnRlbnQgew0KICAgIHBhZGRpbmc6IDIwcHg7DQoNCiAgICAuY2hhcnQgew0KICAgICAgd2lkdGg6IDEwMCU7DQogICAgICBoZWlnaHQ6IDEwMCU7DQogICAgfQ0KICB9DQp9DQoNCi5jaGFydC1yb3cgew0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KfQ0KDQouY2hhcnQtbGVnZW5kIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC13cmFwOiB3cmFwOw0KICBtYXJnaW4tdG9wOiAxNXB4Ow0KDQogIC5sZWdlbmQtaXRlbSB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIG1hcmdpbi1yaWdodDogMTVweDsNCiAgICBtYXJnaW4tYm90dG9tOiA1cHg7DQoNCiAgICAubGVnZW5kLWNvbG9yIHsNCiAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgICAgIHdpZHRoOiAxMHB4Ow0KICAgICAgaGVpZ2h0OiAxMHB4Ow0KICAgICAgYm9yZGVyLXJhZGl1czogMnB4Ow0KICAgICAgbWFyZ2luLXJpZ2h0OiA1cHg7DQogICAgfQ0KDQogICAgLmxlZ2VuZC10ZXh0IHsNCiAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgIGNvbG9yOiAjNjA2MjY2Ow0KICAgIH0NCiAgfQ0KfQ0KDQouaG90c3BvdC1tYXJrZXJzIHsNCiAgcG9zaXRpb246IGFic29sdXRlOw0KICB0b3A6IDA7DQogIGxlZnQ6IDA7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDEwMCU7DQogIHBvaW50ZXItZXZlbnRzOiBub25lOw0KfQ0KDQovKiDlt6bkvqfmkJzntKLmlrnmoYjljLrln5/moLflvI8gKi8NCi5zaWRlYmFyIHsNCiAgd2lkdGg6IDI0MHB4Ow0KICBtYXJnaW4tcmlnaHQ6IDIwcHg7DQp9DQoNCi5zZWFyY2gtcGxhbiB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgYm94LXNoYWRvdzogMCAxcHggNHB4IHJnYmEoMCwgMCwgMCwgMC4xKTsNCn0NCg0KLnBsYW4tYWN0aW9ucyB7DQogIHBhZGRpbmc6IDE2cHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCg0KICAubmV3LXBsYW4tYnRuIHsNCiAgICBmbGV4OiAxOw0KICAgIGJhY2tncm91bmQtY29sb3I6ICNGRjlBMkY7DQogICAgYm9yZGVyLWNvbG9yOiAjRkY5QTJGOw0KDQogICAgJjpob3ZlciwgJjpmb2N1cyB7DQogICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjRjA4QzFFOw0KICAgICAgYm9yZGVyLWNvbG9yOiAjRjA4QzFFOw0KICAgIH0NCiAgfQ0KDQogIC5mb2xkZXItYnRuIHsNCiAgICBtYXJnaW4tbGVmdDogMTBweDsNCiAgICBwYWRkaW5nOiAxMHB4Ow0KICAgIGNvbG9yOiAjRkY5QTJGOw0KICAgIGJvcmRlci1jb2xvcjogI0RDREZFNjsNCiAgfQ0KfQ0KDQoucGxhbi1zZWFyY2ggew0KICBwYWRkaW5nOiAwIDE2cHggMTZweDsNCg0KICAuZWwtaW5wdXQgOjp2LWRlZXAgLmVsLWlucHV0X19pbm5lciB7DQogICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgIGhlaWdodDogMzJweDsNCiAgfQ0KfQ0KDQoucGxhbi1kaXZpZGVyIHsNCiAgaGVpZ2h0OiAxcHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNFQkVFRjU7DQogIG1hcmdpbjogMCAxNnB4Ow0KfQ0KDQoucGxhbi1saXN0IHsNCiAgcGFkZGluZzogMTZweDsNCn0NCg0KLnBsYW4taXRlbSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCg0KICAmLmFjdGl2ZSB7DQogICAgOjp2LWRlZXAgLmVsLXJhZGlvX19pbnB1dC5pcy1jaGVja2VkIC5lbC1yYWRpb19faW5uZXIgew0KICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzQwOUVGRjsNCiAgICAgIGJvcmRlci1jb2xvcjogIzQwOUVGRjsNCiAgICB9DQoNCiAgICA6OnYtZGVlcCAuZWwtcmFkaW9fX2lucHV0LmlzLWNoZWNrZWQgKyAuZWwtcmFkaW9fX2xhYmVsIHsNCiAgICAgIGNvbG9yOiAjMzAzMTMzOw0KICAgIH0NCiAgfQ0KDQogIGkgew0KICAgIGNvbG9yOiAjOTA5Mzk5Ow0KICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgfQ0KfQ0KDQouYnJhbmQtZGV0YWlsIHsNCiAgbWFyZ2luOiAtMTBweCAwIDE2cHggMjRweDsNCg0KICAuYnJhbmQtbmFtZSB7DQogICAgYmFja2dyb3VuZC1jb2xvcjogI0U2RjFGQzsNCiAgICBjb2xvcjogIzMwMzEzMzsNCiAgICBwYWRkaW5nOiA4cHggMTJweDsNCiAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgZm9udC1zaXplOiAxNHB4Ow0KICB9DQp9DQoNCi8qIOWPs+S+p+WGheWuueWMuuWfn+agt+W8jyAqLw0KLm1haW4tY29udGVudCB7DQogIGZsZXg6IDE7DQp9DQovKiDmoLflvI/lj6/ku6XmoLnmja7pnIDopoHov5vkuIDmraXnu4bljJYgKi8NCg0KLyog5pa55aSq562b6YCJ5Zmo5LiT5bGe5qC35byPICovDQouZmFuZ3RhaS1maWx0ZXItY29udGFpbmVyIC5maWx0ZXItaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBwYWRkaW5nLWJvdHRvbTogMTVweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlYmVlZjU7DQp9DQoNCi5mYW5ndGFpLWZpbHRlci1jb250YWluZXIgLmZpbHRlci10aXRsZSB7DQogIGZvbnQtc2l6ZTogMThweDsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQp9DQoNCi5mYW5ndGFpLWZpbHRlci1jb250YWluZXIgLmZpbHRlci1jb250ZW50IHsNCiAgcGFkZGluZy10b3A6IDE1cHg7DQp9DQoNCi5mYW5ndGFpLWZpbHRlci1jb250YWluZXIgLmVsLWZvcm0taXRlbSB7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQp9DQoNCi5mYW5ndGFpLWZpbHRlci1jb250YWluZXIgLmVsLWNoZWNrYm94LWdyb3VwIC5lbC1jaGVja2JveCB7DQogIG1hcmdpbi1yaWdodDogMjBweDsNCn0NCg0KLyog5L+h5oGv5oC76KeI5Y2h54mH5qC35byPICovDQouc3VtbWFyeS1jYXJkcyB7DQogIG1hcmdpbi10b3A6IDIwcHg7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQp9DQoNCi5zdW1tYXJ5LWNhcmQgLmVsLWNhcmRfX2JvZHkgew0KICBwYWRkaW5nOiAxNXB4ICFpbXBvcnRhbnQ7IC8qIOW8uuWItuS/ruaUuWVsZW1lbnQtdWnpu5jorqRwYWRkaW5nICovDQp9DQoNCi5zdW1tYXJ5LWNhcmQgZGl2OmZpcnN0LWNoaWxkIHsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBjb2xvcjogIzYwNjI2NjsNCn0NCg0KLnN1bW1hcnktY2FyZCBkaXY6bGFzdC1jaGlsZCB7DQogIGZvbnQtc2l6ZTogMjRweDsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogIG1hcmdpbi10b3A6IDVweDsNCn0NCg0KLyog6YCa55So5Zu+6KGo5Y2h54mH5qC35byP6LCD5pW0ICovDQouY2hhcnQtY2FyZCAuY2hhcnQtaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBwYWRkaW5nLWJvdHRvbTogMTBweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7IC8qIOe7n+S4gOi+ueahhuminOiJsiAqLw0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KfQ0KDQouY2hhcnQtY2FyZCAuY2hhcnQtdGl0bGUgew0KICBmb250LXNpemU6IDE2cHg7IC8qIOe7n+S4gOagh+mimOWtl+WPtyAqLw0KICBmb250LXdlaWdodDogNTAwOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KfQ0KDQouY2hhcnQtY2FyZCAuY2hhcnQtdGl0bGUgLmJsdWUtbGluZSB7DQogIGNvbG9yOiAjNDA5RUZGOyAvKiDnu5/kuIDlm77moIfpopzoibIgKi8NCiAgbWFyZ2luLXJpZ2h0OiA4cHg7DQogIGZvbnQtc2l6ZTogMThweDsNCn0NCg0KLmNoYXJ0LWNhcmQgLmNoYXJ0LWFjdGlvbnMgLmVsLWJ1dHRvbi1ncm91cCAuZWwtYnV0dG9uIHsNCiAgcGFkZGluZzogNXB4IDEwcHg7IC8qIOiwg+aVtOaMiemSrue7hOWGheaMiemSrnBhZGRpbmcgKi8NCn0NCg0KLmNoYXJ0LWNhcmQgLmNoYXJ0LWFjdGlvbnMgLmVsLWljb24tcmVmcmVzaCwNCi5jaGFydC1jYXJkIC5jaGFydC1hY3Rpb25zIC5lbC1pY29uLWRvd25sb2FkLA0KLmNoYXJ0LWNhcmQgLmNoYXJ0LWFjdGlvbnMgLmVsLWljb24tbW9yZSB7DQogIG1hcmdpbi1sZWZ0OiAxMHB4Ow0KICBjdXJzb3I6IHBvaW50ZXI7DQogIGZvbnQtc2l6ZTogMTZweDsgLyog57uf5LiA5pON5L2c5Zu+5qCH5aSn5bCPICovDQogIGNvbG9yOiAjNjA2MjY2OyAvKiDnu5/kuIDmk43kvZzlm77moIfpopzoibIgKi8NCn0NCg0KLmNoYXJ0LWNhcmQgLmNoYXJ0LWFjdGlvbnMgLmVsLWljb24tcmVmcmVzaDpob3ZlciwNCi5jaGFydC1jYXJkIC5jaGFydC1hY3Rpb25zIC5lbC1pY29uLWRvd25sb2FkOmhvdmVyLA0KLmNoYXJ0LWNhcmQgLmNoYXJ0LWFjdGlvbnMgLmVsLWljb24tbW9yZTpob3ZlciB7DQogIGNvbG9yOiAjNDA5RUZGOw0KfQ0KDQovKiDlubPlj7DmnaXmupDljaDmr5Tlm77nmoTlm77kvovmoLflvI8gKi8NCi5jaGFydC1sZWdlbmQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgbWFyZ2luLXRvcDogMTBweDsNCiAgZmxleC13cmFwOiB3cmFwOyAvKiDlhYHorrjlm77kvovmjaLooYwgKi8NCn0NCg0KLmxlZ2VuZC1pdGVtIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLXJpZ2h0OiAxNXB4Ow0KICBtYXJnaW4tYm90dG9tOiA1cHg7IC8qIOWinuWKoOW6lemDqOmXtOi3neS7peS+v+aNouihjCAqLw0KICBmb250LXNpemU6IDEycHg7IC8qIOiwg+aVtOWbvuS+i+aWh+Wtl+Wkp+WwjyAqLw0KfQ0KDQoubGVnZW5kLWNvbG9yIHsNCiAgd2lkdGg6IDEwcHg7DQogIGhlaWdodDogMTBweDsNCiAgYm9yZGVyLXJhZGl1czogNTAlOw0KICBtYXJnaW4tcmlnaHQ6IDVweDsNCiAgZGlzcGxheTogaW5saW5lLWJsb2NrOw0KfQ0KDQovKiDlt6bkvqfovrnmoI/mv4DmtLvnirbmgIEgKi8NCi5zaWRlYmFyIC5wbGFuLWl0ZW0uYWN0aXZlIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2VjZjVmZjsgLyogRWxlbWVudCBVSSDkuLvpopjok53nmoTmtYXoibIgKi8NCiAgY29sb3I6ICM0MDlFRkY7DQogIGJvcmRlci1yaWdodDogM3B4IHNvbGlkICM0MDlFRkY7DQp9DQoNCi5zaWRlYmFyIC5wbGFuLWl0ZW0uYWN0aXZlIC5lbC1yYWRpb19fbGFiZWwgew0KIGNvbG9yOiAjNDA5RUZGICFpbXBvcnRhbnQ7DQp9DQoNCi8qIOmhtumDqOWvvOiIqua/gOa0u+eKtuaAgSAqLw0KLnRvcC1tZW51IC5lbC1tZW51LWl0ZW0uaXMtYWN0aXZlIHsNCiAgYm9yZGVyLWJvdHRvbS1jb2xvcjogIzQwOUVGRiAhaW1wb3J0YW50Ow0KICBjb2xvcjogIzQwOUVGRiAhaW1wb3J0YW50Ow0KfQ0KDQoudG9wLW1lbnUgLmFjdGl2ZS1tZW51IHsNCiAgIGJvcmRlci1ib3R0b20tY29sb3I6ICM0MDlFRkYgIWltcG9ydGFudDsNCiAgIGNvbG9yOiAjNDA5RUZGICFpbXBvcnRhbnQ7DQogICBmb250LXdlaWdodDogYm9sZDsNCn0NCg0KLyog6LCD5pW05Zu+6KGo5a655Zmo56Gu5L+d5YW25pKR5ruh54i25a655ZmoICovDQouY2hhcnQgew0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiAxMDAlOw0KfQ0KDQovKiDnoa7kv53lhoXlrrnljLrln5/lnKjlsI/lsY/luZXkuIrkuZ/og73oia/lpb3mmL7npLogKi8NCi5jb250ZW50LWNvbnRhaW5lciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiByb3c7IC8qIOS/neaMgeW3puWPs+W4g+WxgCAqLw0KfQ0KDQouc2lkZWJhciB7DQogIHdpZHRoOiAyMjBweDsgLyog5Zu65a6a5bem5L6n5qCP5a695bqmICovDQogIG1pbi13aWR0aDogMjIwcHg7DQogIGJvcmRlci1yaWdodDogMXB4IHNvbGlkICNlNmU2ZTY7DQogIHBhZGRpbmctcmlnaHQ6IDE1cHg7DQogIG1hcmdpbi1yaWdodDogMTVweDsNCn0NCg0KLm1haW4tY29udGVudCB7DQogIGZsZXgtZ3JvdzogMTsgLyog5Y+z5L6n5YaF5a655Yy65Z+f5Y2g5o2u5Ymp5L2Z56m66Ze0ICovDQogIG92ZXJmbG93LXg6IGF1dG87IC8qIOWmguaenOWGheWuuei/h+Wuve+8jOWFgeiuuOawtOW5s+a7muWKqCAqLw0KfQ0KDQovKiDlk43lupTlvI/osIPmlbTvvJrlvZPlsY/luZXlrr3luqblsI/kuo43NjhweOaXtu+8jOWPr+S7peiAg+iZkeWwhuS+p+i+ueagj+makOiXj+aIluWPmOS4uuaKveWxieW8jyAqLw0KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7DQogIC5jb250ZW50LWNvbnRhaW5lciB7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgfQ0KICAuc2lkZWJhciB7DQogICAgd2lkdGg6IDEwMCU7DQogICAgYm9yZGVyLXJpZ2h0OiBub25lOw0KICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTZlNmU2Ow0KICAgIG1hcmdpbi1yaWdodDogMDsNCiAgICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KICAgIHBhZGRpbmctcmlnaHQ6IDA7DQogIH0NCiAgLmZhbmd0YWktZmlsdGVyLWNvbnRhaW5lciAuZWwtZm9ybS1pdGVtIC5lbC1jaGVja2JveC1ncm91cCwNCiAgLmZhbmd0YWktZmlsdGVyLWNvbnRhaW5lciAuZWwtZm9ybS1pdGVtIC5lbC1yYWRpby1ncm91cCB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBmbGV4LXdyYXA6IHdyYXA7DQogIH0NCiAgLmZhbmd0YWktZmlsdGVyLWNvbnRhaW5lciAuZWwtZm9ybS1pdGVtIC5lbC1jaGVja2JveCwNCiAgLmZhbmd0YWktZmlsdGVyLWNvbnRhaW5lciAuZWwtZm9ybS1pdGVtIC5lbC1yYWRpby1idXR0b24gew0KICAgIG1hcmdpbi1ib3R0b206IDVweDsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2xBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/spread-analysis", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 内容区域 -->\r\n    <div class=\"content-container\">\r\n      <!-- 左侧搜索方案区域 -->\r\n      <div class=\"sidebar\">\r\n        <div class=\"search-plan\">\r\n          <div class=\"plan-actions\">\r\n            <el-button type=\"primary\" class=\"new-plan-btn\">\r\n              <i class=\"el-icon-plus\"></i> 新建方案\r\n            </el-button>\r\n            <el-button class=\"folder-btn\">\r\n              <i class=\"el-icon-folder-opened\"></i>\r\n            </el-button>\r\n          </div>\r\n          <div class=\"plan-search\">\r\n            <el-input\r\n              placeholder=\"方案搜索\"\r\n              prefix-icon=\"el-icon-search\"\r\n              v-model=\"searchQuery\"\r\n              clearable\r\n              size=\"small\"\r\n            ></el-input>\r\n          </div>\r\n          <div class=\"plan-divider\"></div>\r\n          <div class=\"plan-list\">\r\n            <div class=\"plan-item\" :class=\"{ 'active': searchType === 'product' }\">\r\n              <el-radio v-model=\"searchType\" label=\"product\">竞品(1)</el-radio>\r\n              <i class=\"el-icon-arrow-down\"></i>\r\n            </div>\r\n            <div class=\"plan-item\" :class=\"{ 'active': searchType === 'brand' }\">\r\n              <el-radio v-model=\"searchType\" label=\"brand\">品牌(1)</el-radio>\r\n              <i class=\"el-icon-arrow-up\"></i>\r\n            </div>\r\n            <div class=\"brand-detail\" v-if=\"searchType === 'brand'\">\r\n              <div class=\"brand-name\">方太</div>\r\n            </div>\r\n            <div class=\"plan-item\">\r\n              <el-radio v-model=\"searchType\" label=\"person\">人物(0)</el-radio>\r\n              <i class=\"el-icon-arrow-down\"></i>\r\n            </div>\r\n            <div class=\"plan-item\">\r\n              <el-radio v-model=\"searchType\" label=\"organization\">机构(0)</el-radio>\r\n              <i class=\"el-icon-arrow-down\"></i>\r\n            </div>\r\n            <div class=\"plan-item\">\r\n              <el-radio v-model=\"searchType\" label=\"product-detail\">产品(0)</el-radio>\r\n              <i class=\"el-icon-arrow-down\"></i>\r\n            </div>\r\n            <div class=\"plan-item\">\r\n              <el-radio v-model=\"searchType\" label=\"event\">事件(0)</el-radio>\r\n              <i class=\"el-icon-arrow-down\"></i>\r\n            </div>\r\n            <div class=\"plan-item\">\r\n              <el-radio v-model=\"searchType\" label=\"other\">其他(0)</el-radio>\r\n              <i class=\"el-icon-arrow-down\"></i>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧图表区域 -->\r\n      <div class=\"main-content\">\r\n        <!-- 筛选条件区域 - 方太 -->\r\n        <div class=\"fangtai-filter-container\" v-if=\"searchType === 'brand' && brandName === '方太'\">\r\n          <div class=\"filter-header\" style=\"display: flex; justify-content: space-between; align-items: center; padding-bottom: 15px; border-bottom: 1px solid #ebeef5;\">\r\n            <span class=\"filter-title\" style=\"font-size: 18px; font-weight: bold;\">\r\n              {{ brandName }}\r\n              <i class=\"el-icon-edit\" style=\"margin-left: 8px; cursor: pointer; font-size: 16px;\"></i>\r\n              <i class=\"el-icon-s-tools\" style=\"margin-left: 8px; cursor: pointer; font-size: 16px;\"></i>\r\n            </span>\r\n            <!-- Add any top-right actions for this header if needed -->\r\n          </div>\r\n\r\n          <div class=\"filter-content\" style=\"padding-top: 15px;\">\r\n            <!-- 时间范围筛选 -->\r\n            <el-form size=\"small\" label-width=\"80px\">\r\n              <el-form-item label=\"时间范围:\">\r\n                <el-radio-group v-model=\"fangtaiTimeRange\" @change=\"handleFangtaiFilterChange\">\r\n                  <el-radio-button label=\"today\">今天</el-radio-button>\r\n                  <el-radio-button label=\"yesterday\">昨天</el-radio-button>\r\n                  <el-radio-button label=\"7d\">近七天</el-radio-button>\r\n                  <el-radio-button label=\"30d\">近30天</el-radio-button>\r\n                  <el-radio-button label=\"custom\">自定义</el-radio-button>\r\n                </el-radio-group>\r\n                <el-date-picker\r\n                  v-if=\"fangtaiTimeRange === 'custom'\"\r\n                  v-model=\"fangtaiCustomTimeRange\"\r\n                  type=\"daterange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  style=\"margin-left: 10px; width: 240px;\"\r\n                  @change=\"handleFangtaiFilterChange\"\r\n                ></el-date-picker>\r\n              </el-form-item>\r\n\r\n              <!-- 平台类型筛选 -->\r\n              <el-form-item label=\"平台类型:\">\r\n                <el-checkbox-group v-model=\"fangtaiSelectedPlatforms\" @change=\"handleFangtaiFilterChange\">\r\n                  <el-checkbox label=\"all_platform\">全部</el-checkbox>\r\n                  <el-checkbox label=\"web\">网页</el-checkbox>\r\n                  <el-checkbox label=\"weibo\">微博</el-checkbox>\r\n                  <el-checkbox label=\"toutiao\">头条号</el-checkbox>\r\n                  <el-checkbox label=\"app\">APP</el-checkbox>\r\n                  <el-checkbox label=\"video\">视频</el-checkbox>\r\n                  <el-checkbox label=\"sms\">短信</el-checkbox>\r\n                  <el-checkbox label=\"newspaper\">报刊</el-checkbox>\r\n                  <el-checkbox label=\"sohu\">搜狐</el-checkbox>\r\n                </el-checkbox-group>\r\n              </el-form-item>\r\n\r\n              <!-- 情感倾向筛选 -->\r\n              <el-form-item label=\"情感倾向:\">\r\n                <el-checkbox-group v-model=\"fangtaiSelectedSentiments\" @change=\"handleFangtaiFilterChange\">\r\n                  <el-checkbox label=\"all_sentiment\">全部</el-checkbox>\r\n                  <el-checkbox label=\"positive\">正面</el-checkbox>\r\n                  <el-checkbox label=\"neutral\">中性</el-checkbox>\r\n                  <el-checkbox label=\"negative\">负面</el-checkbox>\r\n                  <el-checkbox label=\"warning_target\">预警对象</el-checkbox>\r\n                  <el-checkbox label=\"sensitive_info\">敏感信息</el-checkbox>\r\n                  <el-checkbox label=\"official_letter\">正式发函</el-checkbox>\r\n                  <el-checkbox label=\"delete_complaint\">删帖投诉</el-checkbox>\r\n                </el-checkbox-group>\r\n              </el-form-item>\r\n\r\n              <el-form-item>\r\n                <el-button type=\"primary\" @click=\"applyFangtaiFilters\">应用</el-button>\r\n                <el-button @click=\"resetFangtaiFilters\">重置</el-button>\r\n              </el-form-item>\r\n            </el-form>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 信息总览卡片区域 -->\r\n        <el-row :gutter=\"20\" class=\"summary-cards\" style=\"margin-top: 20px; margin-bottom: 20px;\">\r\n          <el-col :xl=\"4\" :lg=\"4\" :md=\"8\" :sm=\"12\" :xs=\"24\" v-for=\"card in summaryCards\" :key=\"card.title\">\r\n            <el-card shadow=\"hover\" class=\"summary-card\" :body-style=\"{ padding: '15px' }\">\r\n              <div style=\"font-size: 14px; color: #606266;\">{{ card.title }}</div>\r\n              <div style=\"font-size: 24px; font-weight: bold; margin-top: 5px;\" :style=\"{ color: card.color }\">{{ card.value }}</div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 第一行图表 - 数据汇总图 -->\r\n        <el-card class=\"chart-card\">\r\n          <div class=\"chart-header\">\r\n            <div class=\"chart-title\">\r\n              <i class=\"el-icon-data-line blue-line\"></i>\r\n              <span>数据汇总</span>\r\n            </div>\r\n            <div class=\"chart-actions\">\r\n              <el-button-group size=\"mini\">\r\n                <el-button type=\"primary\" plain>数据趋势</el-button>\r\n                <el-button plain>7天监测图</el-button>\r\n              </el-button-group>\r\n              <i class=\"el-icon-refresh\" style=\"margin-left:10px; cursor:pointer;\"></i>\r\n              <i class=\"el-icon-download\" style=\"margin-left:10px; cursor:pointer;\"></i>\r\n            </div>\r\n          </div>\r\n          <div class=\"chart-content\" style=\"height: 300px;\">\r\n            <!-- 这里放置传播热度趋势图表 -->\r\n            <div ref=\"trendChart\" class=\"chart\"></div>\r\n            <!-- 热点标记 -->\r\n            <div class=\"hotspot-markers\">\r\n              <!-- 热点标记内容将由JS动态生成 -->\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n\r\n        <!-- 关键热搜词云图 -->\r\n        <el-card class=\"chart-card\" style=\"margin-top: 20px;\" v-if=\"searchType === 'brand' && brandName === '方太'\">\r\n          <div class=\"chart-header\">\r\n            <div class=\"chart-title\">\r\n              <i class=\"el-icon-magic-stick blue-line\"></i> <!-- Consider a more relevant icon like el-icon-collection-tag or el-icon-price-tag -->\r\n              <span>关键热搜词云</span>\r\n            </div>\r\n            <div class=\"chart-actions\">\r\n              <i class=\"el-icon-refresh\" style=\"cursor:pointer;\"></i>\r\n              <i class=\"el-icon-download\" style=\"margin-left:10px; cursor:pointer;\"></i>\r\n            </div>\r\n          </div>\r\n          <div class=\"chart-content\" style=\"height: 300px;\">\r\n            <div ref=\"keywordCloudChart\" class=\"chart\"></div>\r\n          </div>\r\n        </el-card>\r\n\r\n        <!-- 第二行图表 -->\r\n        <el-row :gutter=\"20\" class=\"chart-row\">\r\n          <!-- 平台来源占比 -->\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"chart-card\">\r\n              <div class=\"chart-header\">\r\n                <div class=\"chart-title\">\r\n                  <i class=\"el-icon-pie-chart blue-line\"></i>\r\n                  <span>平台声量占比</span>\r\n                </div>\r\n                <div class=\"chart-actions\">\r\n                  <i class=\"el-icon-refresh\" style=\"cursor:pointer;\"></i>\r\n                  <i class=\"el-icon-download\" style=\"margin-left:10px; cursor:pointer;\"></i>\r\n                </div>\r\n              </div>\r\n              <div class=\"chart-content\" style=\"height: 250px;\">\r\n                <!-- 这里放置平台来源占比图表 -->\r\n                <div ref=\"sourceChart\" class=\"chart\"></div>\r\n                <!-- 图例 -->\r\n                <div class=\"chart-legend\">\r\n                  <div class=\"legend-item\">\r\n                    <span class=\"legend-color\" style=\"background-color: #4CD384;\"></span>\r\n                    <span class=\"legend-text\">微博</span>\r\n                  </div>\r\n                  <div class=\"legend-item\">\r\n                    <span class=\"legend-color\" style=\"background-color: #36A3F7;\"></span>\r\n                    <span class=\"legend-text\">微信</span>\r\n                  </div>\r\n                  <div class=\"legend-item\">\r\n                    <span class=\"legend-color\" style=\"background-color: #F56C6C;\"></span>\r\n                    <span class=\"legend-text\">APP</span>\r\n                  </div>\r\n                  <!-- 更多图例项 -->\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n\r\n          <!-- 传播热度 -->\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"chart-card\">\r\n              <div class=\"chart-header\">\r\n                <div class=\"chart-title\">\r\n                  <i class=\"el-icon-data-analysis blue-line\"></i>\r\n                  <span>情感画像</span>\r\n                </div>\r\n                <div class=\"chart-actions\">\r\n                  <i class=\"el-icon-refresh\"></i>\r\n                  <i class=\"el-icon-more\"></i>\r\n                </div>\r\n              </div>\r\n              <div class=\"chart-content\" style=\"height: 250px;\">\r\n                <!-- 这里放置传播热度图表 -->\r\n                <div ref=\"heatChart\" class=\"chart\"></div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 第三行图表 -->\r\n        <el-row :gutter=\"20\" class=\"chart-row\">\r\n          <!-- 舆情事件分布 -->\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"chart-card\">\r\n              <div class=\"chart-header\">\r\n                <div class=\"chart-title\">\r\n                  <i class=\"el-icon-s-data blue-line\"></i>\r\n                  <span>媒体类型分布</span>\r\n                </div>\r\n                <div class=\"chart-actions\">\r\n                  <i class=\"el-icon-refresh\"></i>\r\n                  <i class=\"el-icon-more\"></i>\r\n                </div>\r\n              </div>\r\n              <div class=\"chart-content\" style=\"height: 250px;\">\r\n                <!-- 这里放置舆情事件分布图表 -->\r\n                <div ref=\"eventChart\" class=\"chart\"></div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n\r\n          <!-- 舆情来源占比 -->\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"chart-card\">\r\n              <div class=\"chart-header\">\r\n                <div class=\"chart-title\">\r\n                  <i class=\"el-icon-pie-chart blue-line\"></i>\r\n                  <span>媒体声量占比</span>\r\n                </div>\r\n                <div class=\"chart-actions\">\r\n                  <i class=\"el-icon-refresh\"></i>\r\n                  <i class=\"el-icon-more\"></i>\r\n                </div>\r\n              </div>\r\n              <div class=\"chart-content\" style=\"height: 250px;\">\r\n                <!-- 这里放置舆情来源占比图表 -->\r\n                <div ref=\"sourceDistChart\" class=\"chart\"></div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nimport 'echarts-wordcloud'; // 引入词云图\r\n\r\nexport default {\r\n  name: 'SpreadAnalysis',\r\n  data() {\r\n    return {\r\n      originalTopNav: undefined, // 存储原始的topNav状态\r\n      // 方太筛选相关数据\r\n      fangtaiTimeRange: 'today',\r\n      fangtaiCustomTimeRange: [],\r\n      fangtaiSelectedPlatforms: ['all_platform'],\r\n      fangtaiSelectedSentiments: ['all_sentiment'],\r\n      summaryCards: [\r\n        { title: '信息总量', value: '4653', color: '#409EFF' },\r\n        { title: '正面声量', value: '58', color: '#67C23A' },\r\n        { title: '中性声量', value: '4583', color: '#E6A23C' },\r\n        { title: '负面声量', value: '12', color: '#F56C6C' },\r\n        { title: '媒体总数', value: '1853', color: '#909399' }\r\n      ],\r\n      activeTab: 'spread',\r\n      searchType: 'brand',\r\n      brandName: '方太',\r\n      searchQuery: '',\r\n      charts: {\r\n        trendChart: null,\r\n        sourceChart: null,\r\n        heatChart: null,\r\n        eventChart: null,\r\n        sourceDistChart: null,\r\n        keywordCloudChart: null\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    // 隐藏顶部导航栏\r\n    this.originalTopNav = this.$store.state.settings.topNav\r\n    this.$store.dispatch('settings/changeSetting', {\r\n      key: 'topNav',\r\n      value: false\r\n    })\r\n\r\n    this.initCharts()\r\n  },\r\n  watch: {\r\n    searchType(newVal) {\r\n      this.$nextTick(() => {\r\n        this.resizeCharts();\r\n        if (newVal === 'brand' && this.brandName === '方太') {\r\n          // Potentially re-render or update charts specific to '方太' view if needed\r\n          if (!this.charts.keywordCloudChart) {\r\n             this.charts.keywordCloudChart = echarts.init(this.$refs.keywordCloudChart);\r\n             this.renderKeywordCloudChart();\r\n          } else {\r\n            this.renderKeywordCloudChart(); // Re-render if already initialized\r\n          }\r\n        }\r\n      });\r\n    }\r\n  },\r\n  methods: {\r\n    handleTabSelect(key) {\r\n      this.activeTab = key\r\n    },\r\n    handleFangtaiFilterChange() {\r\n      // 处理方太筛选条件变化，可以触发数据重新加载或图表更新\r\n      console.log('方太筛选条件变化:', {\r\n        timeRange: this.fangtaiTimeRange,\r\n        customTime: this.fangtaiCustomTimeRange,\r\n        platforms: this.fangtaiSelectedPlatforms,\r\n        sentiments: this.fangtaiSelectedSentiments\r\n      });\r\n      // 触发图表更新逻辑\r\n      this.applyFangtaiFilters();\r\n    },\r\n    applyFangtaiFilters() {\r\n      // 应用方太筛选条件，实际应用中会调用API获取数据并更新图表\r\n      console.log('应用方太筛选');\r\n      // 示例：重新渲染所有图表\r\n      this.renderTrendChart(); // 注意：图一中此图表为“数据汇总”\r\n      this.renderSourceChart(); // 注意：图一中此图表为“平台声量占比”\r\n      this.renderHeatChart(); // 注意：图一中此图表为“情感画像”\r\n      this.renderEventChart(); // 注意：图一中此图表为“媒体类型分布”\r\n      this.renderSourceDistChart(); // 注意：图一中此图表为“媒体声量占比”\r\n      if (this.searchType === 'brand' && this.brandName === '方太' && this.$refs.keywordCloudChart) {\r\n        if (!this.charts.keywordCloudChart) {\r\n            this.charts.keywordCloudChart = echarts.init(this.$refs.keywordCloudChart);\r\n        }\r\n        this.renderKeywordCloudChart();\r\n      }\r\n    },\r\n    resetFangtaiFilters() {\r\n      this.fangtaiTimeRange = 'today';\r\n      this.fangtaiCustomTimeRange = [];\r\n      this.fangtaiSelectedPlatforms = ['all_platform'];\r\n      this.fangtaiSelectedSentiments = ['all_sentiment'];\r\n      console.log('重置方太筛选');\r\n      this.applyFangtaiFilters();\r\n    },\r\n    initCharts() {\r\n      this.$nextTick(() => {\r\n        // 初始化传播热度趋势图\r\n        this.charts.trendChart = echarts.init(this.$refs.trendChart)\r\n        this.renderTrendChart()\r\n\r\n        // 初始化平台来源占比图\r\n        this.charts.sourceChart = echarts.init(this.$refs.sourceChart)\r\n        this.renderSourceChart()\r\n\r\n        // 初始化传播热度图\r\n        this.charts.heatChart = echarts.init(this.$refs.heatChart)\r\n        this.renderHeatChart()\r\n\r\n        // 初始化舆情事件分布图\r\n        this.charts.eventChart = echarts.init(this.$refs.eventChart)\r\n        this.renderEventChart()\r\n\r\n        // 初始化舆情来源占比图\r\n        this.charts.sourceDistChart = echarts.init(this.$refs.sourceDistChart)\r\n        this.renderSourceDistChart()\r\n\r\n        // 如果初始加载时就是方太品牌，则初始化词云图\r\n        if (this.searchType === 'brand' && this.brandName === '方太' && this.$refs.keywordCloudChart) {\r\n          this.charts.keywordCloudChart = echarts.init(this.$refs.keywordCloudChart)\r\n          this.renderKeywordCloudChart()\r\n        }\r\n\r\n        // 监听窗口大小变化，调整图表大小\r\n        window.addEventListener('resize', this.resizeCharts)\r\n      })\r\n    },\r\n    renderKeywordCloudChart() {\r\n      if (!this.charts.keywordCloudChart) return;\r\n      const option = {\r\n        tooltip: {\r\n          show: true\r\n        },\r\n        series: [{\r\n          type: 'wordCloud',\r\n          gridSize: 2,\r\n          sizeRange: [12, 50],\r\n          rotationRange: [-90, 90],\r\n          shape: 'pentagon',\r\n          width: '90%',\r\n          height: '90%',\r\n          textStyle: {\r\n            fontFamily: 'sans-serif',\r\n            fontWeight: 'bold',\r\n            color: function () {\r\n              return 'rgb(' + [\r\n                Math.round(Math.random() * 160),\r\n                Math.round(Math.random() * 160),\r\n                Math.round(Math.random() * 160)\r\n              ].join(',') + ')';\r\n            }\r\n          },\r\n          emphasis: {\r\n            focus: 'self',\r\n            textStyle: {\r\n              textShadowBlur: 10,\r\n              textShadowColor: '#333'\r\n            }\r\n          },\r\n          data: [\r\n            { name: '方太产品', value: 10000 },\r\n            { name: '厨房电器', value: 6181 },\r\n            { name: '油烟机', value: 4386 },\r\n            { name: '洗碗机', value: 4055 },\r\n            { name: '集成灶', value: 2467 },\r\n            { name: '售后服务', value: 2244 },\r\n            { name: '智能厨电', value: 1898 },\r\n            { name: '高端厨电', value: 1484 },\r\n            { name: '用户体验', value: 1112 },\r\n            { name: '新品发布', value: 965 },\r\n            { name: '质量问题', value: 847 },\r\n            { name: '价格优惠', value: 582 },\r\n            { name: '安装服务', value: 555 },\r\n            { name: '品牌活动', value: 550 },\r\n            { name: '线上购买', value: 462 },\r\n            { name: '线下门店', value: 366 },\r\n            { name: '厨电科技', value: 360 },\r\n            { name: '环保理念', value: 282 },\r\n            { name: '设计美学', value: 273 },\r\n            { name: '客户评价', value: 265 }\r\n          ]\r\n        }]\r\n      };\r\n      this.charts.keywordCloudChart.setOption(option);\r\n    },\r\n    resizeCharts() {\r\n      Object.values(this.charts).forEach(chart => {\r\n        chart && chart.resize()\r\n      })\r\n    },\r\n    renderKeywordCloudChart() {\r\n      if (!this.charts.keywordCloudChart) return;\r\n      const option = {\r\n        tooltip: {\r\n          show: true\r\n        },\r\n        series: [{\r\n          type: 'wordCloud',\r\n          gridSize: 2,\r\n          sizeRange: [12, 50],\r\n          rotationRange: [-90, 90],\r\n          shape: 'pentagon',\r\n          width: '90%',\r\n          height: '90%',\r\n          textStyle: {\r\n            fontFamily: 'sans-serif',\r\n            fontWeight: 'bold',\r\n            color: function () {\r\n              return 'rgb(' + [\r\n                Math.round(Math.random() * 160),\r\n                Math.round(Math.random() * 160),\r\n                Math.round(Math.random() * 160)\r\n              ].join(',') + ')';\r\n            }\r\n          },\r\n          emphasis: {\r\n            focus: 'self',\r\n            textStyle: {\r\n              textShadowBlur: 10,\r\n              textShadowColor: '#333'\r\n            }\r\n          },\r\n          data: [\r\n            { name: '方太产品', value: 10000 },\r\n            { name: '厨房电器', value: 6181 },\r\n            { name: '油烟机', value: 4386 },\r\n            { name: '洗碗机', value: 4055 },\r\n            { name: '集成灶', value: 2467 },\r\n            { name: '售后服务', value: 2244 },\r\n            { name: '智能厨电', value: 1898 },\r\n            { name: '高端厨电', value: 1484 },\r\n            { name: '用户体验', value: 1112 },\r\n            { name: '新品发布', value: 965 },\r\n            { name: '质量问题', value: 847 },\r\n            { name: '价格优惠', value: 582 },\r\n            { name: '安装服务', value: 555 },\r\n            { name: '品牌活动', value: 550 },\r\n            { name: '线上购买', value: 462 },\r\n            { name: '线下门店', value: 366 },\r\n            { name: '厨电科技', value: 360 },\r\n            { name: '环保理念', value: 282 },\r\n            { name: '设计美学', value: 273 },\r\n            { name: '客户评价', value: 265 }\r\n          ]\r\n        }]\r\n      };\r\n      this.charts.keywordCloudChart.setOption(option);\r\n    },\r\n    renderTrendChart() {\r\n      // 传播热度趋势图配置\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis'\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: ['00:00', '01:00', '02:00', '03:00', '04:00', '05:00', '06:00', '07:00', '08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00']\r\n        },\r\n        yAxis: {\r\n          type: 'value'\r\n        },\r\n        series: [{\r\n          name: '传播热度',\r\n          type: 'line',\r\n          smooth: true,\r\n          lineStyle: {\r\n            color: '#67C23A',\r\n            width: 2\r\n          },\r\n          areaStyle: {\r\n            color: {\r\n              type: 'linear',\r\n              x: 0,\r\n              y: 0,\r\n              x2: 0,\r\n              y2: 1,\r\n              colorStops: [{\r\n                offset: 0,\r\n                color: 'rgba(103, 194, 58, 0.3)'\r\n              }, {\r\n                offset: 1,\r\n                color: 'rgba(103, 194, 58, 0.1)'\r\n              }]\r\n            }\r\n          },\r\n          data: [100, 120, 110, 125, 130, 150, 160, 170, 180, 190, 210, 230, 210, 200, 190, 180, 200, 210, 220, 210, 200]\r\n        }]\r\n      }\r\n      this.charts.trendChart.setOption(option)\r\n    },\r\n    renderSourceChart() {\r\n      // 平台来源占比图配置\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\r\n        },\r\n        series: [{\r\n          name: '平台来源',\r\n          type: 'pie',\r\n          radius: ['40%', '70%'],\r\n          avoidLabelOverlap: false,\r\n          label: {\r\n            show: true,\r\n            position: 'outside',\r\n            formatter: '{b}: {d}%'\r\n          },\r\n          emphasis: {\r\n            label: {\r\n              show: true,\r\n              fontSize: '16',\r\n              fontWeight: 'bold'\r\n            }\r\n          },\r\n          labelLine: {\r\n            show: true\r\n          },\r\n          data: [\r\n            { value: 31.26, name: '微博', itemStyle: { color: '#4CD384' } },\r\n            { value: 31.11, name: '微信', itemStyle: { color: '#36A3F7' } },\r\n            { value: 16.32, name: 'APP', itemStyle: { color: '#F56C6C' } },\r\n            { value: 10.47, name: '网站', itemStyle: { color: '#E6A23C' } },\r\n            { value: 5.47, name: '论坛', itemStyle: { color: '#909399' } },\r\n            { value: 3.29, name: '报刊', itemStyle: { color: '#9B55FF' } },\r\n            { value: 2.08, name: '其他', itemStyle: { color: '#FF9A2F' } }\r\n          ]\r\n        }]\r\n      }\r\n      this.charts.sourceChart.setOption(option)\r\n    },\r\n    renderHeatChart() {\r\n      // 传播热度图配置\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis'\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: ['00:00', '02:00', '04:00', '06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00']\r\n        },\r\n        yAxis: {\r\n          type: 'value'\r\n        },\r\n        legend: {\r\n          data: ['正面声量', '中性声量', '负面声量'],\r\n          bottom: 10\r\n        },\r\n        series: [\r\n          {\r\n            name: '正面声量',\r\n            type: 'line',\r\n            smooth: true,\r\n            lineStyle: { color: '#67C23A' },\r\n            areaStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: 'rgba(103, 194, 58, 0.3)' }, { offset: 1, color: 'rgba(103, 194, 58, 0.1)' }]) },\r\n            data: [10, 15, 12, 18, 22, 25, 30, 28, 35, 40, 38, 42, 45, 50, 48, 52, 55, 58, 60, 56, 50]\r\n          },\r\n          {\r\n            name: '中性声量',\r\n            type: 'line',\r\n            smooth: true,\r\n            lineStyle: { color: '#E6A23C' },\r\n            areaStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: 'rgba(230, 162, 60, 0.3)' }, { offset: 1, color: 'rgba(230, 162, 60, 0.1)' }]) },\r\n            data: [100, 120, 110, 125, 130, 150, 160, 170, 180, 190, 210, 230, 210, 200, 190, 180, 200, 210, 220, 210, 200]\r\n          },\r\n          {\r\n            name: '负面声量',\r\n            type: 'line',\r\n            smooth: true,\r\n            lineStyle: { color: '#F56C6C' },\r\n            areaStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: 'rgba(245, 108, 108, 0.3)' }, { offset: 1, color: 'rgba(245, 108, 108, 0.1)' }]) },\r\n            data: [5, 8, 6, 10, 12, 9, 11, 14, 10, 13, 15, 12, 16, 18, 15, 17, 20, 16, 19, 22, 18]\r\n          }\r\n        ]\r\n      }\r\n      this.charts.heatChart.setOption(option)\r\n    },\r\n    renderEventChart() {\r\n      // 舆情事件分布图配置\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis'\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: ['新闻', 'APP', '论坛', '博客', '微博', '视频', '微信']\r\n        },\r\n        yAxis: {\r\n          type: 'value'\r\n        },\r\n        series: [{\r\n          name: '事件数量',\r\n          type: 'bar',\r\n          barWidth: '60%',\r\n          data: [320, 280, 220, 180, 150, 120, 100]\r\n        }]\r\n      }\r\n      this.charts.eventChart.setOption(option)\r\n    },\r\n    renderSourceDistChart() {\r\n      // 舆情来源占比图配置\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross',\r\n            label: {\r\n              backgroundColor: '#6a7985'\r\n            }\r\n          }\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: ['00:00', '02:00', '04:00', '06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00']\r\n        },\r\n        yAxis: {\r\n          type: 'value'\r\n        },\r\n        series: [{\r\n          name: '媒体声量占比',\r\n          type: 'pie',\r\n          radius: ['40%', '70%'],\r\n          avoidLabelOverlap: false,\r\n          label: {\r\n            show: true,\r\n            position: 'outside',\r\n            formatter: '{b}: {d}%'\r\n          },\r\n          emphasis: {\r\n            label: {\r\n              show: true,\r\n              fontSize: '16',\r\n              fontWeight: 'bold'\r\n            }\r\n          },\r\n          labelLine: {\r\n            show: true\r\n          },\r\n          data: [\r\n            { value: 35, name: '新闻客户端', itemStyle: { color: '#5470C6'} },\r\n            { value: 25, name: '社交媒体', itemStyle: { color: '#91CC75'} },\r\n            { value: 15, name: '视频平台', itemStyle: { color: '#FAC858'} },\r\n            { value: 10, name: '传统媒体网站', itemStyle: { color: '#EE6666'} },\r\n            { value: 8, name: '论坛博客', itemStyle: { color: '#73C0DE'} },\r\n            { value: 7, name: '其他', itemStyle: { color: '#3BA272'} }\r\n          ]\r\n        }]\r\n      }\r\n      this.charts.sourceDistChart.setOption(option)\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    // 恢复顶部导航栏设置\r\n    if (this.originalTopNav !== undefined) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'topNav',\r\n        value: this.originalTopNav\r\n      })\r\n    }\r\n\r\n    // 移除窗口大小变化监听\r\n    window.removeEventListener('resize', this.resizeCharts)\r\n    // 销毁图表实例\r\n    Object.values(this.charts).forEach(chart => {\r\n      chart && chart.dispose()\r\n    })\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background-color: #f0f2f5;\r\n  min-height: 100vh;\r\n  padding: 0;\r\n}\r\n\r\n.top-nav-container {\r\n  background-color: #fff;\r\n  border-bottom: 1px solid #e6e6e6;\r\n\r\n  .top-menu {\r\n    border-bottom: none;\r\n\r\n    .el-menu-item {\r\n      height: 50px;\r\n      line-height: 50px;\r\n      font-size: 14px;\r\n\r\n      &.is-active, &.active-menu {\r\n        color: #1890ff;\r\n        border-bottom: 2px solid #1890ff;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.content-container {\r\n  padding: 20px;\r\n  display: flex;\r\n}\r\n\r\n.chart-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 4px;\r\n\r\n  .chart-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 12px 20px;\r\n    border-bottom: 1px solid #ebeef5;\r\n\r\n    .chart-title {\r\n      display: flex;\r\n      align-items: center;\r\n      font-size: 16px;\r\n      font-weight: 500;\r\n\r\n      .blue-line {\r\n        display: inline-block;\r\n        width: 3px;\r\n        height: 16px;\r\n        background-color: #1890ff;\r\n        margin-right: 8px;\r\n      }\r\n    }\r\n\r\n    .chart-tabs {\r\n      display: flex;\r\n\r\n      .tab-button {\r\n        margin-right: 10px;\r\n\r\n        &.active {\r\n          background-color: #1890ff;\r\n          border-color: #1890ff;\r\n        }\r\n      }\r\n    }\r\n\r\n    .chart-actions {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      i {\r\n        font-size: 16px;\r\n        color: #909399;\r\n        margin-left: 15px;\r\n        cursor: pointer;\r\n\r\n        &:hover {\r\n          color: #1890ff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .chart-content {\r\n    padding: 20px;\r\n\r\n    .chart {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n}\r\n\r\n.chart-row {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.chart-legend {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin-top: 15px;\r\n\r\n  .legend-item {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-right: 15px;\r\n    margin-bottom: 5px;\r\n\r\n    .legend-color {\r\n      display: inline-block;\r\n      width: 10px;\r\n      height: 10px;\r\n      border-radius: 2px;\r\n      margin-right: 5px;\r\n    }\r\n\r\n    .legend-text {\r\n      font-size: 12px;\r\n      color: #606266;\r\n    }\r\n  }\r\n}\r\n\r\n.hotspot-markers {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  pointer-events: none;\r\n}\r\n\r\n/* 左侧搜索方案区域样式 */\r\n.sidebar {\r\n  width: 240px;\r\n  margin-right: 20px;\r\n}\r\n\r\n.search-plan {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.plan-actions {\r\n  padding: 16px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n\r\n  .new-plan-btn {\r\n    flex: 1;\r\n    background-color: #FF9A2F;\r\n    border-color: #FF9A2F;\r\n\r\n    &:hover, &:focus {\r\n      background-color: #F08C1E;\r\n      border-color: #F08C1E;\r\n    }\r\n  }\r\n\r\n  .folder-btn {\r\n    margin-left: 10px;\r\n    padding: 10px;\r\n    color: #FF9A2F;\r\n    border-color: #DCDFE6;\r\n  }\r\n}\r\n\r\n.plan-search {\r\n  padding: 0 16px 16px;\r\n\r\n  .el-input ::v-deep .el-input__inner {\r\n    border-radius: 4px;\r\n    height: 32px;\r\n  }\r\n}\r\n\r\n.plan-divider {\r\n  height: 1px;\r\n  background-color: #EBEEF5;\r\n  margin: 0 16px;\r\n}\r\n\r\n.plan-list {\r\n  padding: 16px;\r\n}\r\n\r\n.plan-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n\r\n  &.active {\r\n    ::v-deep .el-radio__input.is-checked .el-radio__inner {\r\n      background-color: #409EFF;\r\n      border-color: #409EFF;\r\n    }\r\n\r\n    ::v-deep .el-radio__input.is-checked + .el-radio__label {\r\n      color: #303133;\r\n    }\r\n  }\r\n\r\n  i {\r\n    color: #909399;\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n.brand-detail {\r\n  margin: -10px 0 16px 24px;\r\n\r\n  .brand-name {\r\n    background-color: #E6F1FC;\r\n    color: #303133;\r\n    padding: 8px 12px;\r\n    border-radius: 4px;\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n/* 右侧内容区域样式 */\r\n.main-content {\r\n  flex: 1;\r\n}\r\n/* 样式可以根据需要进一步细化 */\r\n\r\n/* 方太筛选器专属样式 */\r\n.fangtai-filter-container .filter-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-bottom: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.fangtai-filter-container .filter-title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.fangtai-filter-container .filter-content {\r\n  padding-top: 15px;\r\n}\r\n\r\n.fangtai-filter-container .el-form-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.fangtai-filter-container .el-checkbox-group .el-checkbox {\r\n  margin-right: 20px;\r\n}\r\n\r\n/* 信息总览卡片样式 */\r\n.summary-cards {\r\n  margin-top: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.summary-card .el-card__body {\r\n  padding: 15px !important; /* 强制修改element-ui默认padding */\r\n}\r\n\r\n.summary-card div:first-child {\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n.summary-card div:last-child {\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  margin-top: 5px;\r\n}\r\n\r\n/* 通用图表卡片样式调整 */\r\n.chart-card .chart-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #f0f0f0; /* 统一边框颜色 */\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.chart-card .chart-title {\r\n  font-size: 16px; /* 统一标题字号 */\r\n  font-weight: 500;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.chart-card .chart-title .blue-line {\r\n  color: #409EFF; /* 统一图标颜色 */\r\n  margin-right: 8px;\r\n  font-size: 18px;\r\n}\r\n\r\n.chart-card .chart-actions .el-button-group .el-button {\r\n  padding: 5px 10px; /* 调整按钮组内按钮padding */\r\n}\r\n\r\n.chart-card .chart-actions .el-icon-refresh,\r\n.chart-card .chart-actions .el-icon-download,\r\n.chart-card .chart-actions .el-icon-more {\r\n  margin-left: 10px;\r\n  cursor: pointer;\r\n  font-size: 16px; /* 统一操作图标大小 */\r\n  color: #606266; /* 统一操作图标颜色 */\r\n}\r\n\r\n.chart-card .chart-actions .el-icon-refresh:hover,\r\n.chart-card .chart-actions .el-icon-download:hover,\r\n.chart-card .chart-actions .el-icon-more:hover {\r\n  color: #409EFF;\r\n}\r\n\r\n/* 平台来源占比图的图例样式 */\r\n.chart-legend {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 10px;\r\n  flex-wrap: wrap; /* 允许图例换行 */\r\n}\r\n\r\n.legend-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-right: 15px;\r\n  margin-bottom: 5px; /* 增加底部间距以便换行 */\r\n  font-size: 12px; /* 调整图例文字大小 */\r\n}\r\n\r\n.legend-color {\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 50%;\r\n  margin-right: 5px;\r\n  display: inline-block;\r\n}\r\n\r\n/* 左侧边栏激活状态 */\r\n.sidebar .plan-item.active {\r\n  background-color: #ecf5ff; /* Element UI 主题蓝的浅色 */\r\n  color: #409EFF;\r\n  border-right: 3px solid #409EFF;\r\n}\r\n\r\n.sidebar .plan-item.active .el-radio__label {\r\n color: #409EFF !important;\r\n}\r\n\r\n/* 顶部导航激活状态 */\r\n.top-menu .el-menu-item.is-active {\r\n  border-bottom-color: #409EFF !important;\r\n  color: #409EFF !important;\r\n}\r\n\r\n.top-menu .active-menu {\r\n   border-bottom-color: #409EFF !important;\r\n   color: #409EFF !important;\r\n   font-weight: bold;\r\n}\r\n\r\n/* 调整图表容器确保其撑满父容器 */\r\n.chart {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n/* 确保内容区域在小屏幕上也能良好显示 */\r\n.content-container {\r\n  display: flex;\r\n  flex-direction: row; /* 保持左右布局 */\r\n}\r\n\r\n.sidebar {\r\n  width: 220px; /* 固定左侧栏宽度 */\r\n  min-width: 220px;\r\n  border-right: 1px solid #e6e6e6;\r\n  padding-right: 15px;\r\n  margin-right: 15px;\r\n}\r\n\r\n.main-content {\r\n  flex-grow: 1; /* 右侧内容区域占据剩余空间 */\r\n  overflow-x: auto; /* 如果内容过宽，允许水平滚动 */\r\n}\r\n\r\n/* 响应式调整：当屏幕宽度小于768px时，可以考虑将侧边栏隐藏或变为抽屉式 */\r\n@media (max-width: 768px) {\r\n  .content-container {\r\n    flex-direction: column;\r\n  }\r\n  .sidebar {\r\n    width: 100%;\r\n    border-right: none;\r\n    border-bottom: 1px solid #e6e6e6;\r\n    margin-right: 0;\r\n    margin-bottom: 15px;\r\n    padding-right: 0;\r\n  }\r\n  .fangtai-filter-container .el-form-item .el-checkbox-group,\r\n  .fangtai-filter-container .el-form-item .el-radio-group {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n  }\r\n  .fangtai-filter-container .el-form-item .el-checkbox,\r\n  .fangtai-filter-container .el-form-item .el-radio-button {\r\n    margin-bottom: 5px;\r\n  }\r\n}\r\n</style>\r\n"]}]}