{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\Screenfull\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\Screenfull\\index.vue", "mtime": 1749104047621}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgc2NyZWVuZnVsbCBmcm9tICdzY3JlZW5mdWxsJw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdTY3JlZW5mdWxsJywNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgaXNGdWxsc2NyZWVuOiBmYWxzZQ0KICAgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmluaXQoKQ0KICB9LA0KICBiZWZvcmVEZXN0cm95KCkgew0KICAgIHRoaXMuZGVzdHJveSgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBjbGljaygpIHsNCiAgICAgIGlmICghc2NyZWVuZnVsbC5pc0VuYWJsZWQpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICfkvaDnmoTmtY/op4jlmajkuI3mlK/mjIHlhajlsY8nLCB0eXBlOiAnd2FybmluZycgfSkNCiAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICB9DQogICAgICBzY3JlZW5mdWxsLnRvZ2dsZSgpDQogICAgfSwNCiAgICBjaGFuZ2UoKSB7DQogICAgICB0aGlzLmlzRnVsbHNjcmVlbiA9IHNjcmVlbmZ1bGwuaXNGdWxsc2NyZWVuDQogICAgfSwNCiAgICBpbml0KCkgew0KICAgICAgaWYgKHNjcmVlbmZ1bGwuaXNFbmFibGVkKSB7DQogICAgICAgIHNjcmVlbmZ1bGwub24oJ2NoYW5nZScsIHRoaXMuY2hhbmdlKQ0KICAgICAgfQ0KICAgIH0sDQogICAgZGVzdHJveSgpIHsNCiAgICAgIGlmIChzY3JlZW5mdWxsLmlzRW5hYmxlZCkgew0KICAgICAgICBzY3JlZW5mdWxsLm9mZignY2hhbmdlJywgdGhpcy5jaGFuZ2UpDQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;AAOA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Screenfull", "sourcesContent": ["<template>\r\n  <div>\r\n    <svg-icon :icon-class=\"isFullscreen?'exit-fullscreen':'fullscreen'\" @click=\"click\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport screenfull from 'screenfull'\r\n\r\nexport default {\r\n  name: 'Screenfull',\r\n  data() {\r\n    return {\r\n      isFullscreen: false\r\n    }\r\n  },\r\n  mounted() {\r\n    this.init()\r\n  },\r\n  beforeDestroy() {\r\n    this.destroy()\r\n  },\r\n  methods: {\r\n    click() {\r\n      if (!screenfull.isEnabled) {\r\n        this.$message({ message: '你的浏览器不支持全屏', type: 'warning' })\r\n        return false\r\n      }\r\n      screenfull.toggle()\r\n    },\r\n    change() {\r\n      this.isFullscreen = screenfull.isFullscreen\r\n    },\r\n    init() {\r\n      if (screenfull.isEnabled) {\r\n        screenfull.on('change', this.change)\r\n      }\r\n    },\r\n    destroy() {\r\n      if (screenfull.isEnabled) {\r\n        screenfull.off('change', this.change)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.screenfull-svg {\r\n  display: inline-block;\r\n  cursor: pointer;\r\n  fill: #5a5e66;;\r\n  width: 20px;\r\n  height: 20px;\r\n  vertical-align: 10px;\r\n}\r\n</style>\r\n"]}]}