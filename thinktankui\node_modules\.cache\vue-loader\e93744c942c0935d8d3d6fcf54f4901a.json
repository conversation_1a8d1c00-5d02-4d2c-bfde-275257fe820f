{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\meta-search\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\meta-search\\index.vue", "mtime": 1749104047640}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdNZXRhU2VhcmNoJywNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgYWN0aXZlVGFiOiAnZnVsbHRleHQnLCAvLyDpu5jorqTmmL7npLrlhajmlofmo4DntKINCiAgICAgIHNlYXJjaEtleXdvcmQ6ICfmlrnlpKonLA0KICAgICAgaGFzU2VhcmNoZWQ6IHRydWUsDQoNCiAgICAgIC8vIOWFqOaWh+ajgOe0ouebuOWFs+aVsOaNrg0KICAgICAgc2VsZWN0ZWRUaW1lOiAnMjRoJywNCiAgICAgIHNlbGVjdGVkUGxhdGZvcm06ICdhbGwnLA0KICAgICAgc2VsZWN0ZWRFbW90aW9uOiAnYWxsJywNCiAgICAgIGN1cnJlbnRQYWdlOiAxLA0KICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgdG90YWxSZXN1bHRzOiAxMDAwMCwNCg0KICAgICAgdGltZU9wdGlvbnM6IFsNCiAgICAgICAgeyBsYWJlbDogJzI05bCP5pe2JywgdmFsdWU6ICcyNGgnIH0sDQogICAgICAgIHsgbGFiZWw6ICfkuIDlkagnLCB2YWx1ZTogJzF3JyB9LA0KICAgICAgICB7IGxhYmVsOiAn5Y2K5bm0JywgdmFsdWU6ICc2bScgfSwNCiAgICAgICAgeyBsYWJlbDogJ+S4gOW5tCcsIHZhbHVlOiAnMXknIH0sDQogICAgICAgIHsgbGFiZWw6ICfoh6rlrprkuYknLCB2YWx1ZTogJ2N1c3RvbScgfQ0KICAgICAgXSwNCg0KICAgICAgcGxhdGZvcm1PcHRpb25zOiBbDQogICAgICAgIHsgbGFiZWw6ICflhajpg6gnLCB2YWx1ZTogJ2FsbCcsIGNvdW50OiAxMDU0MCB9LA0KICAgICAgICB7IGxhYmVsOiAn5b6u5L+hJywgdmFsdWU6ICd3ZWNoYXQnLCBjb3VudDogMTg0NyB9LA0KICAgICAgICB7IGxhYmVsOiAn5b6u5Y2aJywgdmFsdWU6ICd3ZWlibycsIGNvdW50OiAyMDA4IH0sDQogICAgICAgIHsgbGFiZWw6ICflrqLmiLfnq68nLCB2YWx1ZTogJ2FwcCcsIGNvdW50OiAxNzQ4IH0sDQogICAgICAgIHsgbGFiZWw6ICforrrlnZsnLCB2YWx1ZTogJ2ZvcnVtJywgY291bnQ6IDY3MyB9DQogICAgICBdLA0KDQogICAgICBlbW90aW9uT3B0aW9uczogWw0KICAgICAgICB7IGxhYmVsOiAn5YWo6YOoJywgdmFsdWU6ICdhbGwnIH0sDQogICAgICAgIHsgbGFiZWw6ICfmraPpnaInLCB2YWx1ZTogJ3Bvc2l0aXZlJyB9LA0KICAgICAgICB7IGxhYmVsOiAn6LSf6Z2iJywgdmFsdWU6ICduZWdhdGl2ZScgfSwNCiAgICAgICAgeyBsYWJlbDogJ+S4reaApycsIHZhbHVlOiAnbmV1dHJhbCcgfQ0KICAgICAgXSwNCg0KICAgICAgc2VhcmNoUmVzdWx0czogWw0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6ICfku47mlL/lupzpg6jpl6g0NOS4ieS4quS4quS9k+eahOeDreeCuemXrumimO+8jOWIsOWqkuS9kzHvvIzopobnm5bnmoTvvIjlkKvkuYnvvInvvIzmlpfkuonkuLvkuYnlj6Plj7fvvIzniZvlubTkuLvopoHnmoTpl67popjvvIzlhbHlkIzkurrlt6Xmmbrog73nmoTpl67popjkurrlkZjvvIzlpoLlsbHlsbHnmoTpl67popjnmoTkuLvopoHpl67popjvvIzmlrDlt6XkurrvvIzmiZPlt6XvvIznlKjlj4vkuInlrrYuLi4nLA0KICAgICAgICAgIHNvdXJjZTogJ+aWsOWNjue9kScsDQogICAgICAgICAgcHVibGlzaFRpbWU6ICcyMDIyLTA2LTI5IDIwOjA3OjA0JywNCiAgICAgICAgICBhdXRob3I6ICc3N+S6uuiuqOiuuicsDQogICAgICAgICAgcGxhdGZvcm06ICflubPlj7DmnaXmupAnLA0KICAgICAgICAgIHJlYWRDb3VudDogJ+aXoCcsDQogICAgICAgICAgbG9jYXRpb246ICfml6DmiYDlnKjlnLAnLA0KICAgICAgICAgIGNhdGVnb3J5OiAn5paw6Ze7JywNCiAgICAgICAgICBjb250ZW50OiAn5LuO5pS/5bqc6YOo6ZeoNDTkuInkuKrkuKrkvZPnmoTng63ngrnpl67popjvvIzliLDlqpLkvZMx77yM6KaG55uW55qE77yI5ZCr5LmJ77yJ77yM5paX5LqJ5Li75LmJ5Y+j5Y+377yM54mb5bm05Li76KaB55qE6Zeu6aKY77yM5YWx5ZCM5Lq65bel5pm66IO955qE6Zeu6aKY5Lq65ZGY77yM5aaC5bGx5bGx55qE6Zeu6aKY55qE5Li76KaB6Zeu6aKY77yM5paw5bel5Lq677yM5omT5bel77yM55So5Y+L5LiJ5a62Li4uJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6ICfkuK3lpKct6K665paH5Y+R6KGoKDIwMjXlubTkuK3lpKforrrmloflj5Hooagp6Ieq54S25oyH5pWw5Lit5aSnLeiuuuaWh+WPkeihqOS4lueVjOWcsOS9jeiuuuWdmy0yMDI15bm055qE5Lu35YC85Lit5Zu96K665paHJywNCiAgICAgICAgICBzb3VyY2U6ICfkuK3lpKforrrmloflj5HooagnLA0KICAgICAgICAgIHB1Ymxpc2hUaW1lOiAnMjAyMi0wNi0yOSAyMDowNzowNCcsDQogICAgICAgICAgYXV0aG9yOiAnNzfkurrorqjorronLA0KICAgICAgICAgIHBsYXRmb3JtOiAn5bmz5Y+w5p2l5rqQJywNCiAgICAgICAgICByZWFkQ291bnQ6ICfml6AnLA0KICAgICAgICAgIGxvY2F0aW9uOiAn5peg5omA5Zyo5ZywJywNCiAgICAgICAgICBjYXRlZ29yeTogJ+iuuuaWhycsDQogICAgICAgICAgY29udGVudDogJ+S4reWkpy3orrrmloflj5HooagoMjAyNeW5tOS4reWkp+iuuuaWh+WPkeihqCnoh6rnhLbmjIfmlbDkuK3lpKct6K665paH5Y+R6KGo5LiW55WM5Zyw5L2N6K665Z2bLTIwMjXlubTnmoTku7flgLzkuK3lm73orrrmlocuLi4nDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogJ+i9rOWPkeW+ruWNmiPkuK0j5aSn5a2m55Sf77yM5Lq65oOF5LiW5pWF44CCJywNCiAgICAgICAgICBzb3VyY2U6ICflvq7ljZonLA0KICAgICAgICAgIHB1Ymxpc2hUaW1lOiAnMjAyMi0wNi0yOSAyMDowNzowNCcsDQogICAgICAgICAgYXV0aG9yOiAnNzfkurrorqjorronLA0KICAgICAgICAgIHBsYXRmb3JtOiAn5b6u5Y2aJywNCiAgICAgICAgICByZWFkQ291bnQ6ICcxMDAwJywNCiAgICAgICAgICBsb2NhdGlvbjogJ+WMl+S6rCcsDQogICAgICAgICAgY2F0ZWdvcnk6ICfnpL7kuqTlqpLkvZMnLA0KICAgICAgICAgIGNvbnRlbnQ6ICfovazlj5Hlvq7ljZoj5LitI+Wkp+WtpueUn++8jOS6uuaDheS4luaVheOAgui/meaYr+S4gOadoeWFs+S6juWkp+WtpueUn+S6uumZheWFs+ezu+eahOW+ruWNmuWGheWuuS4uLicNCiAgICAgICAgfQ0KICAgICAgXSwNCg0KICAgICAgLy8g5YWD5pCc57Si55u45YWz5pWw5o2uDQogICAgICBhY3RpdmVFbmdpbmU6ICdiaW5nJywNCiAgICAgIHNlbGVjdGVkRW5naW5lczogWydiaW5nJywgJ2JhaWR1JywgJzM2MCddLA0KICAgICAgc2VhcmNoRW5naW5lczogWw0KICAgICAgICB7IGlkOiAnYmluZycsIG5hbWU6ICdNaWNyb3NvZnQgQmluZycsIGljb246ICdodHRwczovL3d3dy5iaW5nLmNvbS9mYXZpY29uLmljbycgfSwNCiAgICAgICAgeyBpZDogJ2JhaWR1JywgbmFtZTogJ+eZvuW6puaQnOe0oicsIGljb246ICdodHRwczovL3d3dy5iYWlkdS5jb20vZmF2aWNvbi5pY28nIH0sDQogICAgICAgIHsgaWQ6ICczNjAnLCBuYW1lOiAnMzYw5pCc57SiJywgaWNvbjogJ2h0dHBzOi8vd3d3LnNvLmNvbS9mYXZpY29uLmljbycgfQ0KICAgICAgXSwNCiAgICAgIGJpbmdSZXN1bHRzOiBbDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogJ+aWueWkquWumOe9kV/pq5jnq6/lhajlnLrmma/ljqjnlLUnLA0KICAgICAgICAgIHVybDogJ2h0dHBzOi8vd3d3LmZvdGlsZS5jb20nLA0KICAgICAgICAgIGxpbms6ICdodHRwczovL3d3dy5mb3RpbGUuY29tJywNCiAgICAgICAgICBkZXNjcmlwdGlvbjogJ+aWueWkquKAlOKAlOS4k+S4mueUn+S6p+mrmOerr+WOqOaIv+eUteWZqOeahOmihuWvvOWTgeeJjO+8jOS4u+iQpeS6p+WTge+8muWQuOayueeDn+acuuOAgeeHg+awlOeBtuOAgea2iOavkuafnOOAgeiSuOeuseOAgeeDpOeuseOAgea0l+eil+acuuetieWOqOaIv+eUteWZqO+8jOaWueWkquWOqOeUte+8jOaWueWkqumbhuaIkOeDuemlquS4reW/gy4uLicNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiAn44CQ5pa55aSq6ZuG5Zui5a6Y572R44CRJywNCiAgICAgICAgICB1cmw6ICdodHRwczovL3d3dy5mb3RpbGUuY29tL2Fib3V0JywNCiAgICAgICAgICBsaW5rOiAnaHR0cHM6Ly93d3cuZm90aWxlLmNvbS9hYm91dCcsDQogICAgICAgICAgZGVzY3JpcHRpb246ICfmlrnlpKrigJTigJTkuJPkuJrnlJ/kuqfpq5jnq6/ljqjmiL/nlLXlmajnmoTpooblr7zlk4HniYzvvIzkuLvokKXkuqflk4HvvJrlkLjmsrnng5/mnLrjgIHnh4PmsJTngbbjgIHmtojmr5Lmn5zjgIHokrjnrrHjgIHng6TnrrHjgIHmtJfnopfmnLrnrYnljqjmiL/nlLXlmajvvIzmlrnlpKrljqjnlLXvvIzmlrnlpKrpm4bmiJDng7npparkuK3lv4MuLi4nDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogJ+S4jeW5s+WHoScsDQogICAgICAgICAgdXJsOiAnaHR0cHM6Ly93d3cuZm90aWxlLmNvbS9wcm9kdWN0JywNCiAgICAgICAgICBsaW5rOiAnaHR0cHM6Ly93d3cuZm90aWxlLmNvbS9wcm9kdWN0JywNCiAgICAgICAgICBkZXNjcmlwdGlvbjogJ+aWueWkqumrmOerr+WOqOeUte+8jOS4k+azqOmrmOerr+WOqOaIv+eUteWZqDIw5bm077yM5Li76JCl5Lqn5ZOB77ya5ZC45rK554Of5py644CB54eD5rCU54G244CB5raI5q+S5p+c44CB6JK4566x44CB54Ok566x44CB5rSX56KX5py6562J5Y6o5oi/55S15Zmo77yM5pa55aSq5Y6o55S177yM5pa55aSq6ZuG5oiQ54O56aWq5Lit5b+DLi4uJw0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgYmFpZHVSZXN1bHRzOiBbDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogJ+aWueWkquWumOe9kV/pq5jnq6/lhajlnLrmma/ljqjnlLUnLA0KICAgICAgICAgIHVybDogJ2h0dHBzOi8vd3d3LmZvdGlsZS5jb20nLA0KICAgICAgICAgIGxpbms6ICdodHRwczovL3d3dy5mb3RpbGUuY29tJywNCiAgICAgICAgICBkZXNjcmlwdGlvbjogJ+aWueWkquKAlOKAlOS4k+S4mueUn+S6p+mrmOerr+WOqOaIv+eUteWZqOeahOmihuWvvOWTgeeJjO+8jOS4u+iQpeS6p+WTge+8muWQuOayueeDn+acuuOAgeeHg+awlOeBtuOAgea2iOavkuafnOOAgeiSuOeuseOAgeeDpOeuseOAgea0l+eil+acuuetieWOqOaIv+eUteWZqO+8jOaWueWkquWOqOeUte+8jOaWueWkqumbhuaIkOeDuemlquS4reW/gy4uLicNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiAn5pa55aSq5Y6o55S1IOmrmOerr+mbhuaIkOWOqOaIv+eUteWZqOWTgeeJjCcsDQogICAgICAgICAgdXJsOiAnaHR0cHM6Ly93d3cuZm90aWxlLmNvbS9wcm9kdWN0JywNCiAgICAgICAgICBsaW5rOiAnaHR0cHM6Ly93d3cuZm90aWxlLmNvbS9wcm9kdWN0JywNCiAgICAgICAgICBkZXNjcmlwdGlvbjogJ+WFqOWbveacjeWKoeeDree6v++8mjQwMC0zMTUtMDAwMCDmlrnlpKrigJTigJTkuJPkuJrnlJ/kuqfpq5jnq6/ljqjmiL/nlLXlmajnmoTpooblr7zlk4HniYzvvIzkuLvokKXkuqflk4HvvJrlkLjmsrnng5/mnLrjgIHnh4PmsJTngbbjgIHmtojmr5Lmn5zjgIHokrjnrrHjgIHng6TnrrHjgIHmtJfnopfmnLrnrYnljqjmiL/nlLXlmaguLi4nDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogJ+aWueWkqiAtIOeZvuW6pueZvuenkScsDQogICAgICAgICAgdXJsOiAnaHR0cHM6Ly9iYWlrZS5iYWlkdS5jb20vaXRlbS/mlrnlpKovMTgzMCcsDQogICAgICAgICAgbGluazogJ2h0dHBzOi8vYmFpa2UuYmFpZHUuY29tL2l0ZW0v5pa55aSqLzE4MzAnLA0KICAgICAgICAgIGRlc2NyaXB0aW9uOiAn5pa55aSq77yM5piv5Lit5Zu96auY56uv5Y6o55S16aKG5a+85ZOB54mM77yM5Yib56uL5LqOMTk5NuW5tO+8jOaAu+mDqOS9jeS6jua1meaxn+Wugeazou+8jOaYr+S4gOWutumbhueglOWPkeOAgeeUn+S6p+OAgemUgOWUruS6juS4gOS9k+eahOeOsOS7o+WMluS8geS4mu+8jOS4u+imgeS6p+WTgeWMheaLrOWQuOayueeDn+acuuOAgeeHg+awlOeBtuOAgea2iOavkuafnOOAgeiSuOeuseOAgeeDpOeuseOAgea0l+eil+acuuetieWOqOaIv+eUteWZqC4uLicNCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIHNvMzYwUmVzdWx0czogWw0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6ICfmlrnlpKrlrpjnvZFf6auY56uv5YWo5Zy65pmv5Y6o55S1JywNCiAgICAgICAgICB1cmw6ICdodHRwczovL3d3dy5mb3RpbGUuY29tJywNCiAgICAgICAgICBsaW5rOiAnaHR0cHM6Ly93d3cuZm90aWxlLmNvbScsDQogICAgICAgICAgZGVzY3JpcHRpb246ICfmlrnlpKrigJTigJTkuJPkuJrnlJ/kuqfpq5jnq6/ljqjmiL/nlLXlmajnmoTpooblr7zlk4HniYzvvIzkuLvokKXkuqflk4HvvJrlkLjmsrnng5/mnLrjgIHnh4PmsJTngbbjgIHmtojmr5Lmn5zjgIHokrjnrrHjgIHng6TnrrHjgIHmtJfnopfmnLrnrYnljqjmiL/nlLXlmajvvIzmlrnlpKrljqjnlLXvvIzmlrnlpKrpm4bmiJDng7npparkuK3lv4MuLi4nDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogJ+aWueWkquWOqOeUteaXl+iIsOW6ly3lpKnnjKsnLA0KICAgICAgICAgIHVybDogJ2h0dHBzOi8vZm90aWxlLnRtYWxsLmNvbScsDQogICAgICAgICAgbGluazogJ2h0dHBzOi8vZm90aWxlLnRtYWxsLmNvbScsDQogICAgICAgICAgZGVzY3JpcHRpb246ICfmlrnlpKrljqjnlLXml5foiLDlupcs5o+Q5L6b5pa55aSq5rK554Of5py6LOaWueWkqueHg+awlOeBtizmlrnlpKrmtojmr5Lmn5ws5pa55aSq5rSX56KX5py6LOaWueWkquiSuOeusSzmlrnlpKrng6TnrrEs5pa55aSq5b6u5rOi54KJLOaWueWkquawtOanvea0l+eil+acuuetieS6p+WTgeOAguWkqeeMq+ato+WTgeS/nemanCzmj5DkvpsuLi4nDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogJ+aWueWkqumbhuWbouaciemZkOWFrOWPuCcsDQogICAgICAgICAgdXJsOiAnaHR0cHM6Ly93d3cuZm90aWxlLmNvbS9hYm91dCcsDQogICAgICAgICAgbGluazogJ2h0dHBzOi8vd3d3LmZvdGlsZS5jb20vYWJvdXQnLA0KICAgICAgICAgIGRlc2NyaXB0aW9uOiAn5pa55aSq6ZuG5Zui5pyJ6ZmQ5YWs5Y+45Yib56uL5LqOMTk5NuW5tO+8jOaAu+mDqOS9jeS6jua1meaxn+Wugeazou+8jOaYr+S4gOWutumbhueglOWPkeOAgeeUn+S6p+OAgemUgOWUruS6juS4gOS9k+eahOeOsOS7o+WMluS8geS4mu+8jOS4u+imgeS6p+WTgeWMheaLrOWQuOayueeDn+acuuOAgeeHg+awlOeBtuOAgea2iOavkuafnOOAgeiSuOeuseOAgeeDpOeuseOAgea0l+eil+acuuetieWOqOaIv+eUteWZqC4uLicNCiAgICAgICAgfQ0KICAgICAgXQ0KICAgIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8vIOagh+etvumhteWIh+aNog0KICAgIHN3aXRjaFRhYih0YWIpIHsNCiAgICAgIHRoaXMuYWN0aXZlVGFiID0gdGFiDQogICAgfSwNCg0KICAgIC8vIOaQnOe0ouWKn+iDvQ0KICAgIGhhbmRsZVNlYXJjaCgpIHsNCiAgICAgIHRoaXMuaGFzU2VhcmNoZWQgPSB0cnVlDQogICAgICAvLyDlrp7pmYXpobnnm67kuK3ov5nph4zlupTor6XosIPnlKhBUEnojrflj5bmkJzntKLnu5PmnpwNCiAgICAgIGNvbnNvbGUubG9nKCfmkJzntKLlhbPplK7or406JywgdGhpcy5zZWFyY2hLZXl3b3JkKQ0KICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDmkJzntKI6ICR7dGhpcy5zZWFyY2hLZXl3b3JkfWApDQogICAgfSwNCg0KICAgIC8vIOWFqOaWh+ajgOe0ouetm+mAieaWueazlQ0KICAgIHNlbGVjdFRpbWUodmFsdWUpIHsNCiAgICAgIHRoaXMuc2VsZWN0ZWRUaW1lID0gdmFsdWUNCiAgICAgIHRoaXMuaGFuZGxlU2VhcmNoKCkNCiAgICB9LA0KDQogICAgc2VsZWN0UGxhdGZvcm0odmFsdWUpIHsNCiAgICAgIHRoaXMuc2VsZWN0ZWRQbGF0Zm9ybSA9IHZhbHVlDQogICAgICB0aGlzLmhhbmRsZVNlYXJjaCgpDQogICAgfSwNCg0KICAgIHNlbGVjdEVtb3Rpb24odmFsdWUpIHsNCiAgICAgIHRoaXMuc2VsZWN0ZWRFbW90aW9uID0gdmFsdWUNCiAgICAgIHRoaXMuaGFuZGxlU2VhcmNoKCkNCiAgICB9LA0KDQogICAgaGFuZGxlUGFnZUNoYW5nZShwYWdlKSB7DQogICAgICB0aGlzLmN1cnJlbnRQYWdlID0gcGFnZQ0KICAgICAgLy8g5Yqg6L295a+55bqU6aG16Z2i5pWw5o2uDQogICAgfSwNCg0KICAgIC8vIOWFg+aQnOe0ouW8leaTjuWIh+aNog0KICAgIHRvZ2dsZUVuZ2luZShlbmdpbmVJZCkgew0KICAgICAgLy8g5YiH5o2i6YCJ5Lit54q25oCBDQogICAgICBpZiAodGhpcy5zZWxlY3RlZEVuZ2luZXMuaW5jbHVkZXMoZW5naW5lSWQpKSB7DQogICAgICAgIC8vIOWmguaenOW3sue7j+mAieS4re+8jOS4lOS4jeaYr+acgOWQjuS4gOS4qumAieS4reeahOW8leaTju+8jOWImeWPlua2iOmAieS4rQ0KICAgICAgICBpZiAodGhpcy5zZWxlY3RlZEVuZ2luZXMubGVuZ3RoID4gMSkgew0KICAgICAgICAgIHRoaXMuc2VsZWN0ZWRFbmdpbmVzID0gdGhpcy5zZWxlY3RlZEVuZ2luZXMuZmlsdGVyKGlkID0+IGlkICE9PSBlbmdpbmVJZCkNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5aaC5p6c5pyq6YCJ5Lit77yM5YiZ5re75Yqg5Yiw6YCJ5Lit5YiX6KGoDQogICAgICAgIHRoaXMuc2VsZWN0ZWRFbmdpbmVzLnB1c2goZW5naW5lSWQpDQogICAgICB9DQoNCiAgICAgIC8vIOiuvue9ruW9k+WJjea0u+WKqOW8leaTjg0KICAgICAgdGhpcy5hY3RpdmVFbmdpbmUgPSBlbmdpbmVJZA0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqNA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/meta-search", "sourcesContent": ["<template>\r\n  <div class=\"meta-search-container\">\r\n    <!-- 顶部搜索区域 -->\r\n    <div class=\"search-header\">\r\n      <div class=\"search-tabs\">\r\n        <div\r\n          class=\"tab\"\r\n          :class=\"{ active: activeTab === 'fulltext' }\"\r\n          @click=\"switchTab('fulltext')\"\r\n        >\r\n          全文检索\r\n        </div>\r\n        <div\r\n          class=\"tab\"\r\n          :class=\"{ active: activeTab === 'meta' }\"\r\n          @click=\"switchTab('meta')\"\r\n        >\r\n          元搜索\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"search-box\">\r\n        <input\r\n          type=\"text\"\r\n          class=\"search-input\"\r\n          v-model=\"searchKeyword\"\r\n          placeholder=\"请输入搜索关键词\"\r\n          @keyup.enter=\"handleSearch\"\r\n        />\r\n        <el-button type=\"primary\" class=\"search-btn\" @click=\"handleSearch\">搜索</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 全文检索结果 -->\r\n    <div v-if=\"activeTab === 'fulltext' && hasSearched\" class=\"fulltext-results\">\r\n      <!-- 筛选条件区域 -->\r\n      <div class=\"filter-section\">\r\n        <!-- 时间筛选 -->\r\n        <div class=\"filter-row\">\r\n          <span class=\"filter-label\">时间范围:</span>\r\n          <div class=\"filter-options\">\r\n            <el-button\r\n              v-for=\"time in timeOptions\"\r\n              :key=\"time.value\"\r\n              :type=\"selectedTime === time.value ? 'primary' : ''\"\r\n              size=\"small\"\r\n              @click=\"selectTime(time.value)\"\r\n            >\r\n              {{ time.label }}\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 平台筛选 -->\r\n        <div class=\"filter-row\">\r\n          <span class=\"filter-label\">平台类型:</span>\r\n          <div class=\"filter-options\">\r\n            <el-button\r\n              v-for=\"platform in platformOptions\"\r\n              :key=\"platform.value\"\r\n              :type=\"selectedPlatform === platform.value ? 'primary' : ''\"\r\n              size=\"small\"\r\n              @click=\"selectPlatform(platform.value)\"\r\n            >\r\n              {{ platform.label }}\r\n              <span v-if=\"platform.count\" class=\"count\">({{ platform.count }})</span>\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 情感筛选 -->\r\n        <div class=\"filter-row\">\r\n          <span class=\"filter-label\">情感类型:</span>\r\n          <div class=\"filter-options\">\r\n            <el-button\r\n              v-for=\"emotion in emotionOptions\"\r\n              :key=\"emotion.value\"\r\n              :type=\"selectedEmotion === emotion.value ? 'primary' : ''\"\r\n              size=\"small\"\r\n              @click=\"selectEmotion(emotion.value)\"\r\n            >\r\n              {{ emotion.label }}\r\n              <span v-if=\"emotion.count\" class=\"count\">({{ emotion.count }})</span>\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 结果统计 -->\r\n      <div class=\"result-stats\">\r\n        <span>共{{ totalResults }}条结果</span>\r\n        <div class=\"action-buttons\">\r\n          <el-button size=\"small\">导出</el-button>\r\n          <el-button type=\"primary\" size=\"small\">分析</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 搜索结果列表 -->\r\n      <div class=\"results-list\">\r\n        <div v-for=\"(item, index) in searchResults\" :key=\"index\" class=\"result-item\">\r\n          <div class=\"result-header\">\r\n            <h3 class=\"result-title\">{{ item.title }}</h3>\r\n            <div class=\"result-actions\">\r\n              <el-button type=\"text\" icon=\"el-icon-view\"></el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"result-meta\">\r\n            <span class=\"meta-item\">{{ item.source }}</span>\r\n            <span class=\"meta-item\">{{ item.publishTime }}</span>\r\n            <span class=\"meta-item\">{{ item.author }}</span>\r\n            <span class=\"meta-item\">{{ item.platform }}</span>\r\n            <span class=\"meta-item\">阅读量: {{ item.readCount }}</span>\r\n            <span class=\"meta-item\">{{ item.location }}</span>\r\n            <span class=\"meta-item\">{{ item.category }}</span>\r\n          </div>\r\n\r\n          <div class=\"result-content\">\r\n            {{ item.content }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          background\r\n          layout=\"prev, pager, next\"\r\n          :total=\"totalResults\"\r\n          :current-page.sync=\"currentPage\"\r\n          :page-size=\"pageSize\"\r\n          @current-change=\"handlePageChange\"\r\n        ></el-pagination>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 元搜索结果 -->\r\n    <div v-if=\"activeTab === 'meta' && hasSearched\" class=\"meta-results\">\r\n      <!-- 搜索引擎选项卡 -->\r\n      <div class=\"search-engines\">\r\n        <div\r\n          v-for=\"engine in searchEngines\"\r\n          :key=\"engine.id\"\r\n          class=\"engine-item\"\r\n          :class=\"{ active: engine.id === activeEngine }\"\r\n          @click=\"toggleEngine(engine.id)\"\r\n        >\r\n          <div class=\"checkbox\">\r\n            <i class=\"el-icon-check\" v-if=\"selectedEngines.includes(engine.id)\"></i>\r\n          </div>\r\n          <img :src=\"engine.icon\" :alt=\"engine.name\" class=\"engine-icon\" />\r\n          <span class=\"engine-name\">{{ engine.name }}</span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 搜索结果展示区 -->\r\n      <div class=\"results-container\">\r\n        <!-- 左侧搜索结果 -->\r\n        <div class=\"result-column\" v-if=\"selectedEngines.includes('bing')\">\r\n          <div class=\"result-header\">\r\n            <img src=\"https://www.bing.com/favicon.ico\" alt=\"Microsoft Bing\" class=\"result-icon\" />\r\n            <span class=\"result-title\">Microsoft Bing</span>\r\n          </div>\r\n          <div class=\"result-list\">\r\n            <div class=\"result-item\" v-for=\"(item, index) in bingResults\" :key=\"'bing-'+index\">\r\n              <h3 class=\"item-title\">\r\n                <a :href=\"item.link\" target=\"_blank\">{{ item.title }}</a>\r\n              </h3>\r\n              <div class=\"item-url\">{{ item.url }}</div>\r\n              <div class=\"item-desc\">{{ item.description }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 中间搜索结果 -->\r\n        <div class=\"result-column\" v-if=\"selectedEngines.includes('baidu')\">\r\n          <div class=\"result-header\">\r\n            <img src=\"https://www.baidu.com/favicon.ico\" alt=\"百度搜索\" class=\"result-icon\" />\r\n            <span class=\"result-title\">百度搜索</span>\r\n          </div>\r\n          <div class=\"result-list\">\r\n            <div class=\"result-item\" v-for=\"(item, index) in baiduResults\" :key=\"'baidu-'+index\">\r\n              <h3 class=\"item-title\">\r\n                <a :href=\"item.link\" target=\"_blank\">{{ item.title }}</a>\r\n              </h3>\r\n              <div class=\"item-url\">{{ item.url }}</div>\r\n              <div class=\"item-desc\">{{ item.description }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 右侧搜索结果 -->\r\n        <div class=\"result-column\" v-if=\"selectedEngines.includes('360')\">\r\n          <div class=\"result-header\">\r\n            <img src=\"https://www.so.com/favicon.ico\" alt=\"360搜索\" class=\"result-icon\" />\r\n            <span class=\"result-title\">360搜索</span>\r\n          </div>\r\n          <div class=\"result-list\">\r\n            <div class=\"result-item\" v-for=\"(item, index) in so360Results\" :key=\"'360-'+index\">\r\n              <h3 class=\"item-title\">\r\n                <a :href=\"item.link\" target=\"_blank\">{{ item.title }}</a>\r\n              </h3>\r\n              <div class=\"item-url\">{{ item.url }}</div>\r\n              <div class=\"item-desc\">{{ item.description }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'MetaSearch',\r\n  data() {\r\n    return {\r\n      activeTab: 'fulltext', // 默认显示全文检索\r\n      searchKeyword: '方太',\r\n      hasSearched: true,\r\n\r\n      // 全文检索相关数据\r\n      selectedTime: '24h',\r\n      selectedPlatform: 'all',\r\n      selectedEmotion: 'all',\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      totalResults: 10000,\r\n\r\n      timeOptions: [\r\n        { label: '24小时', value: '24h' },\r\n        { label: '一周', value: '1w' },\r\n        { label: '半年', value: '6m' },\r\n        { label: '一年', value: '1y' },\r\n        { label: '自定义', value: 'custom' }\r\n      ],\r\n\r\n      platformOptions: [\r\n        { label: '全部', value: 'all', count: 10540 },\r\n        { label: '微信', value: 'wechat', count: 1847 },\r\n        { label: '微博', value: 'weibo', count: 2008 },\r\n        { label: '客户端', value: 'app', count: 1748 },\r\n        { label: '论坛', value: 'forum', count: 673 }\r\n      ],\r\n\r\n      emotionOptions: [\r\n        { label: '全部', value: 'all' },\r\n        { label: '正面', value: 'positive' },\r\n        { label: '负面', value: 'negative' },\r\n        { label: '中性', value: 'neutral' }\r\n      ],\r\n\r\n      searchResults: [\r\n        {\r\n          title: '从政府部门44三个个体的热点问题，到媒体1，覆盖的（含义），斗争主义口号，牛年主要的问题，共同人工智能的问题人员，如山山的问题的主要问题，新工人，打工，用友三家...',\r\n          source: '新华网',\r\n          publishTime: '2022-06-29 20:07:04',\r\n          author: '77人讨论',\r\n          platform: '平台来源',\r\n          readCount: '无',\r\n          location: '无所在地',\r\n          category: '新闻',\r\n          content: '从政府部门44三个个体的热点问题，到媒体1，覆盖的（含义），斗争主义口号，牛年主要的问题，共同人工智能的问题人员，如山山的问题的主要问题，新工人，打工，用友三家...'\r\n        },\r\n        {\r\n          title: '中大-论文发表(2025年中大论文发表)自然指数中大-论文发表世界地位论坛-2025年的价值中国论文',\r\n          source: '中大论文发表',\r\n          publishTime: '2022-06-29 20:07:04',\r\n          author: '77人讨论',\r\n          platform: '平台来源',\r\n          readCount: '无',\r\n          location: '无所在地',\r\n          category: '论文',\r\n          content: '中大-论文发表(2025年中大论文发表)自然指数中大-论文发表世界地位论坛-2025年的价值中国论文...'\r\n        },\r\n        {\r\n          title: '转发微博#中#大学生，人情世故。',\r\n          source: '微博',\r\n          publishTime: '2022-06-29 20:07:04',\r\n          author: '77人讨论',\r\n          platform: '微博',\r\n          readCount: '1000',\r\n          location: '北京',\r\n          category: '社交媒体',\r\n          content: '转发微博#中#大学生，人情世故。这是一条关于大学生人际关系的微博内容...'\r\n        }\r\n      ],\r\n\r\n      // 元搜索相关数据\r\n      activeEngine: 'bing',\r\n      selectedEngines: ['bing', 'baidu', '360'],\r\n      searchEngines: [\r\n        { id: 'bing', name: 'Microsoft Bing', icon: 'https://www.bing.com/favicon.ico' },\r\n        { id: 'baidu', name: '百度搜索', icon: 'https://www.baidu.com/favicon.ico' },\r\n        { id: '360', name: '360搜索', icon: 'https://www.so.com/favicon.ico' }\r\n      ],\r\n      bingResults: [\r\n        {\r\n          title: '方太官网_高端全场景厨电',\r\n          url: 'https://www.fotile.com',\r\n          link: 'https://www.fotile.com',\r\n          description: '方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'\r\n        },\r\n        {\r\n          title: '【方太集团官网】',\r\n          url: 'https://www.fotile.com/about',\r\n          link: 'https://www.fotile.com/about',\r\n          description: '方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'\r\n        },\r\n        {\r\n          title: '不平凡',\r\n          url: 'https://www.fotile.com/product',\r\n          link: 'https://www.fotile.com/product',\r\n          description: '方太高端厨电，专注高端厨房电器20年，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'\r\n        }\r\n      ],\r\n      baiduResults: [\r\n        {\r\n          title: '方太官网_高端全场景厨电',\r\n          url: 'https://www.fotile.com',\r\n          link: 'https://www.fotile.com',\r\n          description: '方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'\r\n        },\r\n        {\r\n          title: '方太厨电 高端集成厨房电器品牌',\r\n          url: 'https://www.fotile.com/product',\r\n          link: 'https://www.fotile.com/product',\r\n          description: '全国服务热线：400-315-0000 方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器...'\r\n        },\r\n        {\r\n          title: '方太 - 百度百科',\r\n          url: 'https://baike.baidu.com/item/方太/1830',\r\n          link: 'https://baike.baidu.com/item/方太/1830',\r\n          description: '方太，是中国高端厨电领导品牌，创立于1996年，总部位于浙江宁波，是一家集研发、生产、销售于一体的现代化企业，主要产品包括吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器...'\r\n        }\r\n      ],\r\n      so360Results: [\r\n        {\r\n          title: '方太官网_高端全场景厨电',\r\n          url: 'https://www.fotile.com',\r\n          link: 'https://www.fotile.com',\r\n          description: '方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'\r\n        },\r\n        {\r\n          title: '方太厨电旗舰店-天猫',\r\n          url: 'https://fotile.tmall.com',\r\n          link: 'https://fotile.tmall.com',\r\n          description: '方太厨电旗舰店,提供方太油烟机,方太燃气灶,方太消毒柜,方太洗碗机,方太蒸箱,方太烤箱,方太微波炉,方太水槽洗碗机等产品。天猫正品保障,提供...'\r\n        },\r\n        {\r\n          title: '方太集团有限公司',\r\n          url: 'https://www.fotile.com/about',\r\n          link: 'https://www.fotile.com/about',\r\n          description: '方太集团有限公司创立于1996年，总部位于浙江宁波，是一家集研发、生产、销售于一体的现代化企业，主要产品包括吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器...'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    // 标签页切换\r\n    switchTab(tab) {\r\n      this.activeTab = tab\r\n    },\r\n\r\n    // 搜索功能\r\n    handleSearch() {\r\n      this.hasSearched = true\r\n      // 实际项目中这里应该调用API获取搜索结果\r\n      console.log('搜索关键词:', this.searchKeyword)\r\n      this.$message.success(`搜索: ${this.searchKeyword}`)\r\n    },\r\n\r\n    // 全文检索筛选方法\r\n    selectTime(value) {\r\n      this.selectedTime = value\r\n      this.handleSearch()\r\n    },\r\n\r\n    selectPlatform(value) {\r\n      this.selectedPlatform = value\r\n      this.handleSearch()\r\n    },\r\n\r\n    selectEmotion(value) {\r\n      this.selectedEmotion = value\r\n      this.handleSearch()\r\n    },\r\n\r\n    handlePageChange(page) {\r\n      this.currentPage = page\r\n      // 加载对应页面数据\r\n    },\r\n\r\n    // 元搜索引擎切换\r\n    toggleEngine(engineId) {\r\n      // 切换选中状态\r\n      if (this.selectedEngines.includes(engineId)) {\r\n        // 如果已经选中，且不是最后一个选中的引擎，则取消选中\r\n        if (this.selectedEngines.length > 1) {\r\n          this.selectedEngines = this.selectedEngines.filter(id => id !== engineId)\r\n        }\r\n      } else {\r\n        // 如果未选中，则添加到选中列表\r\n        this.selectedEngines.push(engineId)\r\n      }\r\n\r\n      // 设置当前活动引擎\r\n      this.activeEngine = engineId\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.meta-search-container {\r\n  padding: 20px;\r\n  background: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n.search-header {\r\n  background: white;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.search-tabs {\r\n  display: flex;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.tab {\r\n  padding: 8px 16px;\r\n  margin-right: 10px;\r\n  cursor: pointer;\r\n  border-bottom: 2px solid transparent;\r\n}\r\n\r\n.tab.active {\r\n  color: #409EFF;\r\n  border-bottom: 2px solid #409EFF;\r\n  font-weight: bold;\r\n}\r\n\r\n.search-box {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.search-input {\r\n  flex: 1;\r\n  height: 40px;\r\n  padding: 0 15px;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  margin-right: 10px;\r\n  font-size: 14px;\r\n}\r\n\r\n.search-btn {\r\n  height: 40px;\r\n}\r\n\r\n.search-results {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.search-engines {\r\n  display: flex;\r\n  background: white;\r\n  border-radius: 8px;\r\n  padding: 15px;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.engine-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px 16px;\r\n  margin-right: 15px;\r\n  cursor: pointer;\r\n  border-radius: 4px;\r\n  transition: all 0.3s;\r\n  border: 1px solid #dcdfe6;\r\n  background: #fff;\r\n}\r\n\r\n.engine-item:hover {\r\n  border-color: #c6e2ff;\r\n}\r\n\r\n.engine-item.active {\r\n  background: #ecf5ff;\r\n  color: #409EFF;\r\n  border-color: #409EFF;\r\n}\r\n\r\n.checkbox {\r\n  width: 16px;\r\n  height: 16px;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 2px;\r\n  margin-right: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #fff;\r\n  background-color: #409EFF;\r\n}\r\n\r\n.engine-icon {\r\n  width: 16px;\r\n  height: 16px;\r\n  margin-right: 8px;\r\n}\r\n\r\n.results-container {\r\n  display: flex;\r\n  gap: 20px;\r\n}\r\n\r\n.result-column {\r\n  flex: 1;\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  background: #f5f7fa;\r\n}\r\n\r\n.result-icon {\r\n  width: 16px;\r\n  height: 16px;\r\n  margin-right: 8px;\r\n}\r\n\r\n.result-title {\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.result-list {\r\n  padding: 15px;\r\n}\r\n\r\n.result-item {\r\n  margin-bottom: 20px;\r\n  padding-bottom: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.result-item:last-child {\r\n  margin-bottom: 0;\r\n  padding-bottom: 0;\r\n  border-bottom: none;\r\n}\r\n\r\n.item-title {\r\n  margin: 0 0 8px 0;\r\n  font-size: 16px;\r\n}\r\n\r\n.item-title a {\r\n  color: #0366d6;\r\n  text-decoration: none;\r\n}\r\n\r\n.item-title a:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.item-url {\r\n  color: #67c23a;\r\n  font-size: 12px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.item-desc {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 全文检索样式 */\r\n.fulltext-results {\r\n  margin-top: 20px;\r\n}\r\n\r\n.filter-section {\r\n  background: white;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.filter-row {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.filter-row:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.filter-label {\r\n  width: 80px;\r\n  color: #666;\r\n  font-size: 14px;\r\n}\r\n\r\n.filter-options {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 10px;\r\n}\r\n\r\n.count {\r\n  color: #999;\r\n  font-size: 12px;\r\n}\r\n\r\n.result-stats {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: white;\r\n  padding: 15px 20px;\r\n  margin-bottom: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.results-list {\r\n  background: white;\r\n  border-radius: 8px;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.results-list .result-item {\r\n  padding: 20px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.results-list .result-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.result-title {\r\n  font-size: 16px;\r\n  color: #333;\r\n  margin: 0;\r\n  line-height: 1.4;\r\n  flex: 1;\r\n  margin-right: 20px;\r\n}\r\n\r\n.result-meta {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n  margin-bottom: 10px;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.meta-item {\r\n  white-space: nowrap;\r\n}\r\n\r\n.result-content {\r\n  color: #666;\r\n  line-height: 1.6;\r\n  font-size: 14px;\r\n}\r\n\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  background: white;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n</style>\r\n"]}]}