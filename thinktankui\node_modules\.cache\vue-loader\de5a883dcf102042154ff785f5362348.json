{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\Crontab\\week.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\Crontab\\week.vue", "mtime": 1749104047618}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["week.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "week.vue", "sourceRoot": "src/components/Crontab", "sourcesContent": ["<template>\r\n\t<el-form size='small'>\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio v-model='radioValue' :label=\"1\">\r\n\t\t\t\t周，允许的通配符[, - * ? / L #]\r\n\t\t\t</el-radio>\r\n\t\t</el-form-item>\r\n\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio v-model='radioValue' :label=\"2\">\r\n\t\t\t\t不指定\r\n\t\t\t</el-radio>\r\n\t\t</el-form-item>\r\n\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio v-model='radioValue' :label=\"3\">\r\n\t\t\t\t周期从星期\r\n\t\t\t\t<el-select clearable v-model=\"cycle01\">\r\n\t\t\t\t\t<el-option\r\n\t\t\t\t\t\tv-for=\"(item,index) of weekList\"\r\n\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t:label=\"item.value\"\r\n\t\t\t\t\t\t:value=\"item.key\"\r\n\t\t\t\t\t\t:disabled=\"item.key === 1\"\r\n\t\t\t\t\t>{{item.value}}</el-option>\r\n\t\t\t\t</el-select>\r\n\t\t\t\t-\r\n\t\t\t\t<el-select clearable v-model=\"cycle02\">\r\n\t\t\t\t\t<el-option\r\n\t\t\t\t\t\tv-for=\"(item,index) of weekList\"\r\n\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t:label=\"item.value\"\r\n\t\t\t\t\t\t:value=\"item.key\"\r\n\t\t\t\t\t\t:disabled=\"item.key < cycle01 && item.key !== 1\"\r\n\t\t\t\t\t>{{item.value}}</el-option>\r\n\t\t\t\t</el-select>\r\n\t\t\t</el-radio>\r\n\t\t</el-form-item>\r\n\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio v-model='radioValue' :label=\"4\">\r\n\t\t\t\t第\r\n\t\t\t\t<el-input-number v-model='average01' :min=\"1\" :max=\"4\" /> 周的星期\r\n\t\t\t\t<el-select clearable v-model=\"average02\">\r\n\t\t\t\t\t<el-option v-for=\"(item,index) of weekList\" :key=\"index\" :label=\"item.value\" :value=\"item.key\">{{item.value}}</el-option>\r\n\t\t\t\t</el-select>\r\n\t\t\t</el-radio>\r\n\t\t</el-form-item>\r\n\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio v-model='radioValue' :label=\"5\">\r\n\t\t\t\t本月最后一个星期\r\n\t\t\t\t<el-select clearable v-model=\"weekday\">\r\n\t\t\t\t\t<el-option v-for=\"(item,index) of weekList\" :key=\"index\" :label=\"item.value\" :value=\"item.key\">{{item.value}}</el-option>\r\n\t\t\t\t</el-select>\r\n\t\t\t</el-radio>\r\n\t\t</el-form-item>\r\n\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio v-model='radioValue' :label=\"6\">\r\n\t\t\t\t指定\r\n\t\t\t\t<el-select clearable v-model=\"checkboxList\" placeholder=\"可多选\" multiple style=\"width:100%\">\r\n\t\t\t\t\t<el-option v-for=\"(item,index) of weekList\" :key=\"index\" :label=\"item.value\" :value=\"String(item.key)\">{{item.value}}</el-option>\r\n\t\t\t\t</el-select>\r\n\t\t\t</el-radio>\r\n\t\t</el-form-item>\r\n\r\n\t</el-form>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tradioValue: 2,\r\n\t\t\tweekday: 2,\r\n\t\t\tcycle01: 2,\r\n\t\t\tcycle02: 3,\r\n\t\t\taverage01: 1,\r\n\t\t\taverage02: 2,\r\n\t\t\tcheckboxList: [],\r\n\t\t\tweekList: [\r\n\t\t\t\t{\r\n\t\t\t\t\tkey: 2,\r\n\t\t\t\t\tvalue: '星期一'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tkey: 3,\r\n\t\t\t\t\tvalue: '星期二'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tkey: 4,\r\n\t\t\t\t\tvalue: '星期三'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tkey: 5,\r\n\t\t\t\t\tvalue: '星期四'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tkey: 6,\r\n\t\t\t\t\tvalue: '星期五'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tkey: 7,\r\n\t\t\t\t\tvalue: '星期六'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tkey: 1,\r\n\t\t\t\t\tvalue: '星期日'\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\tcheckNum: this.$options.propsData.check\r\n\t\t}\r\n\t},\r\n\tname: 'crontab-week',\r\n\tprops: ['check', 'cron'],\r\n\tmethods: {\r\n\t\t// 单选按钮值变化时\r\n\t\tradioChange() {\r\n\t\t\tif (this.radioValue !== 2 && this.cron.day !== '?') {\r\n\t\t\t\tthis.$emit('update', 'day', '?', 'week');\r\n\t\t\t}\r\n\t\t\tswitch (this.radioValue) {\r\n\t\t\t\tcase 1:\r\n\t\t\t\t\tthis.$emit('update', 'week', '*');\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 2:\r\n\t\t\t\t\tthis.$emit('update', 'week', '?');\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 3:\r\n\t\t\t\t\tthis.$emit('update', 'week', this.cycleTotal);\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 4:\r\n\t\t\t\t\tthis.$emit('update', 'week', this.averageTotal);\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 5:\r\n\t\t\t\t\tthis.$emit('update', 'week', this.weekdayCheck + 'L');\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 6:\r\n\t\t\t\t\tthis.$emit('update', 'week', this.checkboxString);\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 周期两个值变化时\r\n\t\tcycleChange() {\r\n\t\t\tif (this.radioValue == '3') {\r\n\t\t\t\tthis.$emit('update', 'week', this.cycleTotal);\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 平均两个值变化时\r\n\t\taverageChange() {\r\n\t\t\tif (this.radioValue == '4') {\r\n\t\t\t\tthis.$emit('update', 'week', this.averageTotal);\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 最近工作日值变化时\r\n\t\tweekdayChange() {\r\n\t\t\tif (this.radioValue == '5') {\r\n\t\t\t\tthis.$emit('update', 'week', this.weekday + 'L');\r\n\t\t\t}\r\n\t\t},\r\n\t\t// checkbox值变化时\r\n\t\tcheckboxChange() {\r\n\t\t\tif (this.radioValue == '6') {\r\n\t\t\t\tthis.$emit('update', 'week', this.checkboxString);\r\n\t\t\t}\r\n\t\t},\r\n\t},\r\n\twatch: {\r\n\t\t'radioValue': 'radioChange',\r\n\t\t'cycleTotal': 'cycleChange',\r\n\t\t'averageTotal': 'averageChange',\r\n\t\t'weekdayCheck': 'weekdayChange',\r\n\t\t'checkboxString': 'checkboxChange',\r\n\t},\r\n\tcomputed: {\r\n\t\t// 计算两个周期值\r\n\t\tcycleTotal: function () {\r\n\t\t\tthis.cycle01 = this.checkNum(this.cycle01, 1, 7)\r\n\t\t\tthis.cycle02 = this.checkNum(this.cycle02, 1, 7)\r\n\t\t\treturn this.cycle01 + '-' + this.cycle02;\r\n\t\t},\r\n\t\t// 计算平均用到的值\r\n\t\taverageTotal: function () {\r\n\t\t\tthis.average01 = this.checkNum(this.average01, 1, 4)\r\n\t\t\tthis.average02 = this.checkNum(this.average02, 1, 7)\r\n\t\t\treturn this.average02 + '#' + this.average01;\r\n\t\t},\r\n\t\t// 最近的工作日（格式）\r\n\t\tweekdayCheck: function () {\r\n\t\t\tthis.weekday = this.checkNum(this.weekday, 1, 7)\r\n\t\t\treturn this.weekday;\r\n\t\t},\r\n\t\t// 计算勾选的checkbox值合集\r\n\t\tcheckboxString: function () {\r\n\t\t\tlet str = this.checkboxList.join();\r\n\t\t\treturn str == '' ? '*' : str;\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n"]}]}