{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\core\\emitter.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\core\\emitter.js", "mtime": 1749104421592}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_eventemitter", "require", "_instances", "_interopRequireDefault", "_logger", "debug", "logger", "EVENTS", "for<PERSON>ach", "eventName", "document", "addEventListener", "_len", "arguments", "length", "args", "Array", "_key", "from", "querySelectorAll", "node", "quill", "instances", "get", "emitter", "_quill$emitter", "handleDOM", "apply", "Emitter", "_EventEmitter", "_this", "_classCallCheck2", "default", "_callSuper2", "domListeners", "on", "error", "_inherits2", "_createClass2", "key", "value", "emit", "_debug$log", "_len2", "_key2", "log", "call", "concat", "_superPropGet2", "event", "_len3", "_key3", "type", "_ref", "handler", "target", "contains", "listenDOM", "push", "EventEmitter", "_defineProperty2", "EDITOR_CHANGE", "SCROLL_BEFORE_UPDATE", "SCROLL_BLOT_MOUNT", "SCROLL_BLOT_UNMOUNT", "SCROLL_OPTIMIZE", "SCROLL_UPDATE", "SCROLL_EMBED_UPDATE", "SELECTION_CHANGE", "TEXT_CHANGE", "COMPOSITION_BEFORE_START", "COMPOSITION_START", "COMPOSITION_BEFORE_END", "COMPOSITION_END", "API", "SILENT", "USER", "_default", "exports"], "sources": ["../../src/core/emitter.ts"], "sourcesContent": ["import { EventEmitter } from 'eventemitter3';\nimport instances from './instances.js';\nimport logger from './logger.js';\n\nconst debug = logger('quill:events');\nconst EVENTS = ['selectionchange', 'mousedown', 'mouseup', 'click'];\n\nEVENTS.forEach((eventName) => {\n  document.addEventListener(eventName, (...args) => {\n    Array.from(document.querySelectorAll('.ql-container')).forEach((node) => {\n      const quill = instances.get(node);\n      if (quill && quill.emitter) {\n        quill.emitter.handleDOM(...args);\n      }\n    });\n  });\n});\n\nclass Emitter extends EventEmitter<string> {\n  static events = {\n    EDITOR_CHANGE: 'editor-change',\n    SCROLL_BEFORE_UPDATE: 'scroll-before-update',\n    SCROLL_BLOT_MOUNT: 'scroll-blot-mount',\n    SCROLL_BLOT_UNMOUNT: 'scroll-blot-unmount',\n    SCROLL_OPTIMIZE: 'scroll-optimize',\n    SCROLL_UPDATE: 'scroll-update',\n    SCROLL_EMBED_UPDATE: 'scroll-embed-update',\n    SELECTION_CHANGE: 'selection-change',\n    TEXT_CHANGE: 'text-change',\n    COMPOSITION_BEFORE_START: 'composition-before-start',\n    COMPOSITION_START: 'composition-start',\n    COMPOSITION_BEFORE_END: 'composition-before-end',\n    COMPOSITION_END: 'composition-end',\n  } as const;\n\n  static sources = {\n    API: 'api',\n    SILENT: 'silent',\n    USER: 'user',\n  } as const;\n\n  protected domListeners: Record<string, { node: Node; handler: Function }[]>;\n\n  constructor() {\n    super();\n    this.domListeners = {};\n    this.on('error', debug.error);\n  }\n\n  emit(...args: unknown[]): boolean {\n    debug.log.call(debug, ...args);\n    // @ts-expect-error\n    return super.emit(...args);\n  }\n\n  handleDOM(event: Event, ...args: unknown[]) {\n    (this.domListeners[event.type] || []).forEach(({ node, handler }) => {\n      if (event.target === node || node.contains(event.target as Node)) {\n        handler(event, ...args);\n      }\n    });\n  }\n\n  listenDOM(eventName: string, node: Node, handler: EventListener) {\n    if (!this.domListeners[eventName]) {\n      this.domListeners[eventName] = [];\n    }\n    this.domListeners[eventName].push({ node, handler });\n  }\n}\n\nexport type EmitterSource =\n  (typeof Emitter.sources)[keyof typeof Emitter.sources];\n\nexport default Emitter;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,aAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,OAAA,GAAAD,sBAAA,CAAAF,OAAA;AAEA,IAAMI,KAAK,GAAG,IAAAC,eAAM,EAAC,cAAc,CAAC;AACpC,IAAMC,MAAM,GAAG,CAAC,iBAAiB,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC;AAEnEA,MAAM,CAACC,OAAO,CAAE,UAAAC,SAAS,EAAK;EAC5BC,QAAQ,CAACC,gBAAgB,CAACF,SAAS,EAAE,YAAa;IAAA,SAAAG,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAATC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAC3CD,KAAK,CAACE,IAAI,CAACR,QAAQ,CAACS,gBAAgB,CAAC,eAAe,CAAC,CAAC,CAACX,OAAO,CAAE,UAAAY,IAAI,EAAK;MACvE,IAAMC,KAAK,GAAGC,kBAAS,CAACC,GAAG,CAACH,IAAI,CAAC;MACjC,IAAIC,KAAK,IAAIA,KAAK,CAACG,OAAO,EAAE;QAAA,IAAAC,cAAA;QAC1B,CAAAA,cAAA,GAAAJ,KAAK,CAACG,OAAO,EAACE,SAAS,CAAAC,KAAA,CAAAF,cAAA,EAAIV,IAAI,CAAC;MAClC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AAAA,IAEIa,OAAO,0BAAAC,aAAA;EAyBX,SAAAD,QAAA,EAAc;IAAA,IAAAE,KAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAJ,OAAA;IACZE,KAAA,OAAAG,WAAA,CAAAD,OAAA,QAAAJ,OAAA;IACAE,KAAA,CAAKI,YAAY,GAAG,CAAC,CAAC;IACtBJ,KAAA,CAAKK,EAAE,CAAC,OAAO,EAAE9B,KAAK,CAAC+B,KAAK,CAAC;IAAA,OAAAN,KAAA;EAC/B;EAAA,IAAAO,UAAA,CAAAL,OAAA,EAAAJ,OAAA,EAAAC,aAAA;EAAA,WAAAS,aAAA,CAAAN,OAAA,EAAAJ,OAAA;IAAAW,GAAA;IAAAC,KAAA,EAEA,SAAAC,IAAIA,CAAA,EAA8B;MAAA,IAAAC,UAAA;MAAA,SAAAC,KAAA,GAAA9B,SAAA,CAAAC,MAAA,EAA1BC,IAAI,OAAAC,KAAA,CAAA2B,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAAJ7B,IAAI,CAAA6B,KAAA,IAAA/B,SAAA,CAAA+B,KAAA;MAAA;MACV,CAAAF,UAAA,GAAArC,KAAK,CAACwC,GAAG,EAACC,IAAI,CAAAnB,KAAA,CAAAe,UAAA,GAACrC,KAAK,EAAA0C,MAAA,CAAKhC,IAAI,EAAC;MAC9B;MACA,WAAAiC,cAAA,CAAAhB,OAAA,EAAAJ,OAAA,mBAAqBb,IAAI;IAC3B;EAAA;IAAAwB,GAAA;IAAAC,KAAA,EAEA,SAAAd,SAASA,CAACuB,KAAY,EAAsB;MAAA,SAAAC,KAAA,GAAArC,SAAA,CAAAC,MAAA,EAAjBC,IAAI,OAAAC,KAAA,CAAAkC,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAAJpC,IAAI,CAAAoC,KAAA,QAAAtC,SAAA,CAAAsC,KAAA;MAAA;MAC7B,CAAC,IAAI,CAACjB,YAAY,CAACe,KAAK,CAACG,IAAI,CAAC,IAAI,EAAE,EAAE5C,OAAO,CAAC,UAAA6C,IAAA,EAAuB;QAAA,IAApBjC,IAAI,GAAWiC,IAAA,CAAfjC,IAAI;UAAEkC,OAAA,GAASD,IAAA,CAATC,OAAA;QACrD,IAAIL,KAAK,CAACM,MAAM,KAAKnC,IAAI,IAAIA,IAAI,CAACoC,QAAQ,CAACP,KAAK,CAACM,MAAc,CAAC,EAAE;UAChED,OAAO,CAAA3B,KAAA,UAACsB,KAAK,EAAAF,MAAA,CAAKhC,IAAI,EAAC;QACzB;MACF,CAAC,CAAC;IACJ;EAAA;IAAAwB,GAAA;IAAAC,KAAA,EAEA,SAAAiB,SAASA,CAAChD,SAAiB,EAAEW,IAAU,EAAEkC,OAAsB,EAAE;MAC/D,IAAI,CAAC,IAAI,CAACpB,YAAY,CAACzB,SAAS,CAAC,EAAE;QACjC,IAAI,CAACyB,YAAY,CAACzB,SAAS,CAAC,GAAG,EAAE;MACnC;MACA,IAAI,CAACyB,YAAY,CAACzB,SAAS,CAAC,CAACiD,IAAI,CAAC;QAAEtC,IAAI,EAAJA,IAAI;QAAEkC,OAAA,EAAAA;MAAQ,CAAC,CAAC;IACtD;EAAA;AAAA,EAlDoBK,0BAAY;AAAA,IAAAC,gBAAA,CAAA5B,OAAA,EAA5BJ,OAAO,YACK;EACdiC,aAAa,EAAE,eAAe;EAC9BC,oBAAoB,EAAE,sBAAsB;EAC5CC,iBAAiB,EAAE,mBAAmB;EACtCC,mBAAmB,EAAE,qBAAqB;EAC1CC,eAAe,EAAE,iBAAiB;EAClCC,aAAa,EAAE,eAAe;EAC9BC,mBAAmB,EAAE,qBAAqB;EAC1CC,gBAAgB,EAAE,kBAAkB;EACpCC,WAAW,EAAE,aAAa;EAC1BC,wBAAwB,EAAE,0BAA0B;EACpDC,iBAAiB,EAAE,mBAAmB;EACtCC,sBAAsB,EAAE,wBAAwB;EAChDC,eAAe,EAAE;AACnB,CAAC;AAAA,IAAAb,gBAAA,CAAA5B,OAAA,EAfGJ,OAAO,aAiBM;EACf8C,GAAG,EAAE,KAAK;EACVC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE;AACR,CAAC;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAA9C,OAAA,GAmCYJ,OAAO", "ignoreList": []}]}