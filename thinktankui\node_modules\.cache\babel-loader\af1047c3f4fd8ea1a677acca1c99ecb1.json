{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\modules\\table.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\modules\\table.js", "mtime": 1749104422724}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_quill<PERSON><PERSON><PERSON>", "_interopRequireDefault", "require", "_quill", "_module", "_table", "Table", "_Module", "_this", "_classCallCheck2", "default", "_callSuper2", "arguments", "listenBalanceCells", "_inherits2", "_createClass2", "key", "value", "balanceTables", "quill", "scroll", "descendants", "TableContainer", "for<PERSON>ach", "table", "balanceCells", "deleteColumn", "_this$getTable", "getTable", "_this$getTable2", "_slicedToArray2", "cell", "cellOffset", "update", "<PERSON><PERSON><PERSON>", "sources", "USER", "deleteRow", "_this$getTable3", "_this$getTable4", "row", "remove", "deleteTable", "_this$getTable5", "_this$getTable6", "offset", "setSelection", "SILENT", "range", "length", "undefined", "getSelection", "_this$quill$getLine", "getLine", "index", "_this$quill$getLine2", "statics", "blotName", "TableCell", "parent", "insertColumn", "_this$getTable7", "_this$getTable8", "column", "shift", "rowOffset", "insertColumnLeft", "insertColumnRight", "insertRow", "_this$getTable9", "_this$getTable0", "children", "insertRowAbove", "insertRowBelow", "insertTable", "rows", "columns", "delta", "Array", "fill", "reduce", "memo", "text", "join", "insert", "tableId", "Delta", "retain", "updateContents", "_this2", "on", "events", "SCROLL_OPTIMIZE", "mutations", "some", "mutation", "includes", "target", "tagName", "once", "TEXT_CHANGE", "old", "source", "register", "TableRow", "TableBody", "<PERSON><PERSON><PERSON>", "_default", "exports"], "sources": ["../../src/modules/table.ts"], "sourcesContent": ["import Delta from 'quill-delta';\nimport Quill from '../core/quill.js';\nimport Module from '../core/module.js';\nimport {\n  TableCell,\n  TableRow,\n  TableBody,\n  TableContainer,\n  tableId,\n} from '../formats/table.js';\n\nclass Table extends Module {\n  static register() {\n    Quill.register(TableCell);\n    Quill.register(TableRow);\n    Quill.register(TableBody);\n    Quill.register(TableContainer);\n  }\n\n  constructor(...args: ConstructorParameters<typeof Module>) {\n    super(...args);\n    this.listenBalanceCells();\n  }\n\n  balanceTables() {\n    this.quill.scroll.descendants(TableContainer).forEach((table) => {\n      table.balanceCells();\n    });\n  }\n\n  deleteColumn() {\n    const [table, , cell] = this.getTable();\n    if (cell == null) return;\n    // @ts-expect-error\n    table.deleteColumn(cell.cellOffset());\n    this.quill.update(Quill.sources.USER);\n  }\n\n  deleteRow() {\n    const [, row] = this.getTable();\n    if (row == null) return;\n    row.remove();\n    this.quill.update(Quill.sources.USER);\n  }\n\n  deleteTable() {\n    const [table] = this.getTable();\n    if (table == null) return;\n    // @ts-expect-error\n    const offset = table.offset();\n    // @ts-expect-error\n    table.remove();\n    this.quill.update(Quill.sources.USER);\n    this.quill.setSelection(offset, Quill.sources.SILENT);\n  }\n\n  getTable(\n    range = this.quill.getSelection(),\n  ): [null, null, null, -1] | [Table, TableRow, TableCell, number] {\n    if (range == null) return [null, null, null, -1];\n    const [cell, offset] = this.quill.getLine(range.index);\n    if (cell == null || cell.statics.blotName !== TableCell.blotName) {\n      return [null, null, null, -1];\n    }\n    const row = cell.parent;\n    const table = row.parent.parent;\n    // @ts-expect-error\n    return [table, row, cell, offset];\n  }\n\n  insertColumn(offset: number) {\n    const range = this.quill.getSelection();\n    if (!range) return;\n    const [table, row, cell] = this.getTable(range);\n    if (cell == null) return;\n    const column = cell.cellOffset();\n    table.insertColumn(column + offset);\n    this.quill.update(Quill.sources.USER);\n    let shift = row.rowOffset();\n    if (offset === 0) {\n      shift += 1;\n    }\n    this.quill.setSelection(\n      range.index + shift,\n      range.length,\n      Quill.sources.SILENT,\n    );\n  }\n\n  insertColumnLeft() {\n    this.insertColumn(0);\n  }\n\n  insertColumnRight() {\n    this.insertColumn(1);\n  }\n\n  insertRow(offset: number) {\n    const range = this.quill.getSelection();\n    if (!range) return;\n    const [table, row, cell] = this.getTable(range);\n    if (cell == null) return;\n    const index = row.rowOffset();\n    table.insertRow(index + offset);\n    this.quill.update(Quill.sources.USER);\n    if (offset > 0) {\n      this.quill.setSelection(range, Quill.sources.SILENT);\n    } else {\n      this.quill.setSelection(\n        range.index + row.children.length,\n        range.length,\n        Quill.sources.SILENT,\n      );\n    }\n  }\n\n  insertRowAbove() {\n    this.insertRow(0);\n  }\n\n  insertRowBelow() {\n    this.insertRow(1);\n  }\n\n  insertTable(rows: number, columns: number) {\n    const range = this.quill.getSelection();\n    if (range == null) return;\n    const delta = new Array(rows).fill(0).reduce((memo) => {\n      const text = new Array(columns).fill('\\n').join('');\n      return memo.insert(text, { table: tableId() });\n    }, new Delta().retain(range.index));\n    this.quill.updateContents(delta, Quill.sources.USER);\n    this.quill.setSelection(range.index, Quill.sources.SILENT);\n    this.balanceTables();\n  }\n\n  listenBalanceCells() {\n    this.quill.on(\n      Quill.events.SCROLL_OPTIMIZE,\n      (mutations: MutationRecord[]) => {\n        mutations.some((mutation) => {\n          if (\n            ['TD', 'TR', 'TBODY', 'TABLE'].includes(\n              (mutation.target as HTMLElement).tagName,\n            )\n          ) {\n            this.quill.once(Quill.events.TEXT_CHANGE, (delta, old, source) => {\n              if (source !== Quill.sources.USER) return;\n              this.balanceTables();\n            });\n            return true;\n          }\n          return false;\n        });\n      },\n    );\n  }\n}\n\nexport default Table;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,OAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAM4B,IAEtBI,KAAK,0BAAAC,OAAA;EAQT,SAAAD,MAAA,EAA2D;IAAA,IAAAE,KAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAJ,KAAA;IACzDE,KAAA,OAAAG,WAAA,CAAAD,OAAA,QAAAJ,KAAA,EAAMM,SAAO;IACbJ,KAAA,CAAKK,kBAAkB,CAAC,CAAC;IAAA,OAAAL,KAAA;EAC3B;EAAA,IAAAM,UAAA,CAAAJ,OAAA,EAAAJ,KAAA,EAAAC,OAAA;EAAA,WAAAQ,aAAA,CAAAL,OAAA,EAAAJ,KAAA;IAAAU,GAAA;IAAAC,KAAA,EAEA,SAAAC,aAAaA,CAAA,EAAG;MACd,IAAI,CAACC,KAAK,CAACC,MAAM,CAACC,WAAW,CAACC,qBAAc,CAAC,CAACC,OAAO,CAAE,UAAAC,KAAK,EAAK;QAC/DA,KAAK,CAACC,YAAY,CAAC,CAAC;MACtB,CAAC,CAAC;IACJ;EAAA;IAAAT,GAAA;IAAAC,KAAA,EAEA,SAAAS,YAAYA,CAAA,EAAG;MACb,IAAAC,cAAA,GAAwB,IAAI,CAACC,QAAQ,CAAC,CAAC;QAAAC,eAAA,OAAAC,eAAA,CAAApB,OAAA,EAAAiB,cAAA;QAAhCH,KAAK,GAAAK,eAAA;QAAIE,IAAI,GAAAF,eAAA;MACpB,IAAIE,IAAI,IAAI,IAAI,EAAE;MAClB;MACAP,KAAK,CAACE,YAAY,CAACK,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC;MACrC,IAAI,CAACb,KAAK,CAACc,MAAM,CAACC,cAAK,CAACC,OAAO,CAACC,IAAI,CAAC;IACvC;EAAA;IAAApB,GAAA;IAAAC,KAAA,EAEA,SAAAoB,SAASA,CAAA,EAAG;MACV,IAAAC,eAAA,GAAgB,IAAI,CAACV,QAAQ,CAAC,CAAC;QAAAW,eAAA,OAAAT,eAAA,CAAApB,OAAA,EAAA4B,eAAA;QAAtBE,GAAG,GAAAD,eAAA;MACZ,IAAIC,GAAG,IAAI,IAAI,EAAE;MACjBA,GAAG,CAACC,MAAM,CAAC,CAAC;MACZ,IAAI,CAACtB,KAAK,CAACc,MAAM,CAACC,cAAK,CAACC,OAAO,CAACC,IAAI,CAAC;IACvC;EAAA;IAAApB,GAAA;IAAAC,KAAA,EAEA,SAAAyB,WAAWA,CAAA,EAAG;MACZ,IAAAC,eAAA,GAAgB,IAAI,CAACf,QAAQ,CAAC,CAAC;QAAAgB,eAAA,OAAAd,eAAA,CAAApB,OAAA,EAAAiC,eAAA;QAAxBnB,KAAK,GAAAoB,eAAA;MACZ,IAAIpB,KAAK,IAAI,IAAI,EAAE;MACnB;MACA,IAAMqB,MAAM,GAAGrB,KAAK,CAACqB,MAAM,CAAC,CAAC;MAC7B;MACArB,KAAK,CAACiB,MAAM,CAAC,CAAC;MACd,IAAI,CAACtB,KAAK,CAACc,MAAM,CAACC,cAAK,CAACC,OAAO,CAACC,IAAI,CAAC;MACrC,IAAI,CAACjB,KAAK,CAAC2B,YAAY,CAACD,MAAM,EAAEX,cAAK,CAACC,OAAO,CAACY,MAAM,CAAC;IACvD;EAAA;IAAA/B,GAAA;IAAAC,KAAA,EAEA,SAAAW,QAAQA,CAAA,EAEyD;MAAA,IAD/DoB,KAAK,GAAApC,SAAA,CAAAqC,MAAA,QAAArC,SAAA,QAAAsC,SAAA,GAAAtC,SAAA,MAAG,IAAI,CAACO,KAAK,CAACgC,YAAY,CAAC,CAAC;MAEjC,IAAIH,KAAK,IAAI,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MAChD,IAAAI,mBAAA,GAAuB,IAAI,CAACjC,KAAK,CAACkC,OAAO,CAACL,KAAK,CAACM,KAAK,CAAC;QAAAC,oBAAA,OAAAzB,eAAA,CAAApB,OAAA,EAAA0C,mBAAA;QAA/CrB,IAAI,GAAAwB,oBAAA;QAAEV,MAAM,GAAAU,oBAAA;MACnB,IAAIxB,IAAI,IAAI,IAAI,IAAIA,IAAI,CAACyB,OAAO,CAACC,QAAQ,KAAKC,gBAAS,CAACD,QAAQ,EAAE;QAChE,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MAC/B;MACA,IAAMjB,GAAG,GAAGT,IAAI,CAAC4B,MAAM;MACvB,IAAMnC,KAAK,GAAGgB,GAAG,CAACmB,MAAM,CAACA,MAAM;MAC/B;MACA,OAAO,CAACnC,KAAK,EAAEgB,GAAG,EAAET,IAAI,EAAEc,MAAM,CAAC;IACnC;EAAA;IAAA7B,GAAA;IAAAC,KAAA,EAEA,SAAA2C,YAAYA,CAACf,MAAc,EAAE;MAC3B,IAAMG,KAAK,GAAG,IAAI,CAAC7B,KAAK,CAACgC,YAAY,CAAC,CAAC;MACvC,IAAI,CAACH,KAAK,EAAE;MACZ,IAAAa,eAAA,GAA2B,IAAI,CAACjC,QAAQ,CAACoB,KAAK,CAAC;QAAAc,eAAA,OAAAhC,eAAA,CAAApB,OAAA,EAAAmD,eAAA;QAAxCrC,KAAK,GAAAsC,eAAA;QAAEtB,GAAG,GAAAsB,eAAA;QAAE/B,IAAI,GAAA+B,eAAA;MACvB,IAAI/B,IAAI,IAAI,IAAI,EAAE;MAClB,IAAMgC,MAAM,GAAGhC,IAAI,CAACC,UAAU,CAAC,CAAC;MAChCR,KAAK,CAACoC,YAAY,CAACG,MAAM,GAAGlB,MAAM,CAAC;MACnC,IAAI,CAAC1B,KAAK,CAACc,MAAM,CAACC,cAAK,CAACC,OAAO,CAACC,IAAI,CAAC;MACrC,IAAI4B,KAAK,GAAGxB,GAAG,CAACyB,SAAS,CAAC,CAAC;MAC3B,IAAIpB,MAAM,KAAK,CAAC,EAAE;QAChBmB,KAAK,IAAI,CAAC;MACZ;MACA,IAAI,CAAC7C,KAAK,CAAC2B,YAAY,CACrBE,KAAK,CAACM,KAAK,GAAGU,KAAK,EACnBhB,KAAK,CAACC,MAAM,EACZf,cAAK,CAACC,OAAO,CAACY,MAChB,CAAC;IACH;EAAA;IAAA/B,GAAA;IAAAC,KAAA,EAEA,SAAAiD,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACN,YAAY,CAAC,CAAC,CAAC;IACtB;EAAA;IAAA5C,GAAA;IAAAC,KAAA,EAEA,SAAAkD,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAACP,YAAY,CAAC,CAAC,CAAC;IACtB;EAAA;IAAA5C,GAAA;IAAAC,KAAA,EAEA,SAAAmD,SAASA,CAACvB,MAAc,EAAE;MACxB,IAAMG,KAAK,GAAG,IAAI,CAAC7B,KAAK,CAACgC,YAAY,CAAC,CAAC;MACvC,IAAI,CAACH,KAAK,EAAE;MACZ,IAAAqB,eAAA,GAA2B,IAAI,CAACzC,QAAQ,CAACoB,KAAK,CAAC;QAAAsB,eAAA,OAAAxC,eAAA,CAAApB,OAAA,EAAA2D,eAAA;QAAxC7C,KAAK,GAAA8C,eAAA;QAAE9B,GAAG,GAAA8B,eAAA;QAAEvC,IAAI,GAAAuC,eAAA;MACvB,IAAIvC,IAAI,IAAI,IAAI,EAAE;MAClB,IAAMuB,KAAK,GAAGd,GAAG,CAACyB,SAAS,CAAC,CAAC;MAC7BzC,KAAK,CAAC4C,SAAS,CAACd,KAAK,GAAGT,MAAM,CAAC;MAC/B,IAAI,CAAC1B,KAAK,CAACc,MAAM,CAACC,cAAK,CAACC,OAAO,CAACC,IAAI,CAAC;MACrC,IAAIS,MAAM,GAAG,CAAC,EAAE;QACd,IAAI,CAAC1B,KAAK,CAAC2B,YAAY,CAACE,KAAK,EAAEd,cAAK,CAACC,OAAO,CAACY,MAAM,CAAC;MACtD,CAAC,MAAM;QACL,IAAI,CAAC5B,KAAK,CAAC2B,YAAY,CACrBE,KAAK,CAACM,KAAK,GAAGd,GAAG,CAAC+B,QAAQ,CAACtB,MAAM,EACjCD,KAAK,CAACC,MAAM,EACZf,cAAK,CAACC,OAAO,CAACY,MAChB,CAAC;MACH;IACF;EAAA;IAAA/B,GAAA;IAAAC,KAAA,EAEA,SAAAuD,cAAcA,CAAA,EAAG;MACf,IAAI,CAACJ,SAAS,CAAC,CAAC,CAAC;IACnB;EAAA;IAAApD,GAAA;IAAAC,KAAA,EAEA,SAAAwD,cAAcA,CAAA,EAAG;MACf,IAAI,CAACL,SAAS,CAAC,CAAC,CAAC;IACnB;EAAA;IAAApD,GAAA;IAAAC,KAAA,EAEA,SAAAyD,WAAWA,CAACC,IAAY,EAAEC,OAAe,EAAE;MACzC,IAAM5B,KAAK,GAAG,IAAI,CAAC7B,KAAK,CAACgC,YAAY,CAAC,CAAC;MACvC,IAAIH,KAAK,IAAI,IAAI,EAAE;MACnB,IAAM6B,KAAK,GAAG,IAAIC,KAAK,CAACH,IAAI,CAAC,CAACI,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,CAAE,UAAAC,IAAI,EAAK;QACrD,IAAMC,IAAI,GAAG,IAAIJ,KAAK,CAACF,OAAO,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC,CAACI,IAAI,CAAC,EAAE,CAAC;QACnD,OAAOF,IAAI,CAACG,MAAM,CAACF,IAAI,EAAE;UAAE1D,KAAK,EAAE,IAAA6D,cAAO,EAAC;QAAE,CAAC,CAAC;MAChD,CAAC,EAAE,IAAIC,mBAAK,CAAC,CAAC,CAACC,MAAM,CAACvC,KAAK,CAACM,KAAK,CAAC,CAAC;MACnC,IAAI,CAACnC,KAAK,CAACqE,cAAc,CAACX,KAAK,EAAE3C,cAAK,CAACC,OAAO,CAACC,IAAI,CAAC;MACpD,IAAI,CAACjB,KAAK,CAAC2B,YAAY,CAACE,KAAK,CAACM,KAAK,EAAEpB,cAAK,CAACC,OAAO,CAACY,MAAM,CAAC;MAC1D,IAAI,CAAC7B,aAAa,CAAC,CAAC;IACtB;EAAA;IAAAF,GAAA;IAAAC,KAAA,EAEA,SAAAJ,kBAAkBA,CAAA,EAAG;MAAA,IAAA4E,MAAA;MACnB,IAAI,CAACtE,KAAK,CAACuE,EAAE,CACXxD,cAAK,CAACyD,MAAM,CAACC,eAAe,EAC3B,UAAAC,SAA2B,EAAK;QAC/BA,SAAS,CAACC,IAAI,CAAE,UAAAC,QAAQ,EAAK;UAC3B,IACE,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAACC,QAAQ,CACpCD,QAAQ,CAACE,MAAM,CAAiBC,OACnC,CAAC,EACD;YACAT,MAAI,CAACtE,KAAK,CAACgF,IAAI,CAACjE,cAAK,CAACyD,MAAM,CAACS,WAAW,EAAE,UAACvB,KAAK,EAAEwB,GAAG,EAAEC,MAAM,EAAK;cAChE,IAAIA,MAAM,KAAKpE,cAAK,CAACC,OAAO,CAACC,IAAI,EAAE;cACnCqD,MAAI,CAACvE,aAAa,CAAC,CAAC;YACtB,CAAC,CAAC;YACF,OAAO,IAAI;UACb;UACA,OAAO,KAAK;QACd,CAAC,CAAC;MACJ,CACF,CAAC;IACH;EAAA;IAAAF,GAAA;IAAAC,KAAA,EAhJA,SAAOsF,QAAQA,CAAA,EAAG;MAChBrE,cAAK,CAACqE,QAAQ,CAAC7C,gBAAS,CAAC;MACzBxB,cAAK,CAACqE,QAAQ,CAACC,eAAQ,CAAC;MACxBtE,cAAK,CAACqE,QAAQ,CAACE,gBAAS,CAAC;MACzBvE,cAAK,CAACqE,QAAQ,CAACjF,qBAAc,CAAC;IAChC;EAAA;AAAA,EANkBoF,eAAM;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAlG,OAAA,GAoJXJ,KAAK", "ignoreList": []}]}