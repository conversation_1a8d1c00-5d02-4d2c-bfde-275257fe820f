{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\IconSelect\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\IconSelect\\index.vue", "mtime": 1749104047621}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5maWx0ZXIuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmluY2x1ZGVzLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5mdW5jdGlvbi5uYW1lLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaW5jbHVkZXMuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5jb25zdHJ1Y3Rvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmZpbHRlci5qcyIpOwp2YXIgX3JlcXVpcmVJY29ucyA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9yZXF1aXJlSWNvbnMiKSk7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAnSWNvblNlbGVjdCcsCiAgcHJvcHM6IHsKICAgIGFjdGl2ZUljb246IHsKICAgICAgdHlwZTogU3RyaW5nCiAgICB9CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbmFtZTogJycsCiAgICAgIGljb25MaXN0OiBfcmVxdWlyZUljb25zLmRlZmF1bHQKICAgIH07CiAgfSwKICBtZXRob2RzOiB7CiAgICBmaWx0ZXJJY29uczogZnVuY3Rpb24gZmlsdGVySWNvbnMoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHRoaXMuaWNvbkxpc3QgPSBfcmVxdWlyZUljb25zLmRlZmF1bHQ7CiAgICAgIGlmICh0aGlzLm5hbWUpIHsKICAgICAgICB0aGlzLmljb25MaXN0ID0gdGhpcy5pY29uTGlzdC5maWx0ZXIoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgIHJldHVybiBpdGVtLmluY2x1ZGVzKF90aGlzLm5hbWUpOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgc2VsZWN0ZWRJY29uOiBmdW5jdGlvbiBzZWxlY3RlZEljb24obmFtZSkgewogICAgICB0aGlzLiRlbWl0KCdzZWxlY3RlZCcsIG5hbWUpOwogICAgICBkb2N1bWVudC5ib2R5LmNsaWNrKCk7CiAgICB9LAogICAgcmVzZXQ6IGZ1bmN0aW9uIHJlc2V0KCkgewogICAgICB0aGlzLm5hbWUgPSAnJzsKICAgICAgdGhpcy5pY29uTGlzdCA9IF9yZXF1aXJlSWNvbnMuZGVmYXVsdDsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_requireIcons", "_interopRequireDefault", "require", "name", "props", "activeIcon", "type", "String", "data", "iconList", "icons", "methods", "filterIcons", "_this", "filter", "item", "includes", "selectedIcon", "$emit", "document", "body", "click", "reset"], "sources": ["src/components/IconSelect/index.vue"], "sourcesContent": ["<!-- <AUTHOR> -->\r\n<template>\r\n  <div class=\"icon-body\">\r\n    <el-input v-model=\"name\" class=\"icon-search\" clearable placeholder=\"请输入图标名称\" @clear=\"filterIcons\" @input=\"filterIcons\">\r\n      <i slot=\"suffix\" class=\"el-icon-search el-input__icon\" />\r\n    </el-input>\r\n    <div class=\"icon-list\">\r\n      <div class=\"list-container\">\r\n        <div v-for=\"(item, index) in iconList\" class=\"icon-item-wrapper\" :key=\"index\" @click=\"selectedIcon(item)\">\r\n          <div :class=\"['icon-item', { active: activeIcon === item }]\">\r\n            <svg-icon :icon-class=\"item\" class-name=\"icon\" style=\"height: 25px;width: 16px;\"/>\r\n            <span>{{ item }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport icons from './requireIcons'\r\nexport default {\r\n  name: 'IconSelect',\r\n  props: {\r\n    activeIcon: {\r\n      type: String\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      name: '',\r\n      iconList: icons\r\n    }\r\n  },\r\n  methods: {\r\n    filterIcons() {\r\n      this.iconList = icons\r\n      if (this.name) {\r\n        this.iconList = this.iconList.filter(item => item.includes(this.name))\r\n      }\r\n    },\r\n    selectedIcon(name) {\r\n      this.$emit('selected', name)\r\n      document.body.click()\r\n    },\r\n    reset() {\r\n      this.name = ''\r\n      this.iconList = icons\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style rel=\"stylesheet/scss\" lang=\"scss\" scoped>\r\n  .icon-body {\r\n    width: 100%;\r\n    padding: 10px;\r\n    .icon-search {\r\n      position: relative;\r\n      margin-bottom: 5px;\r\n    }\r\n    .icon-list {\r\n      height: 200px;\r\n      overflow: auto;\r\n      .list-container {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        .icon-item-wrapper {\r\n          width: calc(100% / 3);\r\n          height: 25px;\r\n          line-height: 25px;\r\n          cursor: pointer;\r\n          display: flex;\r\n          .icon-item {\r\n            display: flex;\r\n            max-width: 100%;\r\n            height: 100%;\r\n            padding: 0 5px;\r\n            &:hover {\r\n              background: #ececec;\r\n              border-radius: 5px;\r\n            }\r\n            .icon {\r\n              flex-shrink: 0;\r\n            }\r\n            span {\r\n              display: inline-block;\r\n              vertical-align: -0.15em;\r\n              fill: currentColor;\r\n              padding-left: 2px;\r\n              overflow: hidden;\r\n              text-overflow: ellipsis;\r\n              white-space: nowrap;\r\n            }\r\n          }\r\n          .icon-item.active {\r\n            background: #ececec;\r\n            border-radius: 5px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;AAoBA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;iCACA;EACAC,IAAA;EACAC,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAL,IAAA;MACAM,QAAA,EAAAC;IACA;EACA;EACAC,OAAA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,KAAA;MACA,KAAAJ,QAAA,GAAAC,qBAAA;MACA,SAAAP,IAAA;QACA,KAAAM,QAAA,QAAAA,QAAA,CAAAK,MAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,QAAA,CAAAH,KAAA,CAAAV,IAAA;QAAA;MACA;IACA;IACAc,YAAA,WAAAA,aAAAd,IAAA;MACA,KAAAe,KAAA,aAAAf,IAAA;MACAgB,QAAA,CAAAC,IAAA,CAAAC,KAAA;IACA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAAnB,IAAA;MACA,KAAAM,QAAA,GAAAC,qBAAA;IACA;EACA;AACA", "ignoreList": []}]}