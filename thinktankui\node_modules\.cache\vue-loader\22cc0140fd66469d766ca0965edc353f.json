{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\login.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\login.vue", "mtime": 1749104047640}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRDb2RlSW1nIH0gZnJvbSAiQC9hcGkvbG9naW4iOw0KaW1wb3J0IENvb2tpZXMgZnJvbSAianMtY29va2llIjsNCmltcG9ydCB7IGVuY3J5cHQsIGRlY3J5cHQgfSBmcm9tICdAL3V0aWxzL2pzZW5jcnlwdCcNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiTG9naW4iLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBjb2RlVXJsOiAiIiwNCiAgICAgIGxvZ2luRm9ybTogew0KICAgICAgICB1c2VybmFtZTogIiIsDQogICAgICAgIHBhc3N3b3JkOiAiIiwNCiAgICAgICAgcmVtZW1iZXJNZTogZmFsc2UsDQogICAgICAgIGNvZGU6ICIiLA0KICAgICAgICB1dWlkOiAiIg0KICAgICAgfSwNCiAgICAgIGxvZ2luUnVsZXM6IHsNCiAgICAgICAgdXNlcm5hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCB0cmlnZ2VyOiAiYmx1ciIsIG1lc3NhZ2U6ICLor7fovpPlhaXmgqjnmoTotKblj7ciIH0NCiAgICAgICAgXSwNCiAgICAgICAgcGFzc3dvcmQ6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCB0cmlnZ2VyOiAiYmx1ciIsIG1lc3NhZ2U6ICLor7fovpPlhaXmgqjnmoTlr4bnoIEiIH0NCiAgICAgICAgXSwNCiAgICAgICAgY29kZTogW3sgcmVxdWlyZWQ6IHRydWUsIHRyaWdnZXI6ICJjaGFuZ2UiLCBtZXNzYWdlOiAi6K+36L6T5YWl6aqM6K+B56CBIiB9XQ0KICAgICAgfSwNCiAgICAgIGxvYWRpbmc6IGZhbHNlLA0KICAgICAgLy8g6aqM6K+B56CB5byA5YWzDQogICAgICBjYXB0Y2hhRW5hYmxlZDogdHJ1ZSwNCiAgICAgIC8vIOazqOWGjOW8gOWFsw0KICAgICAgcmVnaXN0ZXI6IGZhbHNlLA0KICAgICAgcmVkaXJlY3Q6IHVuZGVmaW5lZA0KICAgIH07DQogIH0sDQogIHdhdGNoOiB7DQogICAgJHJvdXRlOiB7DQogICAgICBoYW5kbGVyOiBmdW5jdGlvbihyb3V0ZSkgew0KICAgICAgICB0aGlzLnJlZGlyZWN0ID0gcm91dGUucXVlcnkgJiYgcm91dGUucXVlcnkucmVkaXJlY3Q7DQogICAgICB9LA0KICAgICAgaW1tZWRpYXRlOiB0cnVlDQogICAgfQ0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0Q29kZSgpOw0KICAgIHRoaXMuZ2V0Q29va2llKCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBnZXRDb2RlKCkgew0KICAgICAgZ2V0Q29kZUltZygpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgdGhpcy5jYXB0Y2hhRW5hYmxlZCA9IHJlcy5jYXB0Y2hhRW5hYmxlZCA9PT0gdW5kZWZpbmVkID8gdHJ1ZSA6IHJlcy5jYXB0Y2hhRW5hYmxlZDsNCiAgICAgICAgdGhpcy5yZWdpc3RlciA9IHJlcy5yZWdpc3RlckVuYWJsZWQgPT09IHVuZGVmaW5lZCA/IGZhbHNlIDogcmVzLnJlZ2lzdGVyRW5hYmxlZDsNCiAgICAgICAgaWYgKHRoaXMuY2FwdGNoYUVuYWJsZWQpIHsNCiAgICAgICAgICB0aGlzLmNvZGVVcmwgPSAiZGF0YTppbWFnZS9naWY7YmFzZTY0LCIgKyByZXMuaW1nOw0KICAgICAgICAgIHRoaXMubG9naW5Gb3JtLnV1aWQgPSByZXMudXVpZDsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBnZXRDb29raWUoKSB7DQogICAgICBjb25zdCB1c2VybmFtZSA9IENvb2tpZXMuZ2V0KCJ1c2VybmFtZSIpOw0KICAgICAgY29uc3QgcGFzc3dvcmQgPSBDb29raWVzLmdldCgicGFzc3dvcmQiKTsNCiAgICAgIGNvbnN0IHJlbWVtYmVyTWUgPSBDb29raWVzLmdldCgncmVtZW1iZXJNZScpDQogICAgICB0aGlzLmxvZ2luRm9ybSA9IHsNCiAgICAgICAgdXNlcm5hbWU6IHVzZXJuYW1lID09PSB1bmRlZmluZWQgPyB0aGlzLmxvZ2luRm9ybS51c2VybmFtZSA6IHVzZXJuYW1lLA0KICAgICAgICBwYXNzd29yZDogcGFzc3dvcmQgPT09IHVuZGVmaW5lZCA/IHRoaXMubG9naW5Gb3JtLnBhc3N3b3JkIDogZGVjcnlwdChwYXNzd29yZCksDQogICAgICAgIHJlbWVtYmVyTWU6IHJlbWVtYmVyTWUgPT09IHVuZGVmaW5lZCA/IGZhbHNlIDogQm9vbGVhbihyZW1lbWJlck1lKQ0KICAgICAgfTsNCiAgICB9LA0KICAgIGhhbmRsZUxvZ2luKCkgew0KICAgICAgdGhpcy4kcmVmcy5sb2dpbkZvcm0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgICAgIGlmICh0aGlzLmxvZ2luRm9ybS5yZW1lbWJlck1lKSB7DQogICAgICAgICAgICBDb29raWVzLnNldCgidXNlcm5hbWUiLCB0aGlzLmxvZ2luRm9ybS51c2VybmFtZSwgeyBleHBpcmVzOiAzMCB9KTsNCiAgICAgICAgICAgIENvb2tpZXMuc2V0KCJwYXNzd29yZCIsIGVuY3J5cHQodGhpcy5sb2dpbkZvcm0ucGFzc3dvcmQpLCB7IGV4cGlyZXM6IDMwIH0pOw0KICAgICAgICAgICAgQ29va2llcy5zZXQoJ3JlbWVtYmVyTWUnLCB0aGlzLmxvZ2luRm9ybS5yZW1lbWJlck1lLCB7IGV4cGlyZXM6IDMwIH0pOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBDb29raWVzLnJlbW92ZSgidXNlcm5hbWUiKTsNCiAgICAgICAgICAgIENvb2tpZXMucmVtb3ZlKCJwYXNzd29yZCIpOw0KICAgICAgICAgICAgQ29va2llcy5yZW1vdmUoJ3JlbWVtYmVyTWUnKTsNCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goIkxvZ2luIiwgdGhpcy5sb2dpbkZvcm0pLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goeyBwYXRoOiB0aGlzLnJlZGlyZWN0IHx8ICIvIiB9KS5jYXRjaCgoKT0+e30pOw0KICAgICAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgaWYgKHRoaXMuY2FwdGNoYUVuYWJsZWQpIHsNCiAgICAgICAgICAgICAgdGhpcy5nZXRDb2RlKCk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["login.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "login.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"login\">\r\n    <el-form ref=\"loginForm\" :model=\"loginForm\" :rules=\"loginRules\" class=\"login-form\">\r\n      <h3 class=\"title\">金刚舆情智库系统</h3>\r\n      <el-form-item prop=\"username\">\r\n        <el-input\r\n          v-model=\"loginForm.username\"\r\n          type=\"text\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"账号\"\r\n        >\r\n          <svg-icon slot=\"prefix\" icon-class=\"user\" class=\"el-input__icon input-icon\" />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"password\">\r\n        <el-input\r\n          v-model=\"loginForm.password\"\r\n          type=\"password\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"密码\"\r\n          @keyup.enter.native=\"handleLogin\"\r\n        >\r\n          <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"code\" v-if=\"captchaEnabled\">\r\n        <el-input\r\n          v-model=\"loginForm.code\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"验证码\"\r\n          style=\"width: 63%\"\r\n          @keyup.enter.native=\"handleLogin\"\r\n        >\r\n          <svg-icon slot=\"prefix\" icon-class=\"validCode\" class=\"el-input__icon input-icon\" />\r\n        </el-input>\r\n        <div class=\"login-code\">\r\n          <img :src=\"codeUrl\" @click=\"getCode\" class=\"login-code-img\"/>\r\n        </div>\r\n      </el-form-item>\r\n      <el-checkbox v-model=\"loginForm.rememberMe\" style=\"margin:0px 0px 25px 0px;\">记住密码</el-checkbox>\r\n      <el-form-item style=\"width:100%;\">\r\n        <el-button\r\n          :loading=\"loading\"\r\n          size=\"medium\"\r\n          type=\"primary\"\r\n          style=\"width:100%;\"\r\n          @click.native.prevent=\"handleLogin\"\r\n        >\r\n          <span v-if=\"!loading\">登 录</span>\r\n          <span v-else>登 录 中...</span>\r\n        </el-button>\r\n        <div style=\"float: right;\" v-if=\"register\">\r\n          <router-link class=\"link-type\" :to=\"'/register'\">立即注册</router-link>\r\n        </div>\r\n      </el-form-item>\r\n    </el-form>\r\n    <!--  底部  -->\r\n    <div class=\"el-login-footer\">\r\n      <span>Copyright © 2024 insistence.tech All Rights Reserved.</span>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCodeImg } from \"@/api/login\";\r\nimport Cookies from \"js-cookie\";\r\nimport { encrypt, decrypt } from '@/utils/jsencrypt'\r\n\r\nexport default {\r\n  name: \"Login\",\r\n  data() {\r\n    return {\r\n      codeUrl: \"\",\r\n      loginForm: {\r\n        username: \"\",\r\n        password: \"\",\r\n        rememberMe: false,\r\n        code: \"\",\r\n        uuid: \"\"\r\n      },\r\n      loginRules: {\r\n        username: [\r\n          { required: true, trigger: \"blur\", message: \"请输入您的账号\" }\r\n        ],\r\n        password: [\r\n          { required: true, trigger: \"blur\", message: \"请输入您的密码\" }\r\n        ],\r\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }]\r\n      },\r\n      loading: false,\r\n      // 验证码开关\r\n      captchaEnabled: true,\r\n      // 注册开关\r\n      register: false,\r\n      redirect: undefined\r\n    };\r\n  },\r\n  watch: {\r\n    $route: {\r\n      handler: function(route) {\r\n        this.redirect = route.query && route.query.redirect;\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  created() {\r\n    this.getCode();\r\n    this.getCookie();\r\n  },\r\n  methods: {\r\n    getCode() {\r\n      getCodeImg().then(res => {\r\n        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled;\r\n        this.register = res.registerEnabled === undefined ? false : res.registerEnabled;\r\n        if (this.captchaEnabled) {\r\n          this.codeUrl = \"data:image/gif;base64,\" + res.img;\r\n          this.loginForm.uuid = res.uuid;\r\n        }\r\n      });\r\n    },\r\n    getCookie() {\r\n      const username = Cookies.get(\"username\");\r\n      const password = Cookies.get(\"password\");\r\n      const rememberMe = Cookies.get('rememberMe')\r\n      this.loginForm = {\r\n        username: username === undefined ? this.loginForm.username : username,\r\n        password: password === undefined ? this.loginForm.password : decrypt(password),\r\n        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)\r\n      };\r\n    },\r\n    handleLogin() {\r\n      this.$refs.loginForm.validate(valid => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          if (this.loginForm.rememberMe) {\r\n            Cookies.set(\"username\", this.loginForm.username, { expires: 30 });\r\n            Cookies.set(\"password\", encrypt(this.loginForm.password), { expires: 30 });\r\n            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 });\r\n          } else {\r\n            Cookies.remove(\"username\");\r\n            Cookies.remove(\"password\");\r\n            Cookies.remove('rememberMe');\r\n          }\r\n          this.$store.dispatch(\"Login\", this.loginForm).then(() => {\r\n            this.$router.push({ path: this.redirect || \"/\" }).catch(()=>{});\r\n          }).catch(() => {\r\n            this.loading = false;\r\n            if (this.captchaEnabled) {\r\n              this.getCode();\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style rel=\"stylesheet/scss\" lang=\"scss\">\r\n.login {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100%;\r\n  background-image: url(\"../assets/images/login-background.jpg\");\r\n  background-size: cover;\r\n}\r\n.title {\r\n  margin: 0px auto 30px auto;\r\n  text-align: center;\r\n  color: #707070;\r\n}\r\n\r\n.login-form {\r\n  border-radius: 6px;\r\n  background: #ffffff;\r\n  width: 400px;\r\n  padding: 25px 25px 5px 25px;\r\n  .el-input {\r\n    height: 38px;\r\n    input {\r\n      height: 38px;\r\n    }\r\n  }\r\n  .input-icon {\r\n    height: 39px;\r\n    width: 14px;\r\n    margin-left: 2px;\r\n  }\r\n}\r\n.login-tip {\r\n  font-size: 13px;\r\n  text-align: center;\r\n  color: #bfbfbf;\r\n}\r\n.login-code {\r\n  width: 33%;\r\n  height: 38px;\r\n  float: right;\r\n  img {\r\n    cursor: pointer;\r\n    vertical-align: middle;\r\n  }\r\n}\r\n.el-login-footer {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  position: fixed;\r\n  bottom: 0;\r\n  width: 100%;\r\n  text-align: center;\r\n  color: #fff;\r\n  font-family: Arial;\r\n  font-size: 12px;\r\n  letter-spacing: 1px;\r\n}\r\n.login-code-img {\r\n  height: 38px;\r\n}\r\n</style>\r\n"]}]}