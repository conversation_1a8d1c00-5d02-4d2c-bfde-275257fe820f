{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\opinion-overview\\index.vue?vue&type=style&index=0&id=22c4c981&lang=scss&scoped=true", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\opinion-overview\\index.vue", "mtime": 1749104047642}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749104419119}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749104421224}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749104419872}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749104418479}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5vcGluaW9uLW92ZXJ2aWV3IHsNCiAgZGlzcGxheTogZmxleDsNCiAgaGVpZ2h0OiAxMDB2aDsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTsNCn0NCg0KLmxlZnQtc2lkZWJhciB7DQogIHdpZHRoOiAyODBweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgYm9yZGVyLXJpZ2h0OiAxcHggc29saWQgI2U4ZThlODsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCg0KICAuc2lkZWJhci1oZWFkZXIgew0KICAgIHBhZGRpbmc6IDE2cHg7DQogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOGU4ZTg7DQogIH0NCg0KICAuc2lkZWJhci1zZWFyY2ggew0KICAgIHBhZGRpbmc6IDE2cHg7DQogIH0NCg0KICAuc2lkZWJhci1tZW51IHsNCiAgICBmbGV4OiAxOw0KICAgIHBhZGRpbmc6IDAgMTZweDsNCg0KICAgIC5zZWN0aW9uLXRpdGxlIHsNCiAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgIGNvbG9yOiAjNjY2Ow0KICAgICAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KICAgIH0NCg0KICAgIC5zaWRlYmFyLW1lbnUtbGlzdCB7DQogICAgICBib3JkZXI6IG5vbmU7DQoNCiAgICAgIC5lbC1tZW51LWl0ZW0gew0KICAgICAgICBoZWlnaHQ6IDQwcHg7DQogICAgICAgIGxpbmUtaGVpZ2h0OiA0MHB4Ow0KICAgICAgICBtYXJnaW4tYm90dG9tOiA0cHg7DQogICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCg0KICAgICAgICAmLmFjdGl2ZS1pdGVtIHsNCiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZTZmN2ZmOw0KICAgICAgICAgIGNvbG9yOiAjMTg5MGZmOw0KICAgICAgICB9DQoNCiAgICAgICAgJjpob3ZlciB7DQogICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0KDQoucmlnaHQtY29udGVudCB7DQogIGZsZXg6IDE7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQoNCiAgLnRvcC10YWJzIHsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZThlOGU4Ow0KDQogICAgLmVsLXRhYnMgew0KICAgICAgLmVsLXRhYnNfX2hlYWRlciB7DQogICAgICAgIG1hcmdpbjogMDsNCg0KICAgICAgICAuZWwtdGFic19fbmF2LXdyYXAgew0KICAgICAgICAgIHBhZGRpbmc6IDAgMjRweDsNCiAgICAgICAgfQ0KDQogICAgICAgIC5lbC10YWJzX19pdGVtIHsNCiAgICAgICAgICBoZWlnaHQ6IDUwcHg7DQogICAgICAgICAgbGluZS1oZWlnaHQ6IDUwcHg7DQogICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KDQogICAgICAgICAgJi5pcy1hY3RpdmUgew0KICAgICAgICAgICAgY29sb3I6ICMxODkwZmY7DQogICAgICAgICAgICBib3JkZXItYm90dG9tLWNvbG9yOiAjMTg5MGZmOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC5tYWluLWNvbnRlbnQgew0KICAgIGZsZXg6IDE7DQogICAgcGFkZGluZzogMjRweDsNCiAgICBvdmVyZmxvdy15OiBhdXRvOw0KICB9DQp9DQoNCi5zZWN0aW9uLWNhcmQgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIHBhZGRpbmc6IDI0cHg7DQogIG1hcmdpbi1ib3R0b206IDI0cHg7DQogIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQoNCiAgLmNhcmQtaGVhZGVyIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIG1hcmdpbi1ib3R0b206IDI0cHg7DQoNCiAgICBoMyB7DQogICAgICBtYXJnaW46IDA7DQogICAgICBmb250LXNpemU6IDE4cHg7DQogICAgICBmb250LXdlaWdodDogNjAwOw0KICAgIH0NCg0KICAgIC5zdGF0cyB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgZ2FwOiAyNHB4Ow0KDQogICAgICAuc3RhdC1pdGVtIHsNCiAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOw0KDQogICAgICAgIC5sYWJlbCB7DQogICAgICAgICAgZGlzcGxheTogYmxvY2s7DQogICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICAgIGNvbG9yOiAjNjY2Ow0KICAgICAgICAgIG1hcmdpbi1ib3R0b206IDRweDsNCiAgICAgICAgfQ0KDQogICAgICAgIC52YWx1ZSB7DQogICAgICAgICAgZGlzcGxheTogYmxvY2s7DQogICAgICAgICAgZm9udC1zaXplOiAyNHB4Ow0KICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7DQogICAgICAgIH0NCg0KICAgICAgICAmLnBvc2l0aXZlIC52YWx1ZSB7DQogICAgICAgICAgY29sb3I6ICM1MmM0MWE7DQogICAgICAgIH0NCg0KICAgICAgICAmLm5ldXRyYWwgLnZhbHVlIHsNCiAgICAgICAgICBjb2xvcjogI2ZhYWQxNDsNCiAgICAgICAgfQ0KDQogICAgICAgICYubmVnYXRpdmUgLnZhbHVlIHsNCiAgICAgICAgICBjb2xvcjogI2ZmNGQ0ZjsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0KDQouYm90dG9tLXNlY3Rpb24gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDI0cHg7DQoNCiAgLmxlZnQtY2hhcnRzIHsNCiAgICBmbGV4OiAyOw0KICB9DQoNCiAgLnJpZ2h0LXN0YXRzIHsNCiAgICBmbGV4OiAxOw0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICBnYXA6IDI0cHg7DQogIH0NCg0KICAubGVmdC1hcnRpY2xlcyB7DQogICAgZmxleDogMTsNCiAgfQ0KDQogIC5yaWdodC1hbm5vdW5jZW1lbnRzIHsNCiAgICBmbGV4OiAxOw0KICB9DQp9DQoNCi5jaGFydC1jYXJkIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBwYWRkaW5nOiAyNHB4Ow0KICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KDQogIC5jYXJkLWhlYWRlciB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KDQogICAgaDMgew0KICAgICAgbWFyZ2luOiAwOw0KICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICB9DQogIH0NCn0NCg0KLnN0YXRzLWNhcmQgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIHBhZGRpbmc6IDI0cHg7DQogIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQoNCiAgLnRvdGFsLWNvdW50IHsNCiAgICBmb250LXNpemU6IDMycHg7DQogICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgbWFyZ2luOiAyNHB4IDA7DQogICAgbGV0dGVyLXNwYWNpbmc6IDJweDsNCiAgfQ0KDQogIC5wbGF0Zm9ybS1zdGF0cyB7DQogICAgLnBsYXRmb3JtLWl0ZW0gew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICBwYWRkaW5nOiAxMnB4IDA7DQogICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDsNCg0KICAgICAgJjpsYXN0LWNoaWxkIHsNCiAgICAgICAgYm9yZGVyLWJvdHRvbTogbm9uZTsNCiAgICAgIH0NCg0KICAgICAgLnBsYXRmb3JtLWljb24gew0KICAgICAgICB3aWR0aDogMzJweDsNCiAgICAgICAgaGVpZ2h0OiAzMnB4Ow0KICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgICAgICBjb2xvcjogI2ZmZjsNCiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMnB4Ow0KICAgICAgfQ0KDQogICAgICAmLndlaWJvIC5wbGF0Zm9ybS1pY29uIHsNCiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzE4OTBmZjsNCiAgICAgIH0NCg0KICAgICAgJi53ZWNoYXQgLnBsYXRmb3JtLWljb24gew0KICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNTJjNDFhOw0KICAgICAgfQ0KDQogICAgICAmLndlaWJvLXJlZCAucGxhdGZvcm0taWNvbiB7DQogICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZjRkNGY7DQogICAgICB9DQoNCiAgICAgICYueGlhb2hvbmdzaHUgLnBsYXRmb3JtLWljb24gew0KICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZWIyZjk2Ow0KICAgICAgfQ0KDQogICAgICAmLmFwcCAucGxhdGZvcm0taWNvbiB7DQogICAgICAgIGJhY2tncm91bmQtY29sb3I6ICMxM2MyYzI7DQogICAgICB9DQoNCiAgICAgICYudG91dGlhbyAucGxhdGZvcm0taWNvbiB7DQogICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmYThjMTY7DQogICAgICB9DQoNCiAgICAgICYuZG91eWluIC5wbGF0Zm9ybS1pY29uIHsNCiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzcyMmVkMTsNCiAgICAgIH0NCg0KICAgICAgJi5uZXdzIC5wbGF0Zm9ybS1pY29uIHsNCiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZhYWQxNDsNCiAgICAgIH0NCg0KICAgICAgJi5mb3J1bSAucGxhdGZvcm0taWNvbiB7DQogICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNhMGQ5MTE7DQogICAgICB9DQoNCiAgICAgIC5wbGF0Zm9ybS1pbmZvIHsNCiAgICAgICAgZmxleDogMTsNCg0KICAgICAgICAucGxhdGZvcm0tbmFtZSB7DQogICAgICAgICAgZGlzcGxheTogYmxvY2s7DQogICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICAgIGNvbG9yOiAjMzMzOw0KICAgICAgICB9DQoNCiAgICAgICAgLnBsYXRmb3JtLWNvdW50IHsNCiAgICAgICAgICBkaXNwbGF5OiBibG9jazsNCiAgICAgICAgICBmb250LXNpemU6IDE4cHg7DQogICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICAgICAgICBjb2xvcjogIzMzMzsNCiAgICAgICAgfQ0KDQogICAgICAgIC5wbGF0Zm9ybS1jaGFuZ2Ugew0KICAgICAgICAgIGRpc3BsYXk6IGJsb2NrOw0KICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgICAgICBjb2xvcjogIzY2NjsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0KDQovLyDng63pl6jmlofnq6DmoLflvI8NCi5hcnRpY2xlLWNhcmQgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIHBhZGRpbmc6IDI0cHg7DQogIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQoNCiAgLmNhcmQtaGVhZGVyIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIG1hcmdpbi1ib3R0b206IDE2cHg7DQoNCiAgICBoMyB7DQogICAgICBtYXJnaW46IDA7DQogICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICBmb250LXdlaWdodDogNjAwOw0KICAgIH0NCiAgfQ0KDQogIC5hcnRpY2xlLWxpc3Qgew0KICAgIC5hcnRpY2xlLWl0ZW0gew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICBwYWRkaW5nOiAxMnB4IDA7DQogICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDsNCg0KICAgICAgJjpsYXN0LWNoaWxkIHsNCiAgICAgICAgYm9yZGVyLWJvdHRvbTogbm9uZTsNCiAgICAgIH0NCg0KICAgICAgLmFydGljbGUtaWNvbiB7DQogICAgICAgIHdpZHRoOiAzMnB4Ow0KICAgICAgICBoZWlnaHQ6IDMycHg7DQogICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogICAgICAgIGNvbG9yOiAjZmZmOw0KICAgICAgICBmb250LXdlaWdodDogNjAwOw0KICAgICAgICBtYXJnaW4tcmlnaHQ6IDEycHg7DQogICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCg0KICAgICAgICAmLm5lZ2F0aXZlIHsNCiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmY0ZDRmOw0KICAgICAgICB9DQoNCiAgICAgICAgJi5uZXV0cmFsIHsNCiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmFhZDE0Ow0KICAgICAgICB9DQoNCiAgICAgICAgJi5wb3NpdGl2ZSB7DQogICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzUyYzQxYTsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAuYXJ0aWNsZS1jb250ZW50IHsNCiAgICAgICAgZmxleDogMTsNCg0KICAgICAgICAuYXJ0aWNsZS10aXRsZSB7DQogICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICAgIGNvbG9yOiAjMzMzOw0KICAgICAgICAgIG1hcmdpbi1ib3R0b206IDRweDsNCiAgICAgICAgICBsaW5lLWhlaWdodDogMS40Ow0KICAgICAgICB9DQoNCiAgICAgICAgLmFydGljbGUtbWV0YSB7DQogICAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgICAgIGNvbG9yOiAjNjY2Ow0KDQogICAgICAgICAgLmFydGljbGUtc291cmNlIHsNCiAgICAgICAgICAgIG1hcmdpbi1yaWdodDogMTJweDsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg0KLy8g5pyA5paw5YWs5ZGK5qC35byPDQouYW5ub3VuY2VtZW50LWNhcmQgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIHBhZGRpbmc6IDI0cHg7DQogIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQoNCiAgLmNhcmQtaGVhZGVyIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIG1hcmdpbi1ib3R0b206IDE2cHg7DQoNCiAgICBoMyB7DQogICAgICBtYXJnaW46IDA7DQogICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICBmb250LXdlaWdodDogNjAwOw0KICAgIH0NCiAgfQ0KDQogIC5hbm5vdW5jZW1lbnQtbGlzdCB7DQogICAgLmFubm91bmNlbWVudC1pdGVtIHsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgcGFkZGluZzogMTJweCAwOw0KICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7DQoNCiAgICAgICY6bGFzdC1jaGlsZCB7DQogICAgICAgIGJvcmRlci1ib3R0b206IG5vbmU7DQogICAgICB9DQoNCiAgICAgIC5hbm5vdW5jZW1lbnQtaW5kaWNhdG9yIHsNCiAgICAgICAgd2lkdGg6IDhweDsNCiAgICAgICAgaGVpZ2h0OiA4cHg7DQogICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTsNCiAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMnB4Ow0KDQogICAgICAgICYuaGlnaCB7DQogICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmNGQ0ZjsNCiAgICAgICAgfQ0KDQogICAgICAgICYubWVkaXVtIHsNCiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmFhZDE0Ow0KICAgICAgICB9DQoNCiAgICAgICAgJi5sb3cgew0KICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICM1MmM0MWE7DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLmFubm91bmNlbWVudC1jb250ZW50IHsNCiAgICAgICAgZmxleDogMTsNCg0KICAgICAgICAuYW5ub3VuY2VtZW50LXRpdGxlIHsNCiAgICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgICAgY29sb3I6ICMzMzM7DQogICAgICAgICAgbWFyZ2luLWJvdHRvbTogNHB4Ow0KICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjQ7DQogICAgICAgIH0NCg0KICAgICAgICAuYW5ub3VuY2VtZW50LXRpbWUgew0KICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgICAgICBjb2xvcjogIzY2NjsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA64BA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/opinion-overview", "sourcesContent": ["<template>\r\n  <div class=\"opinion-overview\">\r\n    <!-- 左侧导航栏 -->\r\n    <div class=\"left-sidebar\">\r\n      <div class=\"sidebar-header\">\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\">新建方案</el-button>\r\n      </div>\r\n\r\n      <div class=\"sidebar-search\">\r\n        <el-input\r\n          v-model=\"searchText\"\r\n          placeholder=\"搜索方案\"\r\n          size=\"small\"\r\n          prefix-icon=\"el-icon-search\">\r\n        </el-input>\r\n      </div>\r\n\r\n      <div class=\"sidebar-menu\">\r\n        <div class=\"menu-section\">\r\n          <div class=\"section-title\">已有方案</div>\r\n          <el-menu\r\n            :default-active=\"activeMenuItem\"\r\n            class=\"sidebar-menu-list\"\r\n            @select=\"handleMenuSelect\">\r\n            <el-menu-item index=\"基础(1)\">\r\n              <i class=\"el-icon-s-custom\"></i>\r\n              <span>基础(1)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"品牌(1)\">\r\n              <i class=\"el-icon-s-goods\"></i>\r\n              <span>品牌(1)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"方太\" class=\"active-item\">\r\n              <i class=\"el-icon-star-off\"></i>\r\n              <span>方太</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"人物(0)\">\r\n              <i class=\"el-icon-user\"></i>\r\n              <span>人物(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"机构(0)\">\r\n              <i class=\"el-icon-office-building\"></i>\r\n              <span>机构(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"产品(0)\">\r\n              <i class=\"el-icon-goods\"></i>\r\n              <span>产品(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"事件(0)\">\r\n              <i class=\"el-icon-warning\"></i>\r\n              <span>事件(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"话题(0)\">\r\n              <i class=\"el-icon-chat-dot-round\"></i>\r\n              <span>话题(0)</span>\r\n            </el-menu-item>\r\n          </el-menu>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 右侧内容区 -->\r\n    <div class=\"right-content\">\r\n      <!-- 顶部导航标签栏 -->\r\n      <!-- 主要内容区域 -->\r\n      <div class=\"main-content\">\r\n        <!-- 根据activeTab显示不同内容 -->\r\n        <div v-if=\"activeTab === 'opinion-monitor'\">\r\n          <!-- 舆情监测内容 -->\r\n\r\n          <!-- 舆情趋势图表 -->\r\n          <div class=\"section-card\">\r\n            <div class=\"chart-container\">\r\n              <div id=\"trend-chart\" style=\"width: 100%; height: 400px;\"></div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 热门文章和最新公告 -->\r\n          <div class=\"bottom-section\">\r\n            <div class=\"left-articles\">\r\n              <div class=\"article-card\">\r\n                <div class=\"card-header\">\r\n                  <h3><i class=\"el-icon-document\" style=\"color: #1890ff; margin-right: 8px;\"></i>热门文章</h3>\r\n                </div>\r\n                <div class=\"article-list\">\r\n                  <div class=\"article-item\" v-for=\"(article, index) in hotArticles\" :key=\"index\">\r\n                    <div class=\"article-icon\" :class=\"article.type\">{{ article.icon }}</div>\r\n                    <div class=\"article-content\">\r\n                      <div class=\"article-title\">{{ article.title }}</div>\r\n                      <div class=\"article-meta\">\r\n                        <span class=\"article-source\">{{ article.source }}</span>\r\n                        <span class=\"article-author\">{{ article.author }}</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"right-announcements\">\r\n              <div class=\"announcement-card\">\r\n                <div class=\"card-header\">\r\n                  <h3><i class=\"el-icon-bell\" style=\"color: #52c41a; margin-right: 8px;\"></i>最新公告</h3>\r\n                </div>\r\n                <div class=\"announcement-list\">\r\n                  <div class=\"announcement-item\" v-for=\"(announcement, index) in announcements\" :key=\"index\">\r\n                    <div class=\"announcement-indicator\" :class=\"announcement.level\"></div>\r\n                    <div class=\"announcement-content\">\r\n                      <div class=\"announcement-title\">{{ announcement.title }}</div>\r\n                      <div class=\"announcement-time\">{{ announcement.time }}</div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-else-if=\"activeTab === 'info-summary'\">\r\n          <!-- 信息汇总内容 -->\r\n          <!-- 近30天舆情发布地区 -->\r\n          <div class=\"section-card\">\r\n            <div class=\"card-header\">\r\n              <h3><i class=\"el-icon-location\" style=\"color: #409EFF; margin-right: 8px;\"></i>近30天舆情发布地区</h3>\r\n              <div class=\"stats\">\r\n                <div class=\"stat-item positive\">\r\n                  <span class=\"label\">正面舆情</span>\r\n                  <span class=\"value\">111930</span>\r\n                </div>\r\n                <div class=\"stat-item neutral\">\r\n                  <span class=\"label\">中性舆情</span>\r\n                  <span class=\"value\">1118</span>\r\n                </div>\r\n                <div class=\"stat-item negative\">\r\n                  <span class=\"label\">负面舆情</span>\r\n                  <span class=\"value\">444</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"map-container\">\r\n              <div id=\"china-map\" style=\"width: 100%; height: 400px; background-color: #f0f2f5; display: flex; align-items: center; justify-content: center; color: #999;\">\r\n                <div>地图组件加载中...</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 今日舆情总量和平台分析 -->\r\n          <div class=\"bottom-section\">\r\n          <div class=\"left-charts\">\r\n            <!-- 近30天平台分析 -->\r\n            <div class=\"chart-card\">\r\n              <div class=\"card-header\">\r\n                <h3><i class=\"el-icon-pie-chart\" style=\"color: #52C41A; margin-right: 8px;\"></i>近30天平台分析</h3>\r\n                <el-button type=\"text\" icon=\"el-icon-download\">导出</el-button>\r\n              </div>\r\n              <div id=\"platform-chart\" style=\"width: 100%; height: 300px;\"></div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"right-stats\">\r\n            <!-- 今日舆情总量 -->\r\n            <div class=\"stats-card\">\r\n              <div class=\"card-header\">\r\n                <h3><i class=\"el-icon-data-line\" style=\"color: #FA8C16; margin-right: 8px;\"></i>今日舆情总量</h3>\r\n              </div>\r\n              <div class=\"total-count\">0 0 0,0 0 4,6 8 1</div>\r\n              <div class=\"platform-stats\">\r\n                <div class=\"platform-item weibo\">\r\n                  <div class=\"platform-icon\">微</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">微博</span>\r\n                    <span class=\"platform-count\">534</span>\r\n                    <span class=\"platform-change\">今日新增 -0.8%</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"platform-item wechat\">\r\n                  <div class=\"platform-icon\">微</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">微信</span>\r\n                    <span class=\"platform-count\">1483</span>\r\n                    <span class=\"platform-change\">今日新增 15.2%</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"platform-item weibo-red\">\r\n                  <div class=\"platform-icon\">微</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">微博</span>\r\n                    <span class=\"platform-count\">279</span>\r\n                    <span class=\"platform-change\">今日新增 -0.8%</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"platform-item xiaohongshu\">\r\n                  <div class=\"platform-icon\">小</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">小红书</span>\r\n                    <span class=\"platform-count\">129</span>\r\n                    <span class=\"platform-change\">今日新增 3.2%</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"platform-item app\">\r\n                  <div class=\"platform-icon\">A</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">APP</span>\r\n                    <span class=\"platform-count\">764</span>\r\n                    <span class=\"platform-change\">今日新增 -1.8%</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"platform-item toutiao\">\r\n                  <div class=\"platform-icon\">头</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">头条</span>\r\n                    <span class=\"platform-count\">1455</span>\r\n                    <span class=\"platform-change\">今日新增 4.5%</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"platform-item douyin\">\r\n                  <div class=\"platform-icon\">抖</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">抖音</span>\r\n                    <span class=\"platform-count\">23</span>\r\n                    <span class=\"platform-change\">今日新增 100%</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"platform-item news\">\r\n                  <div class=\"platform-icon\">新</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">新闻</span>\r\n                    <span class=\"platform-count\">2</span>\r\n                    <span class=\"platform-change\">今日新增 100%</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"platform-item forum\">\r\n                  <div class=\"platform-icon\">论</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">论坛</span>\r\n                    <span class=\"platform-count\">12</span>\r\n                    <span class=\"platform-change\">今日新增 -2.8%</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 近30天情感属性 -->\r\n            <div class=\"chart-card\">\r\n              <div class=\"card-header\">\r\n                <h3><i class=\"el-icon-sunny\" style=\"color: #722ED1; margin-right: 8px;\"></i>近30天情感属性</h3>\r\n                <el-button type=\"text\" icon=\"el-icon-download\">导出</el-button>\r\n              </div>\r\n              <div id=\"sentiment-chart\" style=\"width: 100%; height: 200px;\"></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: 'OpinionOverview',\r\n  data() {\r\n    return {\r\n      activeMenuItem: '方太',\r\n      searchText: '',\r\n      activeTab: 'opinion-monitor', // 默认激活舆情监测标签\r\n      originalTopNav: undefined, // 存储原始的topNav状态\r\n      // 图表实例\r\n      mapChart: null,\r\n      platformChart: null,\r\n      sentimentChart: null,\r\n      trendChart: null, // 趋势图表实例\r\n      // 热门文章数据\r\n      hotArticles: [\r\n        {\r\n          icon: '红',\r\n          type: 'negative',\r\n          title: '某品牌产品质量问题引发消费者不满',\r\n          source: '新浪财经',\r\n          author: '财经记者'\r\n        },\r\n        {\r\n          icon: '黄',\r\n          type: 'neutral',\r\n          title: '市场分析：家电行业发展趋势',\r\n          source: '中国经济网',\r\n          author: '市场分析师'\r\n        },\r\n        {\r\n          icon: '绿',\r\n          type: 'positive',\r\n          title: '创新技术推动行业发展',\r\n          source: '科技日报',\r\n          author: '科技记者'\r\n        },\r\n        {\r\n          icon: '红',\r\n          type: 'negative',\r\n          title: '消费者投诉处理不当引发关注',\r\n          source: '消费者报',\r\n          author: '消费维权'\r\n        },\r\n        {\r\n          icon: '绿',\r\n          type: 'positive',\r\n          title: '企业社会责任获得认可',\r\n          source: '人民日报',\r\n          author: '社会记者'\r\n        }\r\n      ],\r\n      // 最新公告数据\r\n      announcements: [\r\n        {\r\n          level: 'high',\r\n          title: '舆情监测系统升级通知',\r\n          time: '2023-04-20 10:30:00'\r\n        },\r\n        {\r\n          level: 'medium',\r\n          title: '五一假期监测安排',\r\n          time: '2023-04-19 16:45:00'\r\n        },\r\n        {\r\n          level: 'low',\r\n          title: '数据统计报告已生成',\r\n          time: '2023-04-19 14:20:00'\r\n        },\r\n        {\r\n          level: 'high',\r\n          title: '重要舆情预警提醒',\r\n          time: '2023-04-19 09:15:00'\r\n        },\r\n        {\r\n          level: 'medium',\r\n          title: '系统维护完成通知',\r\n          time: '2023-04-18 18:30:00'\r\n        }\r\n      ],\r\n      // 地图数据\r\n      mapData: [\r\n        {name: '北京', value: 15000},\r\n        {name: '上海', value: 12000},\r\n        {name: '广东', value: 18000},\r\n        {name: '浙江', value: 8000},\r\n        {name: '江苏', value: 9000},\r\n        {name: '山东', value: 7000},\r\n        {name: '四川', value: 6000},\r\n        {name: '湖北', value: 5000}\r\n      ],\r\n      // 平台数据\r\n      platformData: [\r\n        {name: '微博', value: 35.2, color: '#ff6b6b'},\r\n        {name: '微信', value: 28.6, color: '#51cf66'},\r\n        {name: '抖音', value: 18.4, color: '#339af0'},\r\n        {name: '今日头条', value: 12.8, color: '#ffd43b'},\r\n        {name: '其他', value: 5.0, color: '#868e96'}\r\n      ],\r\n      // 情感数据\r\n      sentimentData: [\r\n        {name: '正面', value: 65.2, color: '#52c41a'},\r\n        {name: '中性', value: 28.3, color: '#faad14'},\r\n        {name: '负面', value: 6.5, color: '#ff4d4f'}\r\n      ]\r\n    }\r\n  },\r\n  mounted() {\r\n    // 隐藏顶部导航栏\r\n    this.originalTopNav = this.$store.state.settings.topNav\r\n    this.$store.dispatch('settings/changeSetting', {\r\n      key: 'topNav',\r\n      value: false\r\n    })\r\n\r\n    this.$nextTick(() => {\r\n      // 根据当前激活的标签初始化对应的图表\r\n      if (this.activeTab === 'opinion-monitor') {\r\n        this.initTrendChart()\r\n      } else if (this.activeTab === 'info-summary') {\r\n        this.initCharts()\r\n      }\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    // 恢复顶部导航栏设置\r\n    if (this.originalTopNav !== undefined) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'topNav',\r\n        value: this.originalTopNav\r\n      })\r\n    }\r\n\r\n    // 销毁图表实例\r\n    if (this.mapChart) {\r\n      this.mapChart.dispose()\r\n    }\r\n    if (this.platformChart) {\r\n      this.platformChart.dispose()\r\n    }\r\n    if (this.sentimentChart) {\r\n      this.sentimentChart.dispose()\r\n    }\r\n    if (this.trendChart) {\r\n      this.trendChart.dispose()\r\n    }\r\n  },\r\n  methods: {\r\n    handleMenuSelect(index) {\r\n      this.activeMenuItem = index\r\n      console.log('Menu selected:', index)\r\n      // 这里可以根据选择的方案加载不同的数据\r\n    },\r\n    handleTabClick(tab) {\r\n      console.log('Tab clicked:', tab.name)\r\n      // 根据不同标签加载不同内容\r\n      this.$nextTick(() => {\r\n        if (tab.name === 'opinion-monitor') {\r\n          this.initTrendChart()\r\n        } else if (tab.name === 'info-summary') {\r\n          this.initCharts()\r\n        }\r\n      })\r\n    },\r\n    initCharts() {\r\n      // 初始化图表\r\n      this.initMap()\r\n      this.initPlatformChart()\r\n      this.initSentimentChart()\r\n    },\r\n    initTrendChart() {\r\n      // 初始化舆情趋势图表\r\n      const chartContainer = document.getElementById('trend-chart')\r\n      if (!chartContainer) return\r\n\r\n      this.trendChart = echarts.init(chartContainer)\r\n\r\n      // 生成30天的日期数据\r\n      const dates = []\r\n      const today = new Date()\r\n      for (let i = 29; i >= 0; i--) {\r\n        const date = new Date(today)\r\n        date.setDate(date.getDate() - i)\r\n        dates.push(date.getMonth() + 1 + '.' + date.getDate())\r\n      }\r\n\r\n      // 模拟各平台的数据\r\n      const weiboData = this.generateRandomData(30, 1500, 2500)\r\n      const wechatData = this.generateRandomData(30, 1200, 2000)\r\n      const douyinData = this.generateRandomData(30, 800, 1500)\r\n      const toutiaData = this.generateRandomData(30, 600, 1200)\r\n      const otherData = this.generateRandomData(30, 300, 800)\r\n\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross',\r\n            label: {\r\n              backgroundColor: '#6a7985'\r\n            }\r\n          },\r\n          formatter: function(params) {\r\n            let result = params[0].name + '<br/>'\r\n            params.forEach(param => {\r\n              result += `<span style=\"color:${param.color}\">●</span> ${param.seriesName}: ${param.value}<br/>`\r\n            })\r\n            return result\r\n          }\r\n        },\r\n        legend: {\r\n          data: ['微博', '微信', '抖音', '头条', '其他'],\r\n          top: 20,\r\n          textStyle: {\r\n            fontSize: 12\r\n          }\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          top: '15%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: dates,\r\n          axisLabel: {\r\n            fontSize: 10,\r\n            color: '#666'\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#e0e0e0'\r\n            }\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          axisLabel: {\r\n            fontSize: 10,\r\n            color: '#666'\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#e0e0e0'\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: '#f0f0f0'\r\n            }\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '微博',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(24, 144, 255, 0.6)' },\r\n                { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#1890ff',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#1890ff'\r\n            },\r\n            data: weiboData\r\n          },\r\n          {\r\n            name: '微信',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(82, 196, 26, 0.6)' },\r\n                { offset: 1, color: 'rgba(82, 196, 26, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#52c41a',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#52c41a'\r\n            },\r\n            data: wechatData\r\n          },\r\n          {\r\n            name: '抖音',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(114, 46, 209, 0.6)' },\r\n                { offset: 1, color: 'rgba(114, 46, 209, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#722ed1',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#722ed1'\r\n            },\r\n            data: douyinData\r\n          },\r\n          {\r\n            name: '头条',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(250, 140, 22, 0.6)' },\r\n                { offset: 1, color: 'rgba(250, 140, 22, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#fa8c16',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#fa8c16'\r\n            },\r\n            data: toutiaData\r\n          },\r\n          {\r\n            name: '其他',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(160, 217, 17, 0.6)' },\r\n                { offset: 1, color: 'rgba(160, 217, 17, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#a0d911',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#a0d911'\r\n            },\r\n            data: otherData\r\n          }\r\n        ]\r\n      }\r\n\r\n      this.trendChart.setOption(option)\r\n    },\r\n    initMap() {\r\n      // 暂时跳过地图初始化，避免缺少地图数据导致的错误\r\n      console.log('地图初始化已跳过，需要引入中国地图数据')\r\n    },\r\n    initPlatformChart() {\r\n      // 初始化平台分析面积图\r\n      const chartContainer = document.getElementById('platform-chart')\r\n      if (!chartContainer) return\r\n\r\n      this.platformChart = echarts.init(chartContainer)\r\n\r\n      // 生成30天的日期数据\r\n      const dates = []\r\n      const today = new Date()\r\n      for (let i = 29; i >= 0; i--) {\r\n        const date = new Date(today)\r\n        date.setDate(date.getDate() - i)\r\n        dates.push(date.getMonth() + 1 + '.' + date.getDate())\r\n      }\r\n\r\n      // 模拟各平台的数据\r\n      const weiboData = this.generateRandomData(30, 1500, 2000)\r\n      const wechatData = this.generateRandomData(30, 1200, 1800)\r\n      const douyinData = this.generateRandomData(30, 800, 1200)\r\n      const toutiaData = this.generateRandomData(30, 600, 1000)\r\n      const otherData = this.generateRandomData(30, 300, 600)\r\n\r\n      const option = {\r\n        title: {\r\n          text: '近30天平台舆情趋势',\r\n          left: 'center',\r\n          textStyle: {\r\n            fontSize: 16,\r\n            fontWeight: 'normal',\r\n            color: '#333'\r\n          }\r\n        },\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross',\r\n            label: {\r\n              backgroundColor: '#6a7985'\r\n            }\r\n          },\r\n          formatter: function (params) {\r\n            let result = params[0].name + '<br/>'\r\n            params.forEach(param => {\r\n              result += `<span style=\"color:${param.color}\">●</span> ${param.seriesName}: ${param.value}<br/>`\r\n            })\r\n            return result\r\n          }\r\n        },\r\n        legend: {\r\n          data: ['微博', '微信', '抖音', '头条', '其他'],\r\n          top: 30,\r\n          textStyle: {\r\n            fontSize: 12\r\n          }\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          top: '15%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: dates,\r\n          axisLabel: {\r\n            fontSize: 10,\r\n            color: '#666'\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#e0e0e0'\r\n            }\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          axisLabel: {\r\n            fontSize: 10,\r\n            color: '#666'\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#e0e0e0'\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: '#f0f0f0'\r\n            }\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '微博',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(24, 144, 255, 0.6)' },\r\n                { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#1890ff',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#1890ff'\r\n            },\r\n            data: weiboData\r\n          },\r\n          {\r\n            name: '微信',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(82, 196, 26, 0.6)' },\r\n                { offset: 1, color: 'rgba(82, 196, 26, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#52c41a',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#52c41a'\r\n            },\r\n            data: wechatData\r\n          },\r\n          {\r\n            name: '抖音',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(114, 46, 209, 0.6)' },\r\n                { offset: 1, color: 'rgba(114, 46, 209, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#722ed1',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#722ed1'\r\n            },\r\n            data: douyinData\r\n          },\r\n          {\r\n            name: '头条',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(250, 140, 22, 0.6)' },\r\n                { offset: 1, color: 'rgba(250, 140, 22, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#fa8c16',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#fa8c16'\r\n            },\r\n            data: toutiaData\r\n          },\r\n          {\r\n            name: '其他',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(160, 217, 17, 0.6)' },\r\n                { offset: 1, color: 'rgba(160, 217, 17, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#a0d911',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#a0d911'\r\n            },\r\n            data: otherData\r\n          }\r\n        ]\r\n      }\r\n\r\n      this.platformChart.setOption(option)\r\n    },\r\n    initSentimentChart() {\r\n      // 初始化情感属性饼图\r\n      const chartContainer = document.getElementById('sentiment-chart')\r\n      if (!chartContainer) return\r\n\r\n      this.sentimentChart = echarts.init(chartContainer)\r\n\r\n      const option = {\r\n        title: {\r\n          text: '情感分布',\r\n          left: 'center',\r\n          textStyle: {\r\n            fontSize: 14,\r\n            fontWeight: 'normal',\r\n            color: '#333'\r\n          }\r\n        },\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\r\n        },\r\n        legend: {\r\n          orient: 'vertical',\r\n          left: 'left',\r\n          top: 'middle',\r\n          data: ['正面', '中性', '负面'],\r\n          textStyle: {\r\n            fontSize: 12\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '情感分布',\r\n            type: 'pie',\r\n            radius: ['40%', '70%'],\r\n            center: ['60%', '50%'],\r\n            avoidLabelOverlap: false,\r\n            label: {\r\n              show: false,\r\n              position: 'center'\r\n            },\r\n            emphasis: {\r\n              label: {\r\n                show: true,\r\n                fontSize: '18',\r\n                fontWeight: 'bold'\r\n              }\r\n            },\r\n            labelLine: {\r\n              show: false\r\n            },\r\n            data: [\r\n              {\r\n                name: '正面',\r\n                value: 65.2,\r\n                itemStyle: {\r\n                  color: '#52c41a'\r\n                }\r\n              },\r\n              {\r\n                name: '中性',\r\n                value: 28.3,\r\n                itemStyle: {\r\n                  color: '#faad14'\r\n                }\r\n              },\r\n              {\r\n                name: '负面',\r\n                value: 6.5,\r\n                itemStyle: {\r\n                  color: '#ff4d4f'\r\n                }\r\n              }\r\n            ]\r\n          }\r\n        ]\r\n      }\r\n\r\n      this.sentimentChart.setOption(option)\r\n    },\r\n    // 生成随机数据的辅助方法\r\n    generateRandomData(count, min, max) {\r\n      const data = []\r\n      for (let i = 0; i < count; i++) {\r\n        const value = Math.floor(Math.random() * (max - min + 1)) + min\r\n        data.push(value)\r\n      }\r\n      return data\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.opinion-overview {\r\n  display: flex;\r\n  height: 100vh;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.left-sidebar {\r\n  width: 280px;\r\n  background-color: #fff;\r\n  border-right: 1px solid #e8e8e8;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .sidebar-header {\r\n    padding: 16px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n\r\n  .sidebar-search {\r\n    padding: 16px;\r\n  }\r\n\r\n  .sidebar-menu {\r\n    flex: 1;\r\n    padding: 0 16px;\r\n\r\n    .section-title {\r\n      font-size: 14px;\r\n      color: #666;\r\n      margin-bottom: 8px;\r\n    }\r\n\r\n    .sidebar-menu-list {\r\n      border: none;\r\n\r\n      .el-menu-item {\r\n        height: 40px;\r\n        line-height: 40px;\r\n        margin-bottom: 4px;\r\n        border-radius: 4px;\r\n\r\n        &.active-item {\r\n          background-color: #e6f7ff;\r\n          color: #1890ff;\r\n        }\r\n\r\n        &:hover {\r\n          background-color: #f5f5f5;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.right-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .top-tabs {\r\n    background-color: #fff;\r\n    border-bottom: 1px solid #e8e8e8;\r\n\r\n    .el-tabs {\r\n      .el-tabs__header {\r\n        margin: 0;\r\n\r\n        .el-tabs__nav-wrap {\r\n          padding: 0 24px;\r\n        }\r\n\r\n        .el-tabs__item {\r\n          height: 50px;\r\n          line-height: 50px;\r\n          font-size: 14px;\r\n\r\n          &.is-active {\r\n            color: #1890ff;\r\n            border-bottom-color: #1890ff;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .main-content {\r\n    flex: 1;\r\n    padding: 24px;\r\n    overflow-y: auto;\r\n  }\r\n}\r\n\r\n.section-card {\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n  .card-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 24px;\r\n\r\n    h3 {\r\n      margin: 0;\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n    }\r\n\r\n    .stats {\r\n      display: flex;\r\n      gap: 24px;\r\n\r\n      .stat-item {\r\n        text-align: center;\r\n\r\n        .label {\r\n          display: block;\r\n          font-size: 14px;\r\n          color: #666;\r\n          margin-bottom: 4px;\r\n        }\r\n\r\n        .value {\r\n          display: block;\r\n          font-size: 24px;\r\n          font-weight: 600;\r\n        }\r\n\r\n        &.positive .value {\r\n          color: #52c41a;\r\n        }\r\n\r\n        &.neutral .value {\r\n          color: #faad14;\r\n        }\r\n\r\n        &.negative .value {\r\n          color: #ff4d4f;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.bottom-section {\r\n  display: flex;\r\n  gap: 24px;\r\n\r\n  .left-charts {\r\n    flex: 2;\r\n  }\r\n\r\n  .right-stats {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 24px;\r\n  }\r\n\r\n  .left-articles {\r\n    flex: 1;\r\n  }\r\n\r\n  .right-announcements {\r\n    flex: 1;\r\n  }\r\n}\r\n\r\n.chart-card {\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n  .card-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 16px;\r\n\r\n    h3 {\r\n      margin: 0;\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n    }\r\n  }\r\n}\r\n\r\n.stats-card {\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n  .total-count {\r\n    font-size: 32px;\r\n    font-weight: 600;\r\n    text-align: center;\r\n    margin: 24px 0;\r\n    letter-spacing: 2px;\r\n  }\r\n\r\n  .platform-stats {\r\n    .platform-item {\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 12px 0;\r\n      border-bottom: 1px solid #f0f0f0;\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      .platform-icon {\r\n        width: 32px;\r\n        height: 32px;\r\n        border-radius: 4px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        color: #fff;\r\n        font-weight: 600;\r\n        margin-right: 12px;\r\n      }\r\n\r\n      &.weibo .platform-icon {\r\n        background-color: #1890ff;\r\n      }\r\n\r\n      &.wechat .platform-icon {\r\n        background-color: #52c41a;\r\n      }\r\n\r\n      &.weibo-red .platform-icon {\r\n        background-color: #ff4d4f;\r\n      }\r\n\r\n      &.xiaohongshu .platform-icon {\r\n        background-color: #eb2f96;\r\n      }\r\n\r\n      &.app .platform-icon {\r\n        background-color: #13c2c2;\r\n      }\r\n\r\n      &.toutiao .platform-icon {\r\n        background-color: #fa8c16;\r\n      }\r\n\r\n      &.douyin .platform-icon {\r\n        background-color: #722ed1;\r\n      }\r\n\r\n      &.news .platform-icon {\r\n        background-color: #faad14;\r\n      }\r\n\r\n      &.forum .platform-icon {\r\n        background-color: #a0d911;\r\n      }\r\n\r\n      .platform-info {\r\n        flex: 1;\r\n\r\n        .platform-name {\r\n          display: block;\r\n          font-size: 14px;\r\n          color: #333;\r\n        }\r\n\r\n        .platform-count {\r\n          display: block;\r\n          font-size: 18px;\r\n          font-weight: 600;\r\n          color: #333;\r\n        }\r\n\r\n        .platform-change {\r\n          display: block;\r\n          font-size: 12px;\r\n          color: #666;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 热门文章样式\r\n.article-card {\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n  .card-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 16px;\r\n\r\n    h3 {\r\n      margin: 0;\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n    }\r\n  }\r\n\r\n  .article-list {\r\n    .article-item {\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 12px 0;\r\n      border-bottom: 1px solid #f0f0f0;\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      .article-icon {\r\n        width: 32px;\r\n        height: 32px;\r\n        border-radius: 4px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        color: #fff;\r\n        font-weight: 600;\r\n        margin-right: 12px;\r\n        font-size: 14px;\r\n\r\n        &.negative {\r\n          background-color: #ff4d4f;\r\n        }\r\n\r\n        &.neutral {\r\n          background-color: #faad14;\r\n        }\r\n\r\n        &.positive {\r\n          background-color: #52c41a;\r\n        }\r\n      }\r\n\r\n      .article-content {\r\n        flex: 1;\r\n\r\n        .article-title {\r\n          font-size: 14px;\r\n          color: #333;\r\n          margin-bottom: 4px;\r\n          line-height: 1.4;\r\n        }\r\n\r\n        .article-meta {\r\n          font-size: 12px;\r\n          color: #666;\r\n\r\n          .article-source {\r\n            margin-right: 12px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 最新公告样式\r\n.announcement-card {\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n  .card-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 16px;\r\n\r\n    h3 {\r\n      margin: 0;\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n    }\r\n  }\r\n\r\n  .announcement-list {\r\n    .announcement-item {\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 12px 0;\r\n      border-bottom: 1px solid #f0f0f0;\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      .announcement-indicator {\r\n        width: 8px;\r\n        height: 8px;\r\n        border-radius: 50%;\r\n        margin-right: 12px;\r\n\r\n        &.high {\r\n          background-color: #ff4d4f;\r\n        }\r\n\r\n        &.medium {\r\n          background-color: #faad14;\r\n        }\r\n\r\n        &.low {\r\n          background-color: #52c41a;\r\n        }\r\n      }\r\n\r\n      .announcement-content {\r\n        flex: 1;\r\n\r\n        .announcement-title {\r\n          font-size: 14px;\r\n          color: #333;\r\n          margin-bottom: 4px;\r\n          line-height: 1.4;\r\n        }\r\n\r\n        .announcement-time {\r\n          font-size: 12px;\r\n          color: #666;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}