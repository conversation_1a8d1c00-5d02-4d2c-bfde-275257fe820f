{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\themes\\base.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\themes\\base.js", "mtime": 1749104420138}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_lodashEs", "require", "_emitter", "_interopRequireDefault", "_theme", "_colorPicker", "_iconPicker", "_picker", "_tooltip", "ALIGNS", "COLORS", "FONTS", "HEADERS", "SIZES", "BaseTheme", "exports", "default", "_Theme", "quill", "options", "_this", "_classCallCheck2", "_callSuper2", "listener", "e", "document", "body", "contains", "root", "removeEventListener", "tooltip", "target", "activeElement", "textbox", "hasFocus", "hide", "pickers", "for<PERSON>ach", "picker", "container", "close", "emitter", "listenDOM", "_inherits2", "_createClass2", "key", "value", "addModule", "name", "module", "_superPropGet2", "extendToolbar", "buildButtons", "buttons", "icons", "Array", "from", "button", "className", "getAttribute", "split", "startsWith", "slice", "length", "innerHTML", "rtl", "buildPickers", "selects", "_this2", "map", "select", "classList", "querySelector", "fillSelect", "_typeof2", "align", "IconPicker", "format", "ColorPicker", "Picker", "update", "on", "Emitter", "events", "EDITOR_CHANGE", "Theme", "DEFAULTS", "merge", "modules", "toolbar", "handlers", "formula", "theme", "edit", "image", "_this3", "fileInput", "createElement", "setAttribute", "uploader", "mimetypes", "join", "add", "addEventListener", "range", "getSelection", "upload", "files", "append<PERSON><PERSON><PERSON>", "click", "video", "BaseTooltip", "_Tooltip", "boundsContainer", "_this4", "listen", "_this5", "event", "save", "preventDefault", "cancel", "restoreFocus", "mode", "arguments", "undefined", "preview", "remove", "bounds", "getBounds", "selection", "savedRange", "position", "concat", "focus", "preventScroll", "scrollTop", "linkRange", "formatText", "sources", "USER", "extractVideoUrl", "index", "insertEmbed", "insertText", "setSelection", "<PERSON><PERSON><PERSON>", "url", "match", "values", "defaultValue", "option", "String"], "sources": ["../../src/themes/base.ts"], "sourcesContent": ["import { merge } from 'lodash-es';\nimport type Quill from '../core/quill.js';\nimport Emitter from '../core/emitter.js';\nimport Theme from '../core/theme.js';\nimport type { ThemeOptions } from '../core/theme.js';\nimport ColorPicker from '../ui/color-picker.js';\nimport IconPicker from '../ui/icon-picker.js';\nimport Picker from '../ui/picker.js';\nimport Tooltip from '../ui/tooltip.js';\nimport type { Range } from '../core/selection.js';\nimport type Clipboard from '../modules/clipboard.js';\nimport type History from '../modules/history.js';\nimport type Keyboard from '../modules/keyboard.js';\nimport type Uploader from '../modules/uploader.js';\nimport type Selection from '../core/selection.js';\n\nconst ALIGNS = [false, 'center', 'right', 'justify'];\n\nconst COLORS = [\n  '#000000',\n  '#e60000',\n  '#ff9900',\n  '#ffff00',\n  '#008a00',\n  '#0066cc',\n  '#9933ff',\n  '#ffffff',\n  '#facccc',\n  '#ffebcc',\n  '#ffffcc',\n  '#cce8cc',\n  '#cce0f5',\n  '#ebd6ff',\n  '#bbbbbb',\n  '#f06666',\n  '#ffc266',\n  '#ffff66',\n  '#66b966',\n  '#66a3e0',\n  '#c285ff',\n  '#888888',\n  '#a10000',\n  '#b26b00',\n  '#b2b200',\n  '#006100',\n  '#0047b2',\n  '#6b24b2',\n  '#444444',\n  '#5c0000',\n  '#663d00',\n  '#666600',\n  '#003700',\n  '#002966',\n  '#3d1466',\n];\n\nconst FONTS = [false, 'serif', 'monospace'];\n\nconst HEADERS = ['1', '2', '3', false];\n\nconst SIZES = ['small', false, 'large', 'huge'];\n\nclass BaseTheme extends Theme {\n  pickers: Picker[];\n  tooltip?: Tooltip;\n\n  constructor(quill: Quill, options: ThemeOptions) {\n    super(quill, options);\n    const listener = (e: MouseEvent) => {\n      if (!document.body.contains(quill.root)) {\n        document.body.removeEventListener('click', listener);\n        return;\n      }\n      if (\n        this.tooltip != null &&\n        // @ts-expect-error\n        !this.tooltip.root.contains(e.target) &&\n        // @ts-expect-error\n        document.activeElement !== this.tooltip.textbox &&\n        !this.quill.hasFocus()\n      ) {\n        this.tooltip.hide();\n      }\n      if (this.pickers != null) {\n        this.pickers.forEach((picker) => {\n          // @ts-expect-error\n          if (!picker.container.contains(e.target)) {\n            picker.close();\n          }\n        });\n      }\n    };\n    quill.emitter.listenDOM('click', document.body, listener);\n  }\n\n  addModule(name: 'clipboard'): Clipboard;\n  addModule(name: 'keyboard'): Keyboard;\n  addModule(name: 'uploader'): Uploader;\n  addModule(name: 'history'): History;\n  addModule(name: 'selection'): Selection;\n  addModule(name: string): unknown;\n  addModule(name: string) {\n    const module = super.addModule(name);\n    if (name === 'toolbar') {\n      // @ts-expect-error\n      this.extendToolbar(module);\n    }\n    return module;\n  }\n\n  buildButtons(\n    buttons: NodeListOf<HTMLElement>,\n    icons: Record<string, Record<string, string> | string>,\n  ) {\n    Array.from(buttons).forEach((button) => {\n      const className = button.getAttribute('class') || '';\n      className.split(/\\s+/).forEach((name) => {\n        if (!name.startsWith('ql-')) return;\n        name = name.slice('ql-'.length);\n        if (icons[name] == null) return;\n        if (name === 'direction') {\n          // @ts-expect-error\n          button.innerHTML = icons[name][''] + icons[name].rtl;\n        } else if (typeof icons[name] === 'string') {\n          // @ts-expect-error\n          button.innerHTML = icons[name];\n        } else {\n          // @ts-expect-error\n          const value = button.value || '';\n          // @ts-expect-error\n          if (value != null && icons[name][value]) {\n            // @ts-expect-error\n            button.innerHTML = icons[name][value];\n          }\n        }\n      });\n    });\n  }\n\n  buildPickers(\n    selects: NodeListOf<HTMLSelectElement>,\n    icons: Record<string, string | Record<string, string>>,\n  ) {\n    this.pickers = Array.from(selects).map((select) => {\n      if (select.classList.contains('ql-align')) {\n        if (select.querySelector('option') == null) {\n          fillSelect(select, ALIGNS);\n        }\n        if (typeof icons.align === 'object') {\n          return new IconPicker(select, icons.align);\n        }\n      }\n      if (\n        select.classList.contains('ql-background') ||\n        select.classList.contains('ql-color')\n      ) {\n        const format = select.classList.contains('ql-background')\n          ? 'background'\n          : 'color';\n        if (select.querySelector('option') == null) {\n          fillSelect(\n            select,\n            COLORS,\n            format === 'background' ? '#ffffff' : '#000000',\n          );\n        }\n        return new ColorPicker(select, icons[format] as string);\n      }\n      if (select.querySelector('option') == null) {\n        if (select.classList.contains('ql-font')) {\n          fillSelect(select, FONTS);\n        } else if (select.classList.contains('ql-header')) {\n          fillSelect(select, HEADERS);\n        } else if (select.classList.contains('ql-size')) {\n          fillSelect(select, SIZES);\n        }\n      }\n      return new Picker(select);\n    });\n    const update = () => {\n      this.pickers.forEach((picker) => {\n        picker.update();\n      });\n    };\n    this.quill.on(Emitter.events.EDITOR_CHANGE, update);\n  }\n}\nBaseTheme.DEFAULTS = merge({}, Theme.DEFAULTS, {\n  modules: {\n    toolbar: {\n      handlers: {\n        formula() {\n          this.quill.theme.tooltip.edit('formula');\n        },\n        image() {\n          let fileInput = this.container.querySelector(\n            'input.ql-image[type=file]',\n          );\n          if (fileInput == null) {\n            fileInput = document.createElement('input');\n            fileInput.setAttribute('type', 'file');\n            fileInput.setAttribute(\n              'accept',\n              this.quill.uploader.options.mimetypes.join(', '),\n            );\n            fileInput.classList.add('ql-image');\n            fileInput.addEventListener('change', () => {\n              const range = this.quill.getSelection(true);\n              this.quill.uploader.upload(range, fileInput.files);\n              fileInput.value = '';\n            });\n            this.container.appendChild(fileInput);\n          }\n          fileInput.click();\n        },\n        video() {\n          this.quill.theme.tooltip.edit('video');\n        },\n      },\n    },\n  },\n});\n\nclass BaseTooltip extends Tooltip {\n  textbox: HTMLInputElement | null;\n  linkRange?: Range;\n\n  constructor(quill: Quill, boundsContainer?: HTMLElement) {\n    super(quill, boundsContainer);\n    this.textbox = this.root.querySelector('input[type=\"text\"]');\n    this.listen();\n  }\n\n  listen() {\n    // @ts-expect-error Fix me later\n    this.textbox.addEventListener('keydown', (event) => {\n      if (event.key === 'Enter') {\n        this.save();\n        event.preventDefault();\n      } else if (event.key === 'Escape') {\n        this.cancel();\n        event.preventDefault();\n      }\n    });\n  }\n\n  cancel() {\n    this.hide();\n    this.restoreFocus();\n  }\n\n  edit(mode = 'link', preview: string | null = null) {\n    this.root.classList.remove('ql-hidden');\n    this.root.classList.add('ql-editing');\n    if (this.textbox == null) return;\n\n    if (preview != null) {\n      this.textbox.value = preview;\n    } else if (mode !== this.root.getAttribute('data-mode')) {\n      this.textbox.value = '';\n    }\n    const bounds = this.quill.getBounds(this.quill.selection.savedRange);\n    if (bounds != null) {\n      this.position(bounds);\n    }\n    this.textbox.select();\n    this.textbox.setAttribute(\n      'placeholder',\n      this.textbox.getAttribute(`data-${mode}`) || '',\n    );\n    this.root.setAttribute('data-mode', mode);\n  }\n\n  restoreFocus() {\n    this.quill.focus({ preventScroll: true });\n  }\n\n  save() {\n    // @ts-expect-error Fix me later\n    let { value } = this.textbox;\n    switch (this.root.getAttribute('data-mode')) {\n      case 'link': {\n        const { scrollTop } = this.quill.root;\n        if (this.linkRange) {\n          this.quill.formatText(\n            this.linkRange,\n            'link',\n            value,\n            Emitter.sources.USER,\n          );\n          delete this.linkRange;\n        } else {\n          this.restoreFocus();\n          this.quill.format('link', value, Emitter.sources.USER);\n        }\n        this.quill.root.scrollTop = scrollTop;\n        break;\n      }\n      case 'video': {\n        value = extractVideoUrl(value);\n      } // eslint-disable-next-line no-fallthrough\n      case 'formula': {\n        if (!value) break;\n        const range = this.quill.getSelection(true);\n        if (range != null) {\n          const index = range.index + range.length;\n          this.quill.insertEmbed(\n            index,\n            // @ts-expect-error Fix me later\n            this.root.getAttribute('data-mode'),\n            value,\n            Emitter.sources.USER,\n          );\n          if (this.root.getAttribute('data-mode') === 'formula') {\n            this.quill.insertText(index + 1, ' ', Emitter.sources.USER);\n          }\n          this.quill.setSelection(index + 2, Emitter.sources.USER);\n        }\n        break;\n      }\n      default:\n    }\n    // @ts-expect-error Fix me later\n    this.textbox.value = '';\n    this.hide();\n  }\n}\n\nfunction extractVideoUrl(url: string) {\n  let match =\n    url.match(\n      /^(?:(https?):\\/\\/)?(?:(?:www|m)\\.)?youtube\\.com\\/watch.*v=([a-zA-Z0-9_-]+)/,\n    ) ||\n    url.match(/^(?:(https?):\\/\\/)?(?:(?:www|m)\\.)?youtu\\.be\\/([a-zA-Z0-9_-]+)/);\n  if (match) {\n    return `${match[1] || 'https'}://www.youtube.com/embed/${\n      match[2]\n    }?showinfo=0`;\n  }\n  // eslint-disable-next-line no-cond-assign\n  if ((match = url.match(/^(?:(https?):\\/\\/)?(?:www\\.)?vimeo\\.com\\/(\\d+)/))) {\n    return `${match[1] || 'https'}://player.vimeo.com/video/${match[2]}/`;\n  }\n  return url;\n}\n\nfunction fillSelect(\n  select: HTMLSelectElement,\n  values: Array<string | boolean>,\n  defaultValue: unknown = false,\n) {\n  values.forEach((value) => {\n    const option = document.createElement('option');\n    if (value === defaultValue) {\n      option.setAttribute('selected', 'selected');\n    } else {\n      option.setAttribute('value', String(value));\n    }\n    select.appendChild(option);\n  });\n}\n\nexport { BaseTooltip, BaseTheme as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,SAAA,GAAAC,OAAA;AAEA,IAAAC,QAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,MAAA,GAAAD,sBAAA,CAAAF,OAAA;AAEA,IAAAI,YAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,WAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,OAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,QAAA,GAAAL,sBAAA,CAAAF,OAAA;AAQA,IAAMQ,MAAM,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;AAEpD,IAAMC,MAAM,GAAG,CACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;AAED,IAAMC,KAAK,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,WAAW,CAAC;AAE3C,IAAMC,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC;AAEtC,IAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC;AAAA,IAEzCC,SAAS,GAAAC,OAAA,CAAAC,OAAA,0BAAAC,MAAA;EAIb,SAAAH,UAAYI,KAAY,EAAEC,OAAqB,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAL,OAAA,QAAAF,SAAA;IAC/CM,KAAA,OAAAE,WAAA,CAAAN,OAAA,QAAAF,SAAA,GAAMI,KAAK,EAAEC,OAAO;IACpB,IAAMI,SAAQ,GAAI,SAAZA,QAAQA,CAAIC,CAAa,EAAK;MAClC,IAAI,CAACC,QAAQ,CAACC,IAAI,CAACC,QAAQ,CAACT,KAAK,CAACU,IAAI,CAAC,EAAE;QACvCH,QAAQ,CAACC,IAAI,CAACG,mBAAmB,CAAC,OAAO,EAAEN,SAAQ,CAAC;QACpD;MACF;MACA,IACEH,KAAA,CAAKU,OAAO,IAAI,IAAI;MACpB;MACA,CAACV,KAAA,CAAKU,OAAO,CAACF,IAAI,CAACD,QAAQ,CAACH,CAAC,CAACO,MAAM,CAAC;MACrC;MACAN,QAAQ,CAACO,aAAa,KAAKZ,KAAA,CAAKU,OAAO,CAACG,OAAO,IAC/C,CAACb,KAAA,CAAKF,KAAK,CAACgB,QAAQ,CAAC,CAAC,EACtB;QACAd,KAAA,CAAKU,OAAO,CAACK,IAAI,CAAC,CAAC;MACrB;MACA,IAAIf,KAAA,CAAKgB,OAAO,IAAI,IAAI,EAAE;QACxBhB,KAAA,CAAKgB,OAAO,CAACC,OAAO,CAAE,UAAAC,MAAM,EAAK;UAC/B;UACA,IAAI,CAACA,MAAM,CAACC,SAAS,CAACZ,QAAQ,CAACH,CAAC,CAACO,MAAM,CAAC,EAAE;YACxCO,MAAM,CAACE,KAAK,CAAC,CAAC;UAChB;QACF,CAAC,CAAC;MACJ;IACF,CAAC;IACDtB,KAAK,CAACuB,OAAO,CAACC,SAAS,CAAC,OAAO,EAAEjB,QAAQ,CAACC,IAAI,EAAEH,SAAQ,CAAC;IAAA,OAAAH,KAAA;EAC3D;EAAA,IAAAuB,UAAA,CAAA3B,OAAA,EAAAF,SAAA,EAAAG,MAAA;EAAA,WAAA2B,aAAA,CAAA5B,OAAA,EAAAF,SAAA;IAAA+B,GAAA;IAAAC,KAAA,EAQA,SAAAC,SAASA,CAACC,IAAY,EAAE;MACtB,IAAMC,MAAM,OAAAC,cAAA,CAAAlC,OAAA,EAAAF,SAAA,yBAAmBkC,IAAI,EAAC;MACpC,IAAIA,IAAI,KAAK,SAAS,EAAE;QACtB;QACA,IAAI,CAACG,aAAa,CAACF,MAAM,CAAC;MAC5B;MACA,OAAOA,MAAM;IACf;EAAA;IAAAJ,GAAA;IAAAC,KAAA,EAEA,SAAAM,YAAYA,CACVC,OAAgC,EAChCC,KAAsD,EACtD;MACAC,KAAK,CAACC,IAAI,CAACH,OAAO,CAAC,CAAChB,OAAO,CAAE,UAAAoB,MAAM,EAAK;QACtC,IAAMC,SAAS,GAAGD,MAAM,CAACE,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE;QACpDD,SAAS,CAACE,KAAK,CAAC,KAAK,CAAC,CAACvB,OAAO,CAAE,UAAAW,IAAI,EAAK;UACvC,IAAI,CAACA,IAAI,CAACa,UAAU,CAAC,KAAK,CAAC,EAAE;UAC7Bb,IAAI,GAAGA,IAAI,CAACc,KAAK,CAAC,KAAK,CAACC,MAAM,CAAC;UAC/B,IAAIT,KAAK,CAACN,IAAI,CAAC,IAAI,IAAI,EAAE;UACzB,IAAIA,IAAI,KAAK,WAAW,EAAE;YACxB;YACAS,MAAM,CAACO,SAAS,GAAGV,KAAK,CAACN,IAAI,CAAC,CAAC,EAAE,CAAC,GAAGM,KAAK,CAACN,IAAI,CAAC,CAACiB,GAAG;UACtD,CAAC,MAAM,IAAI,OAAOX,KAAK,CAACN,IAAI,CAAC,KAAK,QAAQ,EAAE;YAC1C;YACAS,MAAM,CAACO,SAAS,GAAGV,KAAK,CAACN,IAAI,CAAC;UAChC,CAAC,MAAM;YACL;YACA,IAAMF,KAAK,GAAGW,MAAM,CAACX,KAAK,IAAI,EAAE;YAChC;YACA,IAAIA,KAAK,IAAI,IAAI,IAAIQ,KAAK,CAACN,IAAI,CAAC,CAACF,KAAK,CAAC,EAAE;cACvC;cACAW,MAAM,CAACO,SAAS,GAAGV,KAAK,CAACN,IAAI,CAAC,CAACF,KAAK,CAAC;YACvC;UACF;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EAAA;IAAAD,GAAA;IAAAC,KAAA,EAEA,SAAAoB,YAAYA,CACVC,OAAsC,EACtCb,KAAsD,EACtD;MAAA,IAAAc,MAAA;MACA,IAAI,CAAChC,OAAO,GAAGmB,KAAK,CAACC,IAAI,CAACW,OAAO,CAAC,CAACE,GAAG,CAAE,UAAAC,MAAM,EAAK;QACjD,IAAIA,MAAM,CAACC,SAAS,CAAC5C,QAAQ,CAAC,UAAU,CAAC,EAAE;UACzC,IAAI2C,MAAM,CAACE,aAAa,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE;YAC1CC,UAAU,CAACH,MAAM,EAAE7D,MAAM,CAAC;UAC5B;UACA,IAAI,IAAAiE,QAAA,CAAA1D,OAAA,EAAOsC,KAAK,CAACqB,KAAK,MAAK,QAAQ,EAAE;YACnC,OAAO,IAAIC,mBAAU,CAACN,MAAM,EAAEhB,KAAK,CAACqB,KAAK,CAAC;UAC5C;QACF;QACA,IACEL,MAAM,CAACC,SAAS,CAAC5C,QAAQ,CAAC,eAAe,CAAC,IAC1C2C,MAAM,CAACC,SAAS,CAAC5C,QAAQ,CAAC,UAAU,CAAC,EACrC;UACA,IAAMkD,MAAM,GAAGP,MAAM,CAACC,SAAS,CAAC5C,QAAQ,CAAC,eAAe,CAAC,GACrD,YAAY,GACZ,OAAO;UACX,IAAI2C,MAAM,CAACE,aAAa,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE;YAC1CC,UAAU,CACRH,MAAM,EACN5D,MAAM,EACNmE,MAAM,KAAK,YAAY,GAAG,SAAS,GAAG,SACxC,CAAC;UACH;UACA,OAAO,IAAIC,oBAAW,CAACR,MAAM,EAAEhB,KAAK,CAACuB,MAAM,CAAW,CAAC;QACzD;QACA,IAAIP,MAAM,CAACE,aAAa,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE;UAC1C,IAAIF,MAAM,CAACC,SAAS,CAAC5C,QAAQ,CAAC,SAAS,CAAC,EAAE;YACxC8C,UAAU,CAACH,MAAM,EAAE3D,KAAK,CAAC;UAC3B,CAAC,MAAM,IAAI2D,MAAM,CAACC,SAAS,CAAC5C,QAAQ,CAAC,WAAW,CAAC,EAAE;YACjD8C,UAAU,CAACH,MAAM,EAAE1D,OAAO,CAAC;UAC7B,CAAC,MAAM,IAAI0D,MAAM,CAACC,SAAS,CAAC5C,QAAQ,CAAC,SAAS,CAAC,EAAE;YAC/C8C,UAAU,CAACH,MAAM,EAAEzD,KAAK,CAAC;UAC3B;QACF;QACA,OAAO,IAAIkE,eAAM,CAACT,MAAM,CAAC;MAC3B,CAAC,CAAC;MACF,IAAMU,MAAM,GAAG,SAATA,MAAMA,CAAA,EAAS;QACnBZ,MAAI,CAAChC,OAAO,CAACC,OAAO,CAAE,UAAAC,MAAM,EAAK;UAC/BA,MAAM,CAAC0C,MAAM,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC;MACD,IAAI,CAAC9D,KAAK,CAAC+D,EAAE,CAACC,gBAAO,CAACC,MAAM,CAACC,aAAa,EAAEJ,MAAM,CAAC;IACrD;EAAA;AAAA,EA3HsBK,cAAK;AA6H7BvE,SAAS,CAACwE,QAAQ,GAAG,IAAAC,eAAK,EAAC,CAAC,CAAC,EAAEF,cAAK,CAACC,QAAQ,EAAE;EAC7CE,OAAO,EAAE;IACPC,OAAO,EAAE;MACPC,QAAQ,EAAE;QACRC,OAAO,WAAPA,OAAOA,CAAA,EAAG;UACR,IAAI,CAACzE,KAAK,CAAC0E,KAAK,CAAC9D,OAAO,CAAC+D,IAAI,CAAC,SAAS,CAAC;QAC1C,CAAC;QACDC,KAAK,WAALA,KAAKA,CAAA,EAAG;UAAA,IAAAC,MAAA;UACN,IAAIC,SAAS,GAAG,IAAI,CAACzD,SAAS,CAACiC,aAAa,CAC1C,2BACF,CAAC;UACD,IAAIwB,SAAS,IAAI,IAAI,EAAE;YACrBA,SAAS,GAAGvE,QAAQ,CAACwE,aAAa,CAAC,OAAO,CAAC;YAC3CD,SAAS,CAACE,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC;YACtCF,SAAS,CAACE,YAAY,CACpB,QAAQ,EACR,IAAI,CAAChF,KAAK,CAACiF,QAAQ,CAAChF,OAAO,CAACiF,SAAS,CAACC,IAAI,CAAC,IAAI,CACjD,CAAC;YACDL,SAAS,CAACzB,SAAS,CAAC+B,GAAG,CAAC,UAAU,CAAC;YACnCN,SAAS,CAACO,gBAAgB,CAAC,QAAQ,EAAE,YAAM;cACzC,IAAMC,KAAK,GAAGT,MAAI,CAAC7E,KAAK,CAACuF,YAAY,CAAC,IAAI,CAAC;cAC3CV,MAAI,CAAC7E,KAAK,CAACiF,QAAQ,CAACO,MAAM,CAACF,KAAK,EAAER,SAAS,CAACW,KAAK,CAAC;cAClDX,SAAS,CAAClD,KAAK,GAAG,EAAE;YACtB,CAAC,CAAC;YACF,IAAI,CAACP,SAAS,CAACqE,WAAW,CAACZ,SAAS,CAAC;UACvC;UACAA,SAAS,CAACa,KAAK,CAAC,CAAC;QACnB,CAAC;QACDC,KAAK,WAALA,KAAKA,CAAA,EAAG;UACN,IAAI,CAAC5F,KAAK,CAAC0E,KAAK,CAAC9D,OAAO,CAAC+D,IAAI,CAAC,OAAO,CAAC;QACxC;MACF;IACF;EACF;AACF,CAAC,CAAC;AAAA,IAEIkB,WAAW,GAAAhG,OAAA,CAAAgG,WAAA,0BAAAC,QAAA;EAIf,SAAAD,YAAY7F,KAAY,EAAE+F,eAA6B,EAAE;IAAA,IAAAC,MAAA;IAAA,IAAA7F,gBAAA,CAAAL,OAAA,QAAA+F,WAAA;IACvDG,MAAA,OAAA5F,WAAA,CAAAN,OAAA,QAAA+F,WAAA,GAAM7F,KAAK,EAAE+F,eAAe;IAC5BC,MAAA,CAAKjF,OAAO,GAAGiF,MAAA,CAAKtF,IAAI,CAAC4C,aAAa,CAAC,oBAAoB,CAAC;IAC5D0C,MAAA,CAAKC,MAAM,CAAC,CAAC;IAAA,OAAAD,MAAA;EACf;EAAA,IAAAvE,UAAA,CAAA3B,OAAA,EAAA+F,WAAA,EAAAC,QAAA;EAAA,WAAApE,aAAA,CAAA5B,OAAA,EAAA+F,WAAA;IAAAlE,GAAA;IAAAC,KAAA,EAEA,SAAAqE,MAAMA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACP;MACA,IAAI,CAACnF,OAAO,CAACsE,gBAAgB,CAAC,SAAS,EAAG,UAAAc,KAAK,EAAK;QAClD,IAAIA,KAAK,CAACxE,GAAG,KAAK,OAAO,EAAE;UACzBuE,MAAI,CAACE,IAAI,CAAC,CAAC;UACXD,KAAK,CAACE,cAAc,CAAC,CAAC;QACxB,CAAC,MAAM,IAAIF,KAAK,CAACxE,GAAG,KAAK,QAAQ,EAAE;UACjCuE,MAAI,CAACI,MAAM,CAAC,CAAC;UACbH,KAAK,CAACE,cAAc,CAAC,CAAC;QACxB;MACF,CAAC,CAAC;IACJ;EAAA;IAAA1E,GAAA;IAAAC,KAAA,EAEA,SAAA0E,MAAMA,CAAA,EAAG;MACP,IAAI,CAACrF,IAAI,CAAC,CAAC;MACX,IAAI,CAACsF,YAAY,CAAC,CAAC;IACrB;EAAA;IAAA5E,GAAA;IAAAC,KAAA,EAEA,SAAA+C,IAAIA,CAAA,EAA+C;MAAA,IAA9C6B,IAAI,GAAAC,SAAA,CAAA5D,MAAA,QAAA4D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,MAAM;MAAA,IAAEE,OAAsB,GAAAF,SAAA,CAAA5D,MAAA,QAAA4D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,IAAI;MAC/C,IAAI,CAAC/F,IAAI,CAAC2C,SAAS,CAACuD,MAAM,CAAC,WAAW,CAAC;MACvC,IAAI,CAAClG,IAAI,CAAC2C,SAAS,CAAC+B,GAAG,CAAC,YAAY,CAAC;MACrC,IAAI,IAAI,CAACrE,OAAO,IAAI,IAAI,EAAE;MAE1B,IAAI4F,OAAO,IAAI,IAAI,EAAE;QACnB,IAAI,CAAC5F,OAAO,CAACa,KAAK,GAAG+E,OAAO;MAC9B,CAAC,MAAM,IAAIH,IAAI,KAAK,IAAI,CAAC9F,IAAI,CAAC+B,YAAY,CAAC,WAAW,CAAC,EAAE;QACvD,IAAI,CAAC1B,OAAO,CAACa,KAAK,GAAG,EAAE;MACzB;MACA,IAAMiF,MAAM,GAAG,IAAI,CAAC7G,KAAK,CAAC8G,SAAS,CAAC,IAAI,CAAC9G,KAAK,CAAC+G,SAAS,CAACC,UAAU,CAAC;MACpE,IAAIH,MAAM,IAAI,IAAI,EAAE;QAClB,IAAI,CAACI,QAAQ,CAACJ,MAAM,CAAC;MACvB;MACA,IAAI,CAAC9F,OAAO,CAACqC,MAAM,CAAC,CAAC;MACrB,IAAI,CAACrC,OAAO,CAACiE,YAAY,CACvB,aAAa,EACb,IAAI,CAACjE,OAAO,CAAC0B,YAAY,SAAAyE,MAAA,CAASV,IAAK,CAAC,CAAC,IAAI,EAC/C,CAAC;MACD,IAAI,CAAC9F,IAAI,CAACsE,YAAY,CAAC,WAAW,EAAEwB,IAAI,CAAC;IAC3C;EAAA;IAAA7E,GAAA;IAAAC,KAAA,EAEA,SAAA2E,YAAYA,CAAA,EAAG;MACb,IAAI,CAACvG,KAAK,CAACmH,KAAK,CAAC;QAAEC,aAAa,EAAE;MAAK,CAAC,CAAC;IAC3C;EAAA;IAAAzF,GAAA;IAAAC,KAAA,EAEA,SAAAwE,IAAIA,CAAA,EAAG;MACL;MACA,IAAMxE,KAAA,GAAU,IAAI,CAACb,OAAO,CAAtBa,KAAA;MACN,QAAQ,IAAI,CAAClB,IAAI,CAAC+B,YAAY,CAAC,WAAW,CAAC;QACzC,KAAK,MAAM;UAAE;YACX,IAAQ4E,SAAA,GAAc,IAAI,CAACrH,KAAK,CAACU,IAAI,CAA7B2G,SAAA;YACR,IAAI,IAAI,CAACC,SAAS,EAAE;cAClB,IAAI,CAACtH,KAAK,CAACuH,UAAU,CACnB,IAAI,CAACD,SAAS,EACd,MAAM,EACN1F,KAAK,EACLoC,gBAAO,CAACwD,OAAO,CAACC,IAClB,CAAC;cACD,OAAO,IAAI,CAACH,SAAS;YACvB,CAAC,MAAM;cACL,IAAI,CAACf,YAAY,CAAC,CAAC;cACnB,IAAI,CAACvG,KAAK,CAAC2D,MAAM,CAAC,MAAM,EAAE/B,KAAK,EAAEoC,gBAAO,CAACwD,OAAO,CAACC,IAAI,CAAC;YACxD;YACA,IAAI,CAACzH,KAAK,CAACU,IAAI,CAAC2G,SAAS,GAAGA,SAAS;YACrC;UACF;QACA,KAAK,OAAO;UAAE;YACZzF,KAAK,GAAG8F,eAAe,CAAC9F,KAAK,CAAC;UAChC;QAAE;QACF,KAAK,SAAS;UAAE;YACd,IAAI,CAACA,KAAK,EAAE;YACZ,IAAM0D,KAAK,GAAG,IAAI,CAACtF,KAAK,CAACuF,YAAY,CAAC,IAAI,CAAC;YAC3C,IAAID,KAAK,IAAI,IAAI,EAAE;cACjB,IAAMqC,KAAK,GAAGrC,KAAK,CAACqC,KAAK,GAAGrC,KAAK,CAACzC,MAAM;cACxC,IAAI,CAAC7C,KAAK,CAAC4H,WAAW,CACpBD,KAAK;cACL;cACA,IAAI,CAACjH,IAAI,CAAC+B,YAAY,CAAC,WAAW,CAAC,EACnCb,KAAK,EACLoC,gBAAO,CAACwD,OAAO,CAACC,IAClB,CAAC;cACD,IAAI,IAAI,CAAC/G,IAAI,CAAC+B,YAAY,CAAC,WAAW,CAAC,KAAK,SAAS,EAAE;gBACrD,IAAI,CAACzC,KAAK,CAAC6H,UAAU,CAACF,KAAK,GAAG,CAAC,EAAE,GAAG,EAAE3D,gBAAO,CAACwD,OAAO,CAACC,IAAI,CAAC;cAC7D;cACA,IAAI,CAACzH,KAAK,CAAC8H,YAAY,CAACH,KAAK,GAAG,CAAC,EAAE3D,gBAAO,CAACwD,OAAO,CAACC,IAAI,CAAC;YAC1D;YACA;UACF;QACA;MACF;MACA;MACA,IAAI,CAAC1G,OAAO,CAACa,KAAK,GAAG,EAAE;MACvB,IAAI,CAACX,IAAI,CAAC,CAAC;IACb;EAAA;AAAA,EAtGwB8G,gBAAO;AAyGjC,SAASL,eAAeA,CAACM,GAAW,EAAE;EACpC,IAAIC,KAAK,GACPD,GAAG,CAACC,KAAK,CACP,4EACF,CAAC,IACDD,GAAG,CAACC,KAAK,CAAC,gEAAgE,CAAC;EAC7E,IAAIA,KAAK,EAAE;IACT,UAAAf,MAAA,CAAUe,KAAK,CAAC,CAAC,CAAC,IAAI,OAAQ,+BAAAf,MAAA,CAC5Be,KAAK,CAAC,CAAC,CACR;EACH;EACA;EACA,IAAKA,KAAK,GAAGD,GAAG,CAACC,KAAK,CAAC,gDAAgD,CAAC,EAAG;IACzE,UAAAf,MAAA,CAAUe,KAAK,CAAC,CAAC,CAAC,IAAI,OAAQ,gCAAAf,MAAA,CAA4Be,KAAK,CAAC,CAAC,CAAE;EACrE;EACA,OAAOD,GAAG;AACZ;AAEA,SAASzE,UAAUA,CACjBH,MAAyB,EACzB8E,MAA+B,EAE/B;EAAA,IADAC,YAAqB,GAAA1B,SAAA,CAAA5D,MAAA,QAAA4D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK;EAE7ByB,MAAM,CAAC/G,OAAO,CAAE,UAAAS,KAAK,EAAK;IACxB,IAAMwG,MAAM,GAAG7H,QAAQ,CAACwE,aAAa,CAAC,QAAQ,CAAC;IAC/C,IAAInD,KAAK,KAAKuG,YAAY,EAAE;MAC1BC,MAAM,CAACpD,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC;IAC7C,CAAC,MAAM;MACLoD,MAAM,CAACpD,YAAY,CAAC,OAAO,EAAEqD,MAAM,CAACzG,KAAK,CAAC,CAAC;IAC7C;IACAwB,MAAM,CAACsC,WAAW,CAAC0C,MAAM,CAAC;EAC5B,CAAC,CAAC;AACJ", "ignoreList": []}]}