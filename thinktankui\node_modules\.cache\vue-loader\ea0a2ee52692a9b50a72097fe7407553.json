{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\tool\\gen\\genInfoForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\tool\\gen\\genInfoForm.vue", "mtime": 1749104047651}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgVHJlZXNlbGVjdCBmcm9tICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdCI7DQppbXBvcnQgIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0L2Rpc3QvdnVlLXRyZWVzZWxlY3QuY3NzIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBjb21wb25lbnRzOiB7IFRyZWVzZWxlY3QgfSwNCiAgcHJvcHM6IHsNCiAgICBpbmZvOiB7DQogICAgICB0eXBlOiBPYmplY3QsDQogICAgICBkZWZhdWx0OiBudWxsDQogICAgfSwNCiAgICB0YWJsZXM6IHsNCiAgICAgIHR5cGU6IEFycmF5LA0KICAgICAgZGVmYXVsdDogbnVsbA0KICAgIH0sDQogICAgbWVudXM6IHsNCiAgICAgIHR5cGU6IEFycmF5LA0KICAgICAgZGVmYXVsdDogW10NCiAgICB9LA0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBzdWJDb2x1bW5zOiBbXSwNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIHRwbENhdGVnb3J5OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqeeUn+aIkOaooeadvyIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICAgIHBhY2thZ2VOYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+i+k+WFpeeUn+aIkOWMhei3r+W+hCIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICAgIG1vZHVsZU5hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl55Sf5oiQ5qih5Z2X5ZCNIiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgYnVzaW5lc3NOYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+i+k+WFpeeUn+aIkOS4muWKoeWQjSIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICAgIGZ1bmN0aW9uTmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fovpPlhaXnlJ/miJDlip/og73lkI0iLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgfQ0KICAgIH07DQogIH0sDQogIHdhdGNoOiB7DQogICAgJ2luZm8uc3ViVGFibGVOYW1lJzogZnVuY3Rpb24odmFsKSB7DQogICAgICB0aGlzLnNldFN1YlRhYmxlQ29sdW1ucyh2YWwpOw0KICAgIH0sDQogICAgJ2luZm8udHBsV2ViVHlwZSc6IGZ1bmN0aW9uKHZhbCkgew0KICAgICAgaWYgKHZhbCA9PT0gJycpIHsNCiAgICAgICAgdGhpcy5pbmZvLnRwbFdlYlR5cGUgPSAiZWxlbWVudC11aSI7DQogICAgICB9DQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOi9rOaNouiPnOWNleaVsOaNrue7k+aehCAqLw0KICAgIG5vcm1hbGl6ZXIobm9kZSkgew0KICAgICAgaWYgKG5vZGUuY2hpbGRyZW4gJiYgIW5vZGUuY2hpbGRyZW4ubGVuZ3RoKSB7DQogICAgICAgIGRlbGV0ZSBub2RlLmNoaWxkcmVuOw0KICAgICAgfQ0KICAgICAgcmV0dXJuIHsNCiAgICAgICAgaWQ6IG5vZGUubWVudUlkLA0KICAgICAgICBsYWJlbDogbm9kZS5tZW51TmFtZSwNCiAgICAgICAgY2hpbGRyZW46IG5vZGUuY2hpbGRyZW4NCiAgICAgIH07DQogICAgfSwNCiAgICAvKiog6YCJ5oup5a2Q6KGo5ZCN6Kem5Y+RICovDQogICAgc3ViU2VsZWN0Q2hhbmdlKHZhbHVlKSB7DQogICAgICB0aGlzLmluZm8uc3ViVGFibGVGa05hbWUgPSAnJzsNCiAgICB9LA0KICAgIC8qKiDpgInmi6nnlJ/miJDmqKHmnb/op6blj5EgKi8NCiAgICB0cGxTZWxlY3RDaGFuZ2UodmFsdWUpIHsNCiAgICAgIGlmKHZhbHVlICE9PSAnc3ViJykgew0KICAgICAgICB0aGlzLmluZm8uc3ViVGFibGVOYW1lID0gJyc7DQogICAgICAgIHRoaXMuaW5mby5zdWJUYWJsZUZrTmFtZSA9ICcnOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOiuvue9ruWFs+iBlOWklumUriAqLw0KICAgIHNldFN1YlRhYmxlQ29sdW1ucyh2YWx1ZSkgew0KICAgICAgZm9yICh2YXIgaXRlbSBpbiB0aGlzLnRhYmxlcykgew0KICAgICAgICBjb25zdCBuYW1lID0gdGhpcy50YWJsZXNbaXRlbV0udGFibGVOYW1lOw0KICAgICAgICBpZiAodmFsdWUgPT09IG5hbWUpIHsNCiAgICAgICAgICB0aGlzLnN1YkNvbHVtbnMgPSB0aGlzLnRhYmxlc1tpdGVtXS5jb2x1bW5zOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["genInfoForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiOA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "genInfoForm.vue", "sourceRoot": "src/views/tool/gen", "sourcesContent": ["<template>\r\n  <el-form ref=\"genInfoForm\" :model=\"info\" :rules=\"rules\" label-width=\"150px\">\r\n    <el-row>\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"tplCategory\">\r\n          <span slot=\"label\">生成模板</span>\r\n          <el-select v-model=\"info.tplCategory\" @change=\"tplSelectChange\">\r\n            <el-option label=\"单表（增删改查）\" value=\"crud\" />\r\n            <el-option label=\"树表（增删改查）\" value=\"tree\" />\r\n            <el-option label=\"主子表（增删改查）\" value=\"sub\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"tplWebType\">\r\n          <span slot=\"label\">前端类型</span>\r\n          <el-select v-model=\"info.tplWebType\">\r\n            <el-option label=\"Vue2 Element UI 模版\" value=\"element-ui\" />\r\n            <el-option label=\"Vue3 Element Plus 模版\" value=\"element-plus\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"packageName\">\r\n          <span slot=\"label\">\r\n            生成包路径\r\n            <el-tooltip content=\"生成在哪个java包下，例如 com.ruoyi.system\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.packageName\" />\r\n        </el-form-item>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"moduleName\">\r\n          <span slot=\"label\">\r\n            生成模块名\r\n            <el-tooltip content=\"可理解为子系统名，例如 system\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.moduleName\" />\r\n        </el-form-item>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"businessName\">\r\n          <span slot=\"label\">\r\n            生成业务名\r\n            <el-tooltip content=\"可理解为功能英文名，例如 user\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.businessName\" />\r\n        </el-form-item>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"functionName\">\r\n          <span slot=\"label\">\r\n            生成功能名\r\n            <el-tooltip content=\"用作类描述，例如 用户\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.functionName\" />\r\n        </el-form-item>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"genType\">\r\n          <span slot=\"label\">\r\n            生成代码方式\r\n            <el-tooltip content=\"默认为zip压缩包下载，也可以自定义生成路径\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n          </span>\r\n          <el-radio v-model=\"info.genType\" label=\"0\">zip压缩包</el-radio>\r\n          <el-radio v-model=\"info.genType\" label=\"1\">自定义路径</el-radio>\r\n        </el-form-item>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\">\r\n        <el-form-item>\r\n          <span slot=\"label\">\r\n            上级菜单\r\n            <el-tooltip content=\"分配到指定菜单下，例如 系统管理\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n          </span>\r\n          <treeselect\r\n            :append-to-body=\"true\"\r\n            v-model=\"info.parentMenuId\"\r\n            :options=\"menus\"\r\n            :normalizer=\"normalizer\"\r\n            :show-count=\"true\"\r\n            placeholder=\"请选择系统菜单\"\r\n          />\r\n        </el-form-item>\r\n      </el-col>\r\n\r\n      <el-col :span=\"24\" v-if=\"info.genType == '1'\">\r\n        <el-form-item prop=\"genPath\">\r\n          <span slot=\"label\">\r\n            自定义路径\r\n            <el-tooltip content=\"填写磁盘绝对路径，若不填写，则生成到当前Web项目下\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.genPath\">\r\n            <el-dropdown slot=\"append\">\r\n              <el-button type=\"primary\">\r\n                最近路径快速选择\r\n                <i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n              </el-button>\r\n              <el-dropdown-menu slot=\"dropdown\">\r\n                <el-dropdown-item @click.native=\"info.genPath = '/'\">恢复默认的生成基础路径</el-dropdown-item>\r\n              </el-dropdown-menu>\r\n            </el-dropdown>\r\n          </el-input>\r\n        </el-form-item>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-row v-show=\"info.tplCategory == 'tree'\">\r\n      <h4 class=\"form-header\">其他信息</h4>\r\n      <el-col :span=\"12\">\r\n        <el-form-item>\r\n          <span slot=\"label\">\r\n            树编码字段\r\n            <el-tooltip content=\"树显示的编码字段名， 如：dept_id\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n          </span>\r\n          <el-select v-model=\"info.treeCode\" placeholder=\"请选择\">\r\n            <el-option\r\n              v-for=\"(column, index) in info.columns\"\r\n              :key=\"index\"\r\n              :label=\"column.columnName + '：' + column.columnComment\"\r\n              :value=\"column.columnName\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item>\r\n          <span slot=\"label\">\r\n            树父编码字段\r\n            <el-tooltip content=\"树显示的父编码字段名， 如：parent_Id\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n          </span>\r\n          <el-select v-model=\"info.treeParentCode\" placeholder=\"请选择\">\r\n            <el-option\r\n              v-for=\"(column, index) in info.columns\"\r\n              :key=\"index\"\r\n              :label=\"column.columnName + '：' + column.columnComment\"\r\n              :value=\"column.columnName\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item>\r\n          <span slot=\"label\">\r\n            树名称字段\r\n            <el-tooltip content=\"树节点的显示名称字段名， 如：dept_name\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n          </span>\r\n          <el-select v-model=\"info.treeName\" placeholder=\"请选择\">\r\n            <el-option\r\n              v-for=\"(column, index) in info.columns\"\r\n              :key=\"index\"\r\n              :label=\"column.columnName + '：' + column.columnComment\"\r\n              :value=\"column.columnName\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col>\r\n    </el-row>\r\n    <el-row v-show=\"info.tplCategory == 'sub'\">\r\n      <h4 class=\"form-header\">关联信息</h4>\r\n      <el-col :span=\"12\">\r\n        <el-form-item>\r\n          <span slot=\"label\">\r\n            关联子表的表名\r\n            <el-tooltip content=\"关联子表的表名， 如：sys_user\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n          </span>\r\n          <el-select v-model=\"info.subTableName\" placeholder=\"请选择\" @change=\"subSelectChange\">\r\n            <el-option\r\n              v-for=\"(table, index) in tables\"\r\n              :key=\"index\"\r\n              :label=\"table.tableName + '：' + table.tableComment\"\r\n              :value=\"table.tableName\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item>\r\n          <span slot=\"label\">\r\n            子表关联的外键名\r\n            <el-tooltip content=\"子表关联的外键名， 如：user_id\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n          </span>\r\n          <el-select v-model=\"info.subTableFkName\" placeholder=\"请选择\">\r\n            <el-option\r\n              v-for=\"(column, index) in subColumns\"\r\n              :key=\"index\"\r\n              :label=\"column.columnName + '：' + column.columnComment\"\r\n              :value=\"column.columnName\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col>\r\n    </el-row>\r\n  </el-form>\r\n</template>\r\n\r\n<script>\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\n\r\nexport default {\r\n  components: { Treeselect },\r\n  props: {\r\n    info: {\r\n      type: Object,\r\n      default: null\r\n    },\r\n    tables: {\r\n      type: Array,\r\n      default: null\r\n    },\r\n    menus: {\r\n      type: Array,\r\n      default: []\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      subColumns: [],\r\n      rules: {\r\n        tplCategory: [\r\n          { required: true, message: \"请选择生成模板\", trigger: \"blur\" }\r\n        ],\r\n        packageName: [\r\n          { required: true, message: \"请输入生成包路径\", trigger: \"blur\" }\r\n        ],\r\n        moduleName: [\r\n          { required: true, message: \"请输入生成模块名\", trigger: \"blur\" }\r\n        ],\r\n        businessName: [\r\n          { required: true, message: \"请输入生成业务名\", trigger: \"blur\" }\r\n        ],\r\n        functionName: [\r\n          { required: true, message: \"请输入生成功能名\", trigger: \"blur\" }\r\n        ],\r\n      }\r\n    };\r\n  },\r\n  watch: {\r\n    'info.subTableName': function(val) {\r\n      this.setSubTableColumns(val);\r\n    },\r\n    'info.tplWebType': function(val) {\r\n      if (val === '') {\r\n        this.info.tplWebType = \"element-ui\";\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    /** 转换菜单数据结构 */\r\n    normalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children;\r\n      }\r\n      return {\r\n        id: node.menuId,\r\n        label: node.menuName,\r\n        children: node.children\r\n      };\r\n    },\r\n    /** 选择子表名触发 */\r\n    subSelectChange(value) {\r\n      this.info.subTableFkName = '';\r\n    },\r\n    /** 选择生成模板触发 */\r\n    tplSelectChange(value) {\r\n      if(value !== 'sub') {\r\n        this.info.subTableName = '';\r\n        this.info.subTableFkName = '';\r\n      }\r\n    },\r\n    /** 设置关联外键 */\r\n    setSubTableColumns(value) {\r\n      for (var item in this.tables) {\r\n        const name = this.tables[item].tableName;\r\n        if (value === name) {\r\n          this.subColumns = this.tables[item].columns;\r\n          break;\r\n        }\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}