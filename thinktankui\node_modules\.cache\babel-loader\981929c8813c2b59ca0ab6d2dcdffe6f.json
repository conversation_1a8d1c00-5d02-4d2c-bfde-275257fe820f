{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\size.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\size.js", "mtime": 1749104422649}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLlNpemVTdHlsZSA9IGV4cG9ydHMuU2l6ZUNsYXNzID0gdm9pZCAwOwp2YXIgX3BhcmNobWVudCA9IHJlcXVpcmUoInBhcmNobWVudCIpOwp2YXIgU2l6ZUNsYXNzID0gZXhwb3J0cy5TaXplQ2xhc3MgPSBuZXcgX3BhcmNobWVudC5DbGFzc0F0dHJpYnV0b3IoJ3NpemUnLCAncWwtc2l6ZScsIHsKICBzY29wZTogX3BhcmNobWVudC5TY29wZS5JTkxJTkUsCiAgd2hpdGVsaXN0OiBbJ3NtYWxsJywgJ2xhcmdlJywgJ2h1Z2UnXQp9KTsKdmFyIFNpemVTdHlsZSA9IGV4cG9ydHMuU2l6ZVN0eWxlID0gbmV3IF9wYXJjaG1lbnQuU3R5bGVBdHRyaWJ1dG9yKCdzaXplJywgJ2ZvbnQtc2l6ZScsIHsKICBzY29wZTogX3BhcmNobWVudC5TY29wZS5JTkxJTkUsCiAgd2hpdGVsaXN0OiBbJzEwcHgnLCAnMThweCcsICczMnB4J10KfSk7"}, {"version": 3, "names": ["_parchment", "require", "SizeClass", "exports", "ClassAttributor", "scope", "<PERSON><PERSON>", "INLINE", "whitelist", "SizeStyle", "StyleAttributor"], "sources": ["../../src/formats/size.ts"], "sourcesContent": ["import { ClassAttributor, Scope, StyleAttributor } from 'parchment';\n\nconst SizeClass = new ClassAttributor('size', 'ql-size', {\n  scope: Scope.INLINE,\n  whitelist: ['small', 'large', 'huge'],\n});\nconst SizeStyle = new StyleAttributor('size', 'font-size', {\n  scope: Scope.INLINE,\n  whitelist: ['10px', '18px', '32px'],\n});\n\nexport { SizeClass, SizeStyle };\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAEA,IAAMC,SAAS,GAAAC,OAAA,CAAAD,SAAA,GAAG,IAAIE,0BAAe,CAAC,MAAM,EAAE,SAAS,EAAE;EACvDC,KAAK,EAAEC,gBAAK,CAACC,MAAM;EACnBC,SAAS,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM;AACtC,CAAC,CAAC;AACF,IAAMC,SAAS,GAAAN,OAAA,CAAAM,SAAA,GAAG,IAAIC,0BAAe,CAAC,MAAM,EAAE,WAAW,EAAE;EACzDL,KAAK,EAAEC,gBAAK,CAACC,MAAM;EACnBC,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM;AACpC,CAAC,CAAC", "ignoreList": []}]}