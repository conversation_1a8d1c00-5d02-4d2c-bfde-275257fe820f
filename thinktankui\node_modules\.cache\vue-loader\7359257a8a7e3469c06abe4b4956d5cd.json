{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\layout\\components\\Sidebar\\Link.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\layout\\components\\Sidebar\\Link.vue", "mtime": 1749104047626}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBpc0V4dGVybmFsIH0gZnJvbSAnQC91dGlscy92YWxpZGF0ZScNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBwcm9wczogew0KICAgIHRvOiB7DQogICAgICB0eXBlOiBbU3RyaW5nLCBPYmplY3RdLA0KICAgICAgcmVxdWlyZWQ6IHRydWUNCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgaXNFeHRlcm5hbCgpIHsNCiAgICAgIHJldHVybiBpc0V4dGVybmFsKHRoaXMudG8pDQogICAgfSwNCiAgICB0eXBlKCkgew0KICAgICAgaWYgKHRoaXMuaXNFeHRlcm5hbCkgew0KICAgICAgICByZXR1cm4gJ2EnDQogICAgICB9DQogICAgICByZXR1cm4gJ3JvdXRlci1saW5rJw0KICAgIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGxpbmtQcm9wcyh0bykgew0KICAgICAgaWYgKHRoaXMuaXNFeHRlcm5hbCkgew0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgIGhyZWY6IHRvLA0KICAgICAgICAgIHRhcmdldDogJ19ibGFuaycsDQogICAgICAgICAgcmVsOiAnbm9vcGVuZXInDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHJldHVybiB7DQogICAgICAgIHRvOiB0bw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["Link.vue"], "names": [], "mappings": ";;;;;;;AAOA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Link.vue", "sourceRoot": "src/layout/components/Sidebar", "sourcesContent": ["<template>\r\n  <component :is=\"type\" v-bind=\"linkProps(to)\">\r\n    <slot />\r\n  </component>\r\n</template>\r\n\r\n<script>\r\nimport { isExternal } from '@/utils/validate'\r\n\r\nexport default {\r\n  props: {\r\n    to: {\r\n      type: [String, Object],\r\n      required: true\r\n    }\r\n  },\r\n  computed: {\r\n    isExternal() {\r\n      return isExternal(this.to)\r\n    },\r\n    type() {\r\n      if (this.isExternal) {\r\n        return 'a'\r\n      }\r\n      return 'router-link'\r\n    }\r\n  },\r\n  methods: {\r\n    linkProps(to) {\r\n      if (this.isExternal) {\r\n        return {\r\n          href: to,\r\n          target: '_blank',\r\n          rel: 'noopener'\r\n        }\r\n      }\r\n      return {\r\n        to: to\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}