{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\api\\monitor\\online.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\api\\monitor\\online.js", "mtime": 1749104047591}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749104418875}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmZvcmNlTG9nb3V0ID0gZm9yY2VMb2dvdXQ7CmV4cG9ydHMubGlzdCA9IGxpc3Q7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovLyDmn6Xor6LlnKjnur/nlKjmiLfliJfooagKZnVuY3Rpb24gbGlzdChxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL21vbml0b3Ivb25saW5lL2xpc3QnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQoKLy8g5by66YCA55So5oi3CmZ1bmN0aW9uIGZvcmNlTG9nb3V0KHRva2VuSWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9tb25pdG9yL29ubGluZS8nICsgdG9rZW5JZCwKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "list", "query", "request", "url", "method", "params", "forceLogout", "tokenId"], "sources": ["D:/thinktank/thinktankui/src/api/monitor/online.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询在线用户列表\r\nexport function list(query) {\r\n  return request({\r\n    url: '/monitor/online/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 强退用户\r\nexport function forceLogout(tokenId) {\r\n  return request({\r\n    url: '/monitor/online/' + tokenId,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,IAAIA,CAACC,KAAK,EAAE;EAC1B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,WAAWA,CAACC,OAAO,EAAE;EACnC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB,GAAGI,OAAO;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}