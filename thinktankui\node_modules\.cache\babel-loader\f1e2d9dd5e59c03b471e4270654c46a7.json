{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\monitor\\cache\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\monitor\\cache\\index.vue", "mtime": 1749104047642}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_cache", "require", "echarts", "_interopRequireWildcard", "name", "data", "commandstats", "usedmemory", "cache", "created", "getList", "openLoading", "methods", "_this", "getCache", "then", "response", "$modal", "closeLoading", "init", "$refs", "setOption", "tooltip", "trigger", "formatter", "series", "type", "roseType", "radius", "center", "commandStats", "animationEasing", "animationDuration", "info", "used_memory_human", "min", "max", "detail", "value", "parseFloat", "window", "addEventListener", "resize", "loading"], "sources": ["src/views/monitor/cache/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <el-col :span=\"24\" class=\"card-box\">\r\n        <el-card>\r\n          <div slot=\"header\"><span><i class=\"el-icon-monitor\"></i> 基本信息</span></div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <table cellspacing=\"0\" style=\"width: 100%\">\r\n              <tbody>\r\n                <tr>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">Redis版本</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.redis_version }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">运行模式</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.redis_mode == \"standalone\" ? \"单机\" : \"集群\" }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">端口</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.tcp_port }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">客户端数</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.connected_clients }}</div></td>\r\n                </tr>\r\n                <tr>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">运行时间(天)</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.uptime_in_days }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">使用内存</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.used_memory_human }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">使用CPU</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ parseFloat(cache.info.used_cpu_user_children).toFixed(2) }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">内存配置</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.maxmemory_human }}</div></td>\r\n                </tr>\r\n                <tr>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">AOF是否开启</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.aof_enabled == \"0\" ? \"否\" : \"是\" }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">RDB是否成功</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.rdb_last_bgsave_status }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">Key数量</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.dbSize\">{{ cache.dbSize }} </div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">网络入口/出口</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.instantaneous_input_kbps }}kps/{{cache.info.instantaneous_output_kbps}}kps</div></td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\" class=\"card-box\">\r\n        <el-card>\r\n          <div slot=\"header\"><span><i class=\"el-icon-pie-chart\"></i> 命令统计</span></div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <div ref=\"commandstats\" style=\"height: 420px\" />\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\" class=\"card-box\">\r\n        <el-card>\r\n          <div slot=\"header\"><span><i class=\"el-icon-odometer\"></i> 内存信息</span></div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <div ref=\"usedmemory\" style=\"height: 420px\" />\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCache } from \"@/api/monitor/cache\";\r\nimport * as echarts from \"echarts\";\r\n\r\nexport default {\r\n  name: \"Cache\",\r\n  data() {\r\n    return {\r\n      // 统计命令信息\r\n      commandstats: null,\r\n      // 使用内存\r\n      usedmemory: null,\r\n      // cache信息\r\n      cache: []\r\n    }\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.openLoading();\r\n  },\r\n  methods: {\r\n    /** 查缓存询信息 */\r\n    getList() {\r\n      getCache().then((response) => {\r\n        this.cache = response.data;\r\n        this.$modal.closeLoading();\r\n\r\n        this.commandstats = echarts.init(this.$refs.commandstats, \"macarons\");\r\n        this.commandstats.setOption({\r\n          tooltip: {\r\n            trigger: \"item\",\r\n            formatter: \"{a} <br/>{b} : {c} ({d}%)\",\r\n          },\r\n          series: [\r\n            {\r\n              name: \"命令\",\r\n              type: \"pie\",\r\n              roseType: \"radius\",\r\n              radius: [15, 95],\r\n              center: [\"50%\", \"38%\"],\r\n              data: response.data.commandStats,\r\n              animationEasing: \"cubicInOut\",\r\n              animationDuration: 1000,\r\n            }\r\n          ]\r\n        });\r\n        this.usedmemory = echarts.init(this.$refs.usedmemory, \"macarons\");\r\n        this.usedmemory.setOption({\r\n          tooltip: {\r\n            formatter: \"{b} <br/>{a} : \" + this.cache.info.used_memory_human,\r\n          },\r\n          series: [\r\n            {\r\n              name: \"峰值\",\r\n              type: \"gauge\",\r\n              min: 0,\r\n              max: 1000,\r\n              detail: {\r\n                formatter: this.cache.info.used_memory_human,\r\n              },\r\n              data: [\r\n                {\r\n                  value: parseFloat(this.cache.info.used_memory_human),\r\n                  name: \"内存消耗\",\r\n                }\r\n              ]\r\n            }\r\n          ]\r\n        });\r\n        window.addEventListener(\"resize\", () => {\r\n          this.commandstats.resize();\r\n          this.usedmemory.resize();\r\n        });\r\n      });\r\n    },\r\n    // 打开加载层\r\n    openLoading() {\r\n      this.$modal.loading(\"正在加载缓存监控数据，请稍候！\");\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;AAmEA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAC,uBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,YAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACA,aACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACA,IAAAC,eAAA,IAAAC,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAL,KAAA,GAAAQ,QAAA,CAAAX,IAAA;QACAQ,KAAA,CAAAI,MAAA,CAAAC,YAAA;QAEAL,KAAA,CAAAP,YAAA,GAAAJ,OAAA,CAAAiB,IAAA,CAAAN,KAAA,CAAAO,KAAA,CAAAd,YAAA;QACAO,KAAA,CAAAP,YAAA,CAAAe,SAAA;UACAC,OAAA;YACAC,OAAA;YACAC,SAAA;UACA;UACAC,MAAA,GACA;YACArB,IAAA;YACAsB,IAAA;YACAC,QAAA;YACAC,MAAA;YACAC,MAAA;YACAxB,IAAA,EAAAW,QAAA,CAAAX,IAAA,CAAAyB,YAAA;YACAC,eAAA;YACAC,iBAAA;UACA;QAEA;QACAnB,KAAA,CAAAN,UAAA,GAAAL,OAAA,CAAAiB,IAAA,CAAAN,KAAA,CAAAO,KAAA,CAAAb,UAAA;QACAM,KAAA,CAAAN,UAAA,CAAAc,SAAA;UACAC,OAAA;YACAE,SAAA,sBAAAX,KAAA,CAAAL,KAAA,CAAAyB,IAAA,CAAAC;UACA;UACAT,MAAA,GACA;YACArB,IAAA;YACAsB,IAAA;YACAS,GAAA;YACAC,GAAA;YACAC,MAAA;cACAb,SAAA,EAAAX,KAAA,CAAAL,KAAA,CAAAyB,IAAA,CAAAC;YACA;YACA7B,IAAA,GACA;cACAiC,KAAA,EAAAC,UAAA,CAAA1B,KAAA,CAAAL,KAAA,CAAAyB,IAAA,CAAAC,iBAAA;cACA9B,IAAA;YACA;UAEA;QAEA;QACAoC,MAAA,CAAAC,gBAAA;UACA5B,KAAA,CAAAP,YAAA,CAAAoC,MAAA;UACA7B,KAAA,CAAAN,UAAA,CAAAmC,MAAA;QACA;MACA;IACA;IACA;IACA/B,WAAA,WAAAA,YAAA;MACA,KAAAM,MAAA,CAAA0B,OAAA;IACA;EACA;AACA", "ignoreList": []}]}