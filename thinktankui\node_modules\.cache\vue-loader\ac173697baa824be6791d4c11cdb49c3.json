{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\monitor\\druid\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\monitor\\druid\\index.vue", "mtime": 1749104047642}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgaUZyYW1lIGZyb20gIkAvY29tcG9uZW50cy9pRnJhbWUvaW5kZXgiOw0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiRHJ1aWQiLA0KICBjb21wb25lbnRzOiB7IGlGcmFtZSB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICB1cmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL2RydWlkL2xvZ2luLmh0bWwiDQogICAgfTsNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/monitor/druid", "sourcesContent": ["<template>\r\n  <!-- <i-frame :src=\"url\" /> -->\r\n  <div>\r\n    <div>我是数据监控</div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport iFrame from \"@/components/iFrame/index\";\r\nexport default {\r\n  name: \"Druid\",\r\n  components: { iFrame },\r\n  data() {\r\n    return {\r\n      url: process.env.VUE_APP_BASE_API + \"/druid/login.html\"\r\n    };\r\n  },\r\n};\r\n</script>\r\n"]}]}