{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\utils\\auth.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\utils\\auth.js", "mtime": 1749104047634}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749104418875}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmdldFRva2VuID0gZ2V0VG9rZW47CmV4cG9ydHMucmVtb3ZlVG9rZW4gPSByZW1vdmVUb2tlbjsKZXhwb3J0cy5zZXRUb2tlbiA9IHNldFRva2VuOwp2YXIgX2pzQ29va2llID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJqcy1jb29raWUiKSk7CnZhciBUb2tlbktleSA9ICdBZG1pbi1Ub2tlbic7CmZ1bmN0aW9uIGdldFRva2VuKCkgewogIHJldHVybiBfanNDb29raWUuZGVmYXVsdC5nZXQoVG9rZW5LZXkpOwp9CmZ1bmN0aW9uIHNldFRva2VuKHRva2VuKSB7CiAgcmV0dXJuIF9qc0Nvb2tpZS5kZWZhdWx0LnNldChUb2tlbktleSwgdG9rZW4pOwp9CmZ1bmN0aW9uIHJlbW92ZVRva2VuKCkgewogIHJldHVybiBfanNDb29raWUuZGVmYXVsdC5yZW1vdmUoVG9rZW5LZXkpOwp9"}, {"version": 3, "names": ["_js<PERSON><PERSON>ie", "_interopRequireDefault", "require", "TokenKey", "getToken", "Cookies", "get", "setToken", "token", "set", "removeToken", "remove"], "sources": ["D:/thinktank/thinktankui/src/utils/auth.js"], "sourcesContent": ["import Cookies from 'js-cookie'\r\n\r\nconst TokenKey = 'Admin-Token'\r\n\r\nexport function getToken() {\r\n  return Cookies.get(TokenKey)\r\n}\r\n\r\nexport function setToken(token) {\r\n  return Cookies.set(To<PERSON><PERSON><PERSON>, token)\r\n}\r\n\r\nexport function removeToken() {\r\n  return Cookies.remove(TokenKey)\r\n}\r\n"], "mappings": ";;;;;;;;;AAAA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAMC,QAAQ,GAAG,aAAa;AAEvB,SAASC,QAAQA,CAAA,EAAG;EACzB,OAAOC,iBAAO,CAACC,GAAG,CAACH,QAAQ,CAAC;AAC9B;AAEO,SAASI,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAOH,iBAAO,CAACI,GAAG,CAACN,QAAQ,EAAEK,KAAK,CAAC;AACrC;AAEO,SAASE,WAAWA,CAAA,EAAG;EAC5B,OAAOL,iBAAO,CAACM,MAAM,CAACR,QAAQ,CAAC;AACjC", "ignoreList": []}]}