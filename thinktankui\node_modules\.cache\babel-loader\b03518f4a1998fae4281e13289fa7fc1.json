{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\warning-center\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\warning-center\\index.vue", "mtime": 1749104047651}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_default", "exports", "default", "name", "data", "originalTopNav", "undefined", "autoRefresh", "searchText", "currentPage", "pageSize", "total", "publishRegionInput", "ipAreaInput", "warningDialogVisible", "keywordDialogVisible", "keywordSettings", "allow<PERSON>ords", "<PERSON><PERSON><PERSON><PERSON>", "autoWarningDialogVisible", "importantAccountOnly", "autoWarningSettings", "timeRange", "startHour", "startMinute", "endHour", "endMinute", "platforms", "weibo", "wechat", "website", "do<PERSON><PERSON>", "redbook", "bilibili", "zhihu", "warningType", "processMethod", "priority", "handleMethod", "notifyMethods", "sms", "email", "wechatNotify", "warningSettings", "platformType", "title", "options", "label", "value", "checked", "contentProperty", "infoType", "matchObject", "allChecked", "matchMethod", "publishRegion", "regions", "ipArea", "areas", "mediaCategory", "count", "articleCategory", "sidebarCollapsed", "sidebarSearchText", "activeMenuItem", "menuCategories", "children", "icon", "isItem", "tableData", "source", "platform", "time", "multipleSelection", "mounted", "$store", "state", "settings", "topNav", "dispatch", "key", "created", "getList", "<PERSON><PERSON><PERSON><PERSON>", "methods", "handleSelectionChange", "val", "console", "log", "toggleSidebar", "handleMenuSelect", "index", "createNewScheme", "$message", "message", "type", "searchSidebar", "openWarningDialog", "closeWarningDialog", "openKeywordDialog", "closeKeywordDialog", "saveWarningSettings", "handleAllCheckbox", "section", "allOption", "find", "opt", "for<PERSON>ach", "handleMatchObjectAll", "addPublishRegion", "region", "some", "r", "push", "removePublishRegion", "findIndex", "splice", "addIpArea", "area", "a", "removeIpArea", "openMediaCategoryDialog", "openArticleCategoryDialog", "saveKeywordSettings", "openAutoWarningDialog", "closeAutoWarningDialog", "saveAutoWarningSettings"], "sources": ["src/views/warning-center/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"page-container\">\r\n      <!-- 左侧导航栏 -->\r\n      <div class=\"left-sidebar\" :class=\"{ 'collapsed': sidebarCollapsed }\">\r\n        <div class=\"sidebar-header\">\r\n          <el-button type=\"warning\" class=\"new-scheme-btn\" @click=\"createNewScheme\">\r\n            <i class=\"el-icon-plus\"></i> 新建方案\r\n          </el-button>\r\n          <div class=\"sidebar-btn\" @click=\"toggleSidebar\">\r\n            <i class=\"el-icon-s-fold\"></i>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"sidebar-search\">\r\n          <el-input\r\n            v-model=\"sidebarSearchText\"\r\n            placeholder=\"搜索\"\r\n            prefix-icon=\"el-icon-search\"\r\n            size=\"small\"\r\n            @input=\"searchSidebar\"\r\n          ></el-input>\r\n        </div>\r\n\r\n        <div class=\"sidebar-menu\">\r\n          <el-menu\r\n            :default-active=\"activeMenuItem\"\r\n            class=\"sidebar-menu-list\"\r\n            @select=\"handleMenuSelect\"\r\n          >\r\n            <template v-for=\"(item, index) in menuCategories\">\r\n              <!-- 使用唯一的key -->\r\n              <el-menu-item\r\n                v-if=\"item.isItem\"\r\n                :key=\"'item-' + item.name\"\r\n                :index=\"item.name\"\r\n                :class=\"{ 'active-menu-item': activeMenuItem === item.name }\"\r\n              >\r\n                <i :class=\"item.icon\" v-if=\"item.icon\"></i>\r\n                <span>{{ item.name }}</span>\r\n              </el-menu-item>\r\n\r\n              <!-- 如果是子菜单 -->\r\n              <el-submenu\r\n                v-else\r\n                :key=\"'submenu-' + item.name\"\r\n                :index=\"item.name\"\r\n              >\r\n                <template slot=\"title\">\r\n                  <i :class=\"item.icon\" v-if=\"item.icon\"></i>\r\n                  <span>{{ item.name }}({{ item.count }})</span>\r\n                </template>\r\n                <!-- 子菜单项 -->\r\n                <el-menu-item\r\n                  v-for=\"child in item.children\"\r\n                  :key=\"child.name\"\r\n                  :index=\"child.name\"\r\n                >\r\n                  {{ child.name }}\r\n                </el-menu-item>\r\n              </el-submenu>\r\n            </template>\r\n          </el-menu>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧内容区 -->\r\n      <div class=\"right-content\">\r\n        <!-- 主体内容 -->\r\n        <div class=\"main-content\">\r\n          <!-- 标题和操作区 -->\r\n          <div class=\"title-area\">\r\n            <div class=\"title\">\r\n              <div style=\"width: 100%; text-align: left;\">\r\n                <h2><i class=\"el-icon-warning-outline\" style=\"color: #E6A23C; margin-right: 8px;\"></i>方太<i class=\"el-icon-edit-outline\"></i></h2>\r\n                <div class=\"tabs\" style=\"text-align: left; margin-top: 10px; margin-left: 0;\">\r\n                  <el-button type=\"text\" icon=\"el-icon-user\">接收人设置</el-button>\r\n                  <el-button type=\"text\" icon=\"el-icon-bell\" @click=\"openWarningDialog\">预警设置</el-button>\r\n                  <el-button type=\"text\" icon=\"el-icon-data-analysis\" @click=\"openKeywordDialog\">关键词设置</el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"actions\">\r\n              <el-switch v-model=\"autoRefresh\" active-text=\"预警开关\"></el-switch>\r\n              <div>\r\n                <el-button type=\"primary\" size=\"small\">人工预警</el-button>\r\n                <el-button type=\"primary\" size=\"small\" @click=\"openAutoWarningDialog\">自动预警</el-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 表格区域 -->\r\n          <div class=\"table-area\">\r\n            <div class=\"table-toolbar\">\r\n              <div class=\"left-tools\">\r\n                <el-checkbox></el-checkbox>\r\n                <el-button type=\"text\" icon=\"el-icon-star-off\"></el-button>\r\n                <el-button type=\"text\" icon=\"el-icon-message\"></el-button>\r\n                <el-button type=\"text\" icon=\"el-icon-download\"></el-button>\r\n              </div>\r\n              <div class=\"right-tools\">\r\n                <span>共计{{total}}条</span>\r\n                <el-button type=\"text\" icon=\"el-icon-download\">导出下载</el-button>\r\n                <el-dropdown>\r\n                  <span class=\"el-dropdown-link\">\r\n                    字段<i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n                  </span>\r\n                </el-dropdown>\r\n                <div class=\"date-range\">\r\n                  <span>2023/04/23 08:00:00 - 2023/04/25 01:00:00</span>\r\n                </div>\r\n                <el-dropdown>\r\n                  <span class=\"el-dropdown-link\">\r\n                    全部<i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n                  </span>\r\n                </el-dropdown>\r\n                <el-input\r\n                  placeholder=\"搜索\"\r\n                  prefix-icon=\"el-icon-search\"\r\n                  v-model=\"searchText\"\r\n                  style=\"width: 200px;\"\r\n                  clearable\r\n                ></el-input>\r\n              </div>\r\n            </div>\r\n\r\n            <el-table\r\n              :data=\"tableData\"\r\n              style=\"width: 100%\"\r\n              @selection-change=\"handleSelectionChange\">\r\n              <el-table-column\r\n                type=\"selection\"\r\n                width=\"55\">\r\n              </el-table-column>\r\n              <el-table-column\r\n                label=\"\"\r\n                width=\"120\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button type=\"text\" icon=\"el-icon-star-off\"></el-button>\r\n                  <el-button type=\"text\" icon=\"el-icon-message\"></el-button>\r\n                  <el-button type=\"text\" icon=\"el-icon-download\"></el-button>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"title\"\r\n                label=\"标题/摘要\"\r\n                show-overflow-tooltip>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"source\"\r\n                label=\"来源类型\"\r\n                width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag size=\"mini\" type=\"danger\">{{ scope.row.source }}</el-tag>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"platform\"\r\n                label=\"平台类型\"\r\n                width=\"100\">\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"time\"\r\n                label=\"发布时间\"\r\n                width=\"150\">\r\n              </el-table-column>\r\n              <el-table-column\r\n                label=\"操作\"\r\n                width=\"150\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button type=\"text\" size=\"mini\" icon=\"el-icon-view\"></el-button>\r\n                  <el-button type=\"text\" size=\"mini\" icon=\"el-icon-edit\"></el-button>\r\n                  <el-button type=\"text\" size=\"mini\" icon=\"el-icon-delete\"></el-button>\r\n                  <el-button type=\"text\" size=\"mini\" icon=\"el-icon-share\"></el-button>\r\n                  <el-button type=\"text\" size=\"mini\" icon=\"el-icon-more\"></el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <pagination\r\n              v-show=\"total>0\"\r\n              :total=\"total\"\r\n              :page.sync=\"currentPage\"\r\n              :limit.sync=\"pageSize\"\r\n              @pagination=\"getList\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 预警设置抽屉 -->\r\n    <el-drawer\r\n      title=\"预警设置\"\r\n      :visible.sync=\"warningDialogVisible\"\r\n      direction=\"rtl\"\r\n      size=\"30%\"\r\n      :before-close=\"closeWarningDialog\"\r\n      custom-class=\"warning-drawer\">\r\n      <div class=\"warning-drawer-content\">\r\n        <!-- 平台类型 -->\r\n        <div class=\"warning-section\">\r\n          <h3>{{ warningSettings.platformType.title }}</h3>\r\n          <div class=\"warning-options\">\r\n            <el-checkbox\r\n              v-for=\"(option, index) in warningSettings.platformType.options\"\r\n              :key=\"'platform-' + index\"\r\n              v-model=\"option.checked\"\r\n              @change=\"option.value === 'all' && handleAllCheckbox(warningSettings.platformType)\">\r\n              {{ option.label }}\r\n            </el-checkbox>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 内容属性 -->\r\n        <div class=\"warning-section\">\r\n          <h3>{{ warningSettings.contentProperty.title }}</h3>\r\n          <div class=\"warning-options\">\r\n            <el-radio-group v-model=\"warningSettings.contentProperty.value\">\r\n              <el-radio\r\n                v-for=\"(option, index) in warningSettings.contentProperty.options\"\r\n                :key=\"'content-property-' + index\"\r\n                :label=\"option.value\">\r\n                {{ option.label }}\r\n              </el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 信息类型 -->\r\n        <div class=\"warning-section\">\r\n          <h3>{{ warningSettings.infoType.title }}</h3>\r\n          <div class=\"warning-options\">\r\n            <el-radio-group v-model=\"warningSettings.infoType.value\">\r\n              <el-radio\r\n                v-for=\"(option, index) in warningSettings.infoType.options\"\r\n                :key=\"'info-type-' + index\"\r\n                :label=\"option.value\">\r\n                {{ option.label }}\r\n              </el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 匹配对象 -->\r\n        <div class=\"warning-section\">\r\n          <h3>{{ warningSettings.matchObject.title }}</h3>\r\n          <div class=\"warning-options\">\r\n            <el-checkbox\r\n              v-model=\"warningSettings.matchObject.allChecked\"\r\n              @change=\"handleMatchObjectAll\">\r\n              全部\r\n            </el-checkbox>\r\n            <el-checkbox\r\n              v-for=\"(option, index) in warningSettings.matchObject.options\"\r\n              :key=\"'match-object-' + index\"\r\n              v-model=\"option.checked\"\r\n              :disabled=\"warningSettings.matchObject.allChecked\">\r\n              {{ option.label }}\r\n            </el-checkbox>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 匹配方式 -->\r\n        <div class=\"warning-section\">\r\n          <h3>{{ warningSettings.matchMethod.title }}</h3>\r\n          <div class=\"warning-options\">\r\n            <el-radio-group v-model=\"warningSettings.matchMethod.value\">\r\n              <el-radio\r\n                v-for=\"(option, index) in warningSettings.matchMethod.options\"\r\n                :key=\"'match-method-' + index\"\r\n                :label=\"option.value\">\r\n                {{ option.label }}\r\n              </el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 发布地区 -->\r\n        <div class=\"warning-section\">\r\n          <h3>{{ warningSettings.publishRegion.title }}</h3>\r\n          <div class=\"region-section\">\r\n            <div class=\"region-input\">\r\n              <el-input\r\n                placeholder=\"添加发布地区\"\r\n                size=\"small\"\r\n                style=\"width: 200px;\"\r\n                v-model=\"publishRegionInput\">\r\n                <i slot=\"suffix\" class=\"el-icon-location\"></i>\r\n              </el-input>\r\n            </div>\r\n            <div class=\"region-tags\" v-if=\"warningSettings.publishRegion.regions.length > 0\">\r\n              <el-tag\r\n                v-for=\"(region, index) in warningSettings.publishRegion.regions\"\r\n                :key=\"'region-' + index\"\r\n                size=\"small\"\r\n                closable\r\n                @close=\"removePublishRegion(region.name)\">\r\n                {{ region.name }}\r\n              </el-tag>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- IP属地 -->\r\n        <div class=\"warning-section\">\r\n          <h3>{{ warningSettings.ipArea.title }}</h3>\r\n          <div class=\"region-section\">\r\n            <div class=\"region-input\">\r\n              <el-input\r\n                placeholder=\"添加IP属地\"\r\n                size=\"small\"\r\n                style=\"width: 200px;\"\r\n                v-model=\"ipAreaInput\">\r\n                <i slot=\"suffix\" class=\"el-icon-location\"></i>\r\n              </el-input>\r\n            </div>\r\n            <div class=\"region-tags\" v-if=\"warningSettings.ipArea.areas.length > 0\">\r\n              <el-tag\r\n                v-for=\"(area, index) in warningSettings.ipArea.areas\"\r\n                :key=\"'ip-area-' + index\"\r\n                size=\"small\"\r\n                closable\r\n                @close=\"removeIpArea(area.name)\">\r\n                {{ area.name }}\r\n              </el-tag>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 媒体类别 -->\r\n        <div class=\"warning-section category-section\" @click=\"openMediaCategoryDialog\">\r\n          <div class=\"category-header\">\r\n            <h3>{{ warningSettings.mediaCategory.title }}</h3>\r\n            <div class=\"category-count\">\r\n              <span>(已选{{ warningSettings.mediaCategory.count }}个)</span>\r\n              <i class=\"el-icon-arrow-right\"></i>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 文章类别 -->\r\n        <div class=\"warning-section category-section\" @click=\"openArticleCategoryDialog\">\r\n          <div class=\"category-header\">\r\n            <h3>{{ warningSettings.articleCategory.title }}</h3>\r\n            <div class=\"category-count\">\r\n              <span>(已选{{ warningSettings.articleCategory.count }}个)</span>\r\n              <i class=\"el-icon-arrow-right\"></i>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 底部按钮 -->\r\n        <div class=\"drawer-footer\">\r\n          <el-button @click=\"closeWarningDialog\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"saveWarningSettings\">确定</el-button>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n\r\n    <!-- 关键词设置抽屉 -->\r\n    <el-drawer\r\n      title=\"文本词设置\"\r\n      :visible.sync=\"keywordDialogVisible\"\r\n      direction=\"rtl\"\r\n      size=\"30%\"\r\n      :before-close=\"closeKeywordDialog\"\r\n      custom-class=\"keyword-drawer\">\r\n      <div class=\"keyword-drawer-content\">\r\n        <!-- 允许词 -->\r\n        <div class=\"keyword-section\">\r\n          <h3>允许词</h3>\r\n          <el-input\r\n            type=\"textarea\"\r\n            :rows=\"8\"\r\n            placeholder=\"允许词：对文本进行筛选，命中文本的内容会被允许通过\"\r\n            v-model=\"keywordSettings.allowWords\">\r\n          </el-input>\r\n        </div>\r\n\r\n        <!-- 拒绝词 -->\r\n        <div class=\"keyword-section\">\r\n          <h3>拒绝词</h3>\r\n          <el-input\r\n            type=\"textarea\"\r\n            :rows=\"8\"\r\n            placeholder=\"拒绝词：对文本进行筛选，命中文本的内容会被拒绝通过\"\r\n            v-model=\"keywordSettings.rejectWords\">\r\n          </el-input>\r\n        </div>\r\n\r\n        <!-- 底部按钮 -->\r\n        <div class=\"drawer-footer\">\r\n          <el-button @click=\"closeKeywordDialog\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"saveKeywordSettings\">确定</el-button>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n\r\n    <!-- 自动预警设置抽屉 -->\r\n    <el-drawer\r\n      title=\"预警设置\"\r\n      :visible.sync=\"autoWarningDialogVisible\"\r\n      direction=\"rtl\"\r\n      size=\"30%\"\r\n      :before-close=\"closeAutoWarningDialog\"\r\n      custom-class=\"auto-warning-drawer\">\r\n      <div class=\"auto-warning-drawer-content\">\r\n        <h3 class=\"auto-warning-title\">自动预警设置</h3>\r\n\r\n        <!-- 预警时间 -->\r\n        <div class=\"auto-warning-section\">\r\n          <div class=\"section-label\">预警时间</div>\r\n          <div class=\"time-range-selector\">\r\n            <el-select v-model=\"autoWarningSettings.timeRange.startHour\" placeholder=\"小时\" size=\"small\">\r\n              <el-option\r\n                v-for=\"h in 24\"\r\n                :key=\"'start-hour-' + h\"\r\n                :label=\"(h - 1).toString().padStart(2, '0')\"\r\n                :value=\"(h - 1).toString().padStart(2, '0')\">\r\n              </el-option>\r\n            </el-select>\r\n            <span class=\"time-separator\">:</span>\r\n            <el-select v-model=\"autoWarningSettings.timeRange.startMinute\" placeholder=\"分钟\" size=\"small\">\r\n              <el-option\r\n                v-for=\"m in 60\"\r\n                :key=\"'start-minute-' + m\"\r\n                :label=\"(m - 1).toString().padStart(2, '0')\"\r\n                :value=\"(m - 1).toString().padStart(2, '0')\">\r\n              </el-option>\r\n            </el-select>\r\n          </div>\r\n          <div class=\"time-range-note\">\r\n            <span class=\"note-text\">预警时间段开始时间到结束时间</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 预警平台 -->\r\n        <div class=\"auto-warning-section\">\r\n          <div class=\"section-label\">预警平台</div>\r\n          <div class=\"platform-checkboxes\">\r\n            <el-checkbox v-model=\"autoWarningSettings.platforms.weibo\">微博</el-checkbox>\r\n            <el-checkbox v-model=\"autoWarningSettings.platforms.wechat\">微信</el-checkbox>\r\n            <el-checkbox v-model=\"autoWarningSettings.platforms.website\">网站</el-checkbox>\r\n            <el-checkbox v-model=\"autoWarningSettings.platforms.douyin\">抖音</el-checkbox>\r\n            <el-checkbox v-model=\"autoWarningSettings.platforms.redbook\">小红书</el-checkbox>\r\n            <el-checkbox v-model=\"autoWarningSettings.platforms.bilibili\">B站</el-checkbox>\r\n            <el-checkbox v-model=\"autoWarningSettings.platforms.zhihu\">知乎</el-checkbox>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 预警类型 -->\r\n        <div class=\"auto-warning-section\">\r\n          <div class=\"section-label\">预警类型</div>\r\n          <div class=\"warning-type-selector\">\r\n            <el-radio v-model=\"autoWarningSettings.warningType\" label=\"negative\">负面</el-radio>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 处理方式 -->\r\n        <div class=\"auto-warning-section\">\r\n          <div class=\"section-label\">处理方式</div>\r\n          <div class=\"process-method-selector\">\r\n            <el-radio v-model=\"autoWarningSettings.processMethod\" label=\"all\">全部预警</el-radio>\r\n            <el-radio v-model=\"autoWarningSettings.processMethod\" label=\"onlyAlert\">仅告警 <span class=\"note-text\">(只对符合条件的)</span></el-radio>\r\n          </div>\r\n          <div class=\"process-switch\">\r\n            <span class=\"switch-label\">只对重要账号 <span class=\"note-text\">(对方粉丝大于10万或认证账号)</span></span>\r\n            <el-switch v-model=\"importantAccountOnly\"></el-switch>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 优先级别 -->\r\n        <div class=\"auto-warning-section\">\r\n          <div class=\"section-label\">优先级别</div>\r\n          <div class=\"priority-selector\">\r\n            <el-radio v-model=\"autoWarningSettings.priority\" label=\"normal\">正常</el-radio>\r\n            <el-radio v-model=\"autoWarningSettings.priority\" label=\"urgent\">紧急</el-radio>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 处理方式 -->\r\n        <div class=\"auto-warning-section\">\r\n          <div class=\"section-label\">处理方式</div>\r\n          <div class=\"handle-method-selector\">\r\n            <el-radio v-model=\"autoWarningSettings.handleMethod\" label=\"auto\">自动</el-radio>\r\n            <el-radio v-model=\"autoWarningSettings.handleMethod\" label=\"manual\">人工</el-radio>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 告知方式 -->\r\n        <div class=\"auto-warning-section\">\r\n          <div class=\"section-label\">告知方式</div>\r\n          <div class=\"notify-method-checkboxes\">\r\n            <el-checkbox v-model=\"autoWarningSettings.notifyMethods.sms\">短信</el-checkbox>\r\n            <el-checkbox v-model=\"autoWarningSettings.notifyMethods.email\">邮件</el-checkbox>\r\n            <el-checkbox v-model=\"autoWarningSettings.notifyMethods.wechatNotify\">微信通知</el-checkbox>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 底部按钮 -->\r\n        <div class=\"drawer-footer\">\r\n          <el-button @click=\"closeAutoWarningDialog\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"saveAutoWarningSettings\">确定</el-button>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// 导入分页组件（如果需要）\r\n// import Pagination from \"@/components/Pagination\";\r\n\r\nexport default {\r\n  name: 'InfoSummary',\r\n  // 注册组件（如果需要）\r\n  // components: {\r\n  //   Pagination\r\n  // },\r\n  data() {\r\n    return {\r\n      originalTopNav: undefined, // 存储原始的topNav状态\r\n      autoRefresh: true,\r\n      searchText: '',\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      total: 156,\r\n      // 预警设置弹窗相关数据\r\n      publishRegionInput: '',\r\n      ipAreaInput: '',\r\n      warningDialogVisible: false,\r\n      // 关键词设置抽屉相关数据\r\n      keywordDialogVisible: false,\r\n      keywordSettings: {\r\n        allowWords: '允许词：对文本进行筛选，命中文本的内容会被允许通过',\r\n        rejectWords: '拒绝词：对文本进行筛选，命中文本的内容会被拒绝通过'\r\n      },\r\n      // 自动预警设置抽屉相关数据\r\n      autoWarningDialogVisible: false,\r\n      importantAccountOnly: true,\r\n      autoWarningSettings: {\r\n        timeRange: {\r\n          startHour: '06',\r\n          startMinute: '00',\r\n          endHour: '18',\r\n          endMinute: '00'\r\n        },\r\n        platforms: {\r\n          weibo: true,\r\n          wechat: true,\r\n          website: true,\r\n          douyin: true,\r\n          redbook: true,\r\n          bilibili: false,\r\n          zhihu: false\r\n        },\r\n        warningType: 'negative', // negative, positive, all\r\n        processMethod: 'all', // all, onlyAlert\r\n        priority: 'normal', // normal, urgent\r\n        handleMethod: 'auto', // auto, manual\r\n        notifyMethods: {\r\n          sms: true,\r\n          email: false,\r\n          wechatNotify: true\r\n        }\r\n      },\r\n      warningSettings: {\r\n        platformType: {\r\n          title: '平台类型',\r\n          options: [\r\n            { label: '全部', value: 'all', checked: true },\r\n            { label: '网页', value: 'webpage', checked: true },\r\n            { label: '微信', value: 'wechat', checked: true },\r\n            { label: '微博', value: 'weibo', checked: true },\r\n            { label: '头条号', value: 'toutiao', checked: true },\r\n            { label: 'APP', value: 'app', checked: true },\r\n            { label: '视频', value: 'video', checked: true },\r\n            { label: '论坛', value: 'forum', checked: true },\r\n            { label: '报刊', value: 'newspaper', checked: true },\r\n            { label: '问答', value: 'qa', checked: true }\r\n          ]\r\n        },\r\n        contentProperty: {\r\n          title: '内容属性',\r\n          value: 'all', // all, yes, no\r\n          options: [\r\n            { label: '全部', value: 'all' },\r\n            { label: '是', value: 'yes' },\r\n            { label: '不是', value: 'no' }\r\n          ]\r\n        },\r\n        infoType: {\r\n          title: '信息类型',\r\n          value: 'noncomment', // all, noncomment, comment\r\n          options: [\r\n            { label: '全部', value: 'all' },\r\n            { label: '非评论', value: 'noncomment' },\r\n            { label: '评论', value: 'comment' }\r\n          ]\r\n        },\r\n        matchObject: {\r\n          title: '匹配对象',\r\n          allChecked: true,\r\n          options: [\r\n            { label: '标题匹配', value: 'title', checked: false },\r\n            { label: '正文匹配', value: 'content', checked: false },\r\n            { label: '音频/图片匹配', value: 'media', checked: false },\r\n            { label: '原文匹配', value: 'original', checked: false }\r\n          ]\r\n        },\r\n        matchMethod: {\r\n          title: '匹配方式',\r\n          value: 'exact', // exact, fuzzy\r\n          options: [\r\n            { label: '精准', value: 'exact' },\r\n            { label: '模糊', value: 'fuzzy' }\r\n          ]\r\n        },\r\n        publishRegion: {\r\n          title: '发布地区',\r\n          regions: [\r\n            { name: '全部', value: 'all' }\r\n          ]\r\n        },\r\n        ipArea: {\r\n          title: 'IP属地',\r\n          areas: [\r\n            { name: '全部', value: 'all' }\r\n          ]\r\n        },\r\n        mediaCategory: {\r\n          title: '媒体类别',\r\n          count: 0\r\n        },\r\n        articleCategory: {\r\n          title: '文章类别',\r\n          count: 0\r\n        }\r\n      },\r\n      // 侧边栏数据\r\n      sidebarCollapsed: false,\r\n      sidebarSearchText: '',\r\n      activeMenuItem: '方太',\r\n      menuCategories: [\r\n        { name: '总监', count: 1, children: [], icon: 'el-icon-view' },\r\n        { name: '品牌', count: 1, children: [], icon: 'el-icon-star-on' },\r\n        { name: '方太', count: 0, isItem: true, icon: 'el-icon-office-building' },\r\n        { name: '人物', count: 0, children: [], icon: 'el-icon-user' },\r\n        { name: '机构', count: 0, children: [], icon: 'el-icon-office-building' },\r\n        { name: '产品', count: 0, children: [], icon: 'el-icon-goods' },\r\n        { name: '事件', count: 0, children: [], icon: 'el-icon-bell' },\r\n        { name: '话题', count: 0, children: [], icon: 'el-icon-chat-dot-square' }\r\n      ],\r\n      tableData: [\r\n        {\r\n          title: '方太集成灶新品上市发布会',\r\n          source: '负面',\r\n          platform: 'APP',\r\n          time: '2023-04-24 19:01:00'\r\n        },\r\n        {\r\n          title: '方太集成灶新品上市发布会',\r\n          source: '负面',\r\n          platform: 'APP',\r\n          time: '2023-04-24 19:07:46'\r\n        },\r\n        {\r\n          title: '在集成灶领域中的5%，持续超过了7个品牌相加，方太集成灶',\r\n          source: '负面',\r\n          platform: '媒体',\r\n          time: '2023-04-24 18:22:45'\r\n        },\r\n        {\r\n          title: '以空气质量提出史上最严苛标准，方太发布\"致净厨房\"理念',\r\n          source: '负面',\r\n          platform: '头条号',\r\n          time: '2023-04-24 17:49:45'\r\n        },\r\n        {\r\n          title: '厨电行业\"十年一遇\"的创新产品，方太发布全球首款\"蒸烤一体机\"',\r\n          source: '负面',\r\n          platform: '媒体',\r\n          time: '2023-04-24 17:12:10'\r\n        },\r\n        {\r\n          title: '方太成立20周年之际，推出第二代\"智净洗碗机\"，全球首次三代同台亮相',\r\n          source: '负面',\r\n          platform: '媒体',\r\n          time: '2023-04-24 15:15:16'\r\n        },\r\n        {\r\n          title: '厨房电器十年来变革与创新，方新一代集成灶发布，升级蒸、烤一体功能',\r\n          source: '负面',\r\n          platform: '媒体',\r\n          time: '2023-04-24 14:29:09'\r\n        },\r\n        {\r\n          title: '【方太】全球首款\"蒸烤一体机\"发布，方太再次引领厨电行业创新',\r\n          source: '负面',\r\n          platform: '媒体',\r\n          time: '2023-04-24 14:19:21'\r\n        },\r\n        {\r\n          title: '方太厨房电器发布全球首款\"蒸烤一体机\"，方太厨电再次引领厨电行业创新',\r\n          source: '负面',\r\n          platform: '媒体',\r\n          time: '2023-04-24 12:48:04'\r\n        },\r\n        {\r\n          title: '【方太】家用厨电市场增长放缓，方太发力高端市场，AI/IOT技术成新增长点',\r\n          source: '负面',\r\n          platform: '媒体',\r\n          time: '2023-04-24 12:34:54'\r\n        }\r\n      ],\r\n      multipleSelection: []\r\n    };\r\n  },\r\n  mounted() {\r\n    // 隐藏顶部导航栏\r\n    this.originalTopNav = this.$store.state.settings.topNav\r\n    this.$store.dispatch('settings/changeSetting', {\r\n      key: 'topNav',\r\n      value: false\r\n    })\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  beforeDestroy() {\r\n    // 恢复顶部导航栏设置\r\n    if (this.originalTopNav !== undefined) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'topNav',\r\n        value: this.originalTopNav\r\n      })\r\n    }\r\n  },\r\n  methods: {\r\n    handleSelectionChange(val) {\r\n      this.multipleSelection = val;\r\n    },\r\n    getList() {\r\n      // 这里可以添加获取数据的逻辑\r\n      console.log('获取数据，页码：', this.currentPage, '每页条数：', this.pageSize);\r\n      // 模拟API调用\r\n      // listData(this.queryParams).then(response => {\r\n      //   this.tableData = response.rows;\r\n      //   this.total = response.total;\r\n      // });\r\n    },\r\n    // 侧边栏相关方法\r\n    toggleSidebar() {\r\n      this.sidebarCollapsed = !this.sidebarCollapsed;\r\n    },\r\n    handleMenuSelect(index) {\r\n      this.activeMenuItem = index;\r\n      // 这里可以添加切换菜单项后的逻辑，如重新获取数据等\r\n      this.getList();\r\n    },\r\n    createNewScheme() {\r\n      // 新建方案的逻辑\r\n      this.$message({\r\n        message: '新建方案功能待实现',\r\n        type: 'info'\r\n      });\r\n    },\r\n    searchSidebar() {\r\n      // 侧边栏搜索逻辑\r\n      console.log('搜索关键词：', this.sidebarSearchText);\r\n      // 实现搜索逻辑\r\n    },\r\n    // 打开预警设置弹窗\r\n    openWarningDialog() {\r\n      this.warningDialogVisible = true;\r\n    },\r\n    // 关闭预警设置弹窗\r\n    closeWarningDialog() {\r\n      this.warningDialogVisible = false;\r\n    },\r\n    // 打开关键词设置抽屉\r\n    openKeywordDialog() {\r\n      this.keywordDialogVisible = true;\r\n    },\r\n    // 关闭关键词设置抽屉\r\n    closeKeywordDialog() {\r\n      this.keywordDialogVisible = false;\r\n    },\r\n    // 保存预警设置\r\n    saveWarningSettings() {\r\n      // 这里可以添加保存预警设置的逻辑\r\n      console.log('保存预警设置:', this.warningSettings);\r\n      this.$message({\r\n        message: '预警设置保存成功',\r\n        type: 'success'\r\n      });\r\n      this.closeWarningDialog();\r\n    },\r\n\r\n    // 处理全部复选框\r\n    handleAllCheckbox(section) {\r\n      const allOption = section.options.find(opt => opt.value === 'all');\r\n      if (allOption && allOption.checked) {\r\n        // 如果全部被选中，则选中所有选项\r\n        section.options.forEach(opt => {\r\n          opt.checked = true;\r\n        });\r\n      }\r\n    },\r\n\r\n    // 处理匹配对象全部复选框\r\n    handleMatchObjectAll(checked) {\r\n      this.warningSettings.matchObject.allChecked = checked;\r\n      if (checked) {\r\n        // 如果全部被选中，则取消选中其他选项\r\n        this.warningSettings.matchObject.options.forEach(opt => {\r\n          opt.checked = false;\r\n        });\r\n      }\r\n    },\r\n\r\n    // 添加发布地区\r\n    addPublishRegion(region) {\r\n      if (region && !this.warningSettings.publishRegion.regions.some(r => r.name === region)) {\r\n        this.warningSettings.publishRegion.regions.push({ name: region, value: region });\r\n      }\r\n    },\r\n\r\n    // 删除发布地区\r\n    removePublishRegion(region) {\r\n      const index = this.warningSettings.publishRegion.regions.findIndex(r => r.name === region);\r\n      if (index !== -1) {\r\n        this.warningSettings.publishRegion.regions.splice(index, 1);\r\n      }\r\n    },\r\n\r\n    // 添加IP属地\r\n    addIpArea(area) {\r\n      if (area && !this.warningSettings.ipArea.areas.some(a => a.name === area)) {\r\n        this.warningSettings.ipArea.areas.push({ name: area, value: area });\r\n      }\r\n    },\r\n\r\n    // 删除IP属地\r\n    removeIpArea(area) {\r\n      const index = this.warningSettings.ipArea.areas.findIndex(a => a.name === area);\r\n      if (index !== -1) {\r\n        this.warningSettings.ipArea.areas.splice(index, 1);\r\n      }\r\n    },\r\n\r\n    // 打开媒体类别对话框\r\n    openMediaCategoryDialog() {\r\n      this.$message({\r\n        message: '媒体类别功能待实现',\r\n        type: 'info'\r\n      });\r\n    },\r\n\r\n    // 打开文章类别对话框\r\n    openArticleCategoryDialog() {\r\n      this.$message({\r\n        message: '文章类别功能待实现',\r\n        type: 'info'\r\n      });\r\n    },\r\n\r\n    // 保存关键词设置\r\n    saveKeywordSettings() {\r\n      // 这里可以添加保存关键词设置的逻辑\r\n      console.log('保存关键词设置:', this.keywordSettings);\r\n      this.$message({\r\n        message: '关键词设置保存成功',\r\n        type: 'success'\r\n      });\r\n      this.closeKeywordDialog();\r\n    },\r\n\r\n    // 打开自动预警设置抽屉\r\n    openAutoWarningDialog() {\r\n      this.autoWarningDialogVisible = true;\r\n    },\r\n\r\n    // 关闭自动预警设置抽屉\r\n    closeAutoWarningDialog() {\r\n      this.autoWarningDialogVisible = false;\r\n    },\r\n\r\n    // 保存自动预警设置\r\n    saveAutoWarningSettings() {\r\n      // 这里可以添加保存自动预警设置的逻辑\r\n      console.log('保存自动预警设置:', this.autoWarningSettings);\r\n      this.$message({\r\n        message: '自动预警设置保存成功',\r\n        type: 'success'\r\n      });\r\n      this.closeAutoWarningDialog();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.page-container {\r\n  display: flex;\r\n  height: 100%;\r\n}\r\n\r\n/* 左侧导航栏样式 */\r\n.left-sidebar {\r\n  width: 200px;\r\n  background-color: #fff;\r\n  border-right: 1px solid #e6e6e6;\r\n  display: flex;\r\n  flex-direction: column;\r\n  flex-shrink: 0;\r\n  transition: width 0.3s;\r\n}\r\n\r\n/* 折叠状态的侧边栏 */\r\n.left-sidebar.collapsed {\r\n  width: 64px;\r\n}\r\n\r\n.left-sidebar.collapsed .sidebar-search,\r\n.left-sidebar.collapsed .el-menu-item span,\r\n.left-sidebar.collapsed .el-submenu__title span {\r\n  display: none;\r\n}\r\n\r\n.left-sidebar.collapsed .new-scheme-btn {\r\n  padding: 8px 0;\r\n  font-size: 0;\r\n}\r\n\r\n.left-sidebar.collapsed .new-scheme-btn i {\r\n  font-size: 16px;\r\n  margin: 0;\r\n}\r\n\r\n.sidebar-header {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.new-scheme-btn {\r\n  flex: 1;\r\n  font-size: 12px;\r\n  padding: 8px 10px;\r\n}\r\n\r\n.sidebar-btn {\r\n  width: 30px;\r\n  height: 30px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-left: 5px;\r\n  cursor: pointer;\r\n  color: #909399;\r\n}\r\n\r\n.sidebar-search {\r\n  padding: 10px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.sidebar-menu {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n}\r\n\r\n.sidebar-menu-list {\r\n  border-right: none;\r\n}\r\n\r\n.active-menu-item {\r\n  background-color: #ecf5ff !important;\r\n  color: #409EFF !important;\r\n}\r\n\r\n/* 菜单图标样式 */\r\n::v-deep .el-menu-item i,\r\n::v-deep .el-submenu__title i {\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n  width: 16px;\r\n  text-align: center;\r\n}\r\n\r\n::v-deep .el-menu-item i {\r\n  color: #606266;\r\n}\r\n\r\n::v-deep .el-submenu__title i {\r\n  color: #909399;\r\n}\r\n\r\n::v-deep .el-menu-item.is-active i,\r\n::v-deep .active-menu-item i {\r\n  color: #409EFF;\r\n}\r\n\r\n/* 右侧内容区样式 */\r\n.right-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n}\r\n\r\n.top-nav {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0 20px;\r\n  height: 50px;\r\n  border-bottom: 1px solid #eee;\r\n  background-color: #fff;\r\n}\r\n\r\n.nav-items {\r\n  display: flex;\r\n}\r\n\r\n.nav-item {\r\n  padding: 0 15px;\r\n  line-height: 50px;\r\n  cursor: pointer;\r\n  position: relative;\r\n}\r\n\r\n.nav-item.active {\r\n  color: #409EFF;\r\n  font-weight: bold;\r\n}\r\n\r\n.nav-item.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 2px;\r\n  background-color: #409EFF;\r\n}\r\n\r\n.user-info {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.user-info span {\r\n  margin-left: 8px;\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n.main-content {\r\n  flex: 1;\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  overflow-y: auto;\r\n}\r\n\r\n.title-area {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  background-color: #fff;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.title {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n.title h2 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  margin-right: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  text-align: left;\r\n}\r\n\r\n.title h2 i {\r\n  margin-left: 5px;\r\n  font-size: 16px;\r\n  color: #909399;\r\n  cursor: pointer;\r\n}\r\n\r\n.tabs {\r\n  display: flex;\r\n  flex-direction: row;\r\n  justify-content: flex-start;\r\n}\r\n\r\n.tabs .el-button {\r\n  margin-right: 15px;\r\n  padding-left: 0;\r\n}\r\n\r\n.actions {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.actions .el-button {\r\n  margin-left: 15px;\r\n}\r\n\r\n.table-area {\r\n  background-color: #fff;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.table-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.left-tools, .right-tools {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.left-tools > * {\r\n  margin-right: 10px;\r\n}\r\n\r\n.right-tools > * {\r\n  margin-left: 15px;\r\n}\r\n\r\n.date-range {\r\n  font-size: 12px;\r\n  color: #606266;\r\n}\r\n\r\n.el-dropdown-link {\r\n  cursor: pointer;\r\n  color: #606266;\r\n}\r\n\r\n.el-table {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n/* 覆盖Element UI的一些默认样式 */\r\n::v-deep .el-menu-item, ::v-deep .el-submenu__title {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  font-size: 14px;\r\n}\r\n\r\n::v-deep .el-submenu .el-menu-item {\r\n  height: 36px;\r\n  line-height: 36px;\r\n  padding: 0 20px 0 40px;\r\n}\r\n\r\n/* 预警设置抽屉样式 */\r\n.warning-drawer {\r\n  .el-drawer__header {\r\n    margin-bottom: 0;\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #eee;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .el-drawer__body {\r\n    padding: 0;\r\n  }\r\n}\r\n\r\n.warning-drawer-content {\r\n  height: 100%;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n  position: relative;\r\n  padding-bottom: 70px; /* 为底部按钮留出空间 */\r\n}\r\n\r\n.warning-section {\r\n  margin-bottom: 20px;\r\n  border-bottom: 1px solid #eee;\r\n  padding-bottom: 15px;\r\n}\r\n\r\n.warning-section h3 {\r\n  font-size: 14px;\r\n  margin-bottom: 10px;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.warning-options {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.warning-options .el-checkbox {\r\n  margin-right: 10px;\r\n  margin-bottom: 10px;\r\n  font-size: 13px;\r\n}\r\n\r\n.warning-options .el-radio {\r\n  margin-right: 15px;\r\n  margin-bottom: 10px;\r\n  font-size: 13px;\r\n}\r\n\r\n.region-section {\r\n  padding: 5px 0;\r\n}\r\n\r\n.region-input {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.region-tags {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.region-tags .el-tag {\r\n  margin-right: 10px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.category-section {\r\n  cursor: pointer;\r\n}\r\n\r\n.category-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.category-count {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #999;\r\n  font-size: 13px;\r\n}\r\n\r\n.category-count i {\r\n  margin-left: 5px;\r\n}\r\n\r\n.drawer-footer {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  padding: 15px 20px;\r\n  background-color: #fff;\r\n  border-top: 1px solid #eee;\r\n  text-align: right;\r\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n/* 关键词设置抽屉样式 */\r\n.keyword-drawer {\r\n  .el-drawer__header {\r\n    margin-bottom: 0;\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #eee;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .el-drawer__body {\r\n    padding: 0;\r\n  }\r\n}\r\n\r\n.keyword-drawer-content {\r\n  height: 100%;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n  position: relative;\r\n  padding-bottom: 70px; /* 为底部按钮留出空间 */\r\n}\r\n\r\n.keyword-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.keyword-section h3 {\r\n  font-size: 14px;\r\n  margin-bottom: 10px;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n/* 自动预警设置抽屉样式 */\r\n.auto-warning-drawer {\r\n  .el-drawer__header {\r\n    margin-bottom: 0;\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #eee;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .el-drawer__body {\r\n    padding: 0;\r\n  }\r\n}\r\n\r\n.auto-warning-drawer-content {\r\n  height: 100%;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n  position: relative;\r\n  padding-bottom: 70px; /* 为底部按钮留出空间 */\r\n}\r\n\r\n.auto-warning-title {\r\n  font-size: 16px;\r\n  margin-bottom: 20px;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.auto-warning-section {\r\n  margin-bottom: 20px;\r\n  padding-bottom: 15px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.section-label {\r\n  font-size: 14px;\r\n  margin-bottom: 10px;\r\n  color: #666;\r\n}\r\n\r\n.time-range-selector {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.time-separator {\r\n  margin: 0 5px;\r\n}\r\n\r\n.time-range-note {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-top: 5px;\r\n}\r\n\r\n.platform-checkboxes {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.platform-checkboxes .el-checkbox {\r\n  margin-right: 15px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.warning-type-selector,\r\n.process-method-selector,\r\n.priority-selector,\r\n.handle-method-selector {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.warning-type-selector .el-radio,\r\n.process-method-selector .el-radio,\r\n.priority-selector .el-radio,\r\n.handle-method-selector .el-radio {\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.process-switch {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-top: 10px;\r\n  padding: 5px 0;\r\n}\r\n\r\n.switch-label {\r\n  font-size: 13px;\r\n  color: #666;\r\n}\r\n\r\n.note-text {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.notify-method-checkboxes {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.notify-method-checkboxes .el-checkbox {\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+fA;AACA;AAAA,IAAAA,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA;EACA;EACA;EACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,cAAA,EAAAC,SAAA;MAAA;MACAC,WAAA;MACAC,UAAA;MACAC,WAAA;MACAC,QAAA;MACAC,KAAA;MACA;MACAC,kBAAA;MACAC,WAAA;MACAC,oBAAA;MACA;MACAC,oBAAA;MACAC,eAAA;QACAC,UAAA;QACAC,WAAA;MACA;MACA;MACAC,wBAAA;MACAC,oBAAA;MACAC,mBAAA;QACAC,SAAA;UACAC,SAAA;UACAC,WAAA;UACAC,OAAA;UACAC,SAAA;QACA;QACAC,SAAA;UACAC,KAAA;UACAC,MAAA;UACAC,OAAA;UACAC,MAAA;UACAC,OAAA;UACAC,QAAA;UACAC,KAAA;QACA;QACAC,WAAA;QAAA;QACAC,aAAA;QAAA;QACAC,QAAA;QAAA;QACAC,YAAA;QAAA;QACAC,aAAA;UACAC,GAAA;UACAC,KAAA;UACAC,YAAA;QACA;MACA;MACAC,eAAA;QACAC,YAAA;UACAC,KAAA;UACAC,OAAA,GACA;YAAAC,KAAA;YAAAC,KAAA;YAAAC,OAAA;UAAA,GACA;YAAAF,KAAA;YAAAC,KAAA;YAAAC,OAAA;UAAA,GACA;YAAAF,KAAA;YAAAC,KAAA;YAAAC,OAAA;UAAA,GACA;YAAAF,KAAA;YAAAC,KAAA;YAAAC,OAAA;UAAA,GACA;YAAAF,KAAA;YAAAC,KAAA;YAAAC,OAAA;UAAA,GACA;YAAAF,KAAA;YAAAC,KAAA;YAAAC,OAAA;UAAA,GACA;YAAAF,KAAA;YAAAC,KAAA;YAAAC,OAAA;UAAA,GACA;YAAAF,KAAA;YAAAC,KAAA;YAAAC,OAAA;UAAA,GACA;YAAAF,KAAA;YAAAC,KAAA;YAAAC,OAAA;UAAA,GACA;YAAAF,KAAA;YAAAC,KAAA;YAAAC,OAAA;UAAA;QAEA;QACAC,eAAA;UACAL,KAAA;UACAG,KAAA;UAAA;UACAF,OAAA,GACA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA;QAEA;QACAG,QAAA;UACAN,KAAA;UACAG,KAAA;UAAA;UACAF,OAAA,GACA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA;QAEA;QACAI,WAAA;UACAP,KAAA;UACAQ,UAAA;UACAP,OAAA,GACA;YAAAC,KAAA;YAAAC,KAAA;YAAAC,OAAA;UAAA,GACA;YAAAF,KAAA;YAAAC,KAAA;YAAAC,OAAA;UAAA,GACA;YAAAF,KAAA;YAAAC,KAAA;YAAAC,OAAA;UAAA,GACA;YAAAF,KAAA;YAAAC,KAAA;YAAAC,OAAA;UAAA;QAEA;QACAK,WAAA;UACAT,KAAA;UACAG,KAAA;UAAA;UACAF,OAAA,GACA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA;QAEA;QACAO,aAAA;UACAV,KAAA;UACAW,OAAA,GACA;YAAArD,IAAA;YAAA6C,KAAA;UAAA;QAEA;QACAS,MAAA;UACAZ,KAAA;UACAa,KAAA,GACA;YAAAvD,IAAA;YAAA6C,KAAA;UAAA;QAEA;QACAW,aAAA;UACAd,KAAA;UACAe,KAAA;QACA;QACAC,eAAA;UACAhB,KAAA;UACAe,KAAA;QACA;MACA;MACA;MACAE,gBAAA;MACAC,iBAAA;MACAC,cAAA;MACAC,cAAA,GACA;QAAA9D,IAAA;QAAAyD,KAAA;QAAAM,QAAA;QAAAC,IAAA;MAAA,GACA;QAAAhE,IAAA;QAAAyD,KAAA;QAAAM,QAAA;QAAAC,IAAA;MAAA,GACA;QAAAhE,IAAA;QAAAyD,KAAA;QAAAQ,MAAA;QAAAD,IAAA;MAAA,GACA;QAAAhE,IAAA;QAAAyD,KAAA;QAAAM,QAAA;QAAAC,IAAA;MAAA,GACA;QAAAhE,IAAA;QAAAyD,KAAA;QAAAM,QAAA;QAAAC,IAAA;MAAA,GACA;QAAAhE,IAAA;QAAAyD,KAAA;QAAAM,QAAA;QAAAC,IAAA;MAAA,GACA;QAAAhE,IAAA;QAAAyD,KAAA;QAAAM,QAAA;QAAAC,IAAA;MAAA,GACA;QAAAhE,IAAA;QAAAyD,KAAA;QAAAM,QAAA;QAAAC,IAAA;MAAA,EACA;MACAE,SAAA,GACA;QACAxB,KAAA;QACAyB,MAAA;QACAC,QAAA;QACAC,IAAA;MACA,GACA;QACA3B,KAAA;QACAyB,MAAA;QACAC,QAAA;QACAC,IAAA;MACA,GACA;QACA3B,KAAA;QACAyB,MAAA;QACAC,QAAA;QACAC,IAAA;MACA,GACA;QACA3B,KAAA;QACAyB,MAAA;QACAC,QAAA;QACAC,IAAA;MACA,GACA;QACA3B,KAAA;QACAyB,MAAA;QACAC,QAAA;QACAC,IAAA;MACA,GACA;QACA3B,KAAA;QACAyB,MAAA;QACAC,QAAA;QACAC,IAAA;MACA,GACA;QACA3B,KAAA;QACAyB,MAAA;QACAC,QAAA;QACAC,IAAA;MACA,GACA;QACA3B,KAAA;QACAyB,MAAA;QACAC,QAAA;QACAC,IAAA;MACA,GACA;QACA3B,KAAA;QACAyB,MAAA;QACAC,QAAA;QACAC,IAAA;MACA,GACA;QACA3B,KAAA;QACAyB,MAAA;QACAC,QAAA;QACAC,IAAA;MACA,EACA;MACAC,iBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAArE,cAAA,QAAAsE,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,MAAA;IACA,KAAAH,MAAA,CAAAI,QAAA;MACAC,GAAA;MACAhC,KAAA;IACA;EACA;EACAiC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA;IACA,SAAA9E,cAAA,KAAAC,SAAA;MACA,KAAAqE,MAAA,CAAAI,QAAA;QACAC,GAAA;QACAhC,KAAA,OAAA3C;MACA;IACA;EACA;EACA+E,OAAA;IACAC,qBAAA,WAAAA,sBAAAC,GAAA;MACA,KAAAb,iBAAA,GAAAa,GAAA;IACA;IACAJ,OAAA,WAAAA,QAAA;MACA;MACAK,OAAA,CAAAC,GAAA,kBAAA/E,WAAA,gBAAAC,QAAA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACA+E,aAAA,WAAAA,cAAA;MACA,KAAA3B,gBAAA,SAAAA,gBAAA;IACA;IACA4B,gBAAA,WAAAA,iBAAAC,KAAA;MACA,KAAA3B,cAAA,GAAA2B,KAAA;MACA;MACA,KAAAT,OAAA;IACA;IACAU,eAAA,WAAAA,gBAAA;MACA;MACA,KAAAC,QAAA;QACAC,OAAA;QACAC,IAAA;MACA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA;MACAT,OAAA,CAAAC,GAAA,gBAAAzB,iBAAA;MACA;IACA;IACA;IACAkC,iBAAA,WAAAA,kBAAA;MACA,KAAAnF,oBAAA;IACA;IACA;IACAoF,kBAAA,WAAAA,mBAAA;MACA,KAAApF,oBAAA;IACA;IACA;IACAqF,iBAAA,WAAAA,kBAAA;MACA,KAAApF,oBAAA;IACA;IACA;IACAqF,kBAAA,WAAAA,mBAAA;MACA,KAAArF,oBAAA;IACA;IACA;IACAsF,mBAAA,WAAAA,oBAAA;MACA;MACAd,OAAA,CAAAC,GAAA,iBAAA7C,eAAA;MACA,KAAAkD,QAAA;QACAC,OAAA;QACAC,IAAA;MACA;MACA,KAAAG,kBAAA;IACA;IAEA;IACAI,iBAAA,WAAAA,kBAAAC,OAAA;MACA,IAAAC,SAAA,GAAAD,OAAA,CAAAzD,OAAA,CAAA2D,IAAA,WAAAC,GAAA;QAAA,OAAAA,GAAA,CAAA1D,KAAA;MAAA;MACA,IAAAwD,SAAA,IAAAA,SAAA,CAAAvD,OAAA;QACA;QACAsD,OAAA,CAAAzD,OAAA,CAAA6D,OAAA,WAAAD,GAAA;UACAA,GAAA,CAAAzD,OAAA;QACA;MACA;IACA;IAEA;IACA2D,oBAAA,WAAAA,qBAAA3D,OAAA;MACA,KAAAN,eAAA,CAAAS,WAAA,CAAAC,UAAA,GAAAJ,OAAA;MACA,IAAAA,OAAA;QACA;QACA,KAAAN,eAAA,CAAAS,WAAA,CAAAN,OAAA,CAAA6D,OAAA,WAAAD,GAAA;UACAA,GAAA,CAAAzD,OAAA;QACA;MACA;IACA;IAEA;IACA4D,gBAAA,WAAAA,iBAAAC,MAAA;MACA,IAAAA,MAAA,UAAAnE,eAAA,CAAAY,aAAA,CAAAC,OAAA,CAAAuD,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAA7G,IAAA,KAAA2G,MAAA;MAAA;QACA,KAAAnE,eAAA,CAAAY,aAAA,CAAAC,OAAA,CAAAyD,IAAA;UAAA9G,IAAA,EAAA2G,MAAA;UAAA9D,KAAA,EAAA8D;QAAA;MACA;IACA;IAEA;IACAI,mBAAA,WAAAA,oBAAAJ,MAAA;MACA,IAAAnB,KAAA,QAAAhD,eAAA,CAAAY,aAAA,CAAAC,OAAA,CAAA2D,SAAA,WAAAH,CAAA;QAAA,OAAAA,CAAA,CAAA7G,IAAA,KAAA2G,MAAA;MAAA;MACA,IAAAnB,KAAA;QACA,KAAAhD,eAAA,CAAAY,aAAA,CAAAC,OAAA,CAAA4D,MAAA,CAAAzB,KAAA;MACA;IACA;IAEA;IACA0B,SAAA,WAAAA,UAAAC,IAAA;MACA,IAAAA,IAAA,UAAA3E,eAAA,CAAAc,MAAA,CAAAC,KAAA,CAAAqD,IAAA,WAAAQ,CAAA;QAAA,OAAAA,CAAA,CAAApH,IAAA,KAAAmH,IAAA;MAAA;QACA,KAAA3E,eAAA,CAAAc,MAAA,CAAAC,KAAA,CAAAuD,IAAA;UAAA9G,IAAA,EAAAmH,IAAA;UAAAtE,KAAA,EAAAsE;QAAA;MACA;IACA;IAEA;IACAE,YAAA,WAAAA,aAAAF,IAAA;MACA,IAAA3B,KAAA,QAAAhD,eAAA,CAAAc,MAAA,CAAAC,KAAA,CAAAyD,SAAA,WAAAI,CAAA;QAAA,OAAAA,CAAA,CAAApH,IAAA,KAAAmH,IAAA;MAAA;MACA,IAAA3B,KAAA;QACA,KAAAhD,eAAA,CAAAc,MAAA,CAAAC,KAAA,CAAA0D,MAAA,CAAAzB,KAAA;MACA;IACA;IAEA;IACA8B,uBAAA,WAAAA,wBAAA;MACA,KAAA5B,QAAA;QACAC,OAAA;QACAC,IAAA;MACA;IACA;IAEA;IACA2B,yBAAA,WAAAA,0BAAA;MACA,KAAA7B,QAAA;QACAC,OAAA;QACAC,IAAA;MACA;IACA;IAEA;IACA4B,mBAAA,WAAAA,oBAAA;MACA;MACApC,OAAA,CAAAC,GAAA,kBAAAxE,eAAA;MACA,KAAA6E,QAAA;QACAC,OAAA;QACAC,IAAA;MACA;MACA,KAAAK,kBAAA;IACA;IAEA;IACAwB,qBAAA,WAAAA,sBAAA;MACA,KAAAzG,wBAAA;IACA;IAEA;IACA0G,sBAAA,WAAAA,uBAAA;MACA,KAAA1G,wBAAA;IACA;IAEA;IACA2G,uBAAA,WAAAA,wBAAA;MACA;MACAvC,OAAA,CAAAC,GAAA,mBAAAnE,mBAAA;MACA,KAAAwE,QAAA;QACAC,OAAA;QACAC,IAAA;MACA;MACA,KAAA8B,sBAAA;IACA;EACA;AACA", "ignoreList": []}]}