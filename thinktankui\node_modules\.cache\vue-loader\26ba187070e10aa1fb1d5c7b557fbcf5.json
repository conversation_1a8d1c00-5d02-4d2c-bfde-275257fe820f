{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\tool\\build\\DraggableItem.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\tool\\build\\DraggableItem.vue", "mtime": 1749104047647}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["DraggableItem.vue"], "names": [], "mappings": ";AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "DraggableItem.vue", "sourceRoot": "src/views/tool/build", "sourcesContent": ["<script>\r\nimport draggable from 'vuedraggable'\r\nimport render from '@/utils/generator/render'\r\n\r\nconst components = {\r\n  itemBtns(h, element, index, parent) {\r\n    const { copyItem, deleteItem } = this.$listeners\r\n    return [\r\n      <span class=\"drawing-item-copy\" title=\"复制\" onClick={event => {\r\n        copyItem(element, parent); event.stopPropagation()\r\n      }}>\r\n        <i class=\"el-icon-copy-document\" />\r\n      </span>,\r\n      <span class=\"drawing-item-delete\" title=\"删除\" onClick={event => {\r\n        deleteItem(index, parent); event.stopPropagation()\r\n      }}>\r\n        <i class=\"el-icon-delete\" />\r\n      </span>\r\n    ]\r\n  }\r\n}\r\nconst layouts = {\r\n  colFormItem(h, element, index, parent) {\r\n    const { activeItem } = this.$listeners\r\n    let className = this.activeId === element.formId ? 'drawing-item active-from-item' : 'drawing-item'\r\n    if (this.formConf.unFocusedComponentBorder) className += ' unfocus-bordered'\r\n    return (\r\n      <el-col span={element.span} class={className}\r\n        nativeOnClick={event => { activeItem(element); event.stopPropagation() }}>\r\n        <el-form-item label-width={element.labelWidth ? `${element.labelWidth}px` : null}\r\n          label={element.label} required={element.required}>\r\n          <render key={element.renderKey} conf={element} onInput={ event => {\r\n            this.$set(element, 'defaultValue', event)\r\n          }} />\r\n        </el-form-item>\r\n        {components.itemBtns.apply(this, arguments)}\r\n      </el-col>\r\n    )\r\n  },\r\n  rowFormItem(h, element, index, parent) {\r\n    const { activeItem } = this.$listeners\r\n    const className = this.activeId === element.formId ? 'drawing-row-item active-from-item' : 'drawing-row-item'\r\n    let child = renderChildren.apply(this, arguments)\r\n    if (element.type === 'flex') {\r\n      child = <el-row type={element.type} justify={element.justify} align={element.align}>\r\n              {child}\r\n            </el-row>\r\n    }\r\n    return (\r\n      <el-col span={element.span}>\r\n        <el-row gutter={element.gutter} class={className}\r\n          nativeOnClick={event => { activeItem(element); event.stopPropagation() }}>\r\n          <span class=\"component-name\">{element.componentName}</span>\r\n          <draggable list={element.children} animation={340} group=\"componentsGroup\" class=\"drag-wrapper\">\r\n            {child}\r\n          </draggable>\r\n          {components.itemBtns.apply(this, arguments)}\r\n        </el-row>\r\n      </el-col>\r\n    )\r\n  }\r\n}\r\n\r\nfunction renderChildren(h, element, index, parent) {\r\n  if (!Array.isArray(element.children)) return null\r\n  return element.children.map((el, i) => {\r\n    const layout = layouts[el.layout]\r\n    if (layout) {\r\n      return layout.call(this, h, el, i, element.children)\r\n    }\r\n    return layoutIsNotFound()\r\n  })\r\n}\r\n\r\nfunction layoutIsNotFound() {\r\n  throw new Error(`没有与${this.element.layout}匹配的layout`)\r\n}\r\n\r\nexport default {\r\n  components: {\r\n    render,\r\n    draggable\r\n  },\r\n  props: [\r\n    'element',\r\n    'index',\r\n    'drawingList',\r\n    'activeId',\r\n    'formConf'\r\n  ],\r\n  render(h) {\r\n    const layout = layouts[this.element.layout]\r\n\r\n    if (layout) {\r\n      return layout.call(this, h, this.element, this.index, this.drawingList)\r\n    }\r\n    return layoutIsNotFound()\r\n  }\r\n}\r\n</script>\r\n"]}]}