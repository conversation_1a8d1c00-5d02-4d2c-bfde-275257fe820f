{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\meta-search\\index.vue?vue&type=template&id=102ebc49&scoped=true", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\meta-search\\index.vue", "mtime": 1749104047640}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749104421286}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uICgpIHsKICB2YXIgX3ZtID0gdGhpcwogIHZhciBfaCA9IF92bS4kY3JlYXRlRWxlbWVudAogIHZhciBfYyA9IF92bS5fc2VsZi5fYyB8fCBfaAogIHJldHVybiBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogIm1ldGEtc2VhcmNoLWNvbnRhaW5lciIgfSwgWwogICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJzZWFyY2gtaGVhZGVyIiB9LCBbCiAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAic2VhcmNoLXRhYnMiIH0sIFsKICAgICAgICBfYygKICAgICAgICAgICJkaXYiLAogICAgICAgICAgewogICAgICAgICAgICBzdGF0aWNDbGFzczogInRhYiIsCiAgICAgICAgICAgIGNsYXNzOiB7IGFjdGl2ZTogX3ZtLmFjdGl2ZVRhYiA9PT0gImZ1bGx0ZXh0IiB9LAogICAgICAgICAgICBvbjogewogICAgICAgICAgICAgIGNsaWNrOiBmdW5jdGlvbiAoJGV2ZW50KSB7CiAgICAgICAgICAgICAgICByZXR1cm4gX3ZtLnN3aXRjaFRhYigiZnVsbHRleHQiKQogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgIH0sCiAgICAgICAgICB9LAogICAgICAgICAgW192bS5fdigiIOWFqOaWh+ajgOe0oiAiKV0KICAgICAgICApLAogICAgICAgIF9jKAogICAgICAgICAgImRpdiIsCiAgICAgICAgICB7CiAgICAgICAgICAgIHN0YXRpY0NsYXNzOiAidGFiIiwKICAgICAgICAgICAgY2xhc3M6IHsgYWN0aXZlOiBfdm0uYWN0aXZlVGFiID09PSAibWV0YSIgfSwKICAgICAgICAgICAgb246IHsKICAgICAgICAgICAgICBjbGljazogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgICAgICAgICAgcmV0dXJuIF92bS5zd2l0Y2hUYWIoIm1ldGEiKQogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgIH0sCiAgICAgICAgICB9LAogICAgICAgICAgW192bS5fdigiIOWFg+aQnOe0oiAiKV0KICAgICAgICApLAogICAgICBdKSwKICAgICAgX2MoCiAgICAgICAgImRpdiIsCiAgICAgICAgeyBzdGF0aWNDbGFzczogInNlYXJjaC1ib3giIH0sCiAgICAgICAgWwogICAgICAgICAgX2MoImlucHV0IiwgewogICAgICAgICAgICBkaXJlY3RpdmVzOiBbCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgbmFtZTogIm1vZGVsIiwKICAgICAgICAgICAgICAgIHJhd05hbWU6ICJ2LW1vZGVsIiwKICAgICAgICAgICAgICAgIHZhbHVlOiBfdm0uc2VhcmNoS2V5d29yZCwKICAgICAgICAgICAgICAgIGV4cHJlc3Npb246ICJzZWFyY2hLZXl3b3JkIiwKICAgICAgICAgICAgICB9LAogICAgICAgICAgICBdLAogICAgICAgICAgICBzdGF0aWNDbGFzczogInNlYXJjaC1pbnB1dCIsCiAgICAgICAgICAgIGF0dHJzOiB7IHR5cGU6ICJ0ZXh0IiwgcGxhY2Vob2xkZXI6ICLor7fovpPlhaXmkJzntKLlhbPplK7or40iIH0sCiAgICAgICAgICAgIGRvbVByb3BzOiB7IHZhbHVlOiBfdm0uc2VhcmNoS2V5d29yZCB9LAogICAgICAgICAgICBvbjogewogICAgICAgICAgICAgIGtleXVwOiBmdW5jdGlvbiAoJGV2ZW50KSB7CiAgICAgICAgICAgICAgICBpZiAoCiAgICAgICAgICAgICAgICAgICEkZXZlbnQudHlwZS5pbmRleE9mKCJrZXkiKSAmJgogICAgICAgICAgICAgICAgICBfdm0uX2soJGV2ZW50LmtleUNvZGUsICJlbnRlciIsIDEzLCAkZXZlbnQua2V5LCAiRW50ZXIiKQogICAgICAgICAgICAgICAgKSB7CiAgICAgICAgICAgICAgICAgIHJldHVybiBudWxsCiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICByZXR1cm4gX3ZtLmhhbmRsZVNlYXJjaCgkZXZlbnQpCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICBpbnB1dDogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgICAgICAgICAgaWYgKCRldmVudC50YXJnZXQuY29tcG9zaW5nKSB7CiAgICAgICAgICAgICAgICAgIHJldHVybgogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgX3ZtLnNlYXJjaEtleXdvcmQgPSAkZXZlbnQudGFyZ2V0LnZhbHVlCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgfSwKICAgICAgICAgIH0pLAogICAgICAgICAgX2MoCiAgICAgICAgICAgICJlbC1idXR0b24iLAogICAgICAgICAgICB7CiAgICAgICAgICAgICAgc3RhdGljQ2xhc3M6ICJzZWFyY2gtYnRuIiwKICAgICAgICAgICAgICBhdHRyczogeyB0eXBlOiAicHJpbWFyeSIgfSwKICAgICAgICAgICAgICBvbjogeyBjbGljazogX3ZtLmhhbmRsZVNlYXJjaCB9LAogICAgICAgICAgICB9LAogICAgICAgICAgICBbX3ZtLl92KCLmkJzntKIiKV0KICAgICAgICAgICksCiAgICAgICAgXSwKICAgICAgICAxCiAgICAgICksCiAgICBdKSwKICAgIF92bS5hY3RpdmVUYWIgPT09ICJmdWxsdGV4dCIgJiYgX3ZtLmhhc1NlYXJjaGVkCiAgICAgID8gX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJmdWxsdGV4dC1yZXN1bHRzIiB9LCBbCiAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogImZpbHRlci1zZWN0aW9uIiB9LCBbCiAgICAgICAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAiZmlsdGVyLXJvdyIgfSwgWwogICAgICAgICAgICAgIF9jKCJzcGFuIiwgeyBzdGF0aWNDbGFzczogImZpbHRlci1sYWJlbCIgfSwgWwogICAgICAgICAgICAgICAgX3ZtLl92KCLml7bpl7TojIPlm7Q6IiksCiAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAiZGl2IiwKICAgICAgICAgICAgICAgIHsgc3RhdGljQ2xhc3M6ICJmaWx0ZXItb3B0aW9ucyIgfSwKICAgICAgICAgICAgICAgIF92bS5fbChfdm0udGltZU9wdGlvbnMsIGZ1bmN0aW9uICh0aW1lKSB7CiAgICAgICAgICAgICAgICAgIHJldHVybiBfYygKICAgICAgICAgICAgICAgICAgICAiZWwtYnV0dG9uIiwKICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICBrZXk6IHRpbWUudmFsdWUsCiAgICAgICAgICAgICAgICAgICAgICBhdHRyczogewogICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiBfdm0uc2VsZWN0ZWRUaW1lID09PSB0aW1lLnZhbHVlID8gInByaW1hcnkiIDogIiIsCiAgICAgICAgICAgICAgICAgICAgICAgIHNpemU6ICJzbWFsbCIsCiAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgb246IHsKICAgICAgICAgICAgICAgICAgICAgICAgY2xpY2s6IGZ1bmN0aW9uICgkZXZlbnQpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gX3ZtLnNlbGVjdFRpbWUodGltZS52YWx1ZSkKICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICBbX3ZtLl92KCIgIiArIF92bS5fcyh0aW1lLmxhYmVsKSArICIgIildCiAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgMQogICAgICAgICAgICAgICksCiAgICAgICAgICAgIF0pLAogICAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogImZpbHRlci1yb3ciIH0sIFsKICAgICAgICAgICAgICBfYygic3BhbiIsIHsgc3RhdGljQ2xhc3M6ICJmaWx0ZXItbGFiZWwiIH0sIFsKICAgICAgICAgICAgICAgIF92bS5fdigi5bmz5Y+w57G75Z6LOiIpLAogICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgImRpdiIsCiAgICAgICAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAiZmlsdGVyLW9wdGlvbnMiIH0sCiAgICAgICAgICAgICAgICBfdm0uX2woX3ZtLnBsYXRmb3JtT3B0aW9ucywgZnVuY3Rpb24gKHBsYXRmb3JtKSB7CiAgICAgICAgICAgICAgICAgIHJldHVybiBfYygKICAgICAgICAgICAgICAgICAgICAiZWwtYnV0dG9uIiwKICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICBrZXk6IHBsYXRmb3JtLnZhbHVlLAogICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICAgICAgdHlwZToKICAgICAgICAgICAgICAgICAgICAgICAgICBfdm0uc2VsZWN0ZWRQbGF0Zm9ybSA9PT0gcGxhdGZvcm0udmFsdWUKICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gInByaW1hcnkiCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICIiLAogICAgICAgICAgICAgICAgICAgICAgICBzaXplOiAic21hbGwiLAogICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgIG9uOiB7CiAgICAgICAgICAgICAgICAgICAgICAgIGNsaWNrOiBmdW5jdGlvbiAoJGV2ZW50KSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIF92bS5zZWxlY3RQbGF0Zm9ybShwbGF0Zm9ybS52YWx1ZSkKICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICBfdm0uX3YoIiAiICsgX3ZtLl9zKHBsYXRmb3JtLmxhYmVsKSArICIgIiksCiAgICAgICAgICAgICAgICAgICAgICBwbGF0Zm9ybS5jb3VudAogICAgICAgICAgICAgICAgICAgICAgICA/IF9jKCJzcGFuIiwgeyBzdGF0aWNDbGFzczogImNvdW50IiB9LCBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdm0uX3YoIigiICsgX3ZtLl9zKHBsYXRmb3JtLmNvdW50KSArICIpIiksCiAgICAgICAgICAgICAgICAgICAgICAgICAgXSkKICAgICAgICAgICAgICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgICAgICAgICAgICBdCiAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgMQogICAgICAgICAgICAgICksCiAgICAgICAgICAgIF0pLAogICAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogImZpbHRlci1yb3ciIH0sIFsKICAgICAgICAgICAgICBfYygic3BhbiIsIHsgc3RhdGljQ2xhc3M6ICJmaWx0ZXItbGFiZWwiIH0sIFsKICAgICAgICAgICAgICAgIF92bS5fdigi5oOF5oSf57G75Z6LOiIpLAogICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgImRpdiIsCiAgICAgICAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAiZmlsdGVyLW9wdGlvbnMiIH0sCiAgICAgICAgICAgICAgICBfdm0uX2woX3ZtLmVtb3Rpb25PcHRpb25zLCBmdW5jdGlvbiAoZW1vdGlvbikgewogICAgICAgICAgICAgICAgICByZXR1cm4gX2MoCiAgICAgICAgICAgICAgICAgICAgImVsLWJ1dHRvbiIsCiAgICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgICAga2V5OiBlbW90aW9uLnZhbHVlLAogICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICAgICAgdHlwZToKICAgICAgICAgICAgICAgICAgICAgICAgICBfdm0uc2VsZWN0ZWRFbW90aW9uID09PSBlbW90aW9uLnZhbHVlCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICJwcmltYXJ5IgogICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAiIiwKICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZTogInNtYWxsIiwKICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICBvbjogewogICAgICAgICAgICAgICAgICAgICAgICBjbGljazogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBfdm0uc2VsZWN0RW1vdGlvbihlbW90aW9uLnZhbHVlKQogICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgIF92bS5fdigiICIgKyBfdm0uX3MoZW1vdGlvbi5sYWJlbCkgKyAiICIpLAogICAgICAgICAgICAgICAgICAgICAgZW1vdGlvbi5jb3VudAogICAgICAgICAgICAgICAgICAgICAgICA/IF9jKCJzcGFuIiwgeyBzdGF0aWNDbGFzczogImNvdW50IiB9LCBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdm0uX3YoIigiICsgX3ZtLl9zKGVtb3Rpb24uY291bnQpICsgIikiKSwKICAgICAgICAgICAgICAgICAgICAgICAgICBdKQogICAgICAgICAgICAgICAgICAgICAgICA6IF92bS5fZSgpLAogICAgICAgICAgICAgICAgICAgIF0KICAgICAgICAgICAgICAgICAgKQogICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAxCiAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgXSksCiAgICAgICAgICBdKSwKICAgICAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAicmVzdWx0LXN0YXRzIiB9LCBbCiAgICAgICAgICAgIF9jKCJzcGFuIiwgW192bS5fdigi5YWxIiArIF92bS5fcyhfdm0udG90YWxSZXN1bHRzKSArICLmnaHnu5PmnpwiKV0pLAogICAgICAgICAgICBfYygKICAgICAgICAgICAgICAiZGl2IiwKICAgICAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAiYWN0aW9uLWJ1dHRvbnMiIH0sCiAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgX2MoImVsLWJ1dHRvbiIsIHsgYXR0cnM6IHsgc2l6ZTogInNtYWxsIiB9IH0sIFtfdm0uX3YoIuWvvOWHuiIpXSksCiAgICAgICAgICAgICAgICBfYygiZWwtYnV0dG9uIiwgeyBhdHRyczogeyB0eXBlOiAicHJpbWFyeSIsIHNpemU6ICJzbWFsbCIgfSB9LCBbCiAgICAgICAgICAgICAgICAgIF92bS5fdigi5YiG5p6QIiksCiAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgIDEKICAgICAgICAgICAgKSwKICAgICAgICAgIF0pLAogICAgICAgICAgX2MoCiAgICAgICAgICAgICJkaXYiLAogICAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAicmVzdWx0cy1saXN0IiB9LAogICAgICAgICAgICBfdm0uX2woX3ZtLnNlYXJjaFJlc3VsdHMsIGZ1bmN0aW9uIChpdGVtLCBpbmRleCkgewogICAgICAgICAgICAgIHJldHVybiBfYygiZGl2IiwgeyBrZXk6IGluZGV4LCBzdGF0aWNDbGFzczogInJlc3VsdC1pdGVtIiB9LCBbCiAgICAgICAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogInJlc3VsdC1oZWFkZXIiIH0sIFsKICAgICAgICAgICAgICAgICAgX2MoImgzIiwgeyBzdGF0aWNDbGFzczogInJlc3VsdC10aXRsZSIgfSwgWwogICAgICAgICAgICAgICAgICAgIF92bS5fdihfdm0uX3MoaXRlbS50aXRsZSkpLAogICAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgImRpdiIsCiAgICAgICAgICAgICAgICAgICAgeyBzdGF0aWNDbGFzczogInJlc3VsdC1hY3Rpb25zIiB9LAogICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgIF9jKCJlbC1idXR0b24iLCB7CiAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHR5cGU6ICJ0ZXh0IiwgaWNvbjogImVsLWljb24tdmlldyIgfSwKICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgICAgMQogICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogInJlc3VsdC1tZXRhIiB9LCBbCiAgICAgICAgICAgICAgICAgIF9jKCJzcGFuIiwgeyBzdGF0aWNDbGFzczogIm1ldGEtaXRlbSIgfSwgWwogICAgICAgICAgICAgICAgICAgIF92bS5fdihfdm0uX3MoaXRlbS5zb3VyY2UpKSwKICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgIF9jKCJzcGFuIiwgeyBzdGF0aWNDbGFzczogIm1ldGEtaXRlbSIgfSwgWwogICAgICAgICAgICAgICAgICAgIF92bS5fdihfdm0uX3MoaXRlbS5wdWJsaXNoVGltZSkpLAogICAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgICAgX2MoInNwYW4iLCB7IHN0YXRpY0NsYXNzOiAibWV0YS1pdGVtIiB9LCBbCiAgICAgICAgICAgICAgICAgICAgX3ZtLl92KF92bS5fcyhpdGVtLmF1dGhvcikpLAogICAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgICAgX2MoInNwYW4iLCB7IHN0YXRpY0NsYXNzOiAibWV0YS1pdGVtIiB9LCBbCiAgICAgICAgICAgICAgICAgICAgX3ZtLl92KF92bS5fcyhpdGVtLnBsYXRmb3JtKSksCiAgICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgICAgICBfYygic3BhbiIsIHsgc3RhdGljQ2xhc3M6ICJtZXRhLWl0ZW0iIH0sIFsKICAgICAgICAgICAgICAgICAgICBfdm0uX3YoIumYheivu+mHjzogIiArIF92bS5fcyhpdGVtLnJlYWRDb3VudCkpLAogICAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgICAgX2MoInNwYW4iLCB7IHN0YXRpY0NsYXNzOiAibWV0YS1pdGVtIiB9LCBbCiAgICAgICAgICAgICAgICAgICAgX3ZtLl92KF92bS5fcyhpdGVtLmxvY2F0aW9uKSksCiAgICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgICAgICBfYygic3BhbiIsIHsgc3RhdGljQ2xhc3M6ICJtZXRhLWl0ZW0iIH0sIFsKICAgICAgICAgICAgICAgICAgICBfdm0uX3YoX3ZtLl9zKGl0ZW0uY2F0ZWdvcnkpKSwKICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAicmVzdWx0LWNvbnRlbnQiIH0sIFsKICAgICAgICAgICAgICAgICAgX3ZtLl92KCIgIiArIF92bS5fcyhpdGVtLmNvbnRlbnQpICsgIiAiKSwKICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgIF0pCiAgICAgICAgICAgIH0pLAogICAgICAgICAgICAwCiAgICAgICAgICApLAogICAgICAgICAgX2MoCiAgICAgICAgICAgICJkaXYiLAogICAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAicGFnaW5hdGlvbi1jb250YWluZXIiIH0sCiAgICAgICAgICAgIFsKICAgICAgICAgICAgICBfYygiZWwtcGFnaW5hdGlvbiIsIHsKICAgICAgICAgICAgICAgIGF0dHJzOiB7CiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICIiLAogICAgICAgICAgICAgICAgICBsYXlvdXQ6ICJwcmV2LCBwYWdlciwgbmV4dCIsCiAgICAgICAgICAgICAgICAgIHRvdGFsOiBfdm0udG90YWxSZXN1bHRzLAogICAgICAgICAgICAgICAgICAiY3VycmVudC1wYWdlIjogX3ZtLmN1cnJlbnRQYWdlLAogICAgICAgICAgICAgICAgICAicGFnZS1zaXplIjogX3ZtLnBhZ2VTaXplLAogICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgIG9uOiB7CiAgICAgICAgICAgICAgICAgICJ1cGRhdGU6Y3VycmVudFBhZ2UiOiBmdW5jdGlvbiAoJGV2ZW50KSB7CiAgICAgICAgICAgICAgICAgICAgX3ZtLmN1cnJlbnRQYWdlID0gJGV2ZW50CiAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICJ1cGRhdGU6Y3VycmVudC1wYWdlIjogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgICAgICAgICAgICAgIF92bS5jdXJyZW50UGFnZSA9ICRldmVudAogICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAiY3VycmVudC1jaGFuZ2UiOiBfdm0uaGFuZGxlUGFnZUNoYW5nZSwKICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgIF0sCiAgICAgICAgICAgIDEKICAgICAgICAgICksCiAgICAgICAgXSkKICAgICAgOiBfdm0uX2UoKSwKICAgIF92bS5hY3RpdmVUYWIgPT09ICJtZXRhIiAmJiBfdm0uaGFzU2VhcmNoZWQKICAgICAgPyBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogIm1ldGEtcmVzdWx0cyIgfSwgWwogICAgICAgICAgX2MoCiAgICAgICAgICAgICJkaXYiLAogICAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAic2VhcmNoLWVuZ2luZXMiIH0sCiAgICAgICAgICAgIF92bS5fbChfdm0uc2VhcmNoRW5naW5lcywgZnVuY3Rpb24gKGVuZ2luZSkgewogICAgICAgICAgICAgIHJldHVybiBfYygKICAgICAgICAgICAgICAgICJkaXYiLAogICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICBrZXk6IGVuZ2luZS5pZCwKICAgICAgICAgICAgICAgICAgc3RhdGljQ2xhc3M6ICJlbmdpbmUtaXRlbSIsCiAgICAgICAgICAgICAgICAgIGNsYXNzOiB7IGFjdGl2ZTogZW5naW5lLmlkID09PSBfdm0uYWN0aXZlRW5naW5lIH0sCiAgICAgICAgICAgICAgICAgIG9uOiB7CiAgICAgICAgICAgICAgICAgICAgY2xpY2s6IGZ1bmN0aW9uICgkZXZlbnQpIHsKICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBfdm0udG9nZ2xlRW5naW5lKGVuZ2luZS5pZCkKICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJjaGVja2JveCIgfSwgWwogICAgICAgICAgICAgICAgICAgIF92bS5zZWxlY3RlZEVuZ2luZXMuaW5jbHVkZXMoZW5naW5lLmlkKQogICAgICAgICAgICAgICAgICAgICAgPyBfYygiaSIsIHsgc3RhdGljQ2xhc3M6ICJlbC1pY29uLWNoZWNrIiB9KQogICAgICAgICAgICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgIF9jKCJpbWciLCB7CiAgICAgICAgICAgICAgICAgICAgc3RhdGljQ2xhc3M6ICJlbmdpbmUtaWNvbiIsCiAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgc3JjOiBlbmdpbmUuaWNvbiwgYWx0OiBlbmdpbmUubmFtZSB9LAogICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgX2MoInNwYW4iLCB7IHN0YXRpY0NsYXNzOiAiZW5naW5lLW5hbWUiIH0sIFsKICAgICAgICAgICAgICAgICAgICBfdm0uX3YoX3ZtLl9zKGVuZ2luZS5uYW1lKSksCiAgICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgICAgXQogICAgICAgICAgICAgICkKICAgICAgICAgICAgfSksCiAgICAgICAgICAgIDAKICAgICAgICAgICksCiAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogInJlc3VsdHMtY29udGFpbmVyIiB9LCBbCiAgICAgICAgICAgIF92bS5zZWxlY3RlZEVuZ2luZXMuaW5jbHVkZXMoImJpbmciKQogICAgICAgICAgICAgID8gX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJyZXN1bHQtY29sdW1uIiB9LCBbCiAgICAgICAgICAgICAgICAgIF92bS5fbSgwKSwKICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgImRpdiIsCiAgICAgICAgICAgICAgICAgICAgeyBzdGF0aWNDbGFzczogInJlc3VsdC1saXN0IiB9LAogICAgICAgICAgICAgICAgICAgIF92bS5fbChfdm0uYmluZ1Jlc3VsdHMsIGZ1bmN0aW9uIChpdGVtLCBpbmRleCkgewogICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIF9jKAogICAgICAgICAgICAgICAgICAgICAgICAiZGl2IiwKICAgICAgICAgICAgICAgICAgICAgICAgeyBrZXk6ICJiaW5nLSIgKyBpbmRleCwgc3RhdGljQ2xhc3M6ICJyZXN1bHQtaXRlbSIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJoMyIsIHsgc3RhdGljQ2xhc3M6ICJpdGVtLXRpdGxlIiB9LCBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgImEiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7IGF0dHJzOiB7IGhyZWY6IGl0ZW0ubGluaywgdGFyZ2V0OiAiX2JsYW5rIiB9IH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFtfdm0uX3YoX3ZtLl9zKGl0ZW0udGl0bGUpKV0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJpdGVtLXVybCIgfSwgWwogICAgICAgICAgICAgICAgICAgICAgICAgICAgX3ZtLl92KF92bS5fcyhpdGVtLnVybCkpLAogICAgICAgICAgICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAiaXRlbS1kZXNjIiB9LCBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdm0uX3YoX3ZtLl9zKGl0ZW0uZGVzY3JpcHRpb24pKSwKICAgICAgICAgICAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgICAgICAgICAgXQogICAgICAgICAgICAgICAgICAgICAgKQogICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgIDAKICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgIF0pCiAgICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgICAgX3ZtLnNlbGVjdGVkRW5naW5lcy5pbmNsdWRlcygiYmFpZHUiKQogICAgICAgICAgICAgID8gX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJyZXN1bHQtY29sdW1uIiB9LCBbCiAgICAgICAgICAgICAgICAgIF92bS5fbSgxKSwKICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgImRpdiIsCiAgICAgICAgICAgICAgICAgICAgeyBzdGF0aWNDbGFzczogInJlc3VsdC1saXN0IiB9LAogICAgICAgICAgICAgICAgICAgIF92bS5fbChfdm0uYmFpZHVSZXN1bHRzLCBmdW5jdGlvbiAoaXRlbSwgaW5kZXgpIHsKICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBfYygKICAgICAgICAgICAgICAgICAgICAgICAgImRpdiIsCiAgICAgICAgICAgICAgICAgICAgICAgIHsga2V5OiAiYmFpZHUtIiArIGluZGV4LCBzdGF0aWNDbGFzczogInJlc3VsdC1pdGVtIiB9LAogICAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoImgzIiwgeyBzdGF0aWNDbGFzczogIml0ZW0tdGl0bGUiIH0sIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAiYSIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsgYXR0cnM6IHsgaHJlZjogaXRlbS5saW5rLCB0YXJnZXQ6ICJfYmxhbmsiIH0gfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgW192bS5fdihfdm0uX3MoaXRlbS50aXRsZSkpXQogICAgICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgICAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogIml0ZW0tdXJsIiB9LCBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdm0uX3YoX3ZtLl9zKGl0ZW0udXJsKSksCiAgICAgICAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJpdGVtLWRlc2MiIH0sIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF92bS5fdihfdm0uX3MoaXRlbS5kZXNjcmlwdGlvbikpLAogICAgICAgICAgICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgICAgICAgICAgICBdCiAgICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgICAgMAogICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgXSkKICAgICAgICAgICAgICA6IF92bS5fZSgpLAogICAgICAgICAgICBfdm0uc2VsZWN0ZWRFbmdpbmVzLmluY2x1ZGVzKCIzNjAiKQogICAgICAgICAgICAgID8gX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJyZXN1bHQtY29sdW1uIiB9LCBbCiAgICAgICAgICAgICAgICAgIF92bS5fbSgyKSwKICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgImRpdiIsCiAgICAgICAgICAgICAgICAgICAgeyBzdGF0aWNDbGFzczogInJlc3VsdC1saXN0IiB9LAogICAgICAgICAgICAgICAgICAgIF92bS5fbChfdm0uc28zNjBSZXN1bHRzLCBmdW5jdGlvbiAoaXRlbSwgaW5kZXgpIHsKICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBfYygKICAgICAgICAgICAgICAgICAgICAgICAgImRpdiIsCiAgICAgICAgICAgICAgICAgICAgICAgIHsga2V5OiAiMzYwLSIgKyBpbmRleCwgc3RhdGljQ2xhc3M6ICJyZXN1bHQtaXRlbSIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJoMyIsIHsgc3RhdGljQ2xhc3M6ICJpdGVtLXRpdGxlIiB9LCBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgImEiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7IGF0dHJzOiB7IGhyZWY6IGl0ZW0ubGluaywgdGFyZ2V0OiAiX2JsYW5rIiB9IH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFtfdm0uX3YoX3ZtLl9zKGl0ZW0udGl0bGUpKV0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJpdGVtLXVybCIgfSwgWwogICAgICAgICAgICAgICAgICAgICAgICAgICAgX3ZtLl92KF92bS5fcyhpdGVtLnVybCkpLAogICAgICAgICAgICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAiaXRlbS1kZXNjIiB9LCBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdm0uX3YoX3ZtLl9zKGl0ZW0uZGVzY3JpcHRpb24pKSwKICAgICAgICAgICAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgICAgICAgICAgXQogICAgICAgICAgICAgICAgICAgICAgKQogICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgIDAKICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgIF0pCiAgICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgIF0pLAogICAgICAgIF0pCiAgICAgIDogX3ZtLl9lKCksCiAgXSkKfQp2YXIgc3RhdGljUmVuZGVyRm5zID0gWwogIGZ1bmN0aW9uICgpIHsKICAgIHZhciBfdm0gPSB0aGlzCiAgICB2YXIgX2ggPSBfdm0uJGNyZWF0ZUVsZW1lbnQKICAgIHZhciBfYyA9IF92bS5fc2VsZi5fYyB8fCBfaAogICAgcmV0dXJuIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAicmVzdWx0LWhlYWRlciIgfSwgWwogICAgICBfYygiaW1nIiwgewogICAgICAgIHN0YXRpY0NsYXNzOiAicmVzdWx0LWljb24iLAogICAgICAgIGF0dHJzOiB7CiAgICAgICAgICBzcmM6ICJodHRwczovL3d3dy5iaW5nLmNvbS9mYXZpY29uLmljbyIsCiAgICAgICAgICBhbHQ6ICJNaWNyb3NvZnQgQmluZyIsCiAgICAgICAgfSwKICAgICAgfSksCiAgICAgIF9jKCJzcGFuIiwgeyBzdGF0aWNDbGFzczogInJlc3VsdC10aXRsZSIgfSwgW192bS5fdigiTWljcm9zb2Z0IEJpbmciKV0pLAogICAgXSkKICB9LAogIGZ1bmN0aW9uICgpIHsKICAgIHZhciBfdm0gPSB0aGlzCiAgICB2YXIgX2ggPSBfdm0uJGNyZWF0ZUVsZW1lbnQKICAgIHZhciBfYyA9IF92bS5fc2VsZi5fYyB8fCBfaAogICAgcmV0dXJuIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAicmVzdWx0LWhlYWRlciIgfSwgWwogICAgICBfYygiaW1nIiwgewogICAgICAgIHN0YXRpY0NsYXNzOiAicmVzdWx0LWljb24iLAogICAgICAgIGF0dHJzOiB7IHNyYzogImh0dHBzOi8vd3d3LmJhaWR1LmNvbS9mYXZpY29uLmljbyIsIGFsdDogIueZvuW6puaQnOe0oiIgfSwKICAgICAgfSksCiAgICAgIF9jKCJzcGFuIiwgeyBzdGF0aWNDbGFzczogInJlc3VsdC10aXRsZSIgfSwgW192bS5fdigi55m+5bqm5pCc57SiIildKSwKICAgIF0pCiAgfSwKICBmdW5jdGlvbiAoKSB7CiAgICB2YXIgX3ZtID0gdGhpcwogICAgdmFyIF9oID0gX3ZtLiRjcmVhdGVFbGVtZW50CiAgICB2YXIgX2MgPSBfdm0uX3NlbGYuX2MgfHwgX2gKICAgIHJldHVybiBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogInJlc3VsdC1oZWFkZXIiIH0sIFsKICAgICAgX2MoImltZyIsIHsKICAgICAgICBzdGF0aWNDbGFzczogInJlc3VsdC1pY29uIiwKICAgICAgICBhdHRyczogeyBzcmM6ICJodHRwczovL3d3dy5zby5jb20vZmF2aWNvbi5pY28iLCBhbHQ6ICIzNjDmkJzntKIiIH0sCiAgICAgIH0pLAogICAgICBfYygic3BhbiIsIHsgc3RhdGljQ2xhc3M6ICJyZXN1bHQtdGl0bGUiIH0sIFtfdm0uX3YoIjM2MOaQnOe0oiIpXSksCiAgICBdKQogIH0sCl0KcmVuZGVyLl93aXRoU3RyaXBwZWQgPSB0cnVlCgpleHBvcnQgeyByZW5kZXIsIHN0YXRpY1JlbmRlckZucyB9"}]}