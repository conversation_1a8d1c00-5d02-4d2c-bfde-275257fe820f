{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\themes\\bubble.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\themes\\bubble.js", "mtime": 1749104420888}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_lodashEs", "require", "_emitter", "_interopRequireDefault", "_base", "_interopRequireWildcard", "_selection", "_icons", "_quill", "TOOLBAR_CONFIG", "header", "BubbleTooltip", "exports", "_BaseTooltip", "quill", "bounds", "_this", "_classCallCheck2", "default", "_callSuper2", "on", "Emitter", "events", "EDITOR_CHANGE", "type", "range", "oldRange", "source", "SELECTION_CHANGE", "length", "sources", "USER", "show", "root", "style", "left", "width", "concat", "offsetWidth", "lines", "getLines", "index", "getBounds", "position", "lastLine", "getIndex", "Math", "min", "indexBounds", "Range", "document", "activeElement", "textbox", "hasFocus", "hide", "_inherits2", "_createClass2", "key", "value", "listen", "_this2", "_superPropGet2", "querySelector", "addEventListener", "classList", "remove", "SCROLL_OPTIMIZE", "setTimeout", "contains", "getSelection", "cancel", "reference", "shift", "arrow", "marginLeft", "BaseTooltip", "_defineProperty2", "join", "BubbleTheme", "_BaseTheme", "options", "_this3", "modules", "toolbar", "container", "add", "extendToolbar", "tooltip", "append<PERSON><PERSON><PERSON>", "buildButtons", "querySelectorAll", "icons", "buildPickers", "BaseTheme", "DEFAULTS", "merge", "handlers", "link", "format", "<PERSON><PERSON><PERSON>", "theme", "edit"], "sources": ["../../src/themes/bubble.ts"], "sourcesContent": ["import { merge } from 'lodash-es';\nimport Emitter from '../core/emitter.js';\nimport BaseTheme, { BaseTooltip } from './base.js';\nimport { Range } from '../core/selection.js';\nimport type { Bounds } from '../core/selection.js';\nimport icons from '../ui/icons.js';\nimport Quill from '../core/quill.js';\nimport type { ThemeOptions } from '../core/theme.js';\nimport type Toolbar from '../modules/toolbar.js';\nimport type { ToolbarConfig } from '../modules/toolbar.js';\n\nconst TOOLBAR_CONFIG: ToolbarConfig = [\n  ['bold', 'italic', 'link'],\n  [{ header: 1 }, { header: 2 }, 'blockquote'],\n];\n\nclass BubbleTooltip extends BaseTooltip {\n  static TEMPLATE = [\n    '<span class=\"ql-tooltip-arrow\"></span>',\n    '<div class=\"ql-tooltip-editor\">',\n    '<input type=\"text\" data-formula=\"e=mc^2\" data-link=\"https://quilljs.com\" data-video=\"Embed URL\">',\n    '<a class=\"ql-close\"></a>',\n    '</div>',\n  ].join('');\n\n  constructor(quill: Quill, bounds?: HTMLElement) {\n    super(quill, bounds);\n    this.quill.on(\n      Emitter.events.EDITOR_CHANGE,\n      (type, range, oldRange, source) => {\n        if (type !== Emitter.events.SELECTION_CHANGE) return;\n        if (\n          range != null &&\n          range.length > 0 &&\n          source === Emitter.sources.USER\n        ) {\n          this.show();\n          // Lock our width so we will expand beyond our offsetParent boundaries\n          this.root.style.left = '0px';\n          this.root.style.width = '';\n          this.root.style.width = `${this.root.offsetWidth}px`;\n          const lines = this.quill.getLines(range.index, range.length);\n          if (lines.length === 1) {\n            const bounds = this.quill.getBounds(range);\n            if (bounds != null) {\n              this.position(bounds);\n            }\n          } else {\n            const lastLine = lines[lines.length - 1];\n            const index = this.quill.getIndex(lastLine);\n            const length = Math.min(\n              lastLine.length() - 1,\n              range.index + range.length - index,\n            );\n            const indexBounds = this.quill.getBounds(new Range(index, length));\n            if (indexBounds != null) {\n              this.position(indexBounds);\n            }\n          }\n        } else if (\n          document.activeElement !== this.textbox &&\n          this.quill.hasFocus()\n        ) {\n          this.hide();\n        }\n      },\n    );\n  }\n\n  listen() {\n    super.listen();\n    // @ts-expect-error Fix me later\n    this.root.querySelector('.ql-close').addEventListener('click', () => {\n      this.root.classList.remove('ql-editing');\n    });\n    this.quill.on(Emitter.events.SCROLL_OPTIMIZE, () => {\n      // Let selection be restored by toolbar handlers before repositioning\n      setTimeout(() => {\n        if (this.root.classList.contains('ql-hidden')) return;\n        const range = this.quill.getSelection();\n        if (range != null) {\n          const bounds = this.quill.getBounds(range);\n          if (bounds != null) {\n            this.position(bounds);\n          }\n        }\n      }, 1);\n    });\n  }\n\n  cancel() {\n    this.show();\n  }\n\n  position(reference: Bounds) {\n    const shift = super.position(reference);\n    const arrow = this.root.querySelector('.ql-tooltip-arrow');\n    // @ts-expect-error\n    arrow.style.marginLeft = '';\n    if (shift !== 0) {\n      // @ts-expect-error\n      arrow.style.marginLeft = `${-1 * shift - arrow.offsetWidth / 2}px`;\n    }\n    return shift;\n  }\n}\n\nclass BubbleTheme extends BaseTheme {\n  tooltip: BubbleTooltip;\n\n  constructor(quill: Quill, options: ThemeOptions) {\n    if (\n      options.modules.toolbar != null &&\n      options.modules.toolbar.container == null\n    ) {\n      options.modules.toolbar.container = TOOLBAR_CONFIG;\n    }\n    super(quill, options);\n    this.quill.container.classList.add('ql-bubble');\n  }\n\n  extendToolbar(toolbar: Toolbar) {\n    // @ts-expect-error\n    this.tooltip = new BubbleTooltip(this.quill, this.options.bounds);\n    if (toolbar.container != null) {\n      this.tooltip.root.appendChild<HTMLElement>(toolbar.container);\n      this.buildButtons(toolbar.container.querySelectorAll('button'), icons);\n      this.buildPickers(toolbar.container.querySelectorAll('select'), icons);\n    }\n  }\n}\nBubbleTheme.DEFAULTS = merge({}, BaseTheme.DEFAULTS, {\n  modules: {\n    toolbar: {\n      handlers: {\n        link(value: string) {\n          if (!value) {\n            this.quill.format('link', false, Quill.sources.USER);\n          } else {\n            // @ts-expect-error\n            this.quill.theme.tooltip.edit();\n          }\n        },\n      },\n    },\n  },\n} satisfies ThemeOptions);\n\nexport { BubbleTooltip, BubbleTheme as default };\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,KAAA,GAAAC,uBAAA,CAAAJ,OAAA;AACA,IAAAK,UAAA,GAAAL,OAAA;AAEA,IAAAM,MAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,MAAA,GAAAL,sBAAA,CAAAF,OAAA;AAKA,IAAMQ,cAA6B,GAAG,CACpC,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,EAC1B,CAAC;EAAEC,MAAM,EAAE;AAAE,CAAC,EAAE;EAAEA,MAAM,EAAE;AAAE,CAAC,EAAE,YAAY,CAAC,CAC7C;AAAA,IAEKC,aAAa,GAAAC,OAAA,CAAAD,aAAA,0BAAAE,YAAA;EASjB,SAAAF,cAAYG,KAAY,EAAEC,MAAoB,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAP,aAAA;IAC9CK,KAAA,OAAAG,WAAA,CAAAD,OAAA,QAAAP,aAAA,GAAMG,KAAK,EAAEC,MAAM;IACnBC,KAAA,CAAKF,KAAK,CAACM,EAAE,CACXC,gBAAO,CAACC,MAAM,CAACC,aAAa,EAC5B,UAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAK;MACjC,IAAIH,IAAI,KAAKH,gBAAO,CAACC,MAAM,CAACM,gBAAgB,EAAE;MAC9C,IACEH,KAAK,IAAI,IAAI,IACbA,KAAK,CAACI,MAAM,GAAG,CAAC,IAChBF,MAAM,KAAKN,gBAAO,CAACS,OAAO,CAACC,IAAI,EAC/B;QACAf,KAAA,CAAKgB,IAAI,CAAC,CAAC;QACX;QACAhB,KAAA,CAAKiB,IAAI,CAACC,KAAK,CAACC,IAAI,GAAG,KAAK;QAC5BnB,KAAA,CAAKiB,IAAI,CAACC,KAAK,CAACE,KAAK,GAAG,EAAE;QAC1BpB,KAAA,CAAKiB,IAAI,CAACC,KAAK,CAACE,KAAK,MAAAC,MAAA,CAAMrB,KAAA,CAAKiB,IAAI,CAACK,WAAY,OAAG;QACpD,IAAMC,KAAK,GAAGvB,KAAA,CAAKF,KAAK,CAAC0B,QAAQ,CAACf,KAAK,CAACgB,KAAK,EAAEhB,KAAK,CAACI,MAAM,CAAC;QAC5D,IAAIU,KAAK,CAACV,MAAM,KAAK,CAAC,EAAE;UACtB,IAAMd,OAAM,GAAGC,KAAA,CAAKF,KAAK,CAAC4B,SAAS,CAACjB,KAAK,CAAC;UAC1C,IAAIV,OAAM,IAAI,IAAI,EAAE;YAClBC,KAAA,CAAK2B,QAAQ,CAAC5B,OAAM,CAAC;UACvB;QACF,CAAC,MAAM;UACL,IAAM6B,QAAQ,GAAGL,KAAK,CAACA,KAAK,CAACV,MAAM,GAAG,CAAC,CAAC;UACxC,IAAMY,KAAK,GAAGzB,KAAA,CAAKF,KAAK,CAAC+B,QAAQ,CAACD,QAAQ,CAAC;UAC3C,IAAMf,MAAM,GAAGiB,IAAI,CAACC,GAAG,CACrBH,QAAQ,CAACf,MAAM,CAAC,CAAC,GAAG,CAAC,EACrBJ,KAAK,CAACgB,KAAK,GAAGhB,KAAK,CAACI,MAAM,GAAGY,KAC/B,CAAC;UACD,IAAMO,WAAW,GAAGhC,KAAA,CAAKF,KAAK,CAAC4B,SAAS,CAAC,IAAIO,gBAAK,CAACR,KAAK,EAAEZ,MAAM,CAAC,CAAC;UAClE,IAAImB,WAAW,IAAI,IAAI,EAAE;YACvBhC,KAAA,CAAK2B,QAAQ,CAACK,WAAW,CAAC;UAC5B;QACF;MACF,CAAC,MAAM,IACLE,QAAQ,CAACC,aAAa,KAAKnC,KAAA,CAAKoC,OAAO,IACvCpC,KAAA,CAAKF,KAAK,CAACuC,QAAQ,CAAC,CAAC,EACrB;QACArC,KAAA,CAAKsC,IAAI,CAAC,CAAC;MACb;IACF,CACF,CAAC;IAAA,OAAAtC,KAAA;EACH;EAAA,IAAAuC,UAAA,CAAArC,OAAA,EAAAP,aAAA,EAAAE,YAAA;EAAA,WAAA2C,aAAA,CAAAtC,OAAA,EAAAP,aAAA;IAAA8C,GAAA;IAAAC,KAAA,EAEA,SAAAC,MAAMA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACP,IAAAC,cAAA,CAAA3C,OAAA,EAAAP,aAAA;MACA;MACA,IAAI,CAACsB,IAAI,CAAC6B,aAAa,CAAC,WAAW,CAAC,CAACC,gBAAgB,CAAC,OAAO,EAAE,YAAM;QACnEH,MAAI,CAAC3B,IAAI,CAAC+B,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;MAC1C,CAAC,CAAC;MACF,IAAI,CAACnD,KAAK,CAACM,EAAE,CAACC,gBAAO,CAACC,MAAM,CAAC4C,eAAe,EAAE,YAAM;QAClD;QACAC,UAAU,CAAC,YAAM;UACf,IAAIP,MAAI,CAAC3B,IAAI,CAAC+B,SAAS,CAACI,QAAQ,CAAC,WAAW,CAAC,EAAE;UAC/C,IAAM3C,KAAK,GAAGmC,MAAI,CAAC9C,KAAK,CAACuD,YAAY,CAAC,CAAC;UACvC,IAAI5C,KAAK,IAAI,IAAI,EAAE;YACjB,IAAMV,MAAM,GAAG6C,MAAI,CAAC9C,KAAK,CAAC4B,SAAS,CAACjB,KAAK,CAAC;YAC1C,IAAIV,MAAM,IAAI,IAAI,EAAE;cAClB6C,MAAI,CAACjB,QAAQ,CAAC5B,MAAM,CAAC;YACvB;UACF;QACF,CAAC,EAAE,CAAC,CAAC;MACP,CAAC,CAAC;IACJ;EAAA;IAAA0C,GAAA;IAAAC,KAAA,EAEA,SAAAY,MAAMA,CAAA,EAAG;MACP,IAAI,CAACtC,IAAI,CAAC,CAAC;IACb;EAAA;IAAAyB,GAAA;IAAAC,KAAA,EAEA,SAAAf,QAAQA,CAAC4B,SAAiB,EAAE;MAC1B,IAAMC,KAAK,OAAAX,cAAA,CAAA3C,OAAA,EAAAP,aAAA,wBAAkB4D,SAAS,EAAC;MACvC,IAAME,KAAK,GAAG,IAAI,CAACxC,IAAI,CAAC6B,aAAa,CAAC,mBAAmB,CAAC;MAC1D;MACAW,KAAK,CAACvC,KAAK,CAACwC,UAAU,GAAG,EAAE;MAC3B,IAAIF,KAAK,KAAK,CAAC,EAAE;QACf;QACAC,KAAK,CAACvC,KAAK,CAACwC,UAAU,MAAArC,MAAA,CAAM,CAAC,CAAC,GAAGmC,KAAK,GAAGC,KAAK,CAACnC,WAAW,GAAG,CAAE,OAAG;MACpE;MACA,OAAOkC,KAAK;IACd;EAAA;AAAA,EAxF0BG,iBAAW;AAAA,IAAAC,gBAAA,CAAA1D,OAAA,EAAjCP,aAAa,cACC,CAChB,wCAAwC,EACxC,iCAAiC,EACjC,kGAAkG,EAClG,0BAA0B,EAC1B,QAAQ,CACT,CAACkE,IAAI,CAAC,EAAE,CAAC;AAAA,IAoFNC,WAAW,GAAAlE,OAAA,CAAAM,OAAA,0BAAA6D,UAAA;EAGf,SAAAD,YAAYhE,KAAY,EAAEkE,OAAqB,EAAE;IAAA,IAAAC,MAAA;IAAA,IAAAhE,gBAAA,CAAAC,OAAA,QAAA4D,WAAA;IAC/C,IACEE,OAAO,CAACE,OAAO,CAACC,OAAO,IAAI,IAAI,IAC/BH,OAAO,CAACE,OAAO,CAACC,OAAO,CAACC,SAAS,IAAI,IAAI,EACzC;MACAJ,OAAO,CAACE,OAAO,CAACC,OAAO,CAACC,SAAS,GAAG3E,cAAc;IACpD;IACAwE,MAAA,OAAA9D,WAAA,CAAAD,OAAA,QAAA4D,WAAA,GAAMhE,KAAK,EAAEkE,OAAO;IACpBC,MAAA,CAAKnE,KAAK,CAACsE,SAAS,CAACpB,SAAS,CAACqB,GAAG,CAAC,WAAW,CAAC;IAAA,OAAAJ,MAAA;EACjD;EAAA,IAAA1B,UAAA,CAAArC,OAAA,EAAA4D,WAAA,EAAAC,UAAA;EAAA,WAAAvB,aAAA,CAAAtC,OAAA,EAAA4D,WAAA;IAAArB,GAAA;IAAAC,KAAA,EAEA,SAAA4B,aAAaA,CAACH,OAAgB,EAAE;MAC9B;MACA,IAAI,CAACI,OAAO,GAAG,IAAI5E,aAAa,CAAC,IAAI,CAACG,KAAK,EAAE,IAAI,CAACkE,OAAO,CAACjE,MAAM,CAAC;MACjE,IAAIoE,OAAO,CAACC,SAAS,IAAI,IAAI,EAAE;QAC7B,IAAI,CAACG,OAAO,CAACtD,IAAI,CAACuD,WAAW,CAAcL,OAAO,CAACC,SAAS,CAAC;QAC7D,IAAI,CAACK,YAAY,CAACN,OAAO,CAACC,SAAS,CAACM,gBAAgB,CAAC,QAAQ,CAAC,EAAEC,cAAK,CAAC;QACtE,IAAI,CAACC,YAAY,CAACT,OAAO,CAACC,SAAS,CAACM,gBAAgB,CAAC,QAAQ,CAAC,EAAEC,cAAK,CAAC;MACxE;IACF;EAAA;AAAA,EAtBwBE,aAAS;AAwBnCf,WAAW,CAACgB,QAAQ,GAAG,IAAAC,eAAK,EAAC,CAAC,CAAC,EAAEF,aAAS,CAACC,QAAQ,EAAE;EACnDZ,OAAO,EAAE;IACPC,OAAO,EAAE;MACPa,QAAQ,EAAE;QACRC,IAAI,WAAJA,IAAIA,CAACvC,KAAa,EAAE;UAClB,IAAI,CAACA,KAAK,EAAE;YACV,IAAI,CAAC5C,KAAK,CAACoF,MAAM,CAAC,MAAM,EAAE,KAAK,EAAEC,cAAK,CAACrE,OAAO,CAACC,IAAI,CAAC;UACtD,CAAC,MAAM;YACL;YACA,IAAI,CAACjB,KAAK,CAACsF,KAAK,CAACb,OAAO,CAACc,IAAI,CAAC,CAAC;UACjC;QACF;MACF;IACF;EACF;AACF,CAAwB,CAAC", "ignoreList": []}]}