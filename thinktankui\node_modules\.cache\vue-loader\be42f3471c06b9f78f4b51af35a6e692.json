{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\system\\role\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\system\\role\\index.vue", "mtime": 1749104047647}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0Um9sZSwgZ2V0Um9sZSwgZGVsUm9sZSwgYWRkUm9sZSwgdXBkYXRlUm9sZSwgZGF0YVNjb3BlLCBjaGFuZ2VSb2xlU3RhdHVzLCBkZXB0VHJlZVNlbGVjdCB9IGZyb20gIkAvYXBpL3N5c3RlbS9yb2xlIjsNCmltcG9ydCB7IHRyZWVzZWxlY3QgYXMgbWVudVRyZWVzZWxlY3QsIHJvbGVNZW51VHJlZXNlbGVjdCB9IGZyb20gIkAvYXBpL3N5c3RlbS9tZW51IjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiUm9sZSIsDQogIGRpY3RzOiBbJ3N5c19ub3JtYWxfZGlzYWJsZSddLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgIGlkczogW10sDQogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g6KeS6Imy6KGo5qC85pWw5o2uDQogICAgICByb2xlTGlzdDogW10sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYLvvIjmlbDmja7mnYPpmZDvvIkNCiAgICAgIG9wZW5EYXRhU2NvcGU6IGZhbHNlLA0KICAgICAgbWVudUV4cGFuZDogZmFsc2UsDQogICAgICBtZW51Tm9kZUFsbDogZmFsc2UsDQogICAgICBkZXB0RXhwYW5kOiB0cnVlLA0KICAgICAgZGVwdE5vZGVBbGw6IGZhbHNlLA0KICAgICAgLy8g5pel5pyf6IyD5Zu0DQogICAgICBkYXRlUmFuZ2U6IFtdLA0KICAgICAgLy8g5pWw5o2u6IyD5Zu06YCJ6aG5DQogICAgICBkYXRhU2NvcGVPcHRpb25zOiBbDQogICAgICAgIHsNCiAgICAgICAgICB2YWx1ZTogIjEiLA0KICAgICAgICAgIGxhYmVsOiAi5YWo6YOo5pWw5o2u5p2D6ZmQIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdmFsdWU6ICIyIiwNCiAgICAgICAgICBsYWJlbDogIuiHquWumuaVsOaNruadg+mZkCINCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHZhbHVlOiAiMyIsDQogICAgICAgICAgbGFiZWw6ICLmnKzpg6jpl6jmlbDmja7mnYPpmZAiDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB2YWx1ZTogIjQiLA0KICAgICAgICAgIGxhYmVsOiAi5pys6YOo6Zeo5Y+K5Lul5LiL5pWw5o2u5p2D6ZmQIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdmFsdWU6ICI1IiwNCiAgICAgICAgICBsYWJlbDogIuS7heacrOS6uuaVsOaNruadg+mZkCINCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIC8vIOiPnOWNleWIl+ihqA0KICAgICAgbWVudU9wdGlvbnM6IFtdLA0KICAgICAgLy8g6YOo6Zeo5YiX6KGoDQogICAgICBkZXB0T3B0aW9uczogW10sDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgcm9sZU5hbWU6IHVuZGVmaW5lZCwNCiAgICAgICAgcm9sZUtleTogdW5kZWZpbmVkLA0KICAgICAgICBzdGF0dXM6IHVuZGVmaW5lZA0KICAgICAgfSwNCiAgICAgIC8vIOihqOWNleWPguaVsA0KICAgICAgZm9ybToge30sDQogICAgICBkZWZhdWx0UHJvcHM6IHsNCiAgICAgICAgY2hpbGRyZW46ICJjaGlsZHJlbiIsDQogICAgICAgIGxhYmVsOiAibGFiZWwiDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICBydWxlczogew0KICAgICAgICByb2xlTmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLop5LoibLlkI3np7DkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICByb2xlS2V5OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuadg+mZkOWtl+espuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICAgIHJvbGVTb3J0OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuinkuiJsumhuuW6j+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0NCiAgICAgIH0NCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOafpeivouinkuiJsuWIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGlzdFJvbGUodGhpcy5hZGREYXRlUmFuZ2UodGhpcy5xdWVyeVBhcmFtcywgdGhpcy5kYXRlUmFuZ2UpKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICB0aGlzLnJvbGVMaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgIH0NCiAgICAgICk7DQogICAgfSwNCiAgICAvKiog5p+l6K+i6I+c5Y2V5qCR57uT5p6EICovDQogICAgZ2V0TWVudVRyZWVzZWxlY3QoKSB7DQogICAgICBtZW51VHJlZXNlbGVjdCgpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLm1lbnVPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5omA5pyJ6I+c5Y2V6IqC54K55pWw5o2uDQogICAgZ2V0TWVudUFsbENoZWNrZWRLZXlzKCkgew0KICAgICAgLy8g55uu5YmN6KKr6YCJ5Lit55qE6I+c5Y2V6IqC54K5DQogICAgICBsZXQgY2hlY2tlZEtleXMgPSB0aGlzLiRyZWZzLm1lbnUuZ2V0Q2hlY2tlZEtleXMoKTsNCiAgICAgIC8vIOWNiumAieS4reeahOiPnOWNleiKgueCuQ0KICAgICAgbGV0IGhhbGZDaGVja2VkS2V5cyA9IHRoaXMuJHJlZnMubWVudS5nZXRIYWxmQ2hlY2tlZEtleXMoKTsNCiAgICAgIGNoZWNrZWRLZXlzLnVuc2hpZnQuYXBwbHkoY2hlY2tlZEtleXMsIGhhbGZDaGVja2VkS2V5cyk7DQogICAgICByZXR1cm4gY2hlY2tlZEtleXM7DQogICAgfSwNCiAgICAvLyDmiYDmnInpg6jpl6joioLngrnmlbDmja4NCiAgICBnZXREZXB0QWxsQ2hlY2tlZEtleXMoKSB7DQogICAgICAvLyDnm67liY3ooqvpgInkuK3nmoTpg6jpl6joioLngrkNCiAgICAgIGxldCBjaGVja2VkS2V5cyA9IHRoaXMuJHJlZnMuZGVwdC5nZXRDaGVja2VkS2V5cygpOw0KICAgICAgLy8g5Y2K6YCJ5Lit55qE6YOo6Zeo6IqC54K5DQogICAgICBsZXQgaGFsZkNoZWNrZWRLZXlzID0gdGhpcy4kcmVmcy5kZXB0LmdldEhhbGZDaGVja2VkS2V5cygpOw0KICAgICAgY2hlY2tlZEtleXMudW5zaGlmdC5hcHBseShjaGVja2VkS2V5cywgaGFsZkNoZWNrZWRLZXlzKTsNCiAgICAgIHJldHVybiBjaGVja2VkS2V5czsNCiAgICB9LA0KICAgIC8qKiDmoLnmja7op5LoibJJROafpeivouiPnOWNleagkee7k+aehCAqLw0KICAgIGdldFJvbGVNZW51VHJlZXNlbGVjdChyb2xlSWQpIHsNCiAgICAgIHJldHVybiByb2xlTWVudVRyZWVzZWxlY3Qocm9sZUlkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5tZW51T3B0aW9ucyA9IHJlc3BvbnNlLm1lbnVzOw0KICAgICAgICByZXR1cm4gcmVzcG9uc2U7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDmoLnmja7op5LoibJJROafpeivoumDqOmXqOagkee7k+aehCAqLw0KICAgIGdldERlcHRUcmVlKHJvbGVJZCkgew0KICAgICAgcmV0dXJuIGRlcHRUcmVlU2VsZWN0KHJvbGVJZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZGVwdE9wdGlvbnMgPSByZXNwb25zZS5kZXB0czsNCiAgICAgICAgcmV0dXJuIHJlc3BvbnNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDop5LoibLnirbmgIHkv67mlLkNCiAgICBoYW5kbGVTdGF0dXNDaGFuZ2Uocm93KSB7DQogICAgICBsZXQgdGV4dCA9IHJvdy5zdGF0dXMgPT09ICIwIiA/ICLlkK/nlKgiIDogIuWBnOeUqCI7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfnoa7orqTopoEiJyArIHRleHQgKyAnIiInICsgcm93LnJvbGVOYW1lICsgJyLop5LoibLlkJfvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgew0KICAgICAgICByZXR1cm4gY2hhbmdlUm9sZVN0YXR1cyhyb3cucm9sZUlkLCByb3cuc3RhdHVzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKHRleHQgKyAi5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaChmdW5jdGlvbigpIHsNCiAgICAgICAgcm93LnN0YXR1cyA9IHJvdy5zdGF0dXMgPT09ICIwIiA/ICIxIiA6ICIwIjsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5Y+W5raI5oyJ6ZKuDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgfSwNCiAgICAvLyDlj5bmtojmjInpkq7vvIjmlbDmja7mnYPpmZDvvIkNCiAgICBjYW5jZWxEYXRhU2NvcGUoKSB7DQogICAgICB0aGlzLm9wZW5EYXRhU2NvcGUgPSBmYWxzZTsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICB9LA0KICAgIC8vIOihqOWNlemHjee9rg0KICAgIHJlc2V0KCkgew0KICAgICAgaWYgKHRoaXMuJHJlZnMubWVudSAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgdGhpcy4kcmVmcy5tZW51LnNldENoZWNrZWRLZXlzKFtdKTsNCiAgICAgIH0NCiAgICAgIHRoaXMubWVudUV4cGFuZCA9IGZhbHNlLA0KICAgICAgdGhpcy5tZW51Tm9kZUFsbCA9IGZhbHNlLA0KICAgICAgdGhpcy5kZXB0RXhwYW5kID0gdHJ1ZSwNCiAgICAgIHRoaXMuZGVwdE5vZGVBbGwgPSBmYWxzZSwNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgcm9sZUlkOiB1bmRlZmluZWQsDQogICAgICAgIHJvbGVOYW1lOiB1bmRlZmluZWQsDQogICAgICAgIHJvbGVLZXk6IHVuZGVmaW5lZCwNCiAgICAgICAgcm9sZVNvcnQ6IDAsDQogICAgICAgIHN0YXR1czogIjAiLA0KICAgICAgICBtZW51SWRzOiBbXSwNCiAgICAgICAgZGVwdElkczogW10sDQogICAgICAgIG1lbnVDaGVja1N0cmljdGx5OiB0cnVlLA0KICAgICAgICBkZXB0Q2hlY2tTdHJpY3RseTogdHJ1ZSwNCiAgICAgICAgcmVtYXJrOiB1bmRlZmluZWQNCiAgICAgIH07DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLmRhdGVSYW5nZSA9IFtdOw0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0sDQogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5yb2xlSWQpDQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGghPTENCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aA0KICAgIH0sDQogICAgLy8g5pu05aSa5pON5L2c6Kem5Y+RDQogICAgaGFuZGxlQ29tbWFuZChjb21tYW5kLCByb3cpIHsNCiAgICAgIHN3aXRjaCAoY29tbWFuZCkgew0KICAgICAgICBjYXNlICJoYW5kbGVEYXRhU2NvcGUiOg0KICAgICAgICAgIHRoaXMuaGFuZGxlRGF0YVNjb3BlKHJvdyk7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgImhhbmRsZUF1dGhVc2VyIjoNCiAgICAgICAgICB0aGlzLmhhbmRsZUF1dGhVc2VyKHJvdyk7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGRlZmF1bHQ6DQogICAgICAgICAgYnJlYWs7DQogICAgICB9DQogICAgfSwNCiAgICAvLyDmoJHmnYPpmZDvvIjlsZXlvIAv5oqY5Y+g77yJDQogICAgaGFuZGxlQ2hlY2tlZFRyZWVFeHBhbmQodmFsdWUsIHR5cGUpIHsNCiAgICAgIGlmICh0eXBlID09ICdtZW51Jykgew0KICAgICAgICBsZXQgdHJlZUxpc3QgPSB0aGlzLm1lbnVPcHRpb25zOw0KICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRyZWVMaXN0Lmxlbmd0aDsgaSsrKSB7DQogICAgICAgICAgdGhpcy4kcmVmcy5tZW51LnN0b3JlLm5vZGVzTWFwW3RyZWVMaXN0W2ldLmlkXS5leHBhbmRlZCA9IHZhbHVlOw0KICAgICAgICB9DQogICAgICB9IGVsc2UgaWYgKHR5cGUgPT0gJ2RlcHQnKSB7DQogICAgICAgIGxldCB0cmVlTGlzdCA9IHRoaXMuZGVwdE9wdGlvbnM7DQogICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdHJlZUxpc3QubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgICB0aGlzLiRyZWZzLmRlcHQuc3RvcmUubm9kZXNNYXBbdHJlZUxpc3RbaV0uaWRdLmV4cGFuZGVkID0gdmFsdWU7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOagkeadg+mZkO+8iOWFqOmAiS/lhajkuI3pgInvvIkNCiAgICBoYW5kbGVDaGVja2VkVHJlZU5vZGVBbGwodmFsdWUsIHR5cGUpIHsNCiAgICAgIGlmICh0eXBlID09ICdtZW51Jykgew0KICAgICAgICB0aGlzLiRyZWZzLm1lbnUuc2V0Q2hlY2tlZE5vZGVzKHZhbHVlID8gdGhpcy5tZW51T3B0aW9uczogW10pOw0KICAgICAgfSBlbHNlIGlmICh0eXBlID09ICdkZXB0Jykgew0KICAgICAgICB0aGlzLiRyZWZzLmRlcHQuc2V0Q2hlY2tlZE5vZGVzKHZhbHVlID8gdGhpcy5kZXB0T3B0aW9uczogW10pOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5qCR5p2D6ZmQ77yI54i25a2Q6IGU5Yqo77yJDQogICAgaGFuZGxlQ2hlY2tlZFRyZWVDb25uZWN0KHZhbHVlLCB0eXBlKSB7DQogICAgICBpZiAodHlwZSA9PSAnbWVudScpIHsNCiAgICAgICAgdGhpcy5mb3JtLm1lbnVDaGVja1N0cmljdGx5ID0gdmFsdWUgPyB0cnVlOiBmYWxzZTsNCiAgICAgIH0gZWxzZSBpZiAodHlwZSA9PSAnZGVwdCcpIHsNCiAgICAgICAgdGhpcy5mb3JtLmRlcHRDaGVja1N0cmljdGx5ID0gdmFsdWUgPyB0cnVlOiBmYWxzZTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICB0aGlzLmdldE1lbnVUcmVlc2VsZWN0KCk7DQogICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDop5LoibIiOw0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIGNvbnN0IHJvbGVJZCA9IHJvdy5yb2xlSWQgfHwgdGhpcy5pZHMNCiAgICAgIGNvbnN0IHJvbGVNZW51ID0gdGhpcy5nZXRSb2xlTWVudVRyZWVzZWxlY3Qocm9sZUlkKTsNCiAgICAgIGdldFJvbGUocm9sZUlkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIHJvbGVNZW51LnRoZW4ocmVzID0+IHsNCiAgICAgICAgICAgIGxldCBjaGVja2VkS2V5cyA9IHJlcy5jaGVja2VkS2V5cw0KICAgICAgICAgICAgY2hlY2tlZEtleXMuZm9yRWFjaCgodikgPT4gew0KICAgICAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpPT57DQogICAgICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMubWVudS5zZXRDaGVja2VkKHYsIHRydWUgLGZhbHNlKTsNCiAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9KTsNCiAgICAgICAgfSk7DQogICAgICB9KTsNCiAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS56KeS6ImyIjsNCiAgICB9LA0KICAgIC8qKiDpgInmi6nop5LoibLmnYPpmZDojIPlm7Top6blj5EgKi8NCiAgICBkYXRhU2NvcGVTZWxlY3RDaGFuZ2UodmFsdWUpIHsNCiAgICAgIGlmKHZhbHVlICE9PSAnMicpIHsNCiAgICAgICAgdGhpcy4kcmVmcy5kZXB0LnNldENoZWNrZWRLZXlzKFtdKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDliIbphY3mlbDmja7mnYPpmZDmk43kvZwgKi8NCiAgICBoYW5kbGVEYXRhU2NvcGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICBjb25zdCBkZXB0VHJlZVNlbGVjdCA9IHRoaXMuZ2V0RGVwdFRyZWUocm93LnJvbGVJZCk7DQogICAgICBnZXRSb2xlKHJvdy5yb2xlSWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB0aGlzLm9wZW5EYXRhU2NvcGUgPSB0cnVlOw0KICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgZGVwdFRyZWVTZWxlY3QudGhlbihyZXMgPT4gew0KICAgICAgICAgICAgdGhpcy4kcmVmcy5kZXB0LnNldENoZWNrZWRLZXlzKHJlcy5jaGVja2VkS2V5cyk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0pOw0KICAgICAgfSk7DQogICAgICB0aGlzLnRpdGxlID0gIuWIhumFjeaVsOaNruadg+mZkCI7DQogICAgfSwNCiAgICAvKiog5YiG6YWN55So5oi35pON5L2cICovDQogICAgaGFuZGxlQXV0aFVzZXI6IGZ1bmN0aW9uKHJvdykgew0KICAgICAgY29uc3Qgcm9sZUlkID0gcm93LnJvbGVJZDsNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCIvc3lzdGVtL3JvbGUtYXV0aC91c2VyLyIgKyByb2xlSWQpOw0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm06IGZ1bmN0aW9uKCkgew0KICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5yb2xlSWQgIT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICB0aGlzLmZvcm0ubWVudUlkcyA9IHRoaXMuZ2V0TWVudUFsbENoZWNrZWRLZXlzKCk7DQogICAgICAgICAgICB1cGRhdGVSb2xlKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy5mb3JtLm1lbnVJZHMgPSB0aGlzLmdldE1lbnVBbGxDaGVja2VkS2V5cygpOw0KICAgICAgICAgICAgYWRkUm9sZSh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTmjInpkq7vvIjmlbDmja7mnYPpmZDvvIkgKi8NCiAgICBzdWJtaXREYXRhU2NvcGU6IGZ1bmN0aW9uKCkgew0KICAgICAgaWYgKHRoaXMuZm9ybS5yb2xlSWQgIT0gdW5kZWZpbmVkKSB7DQogICAgICAgIHRoaXMuZm9ybS5kZXB0SWRzID0gdGhpcy5nZXREZXB0QWxsQ2hlY2tlZEtleXMoKTsNCiAgICAgICAgZGF0YVNjb3BlKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgdGhpcy5vcGVuRGF0YVNjb3BlID0gZmFsc2U7DQogICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnN0IHJvbGVJZHMgPSByb3cucm9sZUlkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk6KeS6Imy57yW5Y+35Li6IicgKyByb2xlSWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgew0KICAgICAgICByZXR1cm4gZGVsUm9sZShyb2xlSWRzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgnc3lzdGVtL3JvbGUvZXhwb3J0Jywgew0KICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zDQogICAgICB9LCBgcm9sZV8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkNCiAgICB9DQogIH0NCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8PA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/role", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\r\n      <el-form-item label=\"角色名称\" prop=\"roleName\">\r\n        <el-input\r\n          v-model=\"queryParams.roleName\"\r\n          placeholder=\"请输入角色名称\"\r\n          clearable\r\n          style=\"width: 240px\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"权限字符\" prop=\"roleKey\">\r\n        <el-input\r\n          v-model=\"queryParams.roleKey\"\r\n          placeholder=\"请输入权限字符\"\r\n          clearable\r\n          style=\"width: 240px\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select\r\n          v-model=\"queryParams.status\"\r\n          placeholder=\"角色状态\"\r\n          clearable\r\n          style=\"width: 240px\"\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_normal_disable\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"创建时间\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        ></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['system:role:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['system:role:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['system:role:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['system:role:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"roleList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"角色编号\" prop=\"roleId\" width=\"120\" />\r\n      <el-table-column label=\"角色名称\" prop=\"roleName\" :show-overflow-tooltip=\"true\" width=\"150\" />\r\n      <el-table-column label=\"权限字符\" prop=\"roleKey\" :show-overflow-tooltip=\"true\" width=\"150\" />\r\n      <el-table-column label=\"显示顺序\" prop=\"roleSort\" width=\"100\" />\r\n      <el-table-column label=\"状态\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-switch\r\n            v-model=\"scope.row.status\"\r\n            active-value=\"0\"\r\n            inactive-value=\"1\"\r\n            @change=\"handleStatusChange(scope.row)\"\r\n          ></el-switch>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\" v-if=\"scope.row.roleId !== 1\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['system:role:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['system:role:remove']\"\r\n          >删除</el-button>\r\n          <el-dropdown size=\"mini\" @command=\"(command) => handleCommand(command, scope.row)\" v-hasPermi=\"['system:role:edit']\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-d-arrow-right\">更多</el-button>\r\n            <el-dropdown-menu slot=\"dropdown\">\r\n              <el-dropdown-item command=\"handleDataScope\" icon=\"el-icon-circle-check\"\r\n                v-hasPermi=\"['system:role:edit']\">数据权限</el-dropdown-item>\r\n              <el-dropdown-item command=\"handleAuthUser\" icon=\"el-icon-user\"\r\n                v-hasPermi=\"['system:role:edit']\">分配用户</el-dropdown-item>\r\n            </el-dropdown-menu>\r\n          </el-dropdown>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改角色配置对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-form-item label=\"角色名称\" prop=\"roleName\">\r\n          <el-input v-model=\"form.roleName\" placeholder=\"请输入角色名称\" />\r\n        </el-form-item>\r\n        <el-form-item prop=\"roleKey\">\r\n          <span slot=\"label\">\r\n            <el-tooltip content=\"控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasRole('admin')`)\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n            权限字符\r\n          </span>\r\n          <el-input v-model=\"form.roleKey\" placeholder=\"请输入权限字符\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"角色顺序\" prop=\"roleSort\">\r\n          <el-input-number v-model=\"form.roleSort\" controls-position=\"right\" :min=\"0\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\">\r\n          <el-radio-group v-model=\"form.status\">\r\n            <el-radio\r\n              v-for=\"dict in dict.type.sys_normal_disable\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.value\"\r\n            >{{dict.label}}</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"菜单权限\">\r\n          <el-checkbox v-model=\"menuExpand\" @change=\"handleCheckedTreeExpand($event, 'menu')\">展开/折叠</el-checkbox>\r\n          <el-checkbox v-model=\"menuNodeAll\" @change=\"handleCheckedTreeNodeAll($event, 'menu')\">全选/全不选</el-checkbox>\r\n          <el-checkbox v-model=\"form.menuCheckStrictly\" @change=\"handleCheckedTreeConnect($event, 'menu')\">父子联动</el-checkbox>\r\n          <el-tree\r\n            class=\"tree-border\"\r\n            :data=\"menuOptions\"\r\n            show-checkbox\r\n            ref=\"menu\"\r\n            node-key=\"id\"\r\n            :check-strictly=\"!form.menuCheckStrictly\"\r\n            empty-text=\"加载中，请稍候\"\r\n            :props=\"defaultProps\"\r\n          ></el-tree>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 分配角色数据权限对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"openDataScope\" width=\"500px\" append-to-body>\r\n      <el-form :model=\"form\" label-width=\"80px\">\r\n        <el-form-item label=\"角色名称\">\r\n          <el-input v-model=\"form.roleName\" :disabled=\"true\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"权限字符\">\r\n          <el-input v-model=\"form.roleKey\" :disabled=\"true\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"权限范围\">\r\n          <el-select v-model=\"form.dataScope\" @change=\"dataScopeSelectChange\">\r\n            <el-option\r\n              v-for=\"item in dataScopeOptions\"\r\n              :key=\"item.value\"\r\n              :label=\"item.label\"\r\n              :value=\"item.value\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"数据权限\" v-show=\"form.dataScope == 2\">\r\n          <el-checkbox v-model=\"deptExpand\" @change=\"handleCheckedTreeExpand($event, 'dept')\">展开/折叠</el-checkbox>\r\n          <el-checkbox v-model=\"deptNodeAll\" @change=\"handleCheckedTreeNodeAll($event, 'dept')\">全选/全不选</el-checkbox>\r\n          <el-checkbox v-model=\"form.deptCheckStrictly\" @change=\"handleCheckedTreeConnect($event, 'dept')\">父子联动</el-checkbox>\r\n          <el-tree\r\n            class=\"tree-border\"\r\n            :data=\"deptOptions\"\r\n            show-checkbox\r\n            default-expand-all\r\n            ref=\"dept\"\r\n            node-key=\"id\"\r\n            :check-strictly=\"!form.deptCheckStrictly\"\r\n            empty-text=\"加载中，请稍候\"\r\n            :props=\"defaultProps\"\r\n          ></el-tree>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitDataScope\">确 定</el-button>\r\n        <el-button @click=\"cancelDataScope\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listRole, getRole, delRole, addRole, updateRole, dataScope, changeRoleStatus, deptTreeSelect } from \"@/api/system/role\";\r\nimport { treeselect as menuTreeselect, roleMenuTreeselect } from \"@/api/system/menu\";\r\n\r\nexport default {\r\n  name: \"Role\",\r\n  dicts: ['sys_normal_disable'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 角色表格数据\r\n      roleList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否显示弹出层（数据权限）\r\n      openDataScope: false,\r\n      menuExpand: false,\r\n      menuNodeAll: false,\r\n      deptExpand: true,\r\n      deptNodeAll: false,\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 数据范围选项\r\n      dataScopeOptions: [\r\n        {\r\n          value: \"1\",\r\n          label: \"全部数据权限\"\r\n        },\r\n        {\r\n          value: \"2\",\r\n          label: \"自定数据权限\"\r\n        },\r\n        {\r\n          value: \"3\",\r\n          label: \"本部门数据权限\"\r\n        },\r\n        {\r\n          value: \"4\",\r\n          label: \"本部门及以下数据权限\"\r\n        },\r\n        {\r\n          value: \"5\",\r\n          label: \"仅本人数据权限\"\r\n        }\r\n      ],\r\n      // 菜单列表\r\n      menuOptions: [],\r\n      // 部门列表\r\n      deptOptions: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        roleName: undefined,\r\n        roleKey: undefined,\r\n        status: undefined\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      defaultProps: {\r\n        children: \"children\",\r\n        label: \"label\"\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        roleName: [\r\n          { required: true, message: \"角色名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        roleKey: [\r\n          { required: true, message: \"权限字符不能为空\", trigger: \"blur\" }\r\n        ],\r\n        roleSort: [\r\n          { required: true, message: \"角色顺序不能为空\", trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询角色列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listRole(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\r\n          this.roleList = response.rows;\r\n          this.total = response.total;\r\n          this.loading = false;\r\n        }\r\n      );\r\n    },\r\n    /** 查询菜单树结构 */\r\n    getMenuTreeselect() {\r\n      menuTreeselect().then(response => {\r\n        this.menuOptions = response.data;\r\n      });\r\n    },\r\n    // 所有菜单节点数据\r\n    getMenuAllCheckedKeys() {\r\n      // 目前被选中的菜单节点\r\n      let checkedKeys = this.$refs.menu.getCheckedKeys();\r\n      // 半选中的菜单节点\r\n      let halfCheckedKeys = this.$refs.menu.getHalfCheckedKeys();\r\n      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);\r\n      return checkedKeys;\r\n    },\r\n    // 所有部门节点数据\r\n    getDeptAllCheckedKeys() {\r\n      // 目前被选中的部门节点\r\n      let checkedKeys = this.$refs.dept.getCheckedKeys();\r\n      // 半选中的部门节点\r\n      let halfCheckedKeys = this.$refs.dept.getHalfCheckedKeys();\r\n      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);\r\n      return checkedKeys;\r\n    },\r\n    /** 根据角色ID查询菜单树结构 */\r\n    getRoleMenuTreeselect(roleId) {\r\n      return roleMenuTreeselect(roleId).then(response => {\r\n        this.menuOptions = response.menus;\r\n        return response;\r\n      });\r\n    },\r\n    /** 根据角色ID查询部门树结构 */\r\n    getDeptTree(roleId) {\r\n      return deptTreeSelect(roleId).then(response => {\r\n        this.deptOptions = response.depts;\r\n        return response;\r\n      });\r\n    },\r\n    // 角色状态修改\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\r\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.roleName + '\"角色吗？').then(function() {\r\n        return changeRoleStatus(row.roleId, row.status);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function() {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 取消按钮（数据权限）\r\n    cancelDataScope() {\r\n      this.openDataScope = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      if (this.$refs.menu != undefined) {\r\n        this.$refs.menu.setCheckedKeys([]);\r\n      }\r\n      this.menuExpand = false,\r\n      this.menuNodeAll = false,\r\n      this.deptExpand = true,\r\n      this.deptNodeAll = false,\r\n      this.form = {\r\n        roleId: undefined,\r\n        roleName: undefined,\r\n        roleKey: undefined,\r\n        roleSort: 0,\r\n        status: \"0\",\r\n        menuIds: [],\r\n        deptIds: [],\r\n        menuCheckStrictly: true,\r\n        deptCheckStrictly: true,\r\n        remark: undefined\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.roleId)\r\n      this.single = selection.length!=1\r\n      this.multiple = !selection.length\r\n    },\r\n    // 更多操作触发\r\n    handleCommand(command, row) {\r\n      switch (command) {\r\n        case \"handleDataScope\":\r\n          this.handleDataScope(row);\r\n          break;\r\n        case \"handleAuthUser\":\r\n          this.handleAuthUser(row);\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n    // 树权限（展开/折叠）\r\n    handleCheckedTreeExpand(value, type) {\r\n      if (type == 'menu') {\r\n        let treeList = this.menuOptions;\r\n        for (let i = 0; i < treeList.length; i++) {\r\n          this.$refs.menu.store.nodesMap[treeList[i].id].expanded = value;\r\n        }\r\n      } else if (type == 'dept') {\r\n        let treeList = this.deptOptions;\r\n        for (let i = 0; i < treeList.length; i++) {\r\n          this.$refs.dept.store.nodesMap[treeList[i].id].expanded = value;\r\n        }\r\n      }\r\n    },\r\n    // 树权限（全选/全不选）\r\n    handleCheckedTreeNodeAll(value, type) {\r\n      if (type == 'menu') {\r\n        this.$refs.menu.setCheckedNodes(value ? this.menuOptions: []);\r\n      } else if (type == 'dept') {\r\n        this.$refs.dept.setCheckedNodes(value ? this.deptOptions: []);\r\n      }\r\n    },\r\n    // 树权限（父子联动）\r\n    handleCheckedTreeConnect(value, type) {\r\n      if (type == 'menu') {\r\n        this.form.menuCheckStrictly = value ? true: false;\r\n      } else if (type == 'dept') {\r\n        this.form.deptCheckStrictly = value ? true: false;\r\n      }\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.getMenuTreeselect();\r\n      this.open = true;\r\n      this.title = \"添加角色\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const roleId = row.roleId || this.ids\r\n      const roleMenu = this.getRoleMenuTreeselect(roleId);\r\n      getRole(roleId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.$nextTick(() => {\r\n          roleMenu.then(res => {\r\n            let checkedKeys = res.checkedKeys\r\n            checkedKeys.forEach((v) => {\r\n                this.$nextTick(()=>{\r\n                    this.$refs.menu.setChecked(v, true ,false);\r\n                })\r\n            })\r\n          });\r\n        });\r\n      });\r\n      this.title = \"修改角色\";\r\n    },\r\n    /** 选择角色权限范围触发 */\r\n    dataScopeSelectChange(value) {\r\n      if(value !== '2') {\r\n        this.$refs.dept.setCheckedKeys([]);\r\n      }\r\n    },\r\n    /** 分配数据权限操作 */\r\n    handleDataScope(row) {\r\n      this.reset();\r\n      const deptTreeSelect = this.getDeptTree(row.roleId);\r\n      getRole(row.roleId).then(response => {\r\n        this.form = response.data;\r\n        this.openDataScope = true;\r\n        this.$nextTick(() => {\r\n          deptTreeSelect.then(res => {\r\n            this.$refs.dept.setCheckedKeys(res.checkedKeys);\r\n          });\r\n        });\r\n      });\r\n      this.title = \"分配数据权限\";\r\n    },\r\n    /** 分配用户操作 */\r\n    handleAuthUser: function(row) {\r\n      const roleId = row.roleId;\r\n      this.$router.push(\"/system/role-auth/user/\" + roleId);\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.roleId != undefined) {\r\n            this.form.menuIds = this.getMenuAllCheckedKeys();\r\n            updateRole(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            this.form.menuIds = this.getMenuAllCheckedKeys();\r\n            addRole(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 提交按钮（数据权限） */\r\n    submitDataScope: function() {\r\n      if (this.form.roleId != undefined) {\r\n        this.form.deptIds = this.getDeptAllCheckedKeys();\r\n        dataScope(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\");\r\n          this.openDataScope = false;\r\n          this.getList();\r\n        });\r\n      }\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const roleIds = row.roleId || this.ids;\r\n      this.$modal.confirm('是否确认删除角色编号为\"' + roleIds + '\"的数据项？').then(function() {\r\n        return delRole(roleIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/role/export', {\r\n        ...this.queryParams\r\n      }, `role_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script>"]}]}