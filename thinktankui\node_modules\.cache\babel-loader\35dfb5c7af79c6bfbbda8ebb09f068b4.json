{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\api\\system\\dict\\type.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\api\\system\\dict\\type.js", "mtime": 1749104047591}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749104418875}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listType", "query", "request", "url", "method", "params", "getType", "dictId", "addType", "data", "updateType", "delType", "refreshCache", "optionselect"], "sources": ["D:/thinktank/thinktankui/src/api/system/dict/type.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询字典类型列表\r\nexport function listType(query) {\r\n  return request({\r\n    url: '/system/dict/type/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询字典类型详细\r\nexport function getType(dictId) {\r\n  return request({\r\n    url: '/system/dict/type/' + dictId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增字典类型\r\nexport function addType(data) {\r\n  return request({\r\n    url: '/system/dict/type',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改字典类型\r\nexport function updateType(data) {\r\n  return request({\r\n    url: '/system/dict/type',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除字典类型\r\nexport function delType(dictId) {\r\n  return request({\r\n    url: '/system/dict/type/' + dictId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 刷新字典缓存\r\nexport function refreshCache() {\r\n  return request({\r\n    url: '/system/dict/type/refreshCache',\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 获取字典选择框列表\r\nexport function optionselect() {\r\n  return request({\r\n    url: '/system/dict/type/optionselect',\r\n    method: 'get'\r\n  })\r\n}"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,MAAM;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,OAAOA,CAACJ,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,MAAM;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,YAAYA,CAAA,EAAG;EAC7B,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,YAAYA,CAAA,EAAG;EAC7B,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}