{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\system\\menu\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\system\\menu\\index.vue", "mtime": 1749104047647}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_menu", "require", "_vueTreeselect", "_interopRequireDefault", "_IconSelect", "name", "dicts", "components", "Treeselect", "IconSelect", "data", "loading", "showSearch", "menuList", "menuOptions", "title", "open", "isExpandAll", "refreshTable", "queryParams", "menuName", "undefined", "visible", "form", "rules", "required", "message", "trigger", "orderNum", "path", "created", "getList", "methods", "selected", "icon", "_this", "listMenu", "then", "response", "handleTree", "normalizer", "node", "children", "length", "id", "menuId", "label", "getTreeselect", "_this2", "menu", "push", "cancel", "reset", "parentId", "routeName", "menuType", "isFrame", "isCache", "status", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleAdd", "row", "toggleExpandAll", "_this3", "$nextTick", "handleUpdate", "_this4", "getMenu", "submitForm", "_this5", "$refs", "validate", "valid", "updateMenu", "$modal", "msgSuccess", "addMenu", "handleDelete", "_this6", "confirm", "delMenu", "catch"], "sources": ["src/views/system/menu/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\r\n      <el-form-item label=\"菜单名称\" prop=\"menuName\">\r\n        <el-input\r\n          v-model=\"queryParams.menuName\"\r\n          placeholder=\"请输入菜单名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" placeholder=\"菜单状态\" clearable>\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_normal_disable\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['system:menu:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"info\"\r\n          plain\r\n          icon=\"el-icon-sort\"\r\n          size=\"mini\"\r\n          @click=\"toggleExpandAll\"\r\n        >展开/折叠</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-if=\"refreshTable\"\r\n      v-loading=\"loading\"\r\n      :data=\"menuList\"\r\n      row-key=\"menuId\"\r\n      :default-expand-all=\"isExpandAll\"\r\n      :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\r\n    >\r\n      <el-table-column prop=\"menuName\" label=\"菜单名称\" :show-overflow-tooltip=\"true\" width=\"160\"></el-table-column>\r\n      <el-table-column prop=\"icon\" label=\"图标\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <svg-icon :icon-class=\"scope.row.icon\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"orderNum\" label=\"排序\" width=\"60\"></el-table-column>\r\n      <el-table-column prop=\"perms\" label=\"权限标识\" :show-overflow-tooltip=\"true\"></el-table-column>\r\n      <el-table-column prop=\"component\" label=\"组件路径\" :show-overflow-tooltip=\"true\"></el-table-column>\r\n      <el-table-column prop=\"status\" label=\"状态\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['system:menu:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"handleAdd(scope.row)\"\r\n            v-hasPermi=\"['system:menu:add']\"\r\n          >新增</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['system:menu:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 添加或修改菜单对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"680px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"上级菜单\" prop=\"parentId\">\r\n              <treeselect\r\n                v-model=\"form.parentId\"\r\n                :options=\"menuOptions\"\r\n                :normalizer=\"normalizer\"\r\n                :show-count=\"true\"\r\n                placeholder=\"选择上级菜单\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"菜单类型\" prop=\"menuType\">\r\n              <el-radio-group v-model=\"form.menuType\">\r\n                <el-radio label=\"M\">目录</el-radio>\r\n                <el-radio label=\"C\">菜单</el-radio>\r\n                <el-radio label=\"F\">按钮</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\" v-if=\"form.menuType != 'F'\">\r\n            <el-form-item label=\"菜单图标\" prop=\"icon\">\r\n              <el-popover\r\n                placement=\"bottom-start\"\r\n                width=\"460\"\r\n                trigger=\"click\"\r\n                @show=\"$refs['iconSelect'].reset()\"\r\n              >\r\n                <IconSelect ref=\"iconSelect\" @selected=\"selected\" :active-icon=\"form.icon\" />\r\n                <el-input slot=\"reference\" v-model=\"form.icon\" placeholder=\"点击选择图标\" readonly>\r\n                  <svg-icon\r\n                    v-if=\"form.icon\"\r\n                    slot=\"prefix\"\r\n                    :icon-class=\"form.icon\"\r\n                    style=\"width: 25px;\"\r\n                  />\r\n                  <i v-else slot=\"prefix\" class=\"el-icon-search el-input__icon\" />\r\n                </el-input>\r\n              </el-popover>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"菜单名称\" prop=\"menuName\">\r\n              <el-input v-model=\"form.menuName\" placeholder=\"请输入菜单名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"显示排序\" prop=\"orderNum\">\r\n              <el-input-number v-model=\"form.orderNum\" controls-position=\"right\" :min=\"0\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\" v-if=\"form.menuType != 'F'\">\r\n            <el-form-item prop=\"isFrame\">\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"选择是外链则路由地址需要以`http(s)://`开头\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                是否外链\r\n              </span>\r\n              <el-radio-group v-model=\"form.isFrame\">\r\n                <el-radio :label=\"0\">是</el-radio>\r\n                <el-radio :label=\"1\">否</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\" v-if=\"form.menuType != 'F'\">\r\n            <el-form-item prop=\"path\">\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                路由地址\r\n              </span>\r\n              <el-input v-model=\"form.path\" placeholder=\"请输入路由地址\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\" v-if=\"form.menuType == 'C'\">\r\n            <el-form-item prop=\"routeName\">\r\n                <span slot=\"label\">\r\n                    <el-tooltip content=\"默认不填则和路由地址相同：如地址为：`user`，则名称为`User`（注意：因为router会删除名称相同路由，为避免名字的冲突，特殊情况下请自定义，保证唯一性）\" placement=\"top\">\r\n                      <el-icon><question-filled /></el-icon>\r\n                    </el-tooltip>\r\n                    路由名称\r\n                </span>\r\n                <el-input v-model=\"form.routeName\" placeholder=\"请输入路由名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\" v-if=\"form.menuType == 'C'\">\r\n            <el-form-item prop=\"component\">\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"访问的组件路径，如：`system/user/index`，默认在`views`目录下\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                组件路径\r\n              </span>\r\n              <el-input v-model=\"form.component\" placeholder=\"请输入组件路径\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\" v-if=\"form.menuType != 'M'\">\r\n            <el-form-item prop=\"perms\">\r\n              <el-input v-model=\"form.perms\" placeholder=\"请输入权限标识\" maxlength=\"100\" />\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasPermi('system:user:list')`)\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                权限字符\r\n              </span>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\" v-if=\"form.menuType == 'C'\">\r\n            <el-form-item prop=\"query\">\r\n              <el-input v-model=\"form.query\" placeholder=\"请输入路由参数\" maxlength=\"255\" />\r\n              <span slot=\"label\">\r\n                <el-tooltip content='访问路由的默认传递参数，如：`{\"id\": 1, \"name\": \"ry\"}`' placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                路由参数\r\n              </span>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\" v-if=\"form.menuType == 'C'\">\r\n            <el-form-item prop=\"isCache\">\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                是否缓存\r\n              </span>\r\n              <el-radio-group v-model=\"form.isCache\">\r\n                <el-radio :label=\"0\">缓存</el-radio>\r\n                <el-radio :label=\"1\">不缓存</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\" v-if=\"form.menuType != 'F'\">\r\n            <el-form-item prop=\"visible\">\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"选择隐藏则路由将不会出现在侧边栏，但仍然可以访问\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                显示状态\r\n              </span>\r\n              <el-radio-group v-model=\"form.visible\">\r\n                <el-radio\r\n                  v-for=\"dict in dict.type.sys_show_hide\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.value\"\r\n                >{{dict.label}}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item prop=\"status\">\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"选择停用则路由将不会出现在侧边栏，也不能被访问\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                菜单状态\r\n              </span>\r\n              <el-radio-group v-model=\"form.status\">\r\n                <el-radio\r\n                  v-for=\"dict in dict.type.sys_normal_disable\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.value\"\r\n                >{{dict.label}}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listMenu, getMenu, delMenu, addMenu, updateMenu } from \"@/api/system/menu\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\nimport IconSelect from \"@/components/IconSelect\";\r\n\r\nexport default {\r\n  name: \"Menu\",\r\n  dicts: ['sys_show_hide', 'sys_normal_disable'],\r\n  components: { Treeselect, IconSelect },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 菜单表格树数据\r\n      menuList: [],\r\n      // 菜单树选项\r\n      menuOptions: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否展开，默认全部折叠\r\n      isExpandAll: false,\r\n      // 重新渲染表格状态\r\n      refreshTable: true,\r\n      // 查询参数\r\n      queryParams: {\r\n        menuName: undefined,\r\n        visible: undefined\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        menuName: [\r\n          { required: true, message: \"菜单名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        orderNum: [\r\n          { required: true, message: \"菜单顺序不能为空\", trigger: \"blur\" }\r\n        ],\r\n        path: [\r\n          { required: true, message: \"路由地址不能为空\", trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    // 选择图标\r\n    selected(name) {\r\n      this.form.icon = name;\r\n    },\r\n    /** 查询菜单列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listMenu(this.queryParams).then(response => {\r\n        this.menuList = this.handleTree(response.data, \"menuId\");\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 转换菜单数据结构 */\r\n    normalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children;\r\n      }\r\n      return {\r\n        id: node.menuId,\r\n        label: node.menuName,\r\n        children: node.children\r\n      };\r\n    },\r\n    /** 查询菜单下拉树结构 */\r\n    getTreeselect() {\r\n      listMenu().then(response => {\r\n        this.menuOptions = [];\r\n        const menu = { menuId: 0, menuName: '主类目', children: [] };\r\n        menu.children = this.handleTree(response.data, \"menuId\");\r\n        this.menuOptions.push(menu);\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        menuId: undefined,\r\n        parentId: 0,\r\n        menuName: undefined,\r\n        routeName: undefined,\r\n        icon: undefined,\r\n        menuType: \"M\",\r\n        orderNum: undefined,\r\n        isFrame: 1,\r\n        isCache: 0,\r\n        visible: \"0\",\r\n        status: \"0\"\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd(row) {\r\n      this.reset();\r\n      this.getTreeselect();\r\n      if (row != null && row.menuId) {\r\n        this.form.parentId = row.menuId;\r\n      } else {\r\n        this.form.parentId = 0;\r\n      }\r\n      this.open = true;\r\n      this.title = \"添加菜单\";\r\n    },\r\n    /** 展开/折叠操作 */\r\n    toggleExpandAll() {\r\n      this.refreshTable = false;\r\n      this.isExpandAll = !this.isExpandAll;\r\n      this.$nextTick(() => {\r\n        this.refreshTable = true;\r\n      });\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      this.getTreeselect();\r\n      getMenu(row.menuId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改菜单\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.menuId != undefined) {\r\n            updateMenu(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addMenu(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      this.$modal.confirm('是否确认删除名称为\"' + row.menuName + '\"的数据项？').then(function() {\r\n        return delMenu(row.menuId);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;AA+SA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAC,sBAAA,CAAAF,OAAA;AACAA,OAAA;AACA,IAAAG,WAAA,GAAAD,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAI,IAAA;EACAC,KAAA;EACAC,UAAA;IAAAC,UAAA,EAAAA,sBAAA;IAAAC,UAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACA;MACAC,QAAA;MACA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;MACA;MACAC,YAAA;MACA;MACAC,WAAA;QACAC,QAAA,EAAAC,SAAA;QACAC,OAAA,EAAAD;MACA;MACA;MACAE,IAAA;MACA;MACAC,KAAA;QACAJ,QAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,QAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,IAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA;IACAC,QAAA,WAAAA,SAAA5B,IAAA;MACA,KAAAkB,IAAA,CAAAW,IAAA,GAAA7B,IAAA;IACA;IACA,aACA0B,OAAA,WAAAA,QAAA;MAAA,IAAAI,KAAA;MACA,KAAAxB,OAAA;MACA,IAAAyB,cAAA,OAAAjB,WAAA,EAAAkB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAtB,QAAA,GAAAsB,KAAA,CAAAI,UAAA,CAAAD,QAAA,CAAA5B,IAAA;QACAyB,KAAA,CAAAxB,OAAA;MACA;IACA;IACA,eACA6B,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAA,IAAA,CAAAC,QAAA,KAAAD,IAAA,CAAAC,QAAA,CAAAC,MAAA;QACA,OAAAF,IAAA,CAAAC,QAAA;MACA;MACA;QACAE,EAAA,EAAAH,IAAA,CAAAI,MAAA;QACAC,KAAA,EAAAL,IAAA,CAAArB,QAAA;QACAsB,QAAA,EAAAD,IAAA,CAAAC;MACA;IACA;IACA,gBACAK,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,IAAAZ,cAAA,IAAAC,IAAA,WAAAC,QAAA;QACAU,MAAA,CAAAlC,WAAA;QACA,IAAAmC,IAAA;UAAAJ,MAAA;UAAAzB,QAAA;UAAAsB,QAAA;QAAA;QACAO,IAAA,CAAAP,QAAA,GAAAM,MAAA,CAAAT,UAAA,CAAAD,QAAA,CAAA5B,IAAA;QACAsC,MAAA,CAAAlC,WAAA,CAAAoC,IAAA,CAAAD,IAAA;MACA;IACA;IACA;IACAE,MAAA,WAAAA,OAAA;MACA,KAAAnC,IAAA;MACA,KAAAoC,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA7B,IAAA;QACAsB,MAAA,EAAAxB,SAAA;QACAgC,QAAA;QACAjC,QAAA,EAAAC,SAAA;QACAiC,SAAA,EAAAjC,SAAA;QACAa,IAAA,EAAAb,SAAA;QACAkC,QAAA;QACA3B,QAAA,EAAAP,SAAA;QACAmC,OAAA;QACAC,OAAA;QACAnC,OAAA;QACAoC,MAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA7B,OAAA;IACA;IACA,aACA8B,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA,aACAE,SAAA,WAAAA,UAAAC,GAAA;MACA,KAAAX,KAAA;MACA,KAAAL,aAAA;MACA,IAAAgB,GAAA,YAAAA,GAAA,CAAAlB,MAAA;QACA,KAAAtB,IAAA,CAAA8B,QAAA,GAAAU,GAAA,CAAAlB,MAAA;MACA;QACA,KAAAtB,IAAA,CAAA8B,QAAA;MACA;MACA,KAAArC,IAAA;MACA,KAAAD,KAAA;IACA;IACA,cACAiD,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAA/C,YAAA;MACA,KAAAD,WAAA,SAAAA,WAAA;MACA,KAAAiD,SAAA;QACAD,MAAA,CAAA/C,YAAA;MACA;IACA;IACA,aACAiD,YAAA,WAAAA,aAAAJ,GAAA;MAAA,IAAAK,MAAA;MACA,KAAAhB,KAAA;MACA,KAAAL,aAAA;MACA,IAAAsB,aAAA,EAAAN,GAAA,CAAAlB,MAAA,EAAAR,IAAA,WAAAC,QAAA;QACA8B,MAAA,CAAA7C,IAAA,GAAAe,QAAA,CAAA5B,IAAA;QACA0D,MAAA,CAAApD,IAAA;QACAoD,MAAA,CAAArD,KAAA;MACA;IACA;IACA;IACAuD,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAhD,IAAA,CAAAsB,MAAA,IAAAxB,SAAA;YACA,IAAAsD,gBAAA,EAAAJ,MAAA,CAAAhD,IAAA,EAAAc,IAAA,WAAAC,QAAA;cACAiC,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAvD,IAAA;cACAuD,MAAA,CAAAxC,OAAA;YACA;UACA;YACA,IAAA+C,aAAA,EAAAP,MAAA,CAAAhD,IAAA,EAAAc,IAAA,WAAAC,QAAA;cACAiC,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAvD,IAAA;cACAuD,MAAA,CAAAxC,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAgD,YAAA,WAAAA,aAAAhB,GAAA;MAAA,IAAAiB,MAAA;MACA,KAAAJ,MAAA,CAAAK,OAAA,gBAAAlB,GAAA,CAAA3C,QAAA,aAAAiB,IAAA;QACA,WAAA6C,aAAA,EAAAnB,GAAA,CAAAlB,MAAA;MACA,GAAAR,IAAA;QACA2C,MAAA,CAAAjD,OAAA;QACAiD,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAM,KAAA;IACA;EACA;AACA", "ignoreList": []}]}