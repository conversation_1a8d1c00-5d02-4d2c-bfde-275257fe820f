{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\modules\\uploader.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\modules\\uploader.js", "mtime": 1749104422837}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5mcm9tLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5pbmNsdWRlcy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5wdXNoLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5yZWR1Y2UuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pbmNsdWRlcy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLml0ZXJhdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5mb3ItZWFjaC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLm1hcC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLnJlZHVjZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5mb3ItZWFjaC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5pdGVyYXRvci5qcyIpOwp2YXIgX2NsYXNzQ2FsbENoZWNrMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovdGhpbmt0YW5rL3RoaW5rdGFua3VpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2NsYXNzQ2FsbENoZWNrLmpzIikpOwp2YXIgX2NyZWF0ZUNsYXNzMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovdGhpbmt0YW5rL3RoaW5rdGFua3VpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2NyZWF0ZUNsYXNzLmpzIikpOwp2YXIgX2NhbGxTdXBlcjIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkQ6L3RoaW5rdGFuay90aGlua3Rhbmt1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9jYWxsU3VwZXIuanMiKSk7CnZhciBfaW5oZXJpdHMyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW5oZXJpdHMuanMiKSk7CnZhciBfcXVpbGxEZWx0YSA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgicXVpbGwtZGVsdGEiKSk7CnZhciBfZW1pdHRlciA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi4vY29yZS9lbWl0dGVyLmpzIikpOwp2YXIgX21vZHVsZSA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi4vY29yZS9tb2R1bGUuanMiKSk7CnZhciBVcGxvYWRlciA9IC8qI19fUFVSRV9fKi9mdW5jdGlvbiAoX01vZHVsZSkgewogIGZ1bmN0aW9uIFVwbG9hZGVyKHF1aWxsLCBvcHRpb25zKSB7CiAgICB2YXIgX3RoaXM7CiAgICAoMCwgX2NsYXNzQ2FsbENoZWNrMi5kZWZhdWx0KSh0aGlzLCBVcGxvYWRlcik7CiAgICBfdGhpcyA9ICgwLCBfY2FsbFN1cGVyMi5kZWZhdWx0KSh0aGlzLCBVcGxvYWRlciwgW3F1aWxsLCBvcHRpb25zXSk7CiAgICBxdWlsbC5yb290LmFkZEV2ZW50TGlzdGVuZXIoJ2Ryb3AnLCBmdW5jdGlvbiAoZSkgewogICAgICBlLnByZXZlbnREZWZhdWx0KCk7CiAgICAgIHZhciBuYXRpdmUgPSBudWxsOwogICAgICBpZiAoZG9jdW1lbnQuY2FyZXRSYW5nZUZyb21Qb2ludCkgewogICAgICAgIG5hdGl2ZSA9IGRvY3VtZW50LmNhcmV0UmFuZ2VGcm9tUG9pbnQoZS5jbGllbnRYLCBlLmNsaWVudFkpOwogICAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3IKICAgICAgfSBlbHNlIGlmIChkb2N1bWVudC5jYXJldFBvc2l0aW9uRnJvbVBvaW50KSB7CiAgICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvcgogICAgICAgIHZhciBwb3NpdGlvbiA9IGRvY3VtZW50LmNhcmV0UG9zaXRpb25Gcm9tUG9pbnQoZS5jbGllbnRYLCBlLmNsaWVudFkpOwogICAgICAgIG5hdGl2ZSA9IGRvY3VtZW50LmNyZWF0ZVJhbmdlKCk7CiAgICAgICAgbmF0aXZlLnNldFN0YXJ0KHBvc2l0aW9uLm9mZnNldE5vZGUsIHBvc2l0aW9uLm9mZnNldCk7CiAgICAgICAgbmF0aXZlLnNldEVuZChwb3NpdGlvbi5vZmZzZXROb2RlLCBwb3NpdGlvbi5vZmZzZXQpOwogICAgICB9CiAgICAgIHZhciBub3JtYWxpemVkID0gbmF0aXZlICYmIHF1aWxsLnNlbGVjdGlvbi5ub3JtYWxpemVOYXRpdmUobmF0aXZlKTsKICAgICAgaWYgKG5vcm1hbGl6ZWQpIHsKICAgICAgICB2YXIgX2UkZGF0YVRyYW5zZmVyOwogICAgICAgIHZhciByYW5nZSA9IHF1aWxsLnNlbGVjdGlvbi5ub3JtYWxpemVkVG9SYW5nZShub3JtYWxpemVkKTsKICAgICAgICBpZiAoKF9lJGRhdGFUcmFuc2ZlciA9IGUuZGF0YVRyYW5zZmVyKSAhPT0gbnVsbCAmJiBfZSRkYXRhVHJhbnNmZXIgIT09IHZvaWQgMCAmJiBfZSRkYXRhVHJhbnNmZXIuZmlsZXMpIHsKICAgICAgICAgIF90aGlzLnVwbG9hZChyYW5nZSwgZS5kYXRhVHJhbnNmZXIuZmlsZXMpOwogICAgICAgIH0KICAgICAgfQogICAgfSk7CiAgICByZXR1cm4gX3RoaXM7CiAgfQogICgwLCBfaW5oZXJpdHMyLmRlZmF1bHQpKFVwbG9hZGVyLCBfTW9kdWxlKTsKICByZXR1cm4gKDAsIF9jcmVhdGVDbGFzczIuZGVmYXVsdCkoVXBsb2FkZXIsIFt7CiAgICBrZXk6ICJ1cGxvYWQiLAogICAgdmFsdWU6IGZ1bmN0aW9uIHVwbG9hZChyYW5nZSwgZmlsZXMpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHZhciB1cGxvYWRzID0gW107CiAgICAgIEFycmF5LmZyb20oZmlsZXMpLmZvckVhY2goZnVuY3Rpb24gKGZpbGUpIHsKICAgICAgICB2YXIgX3RoaXMyJG9wdGlvbnMkbWltZXR5OwogICAgICAgIGlmIChmaWxlICYmIChfdGhpczIkb3B0aW9ucyRtaW1ldHkgPSBfdGhpczIub3B0aW9ucy5taW1ldHlwZXMpICE9PSBudWxsICYmIF90aGlzMiRvcHRpb25zJG1pbWV0eSAhPT0gdm9pZCAwICYmIF90aGlzMiRvcHRpb25zJG1pbWV0eS5pbmNsdWRlcyhmaWxlLnR5cGUpKSB7CiAgICAgICAgICB1cGxvYWRzLnB1c2goZmlsZSk7CiAgICAgICAgfQogICAgICB9KTsKICAgICAgaWYgKHVwbG9hZHMubGVuZ3RoID4gMCkgewogICAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgRml4IG1lIGxhdGVyCiAgICAgICAgdGhpcy5vcHRpb25zLmhhbmRsZXIuY2FsbCh0aGlzLCByYW5nZSwgdXBsb2Fkcyk7CiAgICAgIH0KICAgIH0KICB9XSk7Cn0oX21vZHVsZS5kZWZhdWx0KTsKVXBsb2FkZXIuREVGQVVMVFMgPSB7CiAgbWltZXR5cGVzOiBbJ2ltYWdlL3BuZycsICdpbWFnZS9qcGVnJ10sCiAgaGFuZGxlcjogZnVuY3Rpb24gaGFuZGxlcihyYW5nZSwgZmlsZXMpIHsKICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgaWYgKCF0aGlzLnF1aWxsLnNjcm9sbC5xdWVyeSgnaW1hZ2UnKSkgewogICAgICByZXR1cm47CiAgICB9CiAgICB2YXIgcHJvbWlzZXMgPSBmaWxlcy5tYXAoZnVuY3Rpb24gKGZpbGUpIHsKICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKGZ1bmN0aW9uIChyZXNvbHZlKSB7CiAgICAgICAgdmFyIHJlYWRlciA9IG5ldyBGaWxlUmVhZGVyKCk7CiAgICAgICAgcmVhZGVyLm9ubG9hZCA9IGZ1bmN0aW9uICgpIHsKICAgICAgICAgIHJlc29sdmUocmVhZGVyLnJlc3VsdCk7CiAgICAgICAgfTsKICAgICAgICByZWFkZXIucmVhZEFzRGF0YVVSTChmaWxlKTsKICAgICAgfSk7CiAgICB9KTsKICAgIFByb21pc2UuYWxsKHByb21pc2VzKS50aGVuKGZ1bmN0aW9uIChpbWFnZXMpIHsKICAgICAgdmFyIHVwZGF0ZSA9IGltYWdlcy5yZWR1Y2UoZnVuY3Rpb24gKGRlbHRhLCBpbWFnZSkgewogICAgICAgIHJldHVybiBkZWx0YS5pbnNlcnQoewogICAgICAgICAgaW1hZ2U6IGltYWdlCiAgICAgICAgfSk7CiAgICAgIH0sIG5ldyBfcXVpbGxEZWx0YS5kZWZhdWx0KCkucmV0YWluKHJhbmdlLmluZGV4KS5kZWxldGUocmFuZ2UubGVuZ3RoKSk7CiAgICAgIF90aGlzMy5xdWlsbC51cGRhdGVDb250ZW50cyh1cGRhdGUsIF9lbWl0dGVyLmRlZmF1bHQuc291cmNlcy5VU0VSKTsKICAgICAgX3RoaXMzLnF1aWxsLnNldFNlbGVjdGlvbihyYW5nZS5pbmRleCArIGltYWdlcy5sZW5ndGgsIF9lbWl0dGVyLmRlZmF1bHQuc291cmNlcy5TSUxFTlQpOwogICAgfSk7CiAgfQp9Owp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSBVcGxvYWRlcjs="}, {"version": 3, "names": ["_quill<PERSON><PERSON><PERSON>", "_interopRequireDefault", "require", "_emitter", "_module", "Uploader", "_Module", "quill", "options", "_this", "_classCallCheck2", "default", "_callSuper2", "root", "addEventListener", "e", "preventDefault", "native", "document", "caretRangeFromPoint", "clientX", "clientY", "caretPositionFromPoint", "position", "createRange", "setStart", "offsetNode", "offset", "setEnd", "normalized", "selection", "normalizeNative", "_e$dataTransfer", "range", "normalizedToRange", "dataTransfer", "files", "upload", "_inherits2", "_createClass2", "key", "value", "_this2", "uploads", "Array", "from", "for<PERSON>ach", "file", "_this2$options$mimety", "mimetypes", "includes", "type", "push", "length", "handler", "call", "<PERSON><PERSON><PERSON>", "DEFAULTS", "_this3", "scroll", "query", "promises", "map", "Promise", "resolve", "reader", "FileReader", "onload", "result", "readAsDataURL", "all", "then", "images", "update", "reduce", "delta", "image", "insert", "Delta", "retain", "index", "delete", "updateContents", "Emitter", "sources", "USER", "setSelection", "SILENT", "_default", "exports"], "sources": ["../../src/modules/uploader.ts"], "sourcesContent": ["import Delta from 'quill-delta';\nimport type Quill from '../core/quill.js';\nimport Emitter from '../core/emitter.js';\nimport Module from '../core/module.js';\nimport type { Range } from '../core/selection.js';\n\ninterface UploaderOptions {\n  mimetypes: string[];\n  handler: (this: { quill: Quill }, range: Range, files: File[]) => void;\n}\n\nclass Uploader extends Module<UploaderOptions> {\n  static DEFAULTS: UploaderOptions;\n\n  constructor(quill: Quill, options: Partial<UploaderOptions>) {\n    super(quill, options);\n    quill.root.addEventListener('drop', (e) => {\n      e.preventDefault();\n      let native: ReturnType<typeof document.createRange> | null = null;\n      if (document.caretRangeFromPoint) {\n        native = document.caretRangeFromPoint(e.clientX, e.clientY);\n        // @ts-expect-error\n      } else if (document.caretPositionFromPoint) {\n        // @ts-expect-error\n        const position = document.caretPositionFromPoint(e.clientX, e.clientY);\n        native = document.createRange();\n        native.setStart(position.offsetNode, position.offset);\n        native.setEnd(position.offsetNode, position.offset);\n      }\n\n      const normalized = native && quill.selection.normalizeNative(native);\n      if (normalized) {\n        const range = quill.selection.normalizedToRange(normalized);\n        if (e.dataTransfer?.files) {\n          this.upload(range, e.dataTransfer.files);\n        }\n      }\n    });\n  }\n\n  upload(range: Range, files: FileList | File[]) {\n    const uploads: File[] = [];\n    Array.from(files).forEach((file) => {\n      if (file && this.options.mimetypes?.includes(file.type)) {\n        uploads.push(file);\n      }\n    });\n    if (uploads.length > 0) {\n      // @ts-expect-error Fix me later\n      this.options.handler.call(this, range, uploads);\n    }\n  }\n}\n\nUploader.DEFAULTS = {\n  mimetypes: ['image/png', 'image/jpeg'],\n  handler(range: Range, files: File[]) {\n    if (!this.quill.scroll.query('image')) {\n      return;\n    }\n    const promises = files.map<Promise<string>>((file) => {\n      return new Promise((resolve) => {\n        const reader = new FileReader();\n        reader.onload = () => {\n          resolve(reader.result as string);\n        };\n        reader.readAsDataURL(file);\n      });\n    });\n    Promise.all(promises).then((images) => {\n      const update = images.reduce((delta: Delta, image) => {\n        return delta.insert({ image });\n      }, new Delta().retain(range.index).delete(range.length)) as Delta;\n      this.quill.updateContents(update, Emitter.sources.USER);\n      this.quill.setSelection(\n        range.index + images.length,\n        Emitter.sources.SILENT,\n      );\n    });\n  },\n};\n\nexport default Uploader;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,QAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,OAAA,GAAAH,sBAAA,CAAAC,OAAA;AAAsC,IAQhCG,QAAQ,0BAAAC,OAAA;EAGZ,SAAAD,SAAYE,KAAY,EAAEC,OAAiC,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAN,QAAA;IAC3DI,KAAA,OAAAG,WAAA,CAAAD,OAAA,QAAAN,QAAA,GAAME,KAAK,EAAEC,OAAO;IACpBD,KAAK,CAACM,IAAI,CAACC,gBAAgB,CAAC,MAAM,EAAG,UAAAC,CAAC,EAAK;MACzCA,CAAC,CAACC,cAAc,CAAC,CAAC;MAClB,IAAIC,MAAsD,GAAG,IAAI;MACjE,IAAIC,QAAQ,CAACC,mBAAmB,EAAE;QAChCF,MAAM,GAAGC,QAAQ,CAACC,mBAAmB,CAACJ,CAAC,CAACK,OAAO,EAAEL,CAAC,CAACM,OAAO,CAAC;QAC3D;MACF,CAAC,MAAM,IAAIH,QAAQ,CAACI,sBAAsB,EAAE;QAC1C;QACA,IAAMC,QAAQ,GAAGL,QAAQ,CAACI,sBAAsB,CAACP,CAAC,CAACK,OAAO,EAAEL,CAAC,CAACM,OAAO,CAAC;QACtEJ,MAAM,GAAGC,QAAQ,CAACM,WAAW,CAAC,CAAC;QAC/BP,MAAM,CAACQ,QAAQ,CAACF,QAAQ,CAACG,UAAU,EAAEH,QAAQ,CAACI,MAAM,CAAC;QACrDV,MAAM,CAACW,MAAM,CAACL,QAAQ,CAACG,UAAU,EAAEH,QAAQ,CAACI,MAAM,CAAC;MACrD;MAEA,IAAME,UAAU,GAAGZ,MAAM,IAAIV,KAAK,CAACuB,SAAS,CAACC,eAAe,CAACd,MAAM,CAAC;MACpE,IAAIY,UAAU,EAAE;QAAA,IAAAG,eAAA;QACd,IAAMC,KAAK,GAAG1B,KAAK,CAACuB,SAAS,CAACI,iBAAiB,CAACL,UAAU,CAAC;QAC3D,KAAAG,eAAA,GAAIjB,CAAC,CAACoB,YAAY,cAAAH,eAAA,eAAdA,eAAA,CAAgBI,KAAK,EAAE;UACzB3B,KAAA,CAAK4B,MAAM,CAACJ,KAAK,EAAElB,CAAC,CAACoB,YAAY,CAACC,KAAK,CAAC;QAC1C;MACF;IACF,CAAC,CAAC;IAAA,OAAA3B,KAAA;EACJ;EAAA,IAAA6B,UAAA,CAAA3B,OAAA,EAAAN,QAAA,EAAAC,OAAA;EAAA,WAAAiC,aAAA,CAAA5B,OAAA,EAAAN,QAAA;IAAAmC,GAAA;IAAAC,KAAA,EAEA,SAAAJ,MAAMA,CAACJ,KAAY,EAAEG,KAAwB,EAAE;MAAA,IAAAM,MAAA;MAC7C,IAAMC,OAAe,GAAG,EAAE;MAC1BC,KAAK,CAACC,IAAI,CAACT,KAAK,CAAC,CAACU,OAAO,CAAE,UAAAC,IAAI,EAAK;QAAA,IAAAC,qBAAA;QAClC,IAAID,IAAI,KAAAC,qBAAA,GAAIN,MAAI,CAAClC,OAAO,CAACyC,SAAS,cAAAD,qBAAA,eAAtBA,qBAAA,CAAwBE,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,EAAE;UACvDR,OAAO,CAACS,IAAI,CAACL,IAAI,CAAC;QACpB;MACF,CAAC,CAAC;MACF,IAAIJ,OAAO,CAACU,MAAM,GAAG,CAAC,EAAE;QACtB;QACA,IAAI,CAAC7C,OAAO,CAAC8C,OAAO,CAACC,IAAI,CAAC,IAAI,EAAEtB,KAAK,EAAEU,OAAO,CAAC;MACjD;IACF;EAAA;AAAA,EAxCqBa,eAAM;AA2C7BnD,QAAQ,CAACoD,QAAQ,GAAG;EAClBR,SAAS,EAAE,CAAC,WAAW,EAAE,YAAY,CAAC;EACtCK,OAAO,WAAPA,OAAOA,CAACrB,KAAY,EAAEG,KAAa,EAAE;IAAA,IAAAsB,MAAA;IACnC,IAAI,CAAC,IAAI,CAACnD,KAAK,CAACoD,MAAM,CAACC,KAAK,CAAC,OAAO,CAAC,EAAE;MACrC;IACF;IACA,IAAMC,QAAQ,GAAGzB,KAAK,CAAC0B,GAAG,CAAmB,UAAAf,IAAI,EAAK;MACpD,OAAO,IAAIgB,OAAO,CAAE,UAAAC,OAAO,EAAK;QAC9B,IAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,MAAM,GAAG,YAAM;UACpBH,OAAO,CAACC,MAAM,CAACG,MAAgB,CAAC;QAClC,CAAC;QACDH,MAAM,CAACI,aAAa,CAACtB,IAAI,CAAC;MAC5B,CAAC,CAAC;IACJ,CAAC,CAAC;IACFgB,OAAO,CAACO,GAAG,CAACT,QAAQ,CAAC,CAACU,IAAI,CAAE,UAAAC,MAAM,EAAK;MACrC,IAAMC,MAAM,GAAGD,MAAM,CAACE,MAAM,CAAC,UAACC,KAAY,EAAEC,KAAK,EAAK;QACpD,OAAOD,KAAK,CAACE,MAAM,CAAC;UAAED,KAAA,EAAAA;QAAM,CAAC,CAAC;MAChC,CAAC,EAAE,IAAIE,mBAAK,CAAC,CAAC,CAACC,MAAM,CAAC9C,KAAK,CAAC+C,KAAK,CAAC,CAACC,MAAM,CAAChD,KAAK,CAACoB,MAAM,CAAC,CAAU;MACjEK,MAAI,CAACnD,KAAK,CAAC2E,cAAc,CAACT,MAAM,EAAEU,gBAAO,CAACC,OAAO,CAACC,IAAI,CAAC;MACvD3B,MAAI,CAACnD,KAAK,CAAC+E,YAAY,CACrBrD,KAAK,CAAC+C,KAAK,GAAGR,MAAM,CAACnB,MAAM,EAC3B8B,gBAAO,CAACC,OAAO,CAACG,MAClB,CAAC;IACH,CAAC,CAAC;EACJ;AACF,CAAC;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAA9E,OAAA,GAEcN,QAAQ", "ignoreList": []}]}