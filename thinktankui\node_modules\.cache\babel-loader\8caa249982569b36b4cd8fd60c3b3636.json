{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\system\\dict\\data.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\system\\dict\\data.vue", "mtime": 1749104047647}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_data", "require", "_type", "name", "dicts", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "dataList", "defaultDictType", "title", "open", "listClassOptions", "value", "label", "typeOptions", "queryParams", "pageNum", "pageSize", "dictType", "undefined", "dict<PERSON><PERSON>l", "status", "form", "rules", "required", "message", "trigger", "dict<PERSON><PERSON>ue", "dictSort", "created", "dictId", "$route", "params", "getType", "getTypeList", "methods", "_this", "then", "response", "getList", "_this2", "getDictOptionselect", "_this3", "listData", "rows", "cancel", "reset", "dictCode", "cssClass", "listClass", "remark", "resetForm", "handleQuery", "handleClose", "obj", "path", "$tab", "closeOpenPage", "reset<PERSON><PERSON>y", "handleAdd", "handleSelectionChange", "selection", "map", "item", "length", "handleUpdate", "row", "_this4", "getData", "submitForm", "_this5", "$refs", "validate", "valid", "updateData", "$store", "dispatch", "$modal", "msgSuccess", "addData", "handleDelete", "_this6", "dictCodes", "confirm", "delData", "catch", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime"], "sources": ["src/views/system/dict/data.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"字典名称\" prop=\"dictType\">\r\n        <el-select v-model=\"queryParams.dictType\">\r\n          <el-option\r\n            v-for=\"item in typeOptions\"\r\n            :key=\"item.dictId\"\r\n            :label=\"item.dictName\"\r\n            :value=\"item.dictType\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"字典标签\" prop=\"dictLabel\">\r\n        <el-input\r\n          v-model=\"queryParams.dictLabel\"\r\n          placeholder=\"请输入字典标签\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" placeholder=\"数据状态\" clearable>\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_normal_disable\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['system:dict:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['system:dict:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['system:dict:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['system:dict:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-close\"\r\n          size=\"mini\"\r\n          @click=\"handleClose\"\r\n        >关闭</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"dataList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"字典编码\" align=\"center\" prop=\"dictCode\" />\r\n      <el-table-column label=\"字典标签\" align=\"center\" prop=\"dictLabel\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"(scope.row.listClass == '' || scope.row.listClass == 'default') && (scope.row.cssClass == '' || scope.row.cssClass == null)\">{{ scope.row.dictLabel }}</span>\r\n          <el-tag v-else :type=\"scope.row.listClass == 'primary' ? '' : scope.row.listClass\" :class=\"scope.row.cssClass\">{{ scope.row.dictLabel }}</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"字典键值\" align=\"center\" prop=\"dictValue\" />\r\n      <el-table-column label=\"字典排序\" align=\"center\" prop=\"dictSort\" />\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['system:dict:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['system:dict:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改参数配置对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"字典类型\">\r\n          <el-input v-model=\"form.dictType\" :disabled=\"true\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"数据标签\" prop=\"dictLabel\">\r\n          <el-input v-model=\"form.dictLabel\" placeholder=\"请输入数据标签\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"数据键值\" prop=\"dictValue\">\r\n          <el-input v-model=\"form.dictValue\" placeholder=\"请输入数据键值\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"样式属性\" prop=\"cssClass\">\r\n          <el-input v-model=\"form.cssClass\" placeholder=\"请输入样式属性\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"显示排序\" prop=\"dictSort\">\r\n          <el-input-number v-model=\"form.dictSort\" controls-position=\"right\" :min=\"0\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"回显样式\" prop=\"listClass\">\r\n          <el-select v-model=\"form.listClass\">\r\n            <el-option\r\n              v-for=\"item in listClassOptions\"\r\n              :key=\"item.value\"\r\n              :label=\"item.label + '(' + item.value + ')'\"\r\n              :value=\"item.value\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\" prop=\"status\">\r\n          <el-radio-group v-model=\"form.status\">\r\n            <el-radio\r\n              v-for=\"dict in dict.type.sys_normal_disable\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.value\"\r\n            >{{dict.label}}</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listData, getData, delData, addData, updateData } from \"@/api/system/dict/data\";\r\nimport { optionselect as getDictOptionselect, getType } from \"@/api/system/dict/type\";\r\n\r\nexport default {\r\n  name: \"Data\",\r\n  dicts: ['sys_normal_disable'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 字典表格数据\r\n      dataList: [],\r\n      // 默认字典类型\r\n      defaultDictType: \"\",\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 数据标签回显样式\r\n      listClassOptions: [\r\n        {\r\n          value: \"default\",\r\n          label: \"默认\"\r\n        },\r\n        {\r\n          value: \"primary\",\r\n          label: \"主要\"\r\n        },\r\n        {\r\n          value: \"success\",\r\n          label: \"成功\"\r\n        },\r\n        {\r\n          value: \"info\",\r\n          label: \"信息\"\r\n        },\r\n        {\r\n          value: \"warning\",\r\n          label: \"警告\"\r\n        },\r\n        {\r\n          value: \"danger\",\r\n          label: \"危险\"\r\n        }\r\n      ],\r\n      // 类型数据字典\r\n      typeOptions: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        dictType: undefined,\r\n        dictLabel: undefined,\r\n        status: undefined\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        dictLabel: [\r\n          { required: true, message: \"数据标签不能为空\", trigger: \"blur\" }\r\n        ],\r\n        dictValue: [\r\n          { required: true, message: \"数据键值不能为空\", trigger: \"blur\" }\r\n        ],\r\n        dictSort: [\r\n          { required: true, message: \"数据顺序不能为空\", trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    const dictId = this.$route.params && this.$route.params.dictId;\r\n    this.getType(dictId);\r\n    this.getTypeList();\r\n  },\r\n  methods: {\r\n    /** 查询字典类型详细 */\r\n    getType(dictId) {\r\n      getType(dictId).then(response => {\r\n        this.queryParams.dictType = response.data.dictType;\r\n        this.defaultDictType = response.data.dictType;\r\n        this.getList();\r\n      });\r\n    },\r\n    /** 查询字典类型列表 */\r\n    getTypeList() {\r\n      getDictOptionselect().then(response => {\r\n        this.typeOptions = response.data;\r\n      });\r\n    },\r\n    /** 查询字典数据列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listData(this.queryParams).then(response => {\r\n        this.dataList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        dictCode: undefined,\r\n        dictLabel: undefined,\r\n        dictValue: undefined,\r\n        cssClass: undefined,\r\n        listClass: 'default',\r\n        dictSort: 0,\r\n        status: \"0\",\r\n        remark: undefined\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 返回按钮操作 */\r\n    handleClose() {\r\n      const obj = { path: \"/system/dict\" };\r\n      this.$tab.closeOpenPage(obj);\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.queryParams.dictType = this.defaultDictType;\r\n      this.handleQuery();\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加字典数据\";\r\n      this.form.dictType = this.queryParams.dictType;\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.dictCode)\r\n      this.single = selection.length!=1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const dictCode = row.dictCode || this.ids\r\n      getData(dictCode).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改字典数据\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.dictCode != undefined) {\r\n            updateData(this.form).then(response => {\r\n              this.$store.dispatch('dict/removeDict', this.queryParams.dictType);\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addData(this.form).then(response => {\r\n              this.$store.dispatch('dict/removeDict', this.queryParams.dictType);\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const dictCodes = row.dictCode || this.ids;\r\n      this.$modal.confirm('是否确认删除字典编码为\"' + dictCodes + '\"的数据项？').then(function() {\r\n        return delData(dictCodes);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n        this.$store.dispatch('dict/removeDict', this.queryParams.dictType);\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/dict/data/export', {\r\n        ...this.queryParams\r\n      }, `data_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script>"], "mappings": ";;;;;;;;;;;;AAgMA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,eAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,gBAAA,GACA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACA;MACAC,WAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA,EAAAC,SAAA;QACAC,SAAA,EAAAD,SAAA;QACAE,MAAA,EAAAF;MACA;MACA;MACAG,IAAA;MACA;MACAC,KAAA;QACAH,SAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,SAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,QAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA,IAAAC,MAAA,QAAAC,MAAA,CAAAC,MAAA,SAAAD,MAAA,CAAAC,MAAA,CAAAF,MAAA;IACA,KAAAG,OAAA,CAAAH,MAAA;IACA,KAAAI,WAAA;EACA;EACAC,OAAA;IACA,eACAF,OAAA,WAAAA,QAAAH,MAAA;MAAA,IAAAM,KAAA;MACA,IAAAH,aAAA,EAAAH,MAAA,EAAAO,IAAA,WAAAC,QAAA;QACAF,KAAA,CAAArB,WAAA,CAAAG,QAAA,GAAAoB,QAAA,CAAAtC,IAAA,CAAAkB,QAAA;QACAkB,KAAA,CAAA5B,eAAA,GAAA8B,QAAA,CAAAtC,IAAA,CAAAkB,QAAA;QACAkB,KAAA,CAAAG,OAAA;MACA;IACA;IACA,eACAL,WAAA,WAAAA,YAAA;MAAA,IAAAM,MAAA;MACA,IAAAC,kBAAA,IAAAJ,IAAA,WAAAC,QAAA;QACAE,MAAA,CAAA1B,WAAA,GAAAwB,QAAA,CAAAtC,IAAA;MACA;IACA;IACA,eACAuC,OAAA,WAAAA,QAAA;MAAA,IAAAG,MAAA;MACA,KAAAzC,OAAA;MACA,IAAA0C,cAAA,OAAA5B,WAAA,EAAAsB,IAAA,WAAAC,QAAA;QACAI,MAAA,CAAAnC,QAAA,GAAA+B,QAAA,CAAAM,IAAA;QACAF,MAAA,CAAApC,KAAA,GAAAgC,QAAA,CAAAhC,KAAA;QACAoC,MAAA,CAAAzC,OAAA;MACA;IACA;IACA;IACA4C,MAAA,WAAAA,OAAA;MACA,KAAAnC,IAAA;MACA,KAAAoC,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAxB,IAAA;QACAyB,QAAA,EAAA5B,SAAA;QACAC,SAAA,EAAAD,SAAA;QACAQ,SAAA,EAAAR,SAAA;QACA6B,QAAA,EAAA7B,SAAA;QACA8B,SAAA;QACArB,QAAA;QACAP,MAAA;QACA6B,MAAA,EAAA/B;MACA;MACA,KAAAgC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAArC,WAAA,CAAAC,OAAA;MACA,KAAAuB,OAAA;IACA;IACA,aACAc,WAAA,WAAAA,YAAA;MACA,IAAAC,GAAA;QAAAC,IAAA;MAAA;MACA,KAAAC,IAAA,CAAAC,aAAA,CAAAH,GAAA;IACA;IACA,aACAI,UAAA,WAAAA,WAAA;MACA,KAAAP,SAAA;MACA,KAAApC,WAAA,CAAAG,QAAA,QAAAV,eAAA;MACA,KAAA4C,WAAA;IACA;IACA,aACAO,SAAA,WAAAA,UAAA;MACA,KAAAb,KAAA;MACA,KAAApC,IAAA;MACA,KAAAD,KAAA;MACA,KAAAa,IAAA,CAAAJ,QAAA,QAAAH,WAAA,CAAAG,QAAA;IACA;IACA;IACA0C,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA3D,GAAA,GAAA2D,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAhB,QAAA;MAAA;MACA,KAAA5C,MAAA,GAAA0D,SAAA,CAAAG,MAAA;MACA,KAAA5D,QAAA,IAAAyD,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAArB,KAAA;MACA,IAAAC,QAAA,GAAAmB,GAAA,CAAAnB,QAAA,SAAA7C,GAAA;MACA,IAAAkE,aAAA,EAAArB,QAAA,EAAAV,IAAA,WAAAC,QAAA;QACA6B,MAAA,CAAA7C,IAAA,GAAAgB,QAAA,CAAAtC,IAAA;QACAmE,MAAA,CAAAzD,IAAA;QACAyD,MAAA,CAAA1D,KAAA;MACA;IACA;IACA;IACA4D,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAhD,IAAA,CAAAyB,QAAA,IAAA5B,SAAA;YACA,IAAAuD,gBAAA,EAAAJ,MAAA,CAAAhD,IAAA,EAAAe,IAAA,WAAAC,QAAA;cACAgC,MAAA,CAAAK,MAAA,CAAAC,QAAA,oBAAAN,MAAA,CAAAvD,WAAA,CAAAG,QAAA;cACAoD,MAAA,CAAAO,MAAA,CAAAC,UAAA;cACAR,MAAA,CAAA5D,IAAA;cACA4D,MAAA,CAAA/B,OAAA;YACA;UACA;YACA,IAAAwC,aAAA,EAAAT,MAAA,CAAAhD,IAAA,EAAAe,IAAA,WAAAC,QAAA;cACAgC,MAAA,CAAAK,MAAA,CAAAC,QAAA,oBAAAN,MAAA,CAAAvD,WAAA,CAAAG,QAAA;cACAoD,MAAA,CAAAO,MAAA,CAAAC,UAAA;cACAR,MAAA,CAAA5D,IAAA;cACA4D,MAAA,CAAA/B,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAyC,YAAA,WAAAA,aAAAd,GAAA;MAAA,IAAAe,MAAA;MACA,IAAAC,SAAA,GAAAhB,GAAA,CAAAnB,QAAA,SAAA7C,GAAA;MACA,KAAA2E,MAAA,CAAAM,OAAA,kBAAAD,SAAA,aAAA7C,IAAA;QACA,WAAA+C,aAAA,EAAAF,SAAA;MACA,GAAA7C,IAAA;QACA4C,MAAA,CAAA1C,OAAA;QACA0C,MAAA,CAAAJ,MAAA,CAAAC,UAAA;QACAG,MAAA,CAAAN,MAAA,CAAAC,QAAA,oBAAAK,MAAA,CAAAlE,WAAA,CAAAG,QAAA;MACA,GAAAmE,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,gCAAAC,cAAA,CAAAC,OAAA,MACA,KAAA1E,WAAA,WAAA2E,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}