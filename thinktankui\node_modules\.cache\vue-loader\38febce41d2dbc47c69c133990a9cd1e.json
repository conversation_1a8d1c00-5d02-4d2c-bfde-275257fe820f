{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\SvgIcon\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\SvgIcon\\index.vue", "mtime": 1749104047621}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBpc0V4dGVybmFsIH0gZnJvbSAnQC91dGlscy92YWxpZGF0ZScNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnU3ZnSWNvbicsDQogIHByb3BzOiB7DQogICAgaWNvbkNsYXNzOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICByZXF1aXJlZDogdHJ1ZQ0KICAgIH0sDQogICAgY2xhc3NOYW1lOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnJw0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICBpc0V4dGVybmFsKCkgew0KICAgICAgcmV0dXJuIGlzRXh0ZXJuYWwodGhpcy5pY29uQ2xhc3MpDQogICAgfSwNCiAgICBpY29uTmFtZSgpIHsNCiAgICAgIHJldHVybiBgI2ljb24tJHt0aGlzLmljb25DbGFzc31gDQogICAgfSwNCiAgICBzdmdDbGFzcygpIHsNCiAgICAgIGlmICh0aGlzLmNsYXNzTmFtZSkgew0KICAgICAgICByZXR1cm4gJ3N2Zy1pY29uICcgKyB0aGlzLmNsYXNzTmFtZQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuICdzdmctaWNvbicNCiAgICAgIH0NCiAgICB9LA0KICAgIHN0eWxlRXh0ZXJuYWxJY29uKCkgew0KICAgICAgcmV0dXJuIHsNCiAgICAgICAgbWFzazogYHVybCgke3RoaXMuaWNvbkNsYXNzfSkgbm8tcmVwZWF0IDUwJSA1MCVgLA0KICAgICAgICAnLXdlYmtpdC1tYXNrJzogYHVybCgke3RoaXMuaWNvbkNsYXNzfSkgbm8tcmVwZWF0IDUwJSA1MCVgDQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;AAQA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/SvgIcon", "sourcesContent": ["<template>\r\n  <div v-if=\"isExternal\" :style=\"styleExternalIcon\" class=\"svg-external-icon svg-icon\" v-on=\"$listeners\" />\r\n  <svg v-else :class=\"svgClass\" aria-hidden=\"true\" v-on=\"$listeners\">\r\n    <use :xlink:href=\"iconName\" />\r\n  </svg>\r\n</template>\r\n\r\n<script>\r\nimport { isExternal } from '@/utils/validate'\r\n\r\nexport default {\r\n  name: 'SvgIcon',\r\n  props: {\r\n    iconClass: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    className: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  computed: {\r\n    isExternal() {\r\n      return isExternal(this.iconClass)\r\n    },\r\n    iconName() {\r\n      return `#icon-${this.iconClass}`\r\n    },\r\n    svgClass() {\r\n      if (this.className) {\r\n        return 'svg-icon ' + this.className\r\n      } else {\r\n        return 'svg-icon'\r\n      }\r\n    },\r\n    styleExternalIcon() {\r\n      return {\r\n        mask: `url(${this.iconClass}) no-repeat 50% 50%`,\r\n        '-webkit-mask': `url(${this.iconClass}) no-repeat 50% 50%`\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.svg-icon {\r\n  width: 1em;\r\n  height: 1em;\r\n  vertical-align: -0.15em;\r\n  fill: currentColor;\r\n  overflow: hidden;\r\n}\r\n\r\n.svg-external-icon {\r\n  background-color: currentColor;\r\n  mask-size: cover!important;\r\n  display: inline-block;\r\n}\r\n</style>\r\n"]}]}