{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\bold.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\bold.js", "mtime": 1749104420636}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_inline", "_interopRequireDefault", "require", "Bold", "_Inline", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "key", "value", "optimize", "context", "_superPropGet2", "domNode", "tagName", "statics", "replaceWith", "blotName", "create", "formats", "Inline", "_defineProperty2", "_default", "exports"], "sources": ["../../src/formats/bold.ts"], "sourcesContent": ["import Inline from '../blots/inline.js';\n\nclass Bold extends Inline {\n  static blotName = 'bold';\n  static tagName = ['STRONG', 'B'];\n\n  static create() {\n    return super.create();\n  }\n\n  static formats() {\n    return true;\n  }\n\n  optimize(context: { [key: string]: any }) {\n    super.optimize(context);\n    if (this.domNode.tagName !== this.statics.tagName[0]) {\n      this.replaceWith(this.statics.blotName);\n    }\n  }\n}\n\nexport default Bold;\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAuC,IAEjCC,IAAI,0BAAAC,OAAA;EAAA,SAAAD,KAAA;IAAA,IAAAE,gBAAA,CAAAC,OAAA,QAAAH,IAAA;IAAA,WAAAI,WAAA,CAAAD,OAAA,QAAAH,IAAA,EAAAK,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAH,IAAA,EAAAC,OAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAH,IAAA;IAAAQ,GAAA;IAAAC,KAAA,EAYR,SAAAC,QAAQA,CAACC,OAA+B,EAAE;MACxC,IAAAC,cAAA,CAAAT,OAAA,EAAAH,IAAA,wBAAeW,OAAO;MACtB,IAAI,IAAI,CAACE,OAAO,CAACC,OAAO,KAAK,IAAI,CAACC,OAAO,CAACD,OAAO,CAAC,CAAC,CAAC,EAAE;QACpD,IAAI,CAACE,WAAW,CAAC,IAAI,CAACD,OAAO,CAACE,QAAQ,CAAC;MACzC;IACF;EAAA;IAAAT,GAAA;IAAAC,KAAA,EAbA,SAAOS,MAAMA,CAAA,EAAG;MACd,WAAAN,cAAA,CAAAT,OAAA,EAAAH,IAAA;IACF;EAAA;IAAAQ,GAAA;IAAAC,KAAA,EAEA,SAAOU,OAAOA,CAAA,EAAG;MACf,OAAO,IAAI;IACb;EAAA;AAAA,EAViBC,eAAM;AAAA,IAAAC,gBAAA,CAAAlB,OAAA,EAAnBH,IAAI,cACU,MAAM;AAAA,IAAAqB,gBAAA,CAAAlB,OAAA,EADpBH,IAAI,aAES,CAAC,QAAQ,EAAE,GAAG,CAAC;AAAA,IAAAsB,QAAA,GAAAC,OAAA,CAAApB,OAAA,GAkBnBH,IAAI", "ignoreList": []}]}