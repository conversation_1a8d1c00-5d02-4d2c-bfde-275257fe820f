{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\directive\\module\\clipboard.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\directive\\module\\clipboard.js", "mtime": 1749104047626}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749104418875}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_clipboard", "_interopRequireDefault", "require", "_default", "exports", "default", "bind", "el", "binding", "vnode", "arg", "_vClipBoard_success", "value", "_vClipBoard_error", "clipboard", "Clipboard", "text", "action", "on", "e", "callback", "_vClipBoard", "update", "unbind", "_vClipboard", "destroy"], "sources": ["D:/thinktank/thinktankui/src/directive/module/clipboard.js"], "sourcesContent": ["/**\r\n* v-clipboard 文字复制剪贴\r\n* Copyright (c) 2021 ruoyi\r\n*/\r\n\r\nimport Clipboard from 'clipboard'\r\nexport default {\r\n  bind(el, binding, vnode) {\r\n    switch (binding.arg) {\r\n      case 'success':\r\n        el._vClipBoard_success = binding.value;\r\n        break;\r\n      case 'error':\r\n        el._vClipBoard_error = binding.value;\r\n        break;\r\n      default: {\r\n        const clipboard = new Clipboard(el, {\r\n          text: () => binding.value,\r\n          action: () => binding.arg === 'cut' ? 'cut' : 'copy'\r\n        });\r\n        clipboard.on('success', e => {\r\n          const callback = el._vClipBoard_success;\r\n          callback && callback(e);\r\n        });\r\n        clipboard.on('error', e => {\r\n          const callback = el._vClipBoard_error;\r\n          callback && callback(e);\r\n        });\r\n        el._vClipBoard = clipboard;\r\n      }\r\n    }\r\n  },\r\n  update(el, binding) {\r\n    if (binding.arg === 'success') {\r\n      el._vClipBoard_success = binding.value;\r\n    } else if (binding.arg === 'error') {\r\n      el._vClipBoard_error = binding.value;\r\n    } else {\r\n      el._vClipBoard.text = function () { return binding.value; };\r\n      el._vClipBoard.action = () => binding.arg === 'cut' ? 'cut' : 'copy';\r\n    }\r\n  },\r\n  unbind(el, binding) {\r\n    if (!el._vClipboard) return\r\n    if (binding.arg === 'success') {\r\n      delete el._vClipBoard_success;\r\n    } else if (binding.arg === 'error') {\r\n      delete el._vClipBoard_error;\r\n    } else {\r\n      el._vClipBoard.destroy();\r\n      delete el._vClipBoard;\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;AAKA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AALA;AACA;AACA;AACA;AAHA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAMe;EACbC,IAAI,WAAJA,IAAIA,CAACC,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAE;IACvB,QAAQD,OAAO,CAACE,GAAG;MACjB,KAAK,SAAS;QACZH,EAAE,CAACI,mBAAmB,GAAGH,OAAO,CAACI,KAAK;QACtC;MACF,KAAK,OAAO;QACVL,EAAE,CAACM,iBAAiB,GAAGL,OAAO,CAACI,KAAK;QACpC;MACF;QAAS;UACP,IAAME,SAAS,GAAG,IAAIC,kBAAS,CAACR,EAAE,EAAE;YAClCS,IAAI,EAAE,SAANA,IAAIA,CAAA;cAAA,OAAQR,OAAO,CAACI,KAAK;YAAA;YACzBK,MAAM,EAAE,SAARA,MAAMA,CAAA;cAAA,OAAQT,OAAO,CAACE,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG,MAAM;YAAA;UACtD,CAAC,CAAC;UACFI,SAAS,CAACI,EAAE,CAAC,SAAS,EAAE,UAAAC,CAAC,EAAI;YAC3B,IAAMC,QAAQ,GAAGb,EAAE,CAACI,mBAAmB;YACvCS,QAAQ,IAAIA,QAAQ,CAACD,CAAC,CAAC;UACzB,CAAC,CAAC;UACFL,SAAS,CAACI,EAAE,CAAC,OAAO,EAAE,UAAAC,CAAC,EAAI;YACzB,IAAMC,QAAQ,GAAGb,EAAE,CAACM,iBAAiB;YACrCO,QAAQ,IAAIA,QAAQ,CAACD,CAAC,CAAC;UACzB,CAAC,CAAC;UACFZ,EAAE,CAACc,WAAW,GAAGP,SAAS;QAC5B;IACF;EACF,CAAC;EACDQ,MAAM,WAANA,MAAMA,CAACf,EAAE,EAAEC,OAAO,EAAE;IAClB,IAAIA,OAAO,CAACE,GAAG,KAAK,SAAS,EAAE;MAC7BH,EAAE,CAACI,mBAAmB,GAAGH,OAAO,CAACI,KAAK;IACxC,CAAC,MAAM,IAAIJ,OAAO,CAACE,GAAG,KAAK,OAAO,EAAE;MAClCH,EAAE,CAACM,iBAAiB,GAAGL,OAAO,CAACI,KAAK;IACtC,CAAC,MAAM;MACLL,EAAE,CAACc,WAAW,CAACL,IAAI,GAAG,YAAY;QAAE,OAAOR,OAAO,CAACI,KAAK;MAAE,CAAC;MAC3DL,EAAE,CAACc,WAAW,CAACJ,MAAM,GAAG;QAAA,OAAMT,OAAO,CAACE,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG,MAAM;MAAA;IACtE;EACF,CAAC;EACDa,MAAM,WAANA,MAAMA,CAAChB,EAAE,EAAEC,OAAO,EAAE;IAClB,IAAI,CAACD,EAAE,CAACiB,WAAW,EAAE;IACrB,IAAIhB,OAAO,CAACE,GAAG,KAAK,SAAS,EAAE;MAC7B,OAAOH,EAAE,CAACI,mBAAmB;IAC/B,CAAC,MAAM,IAAIH,OAAO,CAACE,GAAG,KAAK,OAAO,EAAE;MAClC,OAAOH,EAAE,CAACM,iBAAiB;IAC7B,CAAC,MAAM;MACLN,EAAE,CAACc,WAAW,CAACI,OAAO,CAAC,CAAC;MACxB,OAAOlB,EAAE,CAACc,WAAW;IACvB;EACF;AACF,CAAC", "ignoreList": []}]}