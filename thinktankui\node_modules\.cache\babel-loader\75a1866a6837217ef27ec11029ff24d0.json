{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\layout\\components\\Navbar.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\layout\\components\\Navbar.vue", "mtime": 1749104047626}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_vuex", "require", "_Breadcrumb", "_interopRequireDefault", "_TopNav", "_<PERSON>er", "_Screenfull", "_SizeSelect", "_HeaderSearch", "_Git", "_Doc", "components", "Breadcrumb", "TopNav", "<PERSON><PERSON>", "Screenfull", "SizeSelect", "Search", "RuoYiGit", "RuoYiDoc", "computed", "_objectSpread2", "default", "mapGetters", "setting", "get", "$store", "state", "settings", "showSettings", "set", "val", "dispatch", "key", "value", "topNav", "methods", "toggleSideBar", "logout", "_this", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "w", "_context", "n", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "location", "href", "catch", "a"], "sources": ["src/layout/components/Navbar.vue"], "sourcesContent": ["<template>\r\n  <div class=\"navbar\">\r\n    <hamburger id=\"hamburger-container\" :is-active=\"sidebar.opened\" class=\"hamburger-container\" @toggleClick=\"toggleSideBar\" />\r\n\r\n    <breadcrumb id=\"breadcrumb-container\" class=\"breadcrumb-container\" v-if=\"!topNav\"/>\r\n    <top-nav id=\"topmenu-container\" class=\"topmenu-container\" v-if=\"topNav\"/>\r\n\r\n    <div class=\"right-menu\">\r\n      <template v-if=\"device!=='mobile'\">\r\n        <search id=\"header-search\" class=\"right-menu-item\" />\r\n\r\n        <el-tooltip content=\"源码地址\" effect=\"dark\" placement=\"bottom\">\r\n          <ruo-yi-git id=\"ruoyi-git\" class=\"right-menu-item hover-effect\" />\r\n        </el-tooltip>\r\n\r\n        <el-tooltip content=\"文档地址\" effect=\"dark\" placement=\"bottom\">\r\n          <ruo-yi-doc id=\"ruoyi-doc\" class=\"right-menu-item hover-effect\" />\r\n        </el-tooltip>\r\n\r\n        <screenfull id=\"screenfull\" class=\"right-menu-item hover-effect\" />\r\n\r\n        <el-tooltip content=\"布局大小\" effect=\"dark\" placement=\"bottom\">\r\n          <size-select id=\"size-select\" class=\"right-menu-item hover-effect\" />\r\n        </el-tooltip>\r\n\r\n      </template>\r\n\r\n      <el-dropdown class=\"avatar-container right-menu-item hover-effect\" trigger=\"click\">\r\n        <div class=\"avatar-wrapper\">\r\n          <img :src=\"avatar\" class=\"user-avatar\">\r\n          <i class=\"el-icon-caret-bottom\" />\r\n        </div>\r\n        <el-dropdown-menu slot=\"dropdown\">\r\n          <router-link to=\"/user/profile\">\r\n            <el-dropdown-item>个人中心</el-dropdown-item>\r\n          </router-link>\r\n          <el-dropdown-item @click.native=\"setting = true\">\r\n            <span>布局设置</span>\r\n          </el-dropdown-item>\r\n          <el-dropdown-item divided @click.native=\"logout\">\r\n            <span>退出登录</span>\r\n          </el-dropdown-item>\r\n        </el-dropdown-menu>\r\n      </el-dropdown>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport Breadcrumb from '@/components/Breadcrumb'\r\nimport TopNav from '@/components/TopNav'\r\nimport Hamburger from '@/components/Hamburger'\r\nimport Screenfull from '@/components/Screenfull'\r\nimport SizeSelect from '@/components/SizeSelect'\r\nimport Search from '@/components/HeaderSearch'\r\nimport RuoYiGit from '@/components/RuoYi/Git'\r\nimport RuoYiDoc from '@/components/RuoYi/Doc'\r\n\r\nexport default {\r\n  components: {\r\n    Breadcrumb,\r\n    TopNav,\r\n    Hamburger,\r\n    Screenfull,\r\n    SizeSelect,\r\n    Search,\r\n    RuoYiGit,\r\n    RuoYiDoc\r\n  },\r\n  computed: {\r\n    ...mapGetters([\r\n      'sidebar',\r\n      'avatar',\r\n      'device'\r\n    ]),\r\n    setting: {\r\n      get() {\r\n        return this.$store.state.settings.showSettings\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'showSettings',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    topNav: {\r\n      get() {\r\n        return this.$store.state.settings.topNav\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSideBar() {\r\n      this.$store.dispatch('app/toggleSideBar')\r\n    },\r\n    async logout() {\r\n      this.$confirm('确定注销并退出系统吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$store.dispatch('LogOut').then(() => {\r\n          location.href = '/index';\r\n        })\r\n      }).catch(() => {});\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.navbar {\r\n  height: 50px;\r\n  overflow: hidden;\r\n  position: relative;\r\n  background: #fff;\r\n  box-shadow: 0 1px 4px rgba(0,21,41,.08);\r\n\r\n  .hamburger-container {\r\n    line-height: 46px;\r\n    height: 100%;\r\n    float: left;\r\n    cursor: pointer;\r\n    transition: background .3s;\r\n    -webkit-tap-highlight-color:transparent;\r\n\r\n    &:hover {\r\n      background: rgba(0, 0, 0, .025)\r\n    }\r\n  }\r\n\r\n  .breadcrumb-container {\r\n    float: left;\r\n  }\r\n\r\n  .topmenu-container {\r\n    position: absolute;\r\n    left: 50px;\r\n  }\r\n\r\n  .errLog-container {\r\n    display: inline-block;\r\n    vertical-align: top;\r\n  }\r\n\r\n  .right-menu {\r\n    float: right;\r\n    height: 100%;\r\n    line-height: 50px;\r\n\r\n    &:focus {\r\n      outline: none;\r\n    }\r\n\r\n    .right-menu-item {\r\n      display: inline-block;\r\n      padding: 0 8px;\r\n      height: 100%;\r\n      font-size: 18px;\r\n      color: #5a5e66;\r\n      vertical-align: text-bottom;\r\n\r\n      &.hover-effect {\r\n        cursor: pointer;\r\n        transition: background .3s;\r\n\r\n        &:hover {\r\n          background: rgba(0, 0, 0, .025)\r\n        }\r\n      }\r\n    }\r\n\r\n    .avatar-container {\r\n      margin-right: 30px;\r\n\r\n      .avatar-wrapper {\r\n        // margin-top: 5px;\r\n        position: relative;\r\n\r\n        .user-avatar {\r\n          cursor: pointer;\r\n          width: 40px;\r\n          height: 40px;\r\n          border-radius: 10px;\r\n        }\r\n\r\n        .el-icon-caret-bottom {\r\n          cursor: pointer;\r\n          position: absolute;\r\n          right: -20px;\r\n          top: 25px;\r\n          font-size: 12px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;AAiDA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,OAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,UAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,WAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,WAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,aAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,IAAA,GAAAN,sBAAA,CAAAF,OAAA;AACA,IAAAS,IAAA,GAAAP,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAU,UAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,MAAA,EAAAA,eAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,MAAA,EAAAA,qBAAA;IACAC,QAAA,EAAAA,YAAA;IACAC,QAAA,EAAAA;EACA;EACAC,QAAA,MAAAC,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA,IAAAC,gBAAA,GACA,WACA,UACA,SACA;IACAC,OAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,YAAA;MACA;MACAC,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAL,MAAA,CAAAM,QAAA;UACAC,GAAA;UACAC,KAAA,EAAAH;QACA;MACA;IACA;IACAI,MAAA;MACAV,GAAA,WAAAA,IAAA;QACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAO,MAAA;MACA;IACA;EAAA,EACA;EACAC,OAAA;IACAC,aAAA,WAAAA,cAAA;MACA,KAAAX,MAAA,CAAAM,QAAA;IACA;IACAM,MAAA,WAAAA,OAAA;MAAA,IAAAC,KAAA;MAAA,WAAAC,kBAAA,CAAAlB,OAAA,mBAAAmB,aAAA,CAAAnB,OAAA,IAAAoB,CAAA,UAAAC,QAAA;QAAA,WAAAF,aAAA,CAAAnB,OAAA,IAAAsB,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cACAP,KAAA,CAAAQ,QAAA;gBACAC,iBAAA;gBACAC,gBAAA;gBACAC,IAAA;cACA,GAAAC,IAAA;gBACAZ,KAAA,CAAAb,MAAA,CAAAM,QAAA,WAAAmB,IAAA;kBACAC,QAAA,CAAAC,IAAA;gBACA;cACA,GAAAC,KAAA;YAAA;cAAA,OAAAT,QAAA,CAAAU,CAAA;UAAA;QAAA,GAAAZ,OAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}