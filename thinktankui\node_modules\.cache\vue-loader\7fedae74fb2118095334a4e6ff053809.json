{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\error\\401.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\error\\401.vue", "mtime": 1749104047639}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgZXJyR2lmIGZyb20gJ0AvYXNzZXRzLzQwMV9pbWFnZXMvNDAxLmdpZicNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnUGFnZTQwMScsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGVyckdpZjogZXJyR2lmICsgJz8nICsgK25ldyBEYXRlKCkNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBiYWNrKCkgew0KICAgICAgaWYgKHRoaXMuJHJvdXRlLnF1ZXJ5Lm5vR29CYWNrKSB7DQogICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsgcGF0aDogJy8nIH0pDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRyb3V0ZXIuZ28oLTEpDQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["401.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "401.vue", "sourceRoot": "src/views/error", "sourcesContent": ["<template>\r\n  <div class=\"errPage-container\">\r\n    <el-button icon=\"arrow-left\" class=\"pan-back-btn\" @click=\"back\">\r\n      返回\r\n    </el-button>\r\n    <el-row>\r\n      <el-col :span=\"12\">\r\n        <h1 class=\"text-jumbo text-ginormous\">\r\n          401错误!\r\n        </h1>\r\n        <h2>您没有访问权限！</h2>\r\n        <h6>对不起，您没有访问权限，请不要进行非法操作！您可以返回主页面</h6>\r\n        <ul class=\"list-unstyled\">\r\n          <li class=\"link-type\">\r\n            <router-link to=\"/\">\r\n              回首页\r\n            </router-link>\r\n          </li>\r\n        </ul>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <img :src=\"errGif\" width=\"313\" height=\"428\" alt=\"Girl has dropped her ice cream.\">\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport errGif from '@/assets/401_images/401.gif'\r\n\r\nexport default {\r\n  name: 'Page401',\r\n  data() {\r\n    return {\r\n      errGif: errGif + '?' + +new Date()\r\n    }\r\n  },\r\n  methods: {\r\n    back() {\r\n      if (this.$route.query.noGoBack) {\r\n        this.$router.push({ path: '/' })\r\n      } else {\r\n        this.$router.go(-1)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .errPage-container {\r\n    width: 800px;\r\n    max-width: 100%;\r\n    margin: 100px auto;\r\n    .pan-back-btn {\r\n      background: #008489;\r\n      color: #fff;\r\n      border: none!important;\r\n    }\r\n    .pan-gif {\r\n      margin: 0 auto;\r\n      display: block;\r\n    }\r\n    .pan-img {\r\n      display: block;\r\n      margin: 0 auto;\r\n      width: 100%;\r\n    }\r\n    .text-jumbo {\r\n      font-size: 60px;\r\n      font-weight: 700;\r\n      color: #484848;\r\n    }\r\n    .list-unstyled {\r\n      font-size: 14px;\r\n      li {\r\n        padding-bottom: 5px;\r\n      }\r\n      a {\r\n        color: #008489;\r\n        text-decoration: none;\r\n        &:hover {\r\n          text-decoration: underline;\r\n        }\r\n      }\r\n    }\r\n  }\r\n</style>\r\n"]}]}