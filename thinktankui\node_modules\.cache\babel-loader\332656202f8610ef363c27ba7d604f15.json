{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\core\\quill.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\core\\quill.js", "mtime": 1749104422440}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_lodashEs", "require", "Parchment", "_interopRequireWildcard", "exports", "_quill<PERSON><PERSON><PERSON>", "_interopRequireDefault", "_editor", "_emitter", "_instances", "_logger", "_module", "_selection", "_composition", "_theme", "_scrollRectIntoView2", "_createRegistryWithFormats", "_excluded", "_excluded2", "debug", "logger", "globalRegistry", "Registry", "ParentBlot", "uiClass", "<PERSON><PERSON><PERSON>", "default", "container", "_this", "_classCallCheck2", "options", "arguments", "length", "undefined", "expandConfig", "error", "html", "innerHTML", "trim", "classList", "add", "instances", "set", "root", "addContainer", "emitter", "Emitter", "scrollBlotName", "ScrollBlot", "blotName", "registry", "query", "Error", "concat", "scroll", "editor", "Editor", "selection", "Selection", "composition", "Composition", "theme", "keyboard", "addModule", "clipboard", "history", "uploader", "init", "on", "events", "EDITOR_CHANGE", "type", "TEXT_CHANGE", "toggle", "isBlank", "SCROLL_UPDATE", "source", "mutations", "oldRange", "<PERSON><PERSON><PERSON><PERSON>", "_this$selection$getRa", "getRange", "_this$selection$getRa2", "_slicedToArray2", "newRange", "selectionInfo", "modify", "call", "update", "SCROLL_EMBED_UPDATE", "blot", "delta", "_this$selection$getRa3", "_this$selection$getRa4", "change", "Delta", "retain", "offset", "_defineProperty2", "statics", "sources", "USER", "contents", "convert", "text", "setContents", "clear", "placeholder", "setAttribute", "readOnly", "disable", "allowReadOnlyEdits", "_createClass2", "key", "value", "refNode", "className", "document", "createElement", "insertBefore", "blur", "setRang<PERSON>", "deleteText", "index", "_this2", "_overload", "overload", "_overload2", "enable", "editReadOnly", "modifier", "enabled", "focus", "preventScroll", "scrollSelectionIntoView", "format", "name", "_this3", "API", "range", "getSelection", "<PERSON><PERSON>", "BLOCK", "formatLine", "formatText", "setSelection", "SILENT", "_this4", "formats", "_overload3", "_overload4", "_this5", "_overload5", "_overload6", "getBounds", "bounds", "containerBounds", "getBoundingClientRect", "bottom", "top", "height", "left", "right", "width", "getContents", "<PERSON><PERSON><PERSON><PERSON>", "_overload7", "_overload8", "getFormat", "getIndex", "<PERSON><PERSON><PERSON><PERSON>", "leaf", "getLine", "line", "getLines", "Number", "MAX_VALUE", "lines", "getModule", "modules", "getSemanticHTML", "_overload9", "_overload0", "getHTML", "getText", "_overload1", "_overload10", "hasFocus", "insertEmbed", "embed", "_this6", "insertText", "_this7", "_overload11", "_overload12", "isEnabled", "off", "_this$emitter", "apply", "_this$emitter2", "once", "_this$emitter3", "removeFormat", "_this8", "_overload13", "_overload14", "scrollRectIntoView", "rect", "scrollIntoView", "console", "warn", "_this9", "delete1", "applied", "insertContents", "delete2", "compose", "_overload15", "_overload16", "Range", "Math", "max", "setText", "insert", "updateContents", "_this0", "<PERSON><PERSON><PERSON><PERSON>", "limit", "level", "find", "node", "bubble", "get", "import", "imports", "register", "_this1", "target", "overwrite", "attrName", "Object", "keys", "for<PERSON>ach", "path", "startsWith", "parchment", "<PERSON><PERSON><PERSON>", "Theme", "resolveSelector", "selector", "querySelector", "expandModuleConfig", "config", "entries", "reduce", "expanded", "_ref", "_ref3", "_objectSpread4", "omitUndefinedValuesFromOptions", "obj", "fromEntries", "filter", "entry", "containerOrSelector", "shouldUseDefaultTheme", "DEFAULTS", "_Quill$DEFAULTS", "quillModuleDefaults", "quill<PERSON><PERSON><PERSON><PERSON>", "_objectWithoutProperties2", "_theme$DEFAULTS", "themeModuleDefaults", "themeDefaults", "userModuleOptions", "toolbar", "constructor", "merge", "createRegistryWithFormats", "modulesWithDefaults", "_ref2", "_ref4", "moduleClass", "shift", "<PERSON><PERSON><PERSON><PERSON>", "shiftRange", "_this$emitter4", "args", "emit", "_this$emitter5", "_typeof2", "lengthOrSource", "start", "end", "transformPosition", "_map", "map", "pos", "_map2", "_map3", "_map4"], "sources": ["../../src/core/quill.ts"], "sourcesContent": ["import { merge } from 'lodash-es';\nimport * as Parchment from 'parchment';\nimport type { Op } from 'quill-delta';\nimport Delta from 'quill-delta';\nimport type { BlockEmbed } from '../blots/block.js';\nimport type Block from '../blots/block.js';\nimport type Scroll from '../blots/scroll.js';\nimport type Clipboard from '../modules/clipboard.js';\nimport type History from '../modules/history.js';\nimport type Keyboard from '../modules/keyboard.js';\nimport type Uploader from '../modules/uploader.js';\nimport Editor from './editor.js';\nimport Emitter from './emitter.js';\nimport type { EmitterSource } from './emitter.js';\nimport instances from './instances.js';\nimport logger from './logger.js';\nimport type { DebugLevel } from './logger.js';\nimport Module from './module.js';\nimport Selection, { Range } from './selection.js';\nimport type { Bounds } from './selection.js';\nimport Composition from './composition.js';\nimport Theme from './theme.js';\nimport type { ThemeConstructor } from './theme.js';\nimport scrollRectIntoView from './utils/scrollRectIntoView.js';\nimport type { Rect } from './utils/scrollRectIntoView.js';\nimport createRegistryWithFormats from './utils/createRegistryWithFormats.js';\n\nconst debug = logger('quill');\n\nconst globalRegistry = new Parchment.Registry();\nParchment.ParentBlot.uiClass = 'ql-ui';\n\n/**\n * Options for initializing a Quill instance\n */\nexport interface QuillOptions {\n  theme?: string;\n  debug?: DebugLevel | boolean;\n  registry?: Parchment.Registry;\n  /**\n   * Whether to disable the editing\n   * @default false\n   */\n  readOnly?: boolean;\n\n  /**\n   * Placeholder text to display when the editor is empty\n   * @default \"\"\n   */\n  placeholder?: string;\n  bounds?: HTMLElement | string | null;\n  modules?: Record<string, unknown>;\n\n  /**\n   * A list of formats that are recognized and can exist within the editor contents.\n   * `null` means all formats are allowed.\n   * @default null\n   */\n  formats?: string[] | null;\n}\n\n/**\n * Similar to QuillOptions, but with all properties expanded to their default values,\n * and all selectors resolved to HTMLElements.\n */\nexport interface ExpandedQuillOptions\n  extends Omit<QuillOptions, 'theme' | 'formats'> {\n  theme: ThemeConstructor;\n  registry: Parchment.Registry;\n  container: HTMLElement;\n  modules: Record<string, unknown>;\n  bounds?: HTMLElement | null;\n  readOnly: boolean;\n}\n\nclass Quill {\n  static DEFAULTS = {\n    bounds: null,\n    modules: {\n      clipboard: true,\n      keyboard: true,\n      history: true,\n      uploader: true,\n    },\n    placeholder: '',\n    readOnly: false,\n    registry: globalRegistry,\n    theme: 'default',\n  } satisfies Partial<QuillOptions>;\n  static events = Emitter.events;\n  static sources = Emitter.sources;\n  static version = typeof QUILL_VERSION === 'undefined' ? 'dev' : QUILL_VERSION;\n\n  static imports: Record<string, unknown> = {\n    delta: Delta,\n    parchment: Parchment,\n    'core/module': Module,\n    'core/theme': Theme,\n  };\n\n  static debug(limit: DebugLevel | boolean) {\n    if (limit === true) {\n      limit = 'log';\n    }\n    logger.level(limit);\n  }\n\n  static find(node: Node, bubble = false) {\n    return instances.get(node) || globalRegistry.find(node, bubble);\n  }\n\n  static import(name: 'core/module'): typeof Module;\n  static import(name: `themes/${string}`): typeof Theme;\n  static import(name: 'parchment'): typeof Parchment;\n  static import(name: 'delta'): typeof Delta;\n  static import(name: string): unknown;\n  static import(name: string) {\n    if (this.imports[name] == null) {\n      debug.error(`Cannot import ${name}. Are you sure it was registered?`);\n    }\n    return this.imports[name];\n  }\n\n  static register(\n    targets: Record<\n      string,\n      | Parchment.RegistryDefinition\n      | Record<string, unknown> // any objects\n      | Theme\n      | Module\n      | Function // ES5 constructors\n    >,\n    overwrite?: boolean,\n  ): void;\n  static register(\n    target: Parchment.RegistryDefinition,\n    overwrite?: boolean,\n  ): void;\n  static register(path: string, target: any, overwrite?: boolean): void;\n  static register(...args: any[]): void {\n    if (typeof args[0] !== 'string') {\n      const target = args[0];\n      const overwrite = !!args[1];\n\n      const name = 'attrName' in target ? target.attrName : target.blotName;\n      if (typeof name === 'string') {\n        // Shortcut for formats:\n        // register(Blot | Attributor, overwrite)\n        this.register(`formats/${name}`, target, overwrite);\n      } else {\n        Object.keys(target).forEach((key) => {\n          this.register(key, target[key], overwrite);\n        });\n      }\n    } else {\n      const path = args[0];\n      const target = args[1];\n      const overwrite = !!args[2];\n\n      if (this.imports[path] != null && !overwrite) {\n        debug.warn(`Overwriting ${path} with`, target);\n      }\n      this.imports[path] = target;\n      if (\n        (path.startsWith('blots/') || path.startsWith('formats/')) &&\n        target &&\n        typeof target !== 'boolean' &&\n        target.blotName !== 'abstract'\n      ) {\n        globalRegistry.register(target);\n      }\n      if (typeof target.register === 'function') {\n        target.register(globalRegistry);\n      }\n    }\n  }\n\n  container: HTMLElement;\n  root: HTMLDivElement;\n  scroll: Scroll;\n  emitter: Emitter;\n  protected allowReadOnlyEdits: boolean;\n  editor: Editor;\n  composition: Composition;\n  selection: Selection;\n\n  theme: Theme;\n  keyboard: Keyboard;\n  clipboard: Clipboard;\n  history: History;\n  uploader: Uploader;\n\n  options: ExpandedQuillOptions;\n\n  constructor(container: HTMLElement | string, options: QuillOptions = {}) {\n    this.options = expandConfig(container, options);\n    this.container = this.options.container;\n    if (this.container == null) {\n      debug.error('Invalid Quill container', container);\n      return;\n    }\n    if (this.options.debug) {\n      Quill.debug(this.options.debug);\n    }\n    const html = this.container.innerHTML.trim();\n    this.container.classList.add('ql-container');\n    this.container.innerHTML = '';\n    instances.set(this.container, this);\n    this.root = this.addContainer('ql-editor');\n    this.root.classList.add('ql-blank');\n    this.emitter = new Emitter();\n    const scrollBlotName = Parchment.ScrollBlot.blotName;\n    const ScrollBlot = this.options.registry.query(scrollBlotName);\n    if (!ScrollBlot || !('blotName' in ScrollBlot)) {\n      throw new Error(\n        `Cannot initialize Quill without \"${scrollBlotName}\" blot`,\n      );\n    }\n    this.scroll = new ScrollBlot(this.options.registry, this.root, {\n      emitter: this.emitter,\n    }) as Scroll;\n    this.editor = new Editor(this.scroll);\n    this.selection = new Selection(this.scroll, this.emitter);\n    this.composition = new Composition(this.scroll, this.emitter);\n    this.theme = new this.options.theme(this, this.options); // eslint-disable-line new-cap\n    this.keyboard = this.theme.addModule('keyboard');\n    this.clipboard = this.theme.addModule('clipboard');\n    this.history = this.theme.addModule('history');\n    this.uploader = this.theme.addModule('uploader');\n    this.theme.addModule('input');\n    this.theme.addModule('uiNode');\n    this.theme.init();\n    this.emitter.on(Emitter.events.EDITOR_CHANGE, (type) => {\n      if (type === Emitter.events.TEXT_CHANGE) {\n        this.root.classList.toggle('ql-blank', this.editor.isBlank());\n      }\n    });\n    this.emitter.on(Emitter.events.SCROLL_UPDATE, (source, mutations) => {\n      const oldRange = this.selection.lastRange;\n      const [newRange] = this.selection.getRange();\n      const selectionInfo =\n        oldRange && newRange ? { oldRange, newRange } : undefined;\n      modify.call(\n        this,\n        () => this.editor.update(null, mutations, selectionInfo),\n        source,\n      );\n    });\n    this.emitter.on(Emitter.events.SCROLL_EMBED_UPDATE, (blot, delta) => {\n      const oldRange = this.selection.lastRange;\n      const [newRange] = this.selection.getRange();\n      const selectionInfo =\n        oldRange && newRange ? { oldRange, newRange } : undefined;\n      modify.call(\n        this,\n        () => {\n          const change = new Delta()\n            .retain(blot.offset(this))\n            .retain({ [blot.statics.blotName]: delta });\n          return this.editor.update(change, [], selectionInfo);\n        },\n        Quill.sources.USER,\n      );\n    });\n    if (html) {\n      const contents = this.clipboard.convert({\n        html: `${html}<p><br></p>`,\n        text: '\\n',\n      });\n      this.setContents(contents);\n    }\n    this.history.clear();\n    if (this.options.placeholder) {\n      this.root.setAttribute('data-placeholder', this.options.placeholder);\n    }\n    if (this.options.readOnly) {\n      this.disable();\n    }\n    this.allowReadOnlyEdits = false;\n  }\n\n  addContainer(container: string, refNode?: Node | null): HTMLDivElement;\n  addContainer(container: HTMLElement, refNode?: Node | null): HTMLElement;\n  addContainer(\n    container: string | HTMLElement,\n    refNode: Node | null = null,\n  ): HTMLDivElement | HTMLElement {\n    if (typeof container === 'string') {\n      const className = container;\n      container = document.createElement('div');\n      container.classList.add(className);\n    }\n    this.container.insertBefore(container, refNode);\n    return container;\n  }\n\n  blur() {\n    this.selection.setRange(null);\n  }\n\n  deleteText(range: Range, source?: EmitterSource): Delta;\n  deleteText(index: number, length: number, source?: EmitterSource): Delta;\n  deleteText(\n    index: number | Range,\n    length?: number | EmitterSource,\n    source?: EmitterSource,\n  ): Delta {\n    // @ts-expect-error\n    [index, length, , source] = overload(index, length, source);\n    return modify.call(\n      this,\n      () => {\n        return this.editor.deleteText(index, length);\n      },\n      source,\n      index,\n      -1 * length,\n    );\n  }\n\n  disable() {\n    this.enable(false);\n  }\n\n  editReadOnly<T>(modifier: () => T): T {\n    this.allowReadOnlyEdits = true;\n    const value = modifier();\n    this.allowReadOnlyEdits = false;\n    return value;\n  }\n\n  enable(enabled = true) {\n    this.scroll.enable(enabled);\n    this.container.classList.toggle('ql-disabled', !enabled);\n  }\n\n  focus(options: { preventScroll?: boolean } = {}) {\n    this.selection.focus();\n    if (!options.preventScroll) {\n      this.scrollSelectionIntoView();\n    }\n  }\n\n  format(\n    name: string,\n    value: unknown,\n    source: EmitterSource = Emitter.sources.API,\n  ): Delta {\n    return modify.call(\n      this,\n      () => {\n        const range = this.getSelection(true);\n        let change = new Delta();\n        if (range == null) return change;\n        if (this.scroll.query(name, Parchment.Scope.BLOCK)) {\n          change = this.editor.formatLine(range.index, range.length, {\n            [name]: value,\n          });\n        } else if (range.length === 0) {\n          this.selection.format(name, value);\n          return change;\n        } else {\n          change = this.editor.formatText(range.index, range.length, {\n            [name]: value,\n          });\n        }\n        this.setSelection(range, Emitter.sources.SILENT);\n        return change;\n      },\n      source,\n    );\n  }\n\n  formatLine(\n    index: number,\n    length: number,\n    formats: Record<string, unknown>,\n    source?: EmitterSource,\n  ): Delta;\n  formatLine(\n    index: number,\n    length: number,\n    name: string,\n    value?: unknown,\n    source?: EmitterSource,\n  ): Delta;\n  formatLine(\n    index: number,\n    length: number,\n    name: string | Record<string, unknown>,\n    value?: unknown | EmitterSource,\n    source?: EmitterSource,\n  ): Delta {\n    let formats: Record<string, unknown>;\n    // eslint-disable-next-line prefer-const\n    [index, length, formats, source] = overload(\n      index,\n      length,\n      // @ts-expect-error\n      name,\n      value,\n      source,\n    );\n    return modify.call(\n      this,\n      () => {\n        return this.editor.formatLine(index, length, formats);\n      },\n      source,\n      index,\n      0,\n    );\n  }\n\n  formatText(\n    range: Range,\n    name: string,\n    value: unknown,\n    source?: EmitterSource,\n  ): Delta;\n  formatText(\n    index: number,\n    length: number,\n    name: string,\n    value: unknown,\n    source?: EmitterSource,\n  ): Delta;\n  formatText(\n    index: number,\n    length: number,\n    formats: Record<string, unknown>,\n    source?: EmitterSource,\n  ): Delta;\n  formatText(\n    index: number | Range,\n    length: number | string,\n    name: string | unknown,\n    value?: unknown | EmitterSource,\n    source?: EmitterSource,\n  ): Delta {\n    let formats: Record<string, unknown>;\n    // eslint-disable-next-line prefer-const\n    [index, length, formats, source] = overload(\n      // @ts-expect-error\n      index,\n      length,\n      name,\n      value,\n      source,\n    );\n    return modify.call(\n      this,\n      () => {\n        return this.editor.formatText(index, length, formats);\n      },\n      source,\n      index,\n      0,\n    );\n  }\n\n  getBounds(index: number | Range, length = 0): Bounds | null {\n    let bounds: Bounds | null = null;\n    if (typeof index === 'number') {\n      bounds = this.selection.getBounds(index, length);\n    } else {\n      bounds = this.selection.getBounds(index.index, index.length);\n    }\n    if (!bounds) return null;\n    const containerBounds = this.container.getBoundingClientRect();\n    return {\n      bottom: bounds.bottom - containerBounds.top,\n      height: bounds.height,\n      left: bounds.left - containerBounds.left,\n      right: bounds.right - containerBounds.left,\n      top: bounds.top - containerBounds.top,\n      width: bounds.width,\n    };\n  }\n\n  getContents(index = 0, length = this.getLength() - index) {\n    [index, length] = overload(index, length);\n    return this.editor.getContents(index, length);\n  }\n\n  getFormat(index?: number, length?: number): { [format: string]: unknown };\n  getFormat(range?: Range): {\n    [format: string]: unknown;\n  };\n  getFormat(\n    index: Range | number = this.getSelection(true),\n    length = 0,\n  ): { [format: string]: unknown } {\n    if (typeof index === 'number') {\n      return this.editor.getFormat(index, length);\n    }\n    return this.editor.getFormat(index.index, index.length);\n  }\n\n  getIndex(blot: Parchment.Blot) {\n    return blot.offset(this.scroll);\n  }\n\n  getLength() {\n    return this.scroll.length();\n  }\n\n  getLeaf(index: number) {\n    return this.scroll.leaf(index);\n  }\n\n  getLine(index: number) {\n    return this.scroll.line(index);\n  }\n\n  getLines(range: Range): (Block | BlockEmbed)[];\n  getLines(index?: number, length?: number): (Block | BlockEmbed)[];\n  getLines(\n    index: Range | number = 0,\n    length = Number.MAX_VALUE,\n  ): (Block | BlockEmbed)[] {\n    if (typeof index !== 'number') {\n      return this.scroll.lines(index.index, index.length);\n    }\n    return this.scroll.lines(index, length);\n  }\n\n  getModule(name: string) {\n    return this.theme.modules[name];\n  }\n\n  getSelection(focus: true): Range;\n  getSelection(focus?: boolean): Range | null;\n  getSelection(focus = false): Range | null {\n    if (focus) this.focus();\n    this.update(); // Make sure we access getRange with editor in consistent state\n    return this.selection.getRange()[0];\n  }\n\n  getSemanticHTML(range: Range): string;\n  getSemanticHTML(index?: number, length?: number): string;\n  getSemanticHTML(index: Range | number = 0, length?: number) {\n    if (typeof index === 'number') {\n      length = length ?? this.getLength() - index;\n    }\n    // @ts-expect-error\n    [index, length] = overload(index, length);\n    return this.editor.getHTML(index, length);\n  }\n\n  getText(range?: Range): string;\n  getText(index?: number, length?: number): string;\n  getText(index: Range | number = 0, length?: number): string {\n    if (typeof index === 'number') {\n      length = length ?? this.getLength() - index;\n    }\n    // @ts-expect-error\n    [index, length] = overload(index, length);\n    return this.editor.getText(index, length);\n  }\n\n  hasFocus() {\n    return this.selection.hasFocus();\n  }\n\n  insertEmbed(\n    index: number,\n    embed: string,\n    value: unknown,\n    source: EmitterSource = Quill.sources.API,\n  ): Delta {\n    return modify.call(\n      this,\n      () => {\n        return this.editor.insertEmbed(index, embed, value);\n      },\n      source,\n      index,\n    );\n  }\n\n  insertText(index: number, text: string, source?: EmitterSource): Delta;\n  insertText(\n    index: number,\n    text: string,\n    formats: Record<string, unknown>,\n    source?: EmitterSource,\n  ): Delta;\n  insertText(\n    index: number,\n    text: string,\n    name: string,\n    value: unknown,\n    source?: EmitterSource,\n  ): Delta;\n  insertText(\n    index: number,\n    text: string,\n    name?: string | Record<string, unknown> | EmitterSource,\n    value?: unknown,\n    source?: EmitterSource,\n  ): Delta {\n    let formats: Record<string, unknown>;\n    // eslint-disable-next-line prefer-const\n    // @ts-expect-error\n    [index, , formats, source] = overload(index, 0, name, value, source);\n    return modify.call(\n      this,\n      () => {\n        return this.editor.insertText(index, text, formats);\n      },\n      source,\n      index,\n      text.length,\n    );\n  }\n\n  isEnabled() {\n    return this.scroll.isEnabled();\n  }\n\n  off(...args: Parameters<(typeof Emitter)['prototype']['off']>) {\n    return this.emitter.off(...args);\n  }\n\n  on(\n    event: (typeof Emitter)['events']['TEXT_CHANGE'],\n    handler: (delta: Delta, oldContent: Delta, source: EmitterSource) => void,\n  ): Emitter;\n  on(\n    event: (typeof Emitter)['events']['SELECTION_CHANGE'],\n    handler: (range: Range, oldRange: Range, source: EmitterSource) => void,\n  ): Emitter;\n  on(\n    event: (typeof Emitter)['events']['EDITOR_CHANGE'],\n    handler: (\n      ...args:\n        | [\n            (typeof Emitter)['events']['TEXT_CHANGE'],\n            Delta,\n            Delta,\n            EmitterSource,\n          ]\n        | [\n            (typeof Emitter)['events']['SELECTION_CHANGE'],\n            Range,\n            Range,\n            EmitterSource,\n          ]\n    ) => void,\n  ): Emitter;\n  on(event: string, ...args: unknown[]): Emitter;\n  on(...args: Parameters<(typeof Emitter)['prototype']['on']>): Emitter {\n    return this.emitter.on(...args);\n  }\n\n  once(...args: Parameters<(typeof Emitter)['prototype']['once']>) {\n    return this.emitter.once(...args);\n  }\n\n  removeFormat(index: number, length: number, source?: EmitterSource): Delta {\n    [index, length, , source] = overload(index, length, source);\n    return modify.call(\n      this,\n      () => {\n        return this.editor.removeFormat(index, length);\n      },\n      source,\n      index,\n    );\n  }\n\n  scrollRectIntoView(rect: Rect) {\n    scrollRectIntoView(this.root, rect);\n  }\n\n  /**\n   * @deprecated Use Quill#scrollSelectionIntoView() instead.\n   */\n  scrollIntoView() {\n    console.warn(\n      'Quill#scrollIntoView() has been deprecated and will be removed in the near future. Please use Quill#scrollSelectionIntoView() instead.',\n    );\n    this.scrollSelectionIntoView();\n  }\n\n  /**\n   * Scroll the current selection into the visible area.\n   * If the selection is already visible, no scrolling will occur.\n   */\n  scrollSelectionIntoView() {\n    const range = this.selection.lastRange;\n    const bounds = range && this.selection.getBounds(range.index, range.length);\n    if (bounds) {\n      this.scrollRectIntoView(bounds);\n    }\n  }\n\n  setContents(\n    delta: Delta | Op[],\n    source: EmitterSource = Emitter.sources.API,\n  ): Delta {\n    return modify.call(\n      this,\n      () => {\n        delta = new Delta(delta);\n        const length = this.getLength();\n        // Quill will set empty editor to \\n\n        const delete1 = this.editor.deleteText(0, length);\n        const applied = this.editor.insertContents(0, delta);\n        // Remove extra \\n from empty editor initialization\n        const delete2 = this.editor.deleteText(this.getLength() - 1, 1);\n        return delete1.compose(applied).compose(delete2);\n      },\n      source,\n    );\n  }\n  setSelection(range: Range | null, source?: EmitterSource): void;\n  setSelection(index: number, source?: EmitterSource): void;\n  setSelection(index: number, length?: number, source?: EmitterSource): void;\n  setSelection(index: number, source?: EmitterSource): void;\n  setSelection(\n    index: Range | null | number,\n    length?: EmitterSource | number,\n    source?: EmitterSource,\n  ): void {\n    if (index == null) {\n      // @ts-expect-error https://github.com/microsoft/TypeScript/issues/22609\n      this.selection.setRange(null, length || Quill.sources.API);\n    } else {\n      // @ts-expect-error\n      [index, length, , source] = overload(index, length, source);\n      this.selection.setRange(new Range(Math.max(0, index), length), source);\n      if (source !== Emitter.sources.SILENT) {\n        this.scrollSelectionIntoView();\n      }\n    }\n  }\n\n  setText(text: string, source: EmitterSource = Emitter.sources.API) {\n    const delta = new Delta().insert(text);\n    return this.setContents(delta, source);\n  }\n\n  update(source: EmitterSource = Emitter.sources.USER) {\n    const change = this.scroll.update(source); // Will update selection before selection.update() does if text changes\n    this.selection.update(source);\n    // TODO this is usually undefined\n    return change;\n  }\n\n  updateContents(\n    delta: Delta | Op[],\n    source: EmitterSource = Emitter.sources.API,\n  ): Delta {\n    return modify.call(\n      this,\n      () => {\n        delta = new Delta(delta);\n        return this.editor.applyDelta(delta);\n      },\n      source,\n      true,\n    );\n  }\n}\n\nfunction resolveSelector(selector: string | HTMLElement | null | undefined) {\n  return typeof selector === 'string'\n    ? document.querySelector<HTMLElement>(selector)\n    : selector;\n}\n\nfunction expandModuleConfig(config: Record<string, unknown> | undefined) {\n  return Object.entries(config ?? {}).reduce(\n    (expanded, [key, value]) => ({\n      ...expanded,\n      [key]: value === true ? {} : value,\n    }),\n    {} as Record<string, unknown>,\n  );\n}\n\nfunction omitUndefinedValuesFromOptions(obj: QuillOptions) {\n  return Object.fromEntries(\n    Object.entries(obj).filter((entry) => entry[1] !== undefined),\n  );\n}\n\nfunction expandConfig(\n  containerOrSelector: HTMLElement | string,\n  options: QuillOptions,\n): ExpandedQuillOptions {\n  const container = resolveSelector(containerOrSelector);\n  if (!container) {\n    throw new Error('Invalid Quill container');\n  }\n\n  const shouldUseDefaultTheme =\n    !options.theme || options.theme === Quill.DEFAULTS.theme;\n  const theme = shouldUseDefaultTheme\n    ? Theme\n    : Quill.import(`themes/${options.theme}`);\n  if (!theme) {\n    throw new Error(`Invalid theme ${options.theme}. Did you register it?`);\n  }\n\n  const { modules: quillModuleDefaults, ...quillDefaults } = Quill.DEFAULTS;\n  const { modules: themeModuleDefaults, ...themeDefaults } = theme.DEFAULTS;\n\n  let userModuleOptions = expandModuleConfig(options.modules);\n  // Special case toolbar shorthand\n  if (\n    userModuleOptions != null &&\n    userModuleOptions.toolbar &&\n    userModuleOptions.toolbar.constructor !== Object\n  ) {\n    userModuleOptions = {\n      ...userModuleOptions,\n      toolbar: { container: userModuleOptions.toolbar },\n    };\n  }\n\n  const modules: ExpandedQuillOptions['modules'] = merge(\n    {},\n    expandModuleConfig(quillModuleDefaults),\n    expandModuleConfig(themeModuleDefaults),\n    userModuleOptions,\n  );\n\n  const config = {\n    ...quillDefaults,\n    ...omitUndefinedValuesFromOptions(themeDefaults),\n    ...omitUndefinedValuesFromOptions(options),\n  };\n\n  let registry = options.registry;\n  if (registry) {\n    if (options.formats) {\n      debug.warn('Ignoring \"formats\" option because \"registry\" is specified');\n    }\n  } else {\n    registry = options.formats\n      ? createRegistryWithFormats(options.formats, config.registry, debug)\n      : config.registry;\n  }\n\n  return {\n    ...config,\n    registry,\n    container,\n    theme,\n    modules: Object.entries(modules).reduce(\n      (modulesWithDefaults, [name, value]) => {\n        if (!value) return modulesWithDefaults;\n\n        const moduleClass = Quill.import(`modules/${name}`);\n        if (moduleClass == null) {\n          debug.error(\n            `Cannot load ${name} module. Are you sure you registered it?`,\n          );\n          return modulesWithDefaults;\n        }\n        return {\n          ...modulesWithDefaults,\n          // @ts-expect-error\n          [name]: merge({}, moduleClass.DEFAULTS || {}, value),\n        };\n      },\n      {},\n    ),\n    bounds: resolveSelector(config.bounds),\n  };\n}\n\n// Handle selection preservation and TEXT_CHANGE emission\n// common to modification APIs\nfunction modify(\n  modifier: () => Delta,\n  source: EmitterSource,\n  index: number | boolean,\n  shift: number | null,\n) {\n  if (\n    !this.isEnabled() &&\n    source === Emitter.sources.USER &&\n    !this.allowReadOnlyEdits\n  ) {\n    return new Delta();\n  }\n  let range = index == null ? null : this.getSelection();\n  const oldDelta = this.editor.delta;\n  const change = modifier();\n  if (range != null) {\n    if (index === true) {\n      index = range.index; // eslint-disable-line prefer-destructuring\n    }\n    if (shift == null) {\n      range = shiftRange(range, change, source);\n    } else if (shift !== 0) {\n      // @ts-expect-error index should always be number\n      range = shiftRange(range, index, shift, source);\n    }\n    this.setSelection(range, Emitter.sources.SILENT);\n  }\n  if (change.length() > 0) {\n    const args = [Emitter.events.TEXT_CHANGE, change, oldDelta, source];\n    this.emitter.emit(Emitter.events.EDITOR_CHANGE, ...args);\n    if (source !== Emitter.sources.SILENT) {\n      this.emitter.emit(...args);\n    }\n  }\n  return change;\n}\n\ntype NormalizedIndexLength = [\n  number,\n  number,\n  Record<string, unknown>,\n  EmitterSource,\n];\nfunction overload(index: number, source?: EmitterSource): NormalizedIndexLength;\nfunction overload(\n  index: number,\n  length: number,\n  source?: EmitterSource,\n): NormalizedIndexLength;\nfunction overload(\n  index: number,\n  length: number,\n  format: string,\n  value: unknown,\n  source?: EmitterSource,\n): NormalizedIndexLength;\nfunction overload(\n  index: number,\n  length: number,\n  format: Record<string, unknown>,\n  source?: EmitterSource,\n): NormalizedIndexLength;\nfunction overload(range: Range, source?: EmitterSource): NormalizedIndexLength;\nfunction overload(\n  range: Range,\n  format: string,\n  value: unknown,\n  source?: EmitterSource,\n): NormalizedIndexLength;\nfunction overload(\n  range: Range,\n  format: Record<string, unknown>,\n  source?: EmitterSource,\n): NormalizedIndexLength;\nfunction overload(\n  index: Range | number,\n  length?: number | string | Record<string, unknown> | EmitterSource,\n  name?: string | unknown | Record<string, unknown> | EmitterSource,\n  value?: unknown | EmitterSource,\n  source?: EmitterSource,\n): NormalizedIndexLength {\n  let formats: Record<string, unknown> = {};\n  // @ts-expect-error\n  if (typeof index.index === 'number' && typeof index.length === 'number') {\n    // Allow for throwaway end (used by insertText/insertEmbed)\n    if (typeof length !== 'number') {\n      // @ts-expect-error\n      source = value;\n      value = name;\n      name = length;\n      // @ts-expect-error\n      length = index.length; // eslint-disable-line prefer-destructuring\n      // @ts-expect-error\n      index = index.index; // eslint-disable-line prefer-destructuring\n    } else {\n      // @ts-expect-error\n      length = index.length; // eslint-disable-line prefer-destructuring\n      // @ts-expect-error\n      index = index.index; // eslint-disable-line prefer-destructuring\n    }\n  } else if (typeof length !== 'number') {\n    // @ts-expect-error\n    source = value;\n    value = name;\n    name = length;\n    length = 0;\n  }\n  // Handle format being object, two format name/value strings or excluded\n  if (typeof name === 'object') {\n    // @ts-expect-error Fix me later\n    formats = name;\n    // @ts-expect-error\n    source = value;\n  } else if (typeof name === 'string') {\n    if (value != null) {\n      formats[name] = value;\n    } else {\n      // @ts-expect-error\n      source = name;\n    }\n  }\n  // Handle optional source\n  source = source || Emitter.sources.API;\n  // @ts-expect-error\n  return [index, length, formats, source];\n}\n\nfunction shiftRange(range: Range, change: Delta, source?: EmitterSource): Range;\nfunction shiftRange(\n  range: Range,\n  index: number,\n  length?: number,\n  source?: EmitterSource,\n): Range;\nfunction shiftRange(\n  range: Range,\n  index: number | Delta,\n  lengthOrSource?: number | EmitterSource,\n  source?: EmitterSource,\n) {\n  const length = typeof lengthOrSource === 'number' ? lengthOrSource : 0;\n  if (range == null) return null;\n  let start;\n  let end;\n  // @ts-expect-error -- TODO: add a better type guard around `index`\n  if (index && typeof index.transformPosition === 'function') {\n    [start, end] = [range.index, range.index + range.length].map((pos) =>\n      // @ts-expect-error -- TODO: add a better type guard around `index`\n      index.transformPosition(pos, source !== Emitter.sources.USER),\n    );\n  } else {\n    [start, end] = [range.index, range.index + range.length].map((pos) => {\n      // @ts-expect-error -- TODO: add a better type guard around `index`\n      if (pos < index || (pos === index && source === Emitter.sources.USER))\n        return pos;\n      if (length >= 0) {\n        return pos + length;\n      }\n      // @ts-expect-error -- TODO: add a better type guard around `index`\n      return Math.max(index, pos + length);\n    });\n  }\n  return new Range(start, end - start);\n}\n\nexport type { Bounds, DebugLevel, EmitterSource };\nexport { Parchment, Range };\n\nexport { globalRegistry, expandConfig, overload, Quill as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAC,uBAAA,CAAAF,OAAA;AAAsCG,OAAA,CAAAF,SAAA,GAAAA,SAAA;AAEtC,IAAAG,WAAA,GAAAC,sBAAA,CAAAL,OAAA;AAQA,IAAAM,OAAA,GAAAD,sBAAA,CAAAL,OAAA;AACA,IAAAO,QAAA,GAAAF,sBAAA,CAAAL,OAAA;AAEA,IAAAQ,UAAA,GAAAH,sBAAA,CAAAL,OAAA;AACA,IAAAS,OAAA,GAAAJ,sBAAA,CAAAL,OAAA;AAEA,IAAAU,OAAA,GAAAL,sBAAA,CAAAL,OAAA;AACA,IAAAW,UAAA,GAAAT,uBAAA,CAAAF,OAAA;AAEA,IAAAY,YAAA,GAAAP,sBAAA,CAAAL,OAAA;AACA,IAAAa,MAAA,GAAAR,sBAAA,CAAAL,OAAA;AAEA,IAAAc,oBAAA,GAAAT,sBAAA,CAAAL,OAAA;AAEA,IAAAe,0BAAA,GAAAV,sBAAA,CAAAL,OAAA;AAA4E,IAAAgB,SAAA;EAAAC,UAAA;AAE5E,IAAMC,KAAK,GAAG,IAAAC,eAAM,EAAC,OAAO,CAAC;AAE7B,IAAMC,cAAc,GAAAjB,OAAA,CAAAiB,cAAA,GAAG,IAAInB,SAAS,CAACoB,QAAQ,CAAC,CAAC;AAC/CpB,SAAS,CAACqB,UAAU,CAACC,OAAO,GAAG,OAAO;;AAEtC;AACA;AACA;;AA2BA;AACA;AACA;AACA;AAHA,IAcMC,KAAK,GAAArB,OAAA,CAAAsB,OAAA;EAuHT,SAAAD,MAAYE,SAA+B,EAA8B;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAH,OAAA,QAAAD,KAAA;IAAA,IAA5BK,OAAqB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IACrE,IAAI,CAACD,OAAO,GAAGI,YAAY,CAACP,SAAS,EAAEG,OAAO,CAAC;IAC/C,IAAI,CAACH,SAAS,GAAG,IAAI,CAACG,OAAO,CAACH,SAAS;IACvC,IAAI,IAAI,CAACA,SAAS,IAAI,IAAI,EAAE;MAC1BR,KAAK,CAACgB,KAAK,CAAC,yBAAyB,EAAER,SAAS,CAAC;MACjD;IACF;IACA,IAAI,IAAI,CAACG,OAAO,CAACX,KAAK,EAAE;MACtBM,KAAK,CAACN,KAAK,CAAC,IAAI,CAACW,OAAO,CAACX,KAAK,CAAC;IACjC;IACA,IAAMiB,IAAI,GAAG,IAAI,CAACT,SAAS,CAACU,SAAS,CAACC,IAAI,CAAC,CAAC;IAC5C,IAAI,CAACX,SAAS,CAACY,SAAS,CAACC,GAAG,CAAC,cAAc,CAAC;IAC5C,IAAI,CAACb,SAAS,CAACU,SAAS,GAAG,EAAE;IAC7BI,kBAAS,CAACC,GAAG,CAAC,IAAI,CAACf,SAAS,EAAE,IAAI,CAAC;IACnC,IAAI,CAACgB,IAAI,GAAG,IAAI,CAACC,YAAY,CAAC,WAAW,CAAC;IAC1C,IAAI,CAACD,IAAI,CAACJ,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;IACnC,IAAI,CAACK,OAAO,GAAG,IAAIC,gBAAO,CAAC,CAAC;IAC5B,IAAMC,cAAc,GAAG7C,SAAS,CAAC8C,UAAU,CAACC,QAAQ;IACpD,IAAMD,UAAU,GAAG,IAAI,CAAClB,OAAO,CAACoB,QAAQ,CAACC,KAAK,CAACJ,cAAc,CAAC;IAC9D,IAAI,CAACC,UAAU,IAAI,EAAE,UAAU,IAAIA,UAAU,CAAC,EAAE;MAC9C,MAAM,IAAII,KAAK,sCAAAC,MAAA,CACuBN,cAAe,YACrD,CAAC;IACH;IACA,IAAI,CAACO,MAAM,GAAG,IAAIN,UAAU,CAAC,IAAI,CAAClB,OAAO,CAACoB,QAAQ,EAAE,IAAI,CAACP,IAAI,EAAE;MAC7DE,OAAO,EAAE,IAAI,CAACA;IAChB,CAAC,CAAW;IACZ,IAAI,CAACU,MAAM,GAAG,IAAIC,eAAM,CAAC,IAAI,CAACF,MAAM,CAAC;IACrC,IAAI,CAACG,SAAS,GAAG,IAAIC,kBAAS,CAAC,IAAI,CAACJ,MAAM,EAAE,IAAI,CAACT,OAAO,CAAC;IACzD,IAAI,CAACc,WAAW,GAAG,IAAIC,oBAAW,CAAC,IAAI,CAACN,MAAM,EAAE,IAAI,CAACT,OAAO,CAAC;IAC7D,IAAI,CAACgB,KAAK,GAAG,IAAI,IAAI,CAAC/B,OAAO,CAAC+B,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC/B,OAAO,CAAC,CAAC,CAAC;IACzD,IAAI,CAACgC,QAAQ,GAAG,IAAI,CAACD,KAAK,CAACE,SAAS,CAAC,UAAU,CAAC;IAChD,IAAI,CAACC,SAAS,GAAG,IAAI,CAACH,KAAK,CAACE,SAAS,CAAC,WAAW,CAAC;IAClD,IAAI,CAACE,OAAO,GAAG,IAAI,CAACJ,KAAK,CAACE,SAAS,CAAC,SAAS,CAAC;IAC9C,IAAI,CAACG,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACE,SAAS,CAAC,UAAU,CAAC;IAChD,IAAI,CAACF,KAAK,CAACE,SAAS,CAAC,OAAO,CAAC;IAC7B,IAAI,CAACF,KAAK,CAACE,SAAS,CAAC,QAAQ,CAAC;IAC9B,IAAI,CAACF,KAAK,CAACM,IAAI,CAAC,CAAC;IACjB,IAAI,CAACtB,OAAO,CAACuB,EAAE,CAACtB,gBAAO,CAACuB,MAAM,CAACC,aAAa,EAAG,UAAAC,IAAI,EAAK;MACtD,IAAIA,IAAI,KAAKzB,gBAAO,CAACuB,MAAM,CAACG,WAAW,EAAE;QACvC5C,KAAI,CAACe,IAAI,CAACJ,SAAS,CAACkC,MAAM,CAAC,UAAU,EAAE7C,KAAI,CAAC2B,MAAM,CAACmB,OAAO,CAAC,CAAC,CAAC;MAC/D;IACF,CAAC,CAAC;IACF,IAAI,CAAC7B,OAAO,CAACuB,EAAE,CAACtB,gBAAO,CAACuB,MAAM,CAACM,aAAa,EAAE,UAACC,MAAM,EAAEC,SAAS,EAAK;MACnE,IAAMC,QAAQ,GAAGlD,KAAI,CAAC6B,SAAS,CAACsB,SAAS;MACzC,IAAAC,qBAAA,GAAmBpD,KAAI,CAAC6B,SAAS,CAACwB,QAAQ,CAAC,CAAC;QAAAC,sBAAA,OAAAC,eAAA,CAAAzD,OAAA,EAAAsD,qBAAA;QAArCI,QAAQ,GAAAF,sBAAA;MACf,IAAMG,aAAa,GACjBP,QAAQ,IAAIM,QAAQ,GAAG;QAAEN,QAAQ,EAARA,QAAQ;QAAEM,QAAA,EAAAA;MAAS,CAAC,GAAGnD,SAAS;MAC3DqD,MAAM,CAACC,IAAI,CACT3D,KAAI,EACJ;QAAA,OAAMA,KAAI,CAAC2B,MAAM,CAACiC,MAAM,CAAC,IAAI,EAAEX,SAAS,EAAEQ,aAAa,CAAC;MAAA,GACxDT,MACF,CAAC;IACH,CAAC,CAAC;IACF,IAAI,CAAC/B,OAAO,CAACuB,EAAE,CAACtB,gBAAO,CAACuB,MAAM,CAACoB,mBAAmB,EAAE,UAACC,IAAI,EAAEC,KAAK,EAAK;MACnE,IAAMb,QAAQ,GAAGlD,KAAI,CAAC6B,SAAS,CAACsB,SAAS;MACzC,IAAAa,sBAAA,GAAmBhE,KAAI,CAAC6B,SAAS,CAACwB,QAAQ,CAAC,CAAC;QAAAY,sBAAA,OAAAV,eAAA,CAAAzD,OAAA,EAAAkE,sBAAA;QAArCR,QAAQ,GAAAS,sBAAA;MACf,IAAMR,aAAa,GACjBP,QAAQ,IAAIM,QAAQ,GAAG;QAAEN,QAAQ,EAARA,QAAQ;QAAEM,QAAA,EAAAA;MAAS,CAAC,GAAGnD,SAAS;MAC3DqD,MAAM,CAACC,IAAI,CACT3D,KAAI,EACJ,YAAM;QACJ,IAAMkE,MAAM,GAAG,IAAIC,mBAAK,CAAC,CAAC,CACvBC,MAAM,CAACN,IAAI,CAACO,MAAM,CAACrE,KAAI,CAAC,CAAC,CACzBoE,MAAM,KAAAE,gBAAA,CAAAxE,OAAA,MAAIgE,IAAI,CAACS,OAAO,CAAClD,QAAQ,EAAG0C,KAAA,CAAO,CAAC;QAC7C,OAAO/D,KAAI,CAAC2B,MAAM,CAACiC,MAAM,CAACM,MAAM,EAAE,EAAE,EAAET,aAAa,CAAC;MACtD,CAAC,EACD5D,KAAK,CAAC2E,OAAO,CAACC,IAChB,CAAC;IACH,CAAC,CAAC;IACF,IAAIjE,IAAI,EAAE;MACR,IAAMkE,QAAQ,GAAG,IAAI,CAACtC,SAAS,CAACuC,OAAO,CAAC;QACtCnE,IAAI,KAAAiB,MAAA,CAAKjB,IAAK,gBAAY;QAC1BoE,IAAI,EAAE;MACR,CAAC,CAAC;MACF,IAAI,CAACC,WAAW,CAACH,QAAQ,CAAC;IAC5B;IACA,IAAI,CAACrC,OAAO,CAACyC,KAAK,CAAC,CAAC;IACpB,IAAI,IAAI,CAAC5E,OAAO,CAAC6E,WAAW,EAAE;MAC5B,IAAI,CAAChE,IAAI,CAACiE,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC9E,OAAO,CAAC6E,WAAW,CAAC;IACtE;IACA,IAAI,IAAI,CAAC7E,OAAO,CAAC+E,QAAQ,EAAE;MACzB,IAAI,CAACC,OAAO,CAAC,CAAC;IAChB;IACA,IAAI,CAACC,kBAAkB,GAAG,KAAK;EACjC;EAAA,WAAAC,aAAA,CAAAtF,OAAA,EAAAD,KAAA;IAAAwF,GAAA;IAAAC,KAAA,EAIA,SAAAtE,YAAYA,CACVjB,SAA+B,EAED;MAAA,IAD9BwF,OAAoB,GAAApF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MAE3B,IAAI,OAAOJ,SAAS,KAAK,QAAQ,EAAE;QACjC,IAAMyF,SAAS,GAAGzF,SAAS;QAC3BA,SAAS,GAAG0F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACzC3F,SAAS,CAACY,SAAS,CAACC,GAAG,CAAC4E,SAAS,CAAC;MACpC;MACA,IAAI,CAACzF,SAAS,CAAC4F,YAAY,CAAC5F,SAAS,EAAEwF,OAAO,CAAC;MAC/C,OAAOxF,SAAS;IAClB;EAAA;IAAAsF,GAAA;IAAAC,KAAA,EAEA,SAAAM,IAAIA,CAAA,EAAG;MACL,IAAI,CAAC/D,SAAS,CAACgE,QAAQ,CAAC,IAAI,CAAC;IAC/B;EAAA;IAAAR,GAAA;IAAAC,KAAA,EAIA,SAAAQ,UAAUA,CACRC,KAAqB,EACrB3F,MAA+B,EAC/B4C,MAAsB,EACf;MAAA,IAAAgD,MAAA;MACP;MAAA,IAAAC,SAAA,GAC4BC,QAAQ,CAACH,KAAK,EAAE3F,MAAM,EAAE4C,MAAM,CAAC;MAAA,IAAAmD,UAAA,OAAA5C,eAAA,CAAAzD,OAAA,EAAAmG,SAAA;MAA1DF,KAAK,GAAAI,UAAA;MAAE/F,MAAM,GAAA+F,UAAA;MAAInD,MAAM,GAAAmD,UAAA;MACxB,OAAOzC,MAAM,CAACC,IAAI,CAChB,IAAI,EACJ,YAAM;QACJ,OAAOqC,MAAI,CAACrE,MAAM,CAACmE,UAAU,CAACC,KAAK,EAAE3F,MAAM,CAAC;MAC9C,CAAC,EACD4C,MAAM,EACN+C,KAAK,EACL,CAAC,CAAC,GAAG3F,MACP,CAAC;IACH;EAAA;IAAAiF,GAAA;IAAAC,KAAA,EAEA,SAAAJ,OAAOA,CAAA,EAAG;MACR,IAAI,CAACkB,MAAM,CAAC,KAAK,CAAC;IACpB;EAAA;IAAAf,GAAA;IAAAC,KAAA,EAEA,SAAAe,YAAYA,CAAIC,QAAiB,EAAK;MACpC,IAAI,CAACnB,kBAAkB,GAAG,IAAI;MAC9B,IAAMG,KAAK,GAAGgB,QAAQ,CAAC,CAAC;MACxB,IAAI,CAACnB,kBAAkB,GAAG,KAAK;MAC/B,OAAOG,KAAK;IACd;EAAA;IAAAD,GAAA;IAAAC,KAAA,EAEA,SAAAc,MAAMA,CAAA,EAAiB;MAAA,IAAhBG,OAAO,GAAApG,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MACnB,IAAI,CAACuB,MAAM,CAAC0E,MAAM,CAACG,OAAO,CAAC;MAC3B,IAAI,CAACxG,SAAS,CAACY,SAAS,CAACkC,MAAM,CAAC,aAAa,EAAE,CAAC0D,OAAO,CAAC;IAC1D;EAAA;IAAAlB,GAAA;IAAAC,KAAA,EAEA,SAAAkB,KAAKA,CAAA,EAA4C;MAAA,IAA3CtG,OAAoC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAC7C,IAAI,CAAC0B,SAAS,CAAC2E,KAAK,CAAC,CAAC;MACtB,IAAI,CAACtG,OAAO,CAACuG,aAAa,EAAE;QAC1B,IAAI,CAACC,uBAAuB,CAAC,CAAC;MAChC;IACF;EAAA;IAAArB,GAAA;IAAAC,KAAA,EAEA,SAAAqB,MAAMA,CACJC,IAAY,EACZtB,KAAc,EAEP;MAAA,IAAAuB,MAAA;MAAA,IADP7D,MAAqB,GAAA7C,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGe,gBAAO,CAACsD,OAAO,CAACsC,GAAG;MAE3C,OAAOpD,MAAM,CAACC,IAAI,CAChB,IAAI,EACJ,YAAM;QACJ,IAAMoD,KAAK,GAAGF,MAAI,CAACG,YAAY,CAAC,IAAI,CAAC;QACrC,IAAI9C,MAAM,GAAG,IAAIC,mBAAK,CAAC,CAAC;QACxB,IAAI4C,KAAK,IAAI,IAAI,EAAE,OAAO7C,MAAM;QAChC,IAAI2C,MAAI,CAACnF,MAAM,CAACH,KAAK,CAACqF,IAAI,EAAEtI,SAAS,CAAC2I,KAAK,CAACC,KAAK,CAAC,EAAE;UAClDhD,MAAM,GAAG2C,MAAI,CAAClF,MAAM,CAACwF,UAAU,CAACJ,KAAK,CAAChB,KAAK,EAAEgB,KAAK,CAAC3G,MAAM,MAAAkE,gBAAA,CAAAxE,OAAA,MACtD8G,IAAI,EAAGtB,KAAA,CACT,CAAC;QACJ,CAAC,MAAM,IAAIyB,KAAK,CAAC3G,MAAM,KAAK,CAAC,EAAE;UAC7ByG,MAAI,CAAChF,SAAS,CAAC8E,MAAM,CAACC,IAAI,EAAEtB,KAAK,CAAC;UAClC,OAAOpB,MAAM;QACf,CAAC,MAAM;UACLA,MAAM,GAAG2C,MAAI,CAAClF,MAAM,CAACyF,UAAU,CAACL,KAAK,CAAChB,KAAK,EAAEgB,KAAK,CAAC3G,MAAM,MAAAkE,gBAAA,CAAAxE,OAAA,MACtD8G,IAAI,EAAGtB,KAAA,CACT,CAAC;QACJ;QACAuB,MAAI,CAACQ,YAAY,CAACN,KAAK,EAAE7F,gBAAO,CAACsD,OAAO,CAAC8C,MAAM,CAAC;QAChD,OAAOpD,MAAM;MACf,CAAC,EACDlB,MACF,CAAC;IACH;EAAA;IAAAqC,GAAA;IAAAC,KAAA,EAeA,SAAA6B,UAAUA,CACRpB,KAAa,EACb3F,MAAc,EACdwG,IAAsC,EACtCtB,KAA+B,EAC/BtC,MAAsB,EACf;MAAA,IAAAuE,MAAA;MACP,IAAIC,OAAgC;MACpC;MAAA,IAAAC,UAAA,GACmCvB,QAAQ,CACzCH,KAAK,EACL3F,MAAM;MACN;MACAwG,IAAI,EACJtB,KAAK,EACLtC,MACF,CAAC;MAAA,IAAA0E,UAAA,OAAAnE,eAAA,CAAAzD,OAAA,EAAA2H,UAAA;MAPA1B,KAAK,GAAA2B,UAAA;MAAEtH,MAAM,GAAAsH,UAAA;MAAEF,OAAO,GAAAE,UAAA;MAAE1E,MAAM,GAAA0E,UAAA;MAQ/B,OAAOhE,MAAM,CAACC,IAAI,CAChB,IAAI,EACJ,YAAM;QACJ,OAAO4D,MAAI,CAAC5F,MAAM,CAACwF,UAAU,CAACpB,KAAK,EAAE3F,MAAM,EAAEoH,OAAO,CAAC;MACvD,CAAC,EACDxE,MAAM,EACN+C,KAAK,EACL,CACF,CAAC;IACH;EAAA;IAAAV,GAAA;IAAAC,KAAA,EAqBA,SAAA8B,UAAUA,CACRrB,KAAqB,EACrB3F,MAAuB,EACvBwG,IAAsB,EACtBtB,KAA+B,EAC/BtC,MAAsB,EACf;MAAA,IAAA2E,MAAA;MACP,IAAIH,OAAgC;MACpC;MAAA,IAAAI,UAAA,GACmC1B,QAAQ;MACzC;MACAH,KAAK,EACL3F,MAAM,EACNwG,IAAI,EACJtB,KAAK,EACLtC,MACF,CAAC;MAAA,IAAA6E,UAAA,OAAAtE,eAAA,CAAAzD,OAAA,EAAA8H,UAAA;MAPA7B,KAAK,GAAA8B,UAAA;MAAEzH,MAAM,GAAAyH,UAAA;MAAEL,OAAO,GAAAK,UAAA;MAAE7E,MAAM,GAAA6E,UAAA;MAQ/B,OAAOnE,MAAM,CAACC,IAAI,CAChB,IAAI,EACJ,YAAM;QACJ,OAAOgE,MAAI,CAAChG,MAAM,CAACyF,UAAU,CAACrB,KAAK,EAAE3F,MAAM,EAAEoH,OAAO,CAAC;MACvD,CAAC,EACDxE,MAAM,EACN+C,KAAK,EACL,CACF,CAAC;IACH;EAAA;IAAAV,GAAA;IAAAC,KAAA,EAEA,SAAAwC,SAASA,CAAC/B,KAAqB,EAA6B;MAAA,IAA3B3F,MAAM,GAAAD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;MACzC,IAAI4H,MAAqB,GAAG,IAAI;MAChC,IAAI,OAAOhC,KAAK,KAAK,QAAQ,EAAE;QAC7BgC,MAAM,GAAG,IAAI,CAAClG,SAAS,CAACiG,SAAS,CAAC/B,KAAK,EAAE3F,MAAM,CAAC;MAClD,CAAC,MAAM;QACL2H,MAAM,GAAG,IAAI,CAAClG,SAAS,CAACiG,SAAS,CAAC/B,KAAK,CAACA,KAAK,EAAEA,KAAK,CAAC3F,MAAM,CAAC;MAC9D;MACA,IAAI,CAAC2H,MAAM,EAAE,OAAO,IAAI;MACxB,IAAMC,eAAe,GAAG,IAAI,CAACjI,SAAS,CAACkI,qBAAqB,CAAC,CAAC;MAC9D,OAAO;QACLC,MAAM,EAAEH,MAAM,CAACG,MAAM,GAAGF,eAAe,CAACG,GAAG;QAC3CC,MAAM,EAAEL,MAAM,CAACK,MAAM;QACrBC,IAAI,EAAEN,MAAM,CAACM,IAAI,GAAGL,eAAe,CAACK,IAAI;QACxCC,KAAK,EAAEP,MAAM,CAACO,KAAK,GAAGN,eAAe,CAACK,IAAI;QAC1CF,GAAG,EAAEJ,MAAM,CAACI,GAAG,GAAGH,eAAe,CAACG,GAAG;QACrCI,KAAK,EAAER,MAAM,CAACQ;MAChB,CAAC;IACH;EAAA;IAAAlD,GAAA;IAAAC,KAAA,EAEA,SAAAkD,WAAWA,CAAA,EAA+C;MAAA,IAA9CzC,KAAK,GAAA5F,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;MAAA,IAAEC,MAAM,GAAAD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI,CAACsI,SAAS,CAAC,CAAC,GAAG1C,KAAK;MAAA,IAAA2C,UAAA,GACpCxC,QAAQ,CAACH,KAAK,EAAE3F,MAAM,CAAC;MAAA,IAAAuI,UAAA,OAAApF,eAAA,CAAAzD,OAAA,EAAA4I,UAAA;MAAxC3C,KAAK,GAAA4C,UAAA;MAAEvI,MAAM,GAAAuI,UAAA;MACd,OAAO,IAAI,CAAChH,MAAM,CAAC6G,WAAW,CAACzC,KAAK,EAAE3F,MAAM,CAAC;IAC/C;EAAA;IAAAiF,GAAA;IAAAC,KAAA,EAMA,SAAAsD,SAASA,CAAA,EAGwB;MAAA,IAF/B7C,KAAqB,GAAA5F,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI,CAAC6G,YAAY,CAAC,IAAI,CAAC;MAAA,IAC/C5G,MAAM,GAAAD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;MAEV,IAAI,OAAO4F,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAO,IAAI,CAACpE,MAAM,CAACiH,SAAS,CAAC7C,KAAK,EAAE3F,MAAM,CAAC;MAC7C;MACA,OAAO,IAAI,CAACuB,MAAM,CAACiH,SAAS,CAAC7C,KAAK,CAACA,KAAK,EAAEA,KAAK,CAAC3F,MAAM,CAAC;IACzD;EAAA;IAAAiF,GAAA;IAAAC,KAAA,EAEA,SAAAuD,QAAQA,CAAC/E,IAAoB,EAAE;MAC7B,OAAOA,IAAI,CAACO,MAAM,CAAC,IAAI,CAAC3C,MAAM,CAAC;IACjC;EAAA;IAAA2D,GAAA;IAAAC,KAAA,EAEA,SAAAmD,SAASA,CAAA,EAAG;MACV,OAAO,IAAI,CAAC/G,MAAM,CAACtB,MAAM,CAAC,CAAC;IAC7B;EAAA;IAAAiF,GAAA;IAAAC,KAAA,EAEA,SAAAwD,OAAOA,CAAC/C,KAAa,EAAE;MACrB,OAAO,IAAI,CAACrE,MAAM,CAACqH,IAAI,CAAChD,KAAK,CAAC;IAChC;EAAA;IAAAV,GAAA;IAAAC,KAAA,EAEA,SAAA0D,OAAOA,CAACjD,KAAa,EAAE;MACrB,OAAO,IAAI,CAACrE,MAAM,CAACuH,IAAI,CAAClD,KAAK,CAAC;IAChC;EAAA;IAAAV,GAAA;IAAAC,KAAA,EAIA,SAAA4D,QAAQA,CAAA,EAGkB;MAAA,IAFxBnD,KAAqB,GAAA5F,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;MAAA,IACzBC,MAAM,GAAAD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGgJ,MAAM,CAACC,SAAS;MAEzB,IAAI,OAAOrD,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAO,IAAI,CAACrE,MAAM,CAAC2H,KAAK,CAACtD,KAAK,CAACA,KAAK,EAAEA,KAAK,CAAC3F,MAAM,CAAC;MACrD;MACA,OAAO,IAAI,CAACsB,MAAM,CAAC2H,KAAK,CAACtD,KAAK,EAAE3F,MAAM,CAAC;IACzC;EAAA;IAAAiF,GAAA;IAAAC,KAAA,EAEA,SAAAgE,SAASA,CAAC1C,IAAY,EAAE;MACtB,OAAO,IAAI,CAAC3E,KAAK,CAACsH,OAAO,CAAC3C,IAAI,CAAC;IACjC;EAAA;IAAAvB,GAAA;IAAAC,KAAA,EAIA,SAAA0B,YAAYA,CAAA,EAA8B;MAAA,IAA7BR,KAAK,GAAArG,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MACxB,IAAIqG,KAAK,EAAE,IAAI,CAACA,KAAK,CAAC,CAAC;MACvB,IAAI,CAAC5C,MAAM,CAAC,CAAC,CAAC,CAAC;MACf,OAAO,IAAI,CAAC/B,SAAS,CAACwB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC;EAAA;IAAAgC,GAAA;IAAAC,KAAA,EAIA,SAAAkE,eAAeA,CAAA,EAA6C;MAAA,IAA5CzD,KAAqB,GAAA5F,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;MAAA,IAAEC,MAAe,GAAAD,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;MACxD,IAAI,OAAO0F,KAAK,KAAK,QAAQ,EAAE;QAC7B3F,MAAM,GAAGA,MAAM,aAANA,MAAM,cAANA,MAAM,GAAI,IAAI,CAACqI,SAAS,CAAC,CAAC,GAAG1C,KAAK;MAC7C;MACA;MAAA,IAAA0D,UAAA,GACkBvD,QAAQ,CAACH,KAAK,EAAE3F,MAAM,CAAC;MAAA,IAAAsJ,UAAA,OAAAnG,eAAA,CAAAzD,OAAA,EAAA2J,UAAA;MAAxC1D,KAAK,GAAA2D,UAAA;MAAEtJ,MAAM,GAAAsJ,UAAA;MACd,OAAO,IAAI,CAAC/H,MAAM,CAACgI,OAAO,CAAC5D,KAAK,EAAE3F,MAAM,CAAC;IAC3C;EAAA;IAAAiF,GAAA;IAAAC,KAAA,EAIA,SAAAsE,OAAOA,CAAA,EAAqD;MAAA,IAApD7D,KAAqB,GAAA5F,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;MAAA,IAAEC,MAAe,GAAAD,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;MAChD,IAAI,OAAO0F,KAAK,KAAK,QAAQ,EAAE;QAC7B3F,MAAM,GAAGA,MAAM,aAANA,MAAM,cAANA,MAAM,GAAI,IAAI,CAACqI,SAAS,CAAC,CAAC,GAAG1C,KAAK;MAC7C;MACA;MAAA,IAAA8D,UAAA,GACkB3D,QAAQ,CAACH,KAAK,EAAE3F,MAAM,CAAC;MAAA,IAAA0J,WAAA,OAAAvG,eAAA,CAAAzD,OAAA,EAAA+J,UAAA;MAAxC9D,KAAK,GAAA+D,WAAA;MAAE1J,MAAM,GAAA0J,WAAA;MACd,OAAO,IAAI,CAACnI,MAAM,CAACiI,OAAO,CAAC7D,KAAK,EAAE3F,MAAM,CAAC;IAC3C;EAAA;IAAAiF,GAAA;IAAAC,KAAA,EAEA,SAAAyE,QAAQA,CAAA,EAAG;MACT,OAAO,IAAI,CAAClI,SAAS,CAACkI,QAAQ,CAAC,CAAC;IAClC;EAAA;IAAA1E,GAAA;IAAAC,KAAA,EAEA,SAAA0E,WAAWA,CACTjE,KAAa,EACbkE,KAAa,EACb3E,KAAc,EAEP;MAAA,IAAA4E,MAAA;MAAA,IADPlH,MAAqB,GAAA7C,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGN,KAAK,CAAC2E,OAAO,CAACsC,GAAG;MAEzC,OAAOpD,MAAM,CAACC,IAAI,CAChB,IAAI,EACJ,YAAM;QACJ,OAAOuG,MAAI,CAACvI,MAAM,CAACqI,WAAW,CAACjE,KAAK,EAAEkE,KAAK,EAAE3E,KAAK,CAAC;MACrD,CAAC,EACDtC,MAAM,EACN+C,KACF,CAAC;IACH;EAAA;IAAAV,GAAA;IAAAC,KAAA,EAgBA,SAAA6E,UAAUA,CACRpE,KAAa,EACbnB,IAAY,EACZgC,IAAuD,EACvDtB,KAAe,EACftC,MAAsB,EACf;MAAA,IAAAoH,MAAA;MACP,IAAI5C,OAAgC;MACpC;MACA;MAAA,IAAA6C,WAAA,GAC6BnE,QAAQ,CAACH,KAAK,EAAE,CAAC,EAAEa,IAAI,EAAEtB,KAAK,EAAEtC,MAAM,CAAC;MAAA,IAAAsH,WAAA,OAAA/G,eAAA,CAAAzD,OAAA,EAAAuK,WAAA;MAAnEtE,KAAK,GAAAuE,WAAA;MAAI9C,OAAO,GAAA8C,WAAA;MAAEtH,MAAM,GAAAsH,WAAA;MACzB,OAAO5G,MAAM,CAACC,IAAI,CAChB,IAAI,EACJ,YAAM;QACJ,OAAOyG,MAAI,CAACzI,MAAM,CAACwI,UAAU,CAACpE,KAAK,EAAEnB,IAAI,EAAE4C,OAAO,CAAC;MACrD,CAAC,EACDxE,MAAM,EACN+C,KAAK,EACLnB,IAAI,CAACxE,MACP,CAAC;IACH;EAAA;IAAAiF,GAAA;IAAAC,KAAA,EAEA,SAAAiF,SAASA,CAAA,EAAG;MACV,OAAO,IAAI,CAAC7I,MAAM,CAAC6I,SAAS,CAAC,CAAC;IAChC;EAAA;IAAAlF,GAAA;IAAAC,KAAA,EAEA,SAAAkF,GAAGA,CAAA,EAA4D;MAAA,IAAAC,aAAA;MAC7D,OAAO,CAAAA,aAAA,OAAI,CAACxJ,OAAO,EAACuJ,GAAG,CAAAE,KAAA,CAAAD,aAAA,EAACtK,SAAO,CAAC;IAClC;EAAA;IAAAkF,GAAA;IAAAC,KAAA,EA6BA,SAAA9C,EAAEA,CAAA,EAAoE;MAAA,IAAAmI,cAAA;MACpE,OAAO,CAAAA,cAAA,OAAI,CAAC1J,OAAO,EAACuB,EAAE,CAAAkI,KAAA,CAAAC,cAAA,EAACxK,SAAO,CAAC;IACjC;EAAA;IAAAkF,GAAA;IAAAC,KAAA,EAEA,SAAAsF,IAAIA,CAAA,EAA6D;MAAA,IAAAC,cAAA;MAC/D,OAAO,CAAAA,cAAA,OAAI,CAAC5J,OAAO,EAAC2J,IAAI,CAAAF,KAAA,CAAAG,cAAA,EAAC1K,SAAO,CAAC;IACnC;EAAA;IAAAkF,GAAA;IAAAC,KAAA,EAEA,SAAAwF,YAAYA,CAAC/E,KAAa,EAAE3F,MAAc,EAAE4C,MAAsB,EAAS;MAAA,IAAA+H,MAAA;MAAA,IAAAC,WAAA,GAC7C9E,QAAQ,CAACH,KAAK,EAAE3F,MAAM,EAAE4C,MAAM,CAAC;MAAA,IAAAiI,WAAA,OAAA1H,eAAA,CAAAzD,OAAA,EAAAkL,WAAA;MAA1DjF,KAAK,GAAAkF,WAAA;MAAE7K,MAAM,GAAA6K,WAAA;MAAIjI,MAAM,GAAAiI,WAAA;MACxB,OAAOvH,MAAM,CAACC,IAAI,CAChB,IAAI,EACJ,YAAM;QACJ,OAAOoH,MAAI,CAACpJ,MAAM,CAACmJ,YAAY,CAAC/E,KAAK,EAAE3F,MAAM,CAAC;MAChD,CAAC,EACD4C,MAAM,EACN+C,KACF,CAAC;IACH;EAAA;IAAAV,GAAA;IAAAC,KAAA,EAEA,SAAA4F,kBAAkBA,CAACC,IAAU,EAAE;MAC7B,IAAAD,4BAAkB,EAAC,IAAI,CAACnK,IAAI,EAAEoK,IAAI,CAAC;IACrC;;IAEA;AACF;AACA;EAFE;IAAA9F,GAAA;IAAAC,KAAA,EAGA,SAAA8F,cAAcA,CAAA,EAAG;MACfC,OAAO,CAACC,IAAI,CACV,wIACF,CAAC;MACD,IAAI,CAAC5E,uBAAuB,CAAC,CAAC;IAChC;;IAEA;AACF;AACA;AACA;EAHE;IAAArB,GAAA;IAAAC,KAAA,EAIA,SAAAoB,uBAAuBA,CAAA,EAAG;MACxB,IAAMK,KAAK,GAAG,IAAI,CAAClF,SAAS,CAACsB,SAAS;MACtC,IAAM4E,MAAM,GAAGhB,KAAK,IAAI,IAAI,CAAClF,SAAS,CAACiG,SAAS,CAACf,KAAK,CAAChB,KAAK,EAAEgB,KAAK,CAAC3G,MAAM,CAAC;MAC3E,IAAI2H,MAAM,EAAE;QACV,IAAI,CAACmD,kBAAkB,CAACnD,MAAM,CAAC;MACjC;IACF;EAAA;IAAA1C,GAAA;IAAAC,KAAA,EAEA,SAAAT,WAAWA,CACTd,KAAmB,EAEZ;MAAA,IAAAwH,MAAA;MAAA,IADPvI,MAAqB,GAAA7C,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGe,gBAAO,CAACsD,OAAO,CAACsC,GAAG;MAE3C,OAAOpD,MAAM,CAACC,IAAI,CAChB,IAAI,EACJ,YAAM;QACJI,KAAK,GAAG,IAAII,mBAAK,CAACJ,KAAK,CAAC;QACxB,IAAM3D,MAAM,GAAGmL,MAAI,CAAC9C,SAAS,CAAC,CAAC;QAC/B;QACA,IAAM+C,OAAO,GAAGD,MAAI,CAAC5J,MAAM,CAACmE,UAAU,CAAC,CAAC,EAAE1F,MAAM,CAAC;QACjD,IAAMqL,OAAO,GAAGF,MAAI,CAAC5J,MAAM,CAAC+J,cAAc,CAAC,CAAC,EAAE3H,KAAK,CAAC;QACpD;QACA,IAAM4H,OAAO,GAAGJ,MAAI,CAAC5J,MAAM,CAACmE,UAAU,CAACyF,MAAI,CAAC9C,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC/D,OAAO+C,OAAO,CAACI,OAAO,CAACH,OAAO,CAAC,CAACG,OAAO,CAACD,OAAO,CAAC;MAClD,CAAC,EACD3I,MACF,CAAC;IACH;EAAA;IAAAqC,GAAA;IAAAC,KAAA,EAKA,SAAA+B,YAAYA,CACVtB,KAA4B,EAC5B3F,MAA+B,EAC/B4C,MAAsB,EAChB;MACN,IAAI+C,KAAK,IAAI,IAAI,EAAE;QACjB;QACA,IAAI,CAAClE,SAAS,CAACgE,QAAQ,CAAC,IAAI,EAAEzF,MAAM,IAAIP,KAAK,CAAC2E,OAAO,CAACsC,GAAG,CAAC;MAC5D,CAAC,MAAM;QACL;QAAA,IAAA+E,WAAA,GAC4B3F,QAAQ,CAACH,KAAK,EAAE3F,MAAM,EAAE4C,MAAM,CAAC;QAAA,IAAA8I,WAAA,OAAAvI,eAAA,CAAAzD,OAAA,EAAA+L,WAAA;QAA1D9F,KAAK,GAAA+F,WAAA;QAAE1L,MAAM,GAAA0L,WAAA;QAAI9I,MAAM,GAAA8I,WAAA;QACxB,IAAI,CAACjK,SAAS,CAACgE,QAAQ,CAAC,IAAIkG,gBAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAElG,KAAK,CAAC,EAAE3F,MAAM,CAAC,EAAE4C,MAAM,CAAC;QACtE,IAAIA,MAAM,KAAK9B,gBAAO,CAACsD,OAAO,CAAC8C,MAAM,EAAE;UACrC,IAAI,CAACZ,uBAAuB,CAAC,CAAC;QAChC;MACF;IACF;EAAA;IAAArB,GAAA;IAAAC,KAAA,EAEA,SAAA4G,OAAOA,CAACtH,IAAY,EAA+C;MAAA,IAA7C5B,MAAqB,GAAA7C,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGe,gBAAO,CAACsD,OAAO,CAACsC,GAAG;MAC/D,IAAM/C,KAAK,GAAG,IAAII,mBAAK,CAAC,CAAC,CAACgI,MAAM,CAACvH,IAAI,CAAC;MACtC,OAAO,IAAI,CAACC,WAAW,CAACd,KAAK,EAAEf,MAAM,CAAC;IACxC;EAAA;IAAAqC,GAAA;IAAAC,KAAA,EAEA,SAAA1B,MAAMA,CAAA,EAA+C;MAAA,IAA9CZ,MAAqB,GAAA7C,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGe,gBAAO,CAACsD,OAAO,CAACC,IAAI;MACjD,IAAMP,MAAM,GAAG,IAAI,CAACxC,MAAM,CAACkC,MAAM,CAACZ,MAAM,CAAC,CAAC,CAAC;MAC3C,IAAI,CAACnB,SAAS,CAAC+B,MAAM,CAACZ,MAAM,CAAC;MAC7B;MACA,OAAOkB,MAAM;IACf;EAAA;IAAAmB,GAAA;IAAAC,KAAA,EAEA,SAAA8G,cAAcA,CACZrI,KAAmB,EAEZ;MAAA,IAAAsI,MAAA;MAAA,IADPrJ,MAAqB,GAAA7C,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGe,gBAAO,CAACsD,OAAO,CAACsC,GAAG;MAE3C,OAAOpD,MAAM,CAACC,IAAI,CAChB,IAAI,EACJ,YAAM;QACJI,KAAK,GAAG,IAAII,mBAAK,CAACJ,KAAK,CAAC;QACxB,OAAOsI,MAAI,CAAC1K,MAAM,CAAC2K,UAAU,CAACvI,KAAK,CAAC;MACtC,CAAC,EACDf,MAAM,EACN,IACF,CAAC;IACH;EAAA;IAAAqC,GAAA;IAAAC,KAAA,EAxpBA,SAAO/F,KAAKA,CAACgN,KAA2B,EAAE;MACxC,IAAIA,KAAK,KAAK,IAAI,EAAE;QAClBA,KAAK,GAAG,KAAK;MACf;MACA/M,eAAM,CAACgN,KAAK,CAACD,KAAK,CAAC;IACrB;EAAA;IAAAlH,GAAA;IAAAC,KAAA,EAEA,SAAOmH,IAAIA,CAACC,IAAU,EAAkB;MAAA,IAAhBC,MAAM,GAAAxM,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MACpC,OAAOU,kBAAS,CAAC+L,GAAG,CAACF,IAAI,CAAC,IAAIjN,cAAc,CAACgN,IAAI,CAACC,IAAI,EAAEC,MAAM,CAAC;IACjE;EAAA;IAAAtH,GAAA;IAAAC,KAAA,EAOA,SAAOuH,OAAMA,CAACjG,IAAY,EAAE;MAC1B,IAAI,IAAI,CAACkG,OAAO,CAAClG,IAAI,CAAC,IAAI,IAAI,EAAE;QAC9BrH,KAAK,CAACgB,KAAK,kBAAAkB,MAAA,CAAkBmF,IAAK,sCAAkC,CAAC;MACvE;MACA,OAAO,IAAI,CAACkG,OAAO,CAAClG,IAAI,CAAC;IAC3B;EAAA;IAAAvB,GAAA;IAAAC,KAAA,EAkBA,SAAOyH,QAAQA,CAAA,EAAuB;MAAA,IAAAC,MAAA;MACpC,IAAI,QAAA7M,SAAA,CAAAC,MAAA,QAAAC,SAAA,GAAAF,SAAA,IAAc,KAAK,QAAQ,EAAE;QAC/B,IAAM8M,MAAM,GAAA9M,SAAA,CAAAC,MAAA,QAAAC,SAAA,GAAAF,SAAA,GAAU;QACtB,IAAM+M,SAAS,GAAG,CAAC,EAAA/M,SAAA,CAAAC,MAAA,QAAAC,SAAA,GAAAF,SAAA,IAAQ;QAE3B,IAAMyG,IAAI,GAAG,UAAU,IAAIqG,MAAM,GAAGA,MAAM,CAACE,QAAQ,GAAGF,MAAM,CAAC5L,QAAQ;QACrE,IAAI,OAAOuF,IAAI,KAAK,QAAQ,EAAE;UAC5B;UACA;UACA,IAAI,CAACmG,QAAQ,YAAAtL,MAAA,CAAYmF,IAAK,GAAGqG,MAAM,EAAEC,SAAS,CAAC;QACrD,CAAC,MAAM;UACLE,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACK,OAAO,CAAE,UAAAjI,GAAG,EAAK;YACnC2H,MAAI,CAACD,QAAQ,CAAC1H,GAAG,EAAE4H,MAAM,CAAC5H,GAAG,CAAC,EAAE6H,SAAS,CAAC;UAC5C,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL,IAAMK,IAAI,GAAApN,SAAA,CAAAC,MAAA,QAAAC,SAAA,GAAAF,SAAA,GAAU;QACpB,IAAM8M,OAAM,GAAA9M,SAAA,CAAAC,MAAA,QAAAC,SAAA,GAAAF,SAAA,GAAU;QACtB,IAAM+M,UAAS,GAAG,CAAC,EAAA/M,SAAA,CAAAC,MAAA,QAAAC,SAAA,GAAAF,SAAA,IAAQ;QAE3B,IAAI,IAAI,CAAC2M,OAAO,CAACS,IAAI,CAAC,IAAI,IAAI,IAAI,CAACL,UAAS,EAAE;UAC5C3N,KAAK,CAAC+L,IAAI,gBAAA7J,MAAA,CAAgB8L,IAAK,YAAQN,OAAM,CAAC;QAChD;QACA,IAAI,CAACH,OAAO,CAACS,IAAI,CAAC,GAAGN,OAAM;QAC3B,IACE,CAACM,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,IAAID,IAAI,CAACC,UAAU,CAAC,UAAU,CAAC,KACzDP,OAAM,IACN,OAAOA,OAAM,KAAK,SAAS,IAC3BA,OAAM,CAAC5L,QAAQ,KAAK,UAAU,EAC9B;UACA5B,cAAc,CAACsN,QAAQ,CAACE,OAAM,CAAC;QACjC;QACA,IAAI,OAAOA,OAAM,CAACF,QAAQ,KAAK,UAAU,EAAE;UACzCE,OAAM,CAACF,QAAQ,CAACtN,cAAc,CAAC;QACjC;MACF;IACF;EAAA;AAAA;AAAA,IAAA6E,gBAAA,CAAAxE,OAAA,EApGID,KAAK,cACS;EAChBkI,MAAM,EAAE,IAAI;EACZwB,OAAO,EAAE;IACPnH,SAAS,EAAE,IAAI;IACfF,QAAQ,EAAE,IAAI;IACdG,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE;EACZ,CAAC;EACDyC,WAAW,EAAE,EAAE;EACfE,QAAQ,EAAE,KAAK;EACf3D,QAAQ,EAAE7B,cAAc;EACxBwC,KAAK,EAAE;AACT,CAAC;AAAA,IAAAqC,gBAAA,CAAAxE,OAAA,EAbGD,KAAK,YAcOqB,gBAAO,CAACuB,MAAM;AAAA,IAAA6B,gBAAA,CAAAxE,OAAA,EAd1BD,KAAK,aAeQqB,gBAAO,CAACsD,OAAO;AAAA,IAAAF,gBAAA,CAAAxE,OAAA,EAf5BD,KAAK,aAgBQ,cAAoB,KAAK,WAAW,GAAG,KAAK,UAAgB;AAAA,IAAAyE,gBAAA,CAAAxE,OAAA,EAhBzED,KAAK,aAkBiC;EACxCkE,KAAK,EAAEI,mBAAK;EACZsJ,SAAS,EAAEnP,SAAS;EACpB,aAAa,EAAEoP,eAAM;EACrB,YAAY,EAAEC;AAChB,CAAC;AA6pBH,SAASC,eAAeA,CAACC,QAAiD,EAAE;EAC1E,OAAO,OAAOA,QAAQ,KAAK,QAAQ,GAC/BpI,QAAQ,CAACqI,aAAa,CAAcD,QAAQ,CAAC,GAC7CA,QAAQ;AACd;AAEA,SAASE,kBAAkBA,CAACC,MAA2C,EAAE;EACvE,OAAOZ,MAAM,CAACa,OAAO,CAACD,MAAM,aAANA,MAAM,cAANA,MAAM,GAAI,CAAC,CAAC,CAAC,CAACE,MAAM,CACxC,UAACC,QAAQ,EAAAC,IAAA;IAAA,IAAAC,KAAA,OAAA9K,eAAA,CAAAzD,OAAA,EAAcsO,IAAA;MAAX/I,GAAG,GAAAgJ,KAAA;MAAE/I,KAAK,GAAA+I,KAAA;IAAC,WAAAC,cAAA,CAAAxO,OAAA,MAAAwO,cAAA,CAAAxO,OAAA,MAClBqO,QAAQ,WAAA7J,gBAAA,CAAAxE,OAAA,MACVuF,GAAG,EAAGC,KAAK,KAAK,IAAI,GAAG,CAAC,CAAC,GAAGA,KAAA;EAC9B,CAAC,EACF,CAAC,CACH,CAAC;AACH;AAEA,SAASiJ,8BAA8BA,CAACC,GAAiB,EAAE;EACzD,OAAOpB,MAAM,CAACqB,WAAW,CACvBrB,MAAM,CAACa,OAAO,CAACO,GAAG,CAAC,CAACE,MAAM,CAAE,UAAAC,KAAK;IAAA,OAAKA,KAAK,CAAC,CAAC,CAAC,KAAKtO,SAAS;EAAA,EAC9D,CAAC;AACH;AAEA,SAASC,YAAYA,CACnBsO,mBAAyC,EACzC1O,OAAqB,EACC;EACtB,IAAMH,SAAS,GAAG6N,eAAe,CAACgB,mBAAmB,CAAC;EACtD,IAAI,CAAC7O,SAAS,EAAE;IACd,MAAM,IAAIyB,KAAK,CAAC,yBAAyB,CAAC;EAC5C;EAEA,IAAMqN,qBAAqB,GACzB,CAAC3O,OAAO,CAAC+B,KAAK,IAAI/B,OAAO,CAAC+B,KAAK,KAAKpC,KAAK,CAACiP,QAAQ,CAAC7M,KAAK;EAC1D,IAAMA,KAAK,GAAG4M,qBAAqB,GAC/BlB,cAAK,GACL9N,KAAK,CAACgN,MAAM,WAAApL,MAAA,CAAWvB,OAAO,CAAC+B,KAAM,CAAC,CAAC;EAC3C,IAAI,CAACA,KAAK,EAAE;IACV,MAAM,IAAIT,KAAK,kBAAAC,MAAA,CAAkBvB,OAAO,CAAC+B,KAAM,2BAAuB,CAAC;EACzE;EAEA,IAAA8M,eAAA,GAA2DlP,KAAK,CAACiP,QAAQ;IAAxDE,mBAAmB,GAAAD,eAAA,CAA5BxF,OAAO;IAA0B0F,aAAA,OAAAC,yBAAA,CAAApP,OAAA,EAAAiP,eAAA,EAAA1P,SAAA;EACzC,IAAA8P,eAAA,GAA2DlN,KAAK,CAAC6M,QAAQ;IAAxDM,mBAAmB,GAAAD,eAAA,CAA5B5F,OAAO;IAA0B8F,aAAA,OAAAH,yBAAA,CAAApP,OAAA,EAAAqP,eAAA,EAAA7P,UAAA;EAEzC,IAAIgQ,iBAAiB,GAAGvB,kBAAkB,CAAC7N,OAAO,CAACqJ,OAAO,CAAC;EAC3D;EACA,IACE+F,iBAAiB,IAAI,IAAI,IACzBA,iBAAiB,CAACC,OAAO,IACzBD,iBAAiB,CAACC,OAAO,CAACC,WAAW,KAAKpC,MAAM,EAChD;IACAkC,iBAAiB,OAAAhB,cAAA,CAAAxO,OAAA,MAAAwO,cAAA,CAAAxO,OAAA,MACZwP,iBAAiB;MACpBC,OAAO,EAAE;QAAExP,SAAS,EAAEuP,iBAAiB,CAACC;MAAQ;IAAA,EACjD;EACH;EAEA,IAAMhG,OAAwC,GAAG,IAAAkG,eAAK,EACpD,CAAC,CAAC,EACF1B,kBAAkB,CAACiB,mBAAmB,CAAC,EACvCjB,kBAAkB,CAACqB,mBAAmB,CAAC,EACvCE,iBACF,CAAC;EAED,IAAMtB,MAAM,OAAAM,cAAA,CAAAxO,OAAA,MAAAwO,cAAA,CAAAxO,OAAA,MAAAwO,cAAA,CAAAxO,OAAA,MACPmP,aAAa,GACbV,8BAA8B,CAACc,aAAa,CAAC,GAC7Cd,8BAA8B,CAACrO,OAAO,EAC1C;EAED,IAAIoB,QAAQ,GAAGpB,OAAO,CAACoB,QAAQ;EAC/B,IAAIA,QAAQ,EAAE;IACZ,IAAIpB,OAAO,CAACsH,OAAO,EAAE;MACnBjI,KAAK,CAAC+L,IAAI,CAAC,2DAA2D,CAAC;IACzE;EACF,CAAC,MAAM;IACLhK,QAAQ,GAAGpB,OAAO,CAACsH,OAAO,GACtB,IAAAkI,kCAAyB,EAACxP,OAAO,CAACsH,OAAO,EAAEwG,MAAM,CAAC1M,QAAQ,EAAE/B,KAAK,CAAC,GAClEyO,MAAM,CAAC1M,QAAQ;EACrB;EAEA,WAAAgN,cAAA,CAAAxO,OAAA,MAAAwO,cAAA,CAAAxO,OAAA,MACKkO,MAAM;IACT1M,QAAQ,EAARA,QAAQ;IACRvB,SAAS,EAATA,SAAS;IACTkC,KAAK,EAALA,KAAK;IACLsH,OAAO,EAAE6D,MAAM,CAACa,OAAO,CAAC1E,OAAO,CAAC,CAAC2E,MAAM,CACrC,UAACyB,mBAAmB,EAAAC,KAAA,EAAoB;MAAA,IAAAC,KAAA,OAAAtM,eAAA,CAAAzD,OAAA,EAAL8P,KAAA;QAAZhJ,IAAI,GAAAiJ,KAAA;QAAEvK,KAAK,GAAAuK,KAAA;MAChC,IAAI,CAACvK,KAAK,EAAE,OAAOqK,mBAAmB;MAEtC,IAAMG,WAAW,GAAGjQ,KAAK,CAACgN,MAAM,YAAApL,MAAA,CAAYmF,IAAK,CAAC,CAAC;MACnD,IAAIkJ,WAAW,IAAI,IAAI,EAAE;QACvBvQ,KAAK,CAACgB,KAAK,gBAAAkB,MAAA,CACMmF,IAAK,6CACtB,CAAC;QACD,OAAO+I,mBAAmB;MAC5B;MACA,WAAArB,cAAA,CAAAxO,OAAA,MAAAwO,cAAA,CAAAxO,OAAA,MACK6P,mBAAmB,WAAArL,gBAAA,CAAAxE,OAAA,MAErB8G,IAAI,EAAG,IAAA6I,eAAK,EAAC,CAAC,CAAC,EAAEK,WAAW,CAAChB,QAAQ,IAAI,CAAC,CAAC,EAAExJ,KAAK;IAEvD,CAAC,EACD,CAAC,CACH,CAAC;IACDyC,MAAM,EAAE6F,eAAe,CAACI,MAAM,CAACjG,MAAM;EAAA;AAEzC;;AAEA;AACA;AACA,SAASrE,MAAMA,CACb4C,QAAqB,EACrBtD,MAAqB,EACrB+C,KAAuB,EACvBgK,KAAoB,EACpB;EACA,IACE,CAAC,IAAI,CAACxF,SAAS,CAAC,CAAC,IACjBvH,MAAM,KAAK9B,gBAAO,CAACsD,OAAO,CAACC,IAAI,IAC/B,CAAC,IAAI,CAACU,kBAAkB,EACxB;IACA,OAAO,IAAIhB,mBAAK,CAAC,CAAC;EACpB;EACA,IAAI4C,KAAK,GAAGhB,KAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,CAACiB,YAAY,CAAC,CAAC;EACtD,IAAMgJ,QAAQ,GAAG,IAAI,CAACrO,MAAM,CAACoC,KAAK;EAClC,IAAMG,MAAM,GAAGoC,QAAQ,CAAC,CAAC;EACzB,IAAIS,KAAK,IAAI,IAAI,EAAE;IACjB,IAAIhB,KAAK,KAAK,IAAI,EAAE;MAClBA,KAAK,GAAGgB,KAAK,CAAChB,KAAK,CAAC,CAAC;IACvB;IACA,IAAIgK,KAAK,IAAI,IAAI,EAAE;MACjBhJ,KAAK,GAAGkJ,UAAU,CAAClJ,KAAK,EAAE7C,MAAM,EAAElB,MAAM,CAAC;IAC3C,CAAC,MAAM,IAAI+M,KAAK,KAAK,CAAC,EAAE;MACtB;MACAhJ,KAAK,GAAGkJ,UAAU,CAAClJ,KAAK,EAAEhB,KAAK,EAAEgK,KAAK,EAAE/M,MAAM,CAAC;IACjD;IACA,IAAI,CAACqE,YAAY,CAACN,KAAK,EAAE7F,gBAAO,CAACsD,OAAO,CAAC8C,MAAM,CAAC;EAClD;EACA,IAAIpD,MAAM,CAAC9D,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE;IAAA,IAAA8P,cAAA;IACvB,IAAMC,IAAI,GAAG,CAACjP,gBAAO,CAACuB,MAAM,CAACG,WAAW,EAAEsB,MAAM,EAAE8L,QAAQ,EAAEhN,MAAM,CAAC;IACnE,CAAAkN,cAAA,OAAI,CAACjP,OAAO,EAACmP,IAAI,CAAA1F,KAAA,CAAAwF,cAAA,GAAChP,gBAAO,CAACuB,MAAM,CAACC,aAAa,EAAAjB,MAAA,CAAK0O,IAAI,EAAC;IACxD,IAAInN,MAAM,KAAK9B,gBAAO,CAACsD,OAAO,CAAC8C,MAAM,EAAE;MAAA,IAAA+I,cAAA;MACrC,CAAAA,cAAA,OAAI,CAACpP,OAAO,EAACmP,IAAI,CAAA1F,KAAA,CAAA2F,cAAA,EAAIF,IAAI,CAAC;IAC5B;EACF;EACA,OAAOjM,MAAM;AACf;AAuCA,SAASgC,QAAQA,CACfH,KAAqB,EACrB3F,MAAkE,EAClEwG,IAAiE,EACjEtB,KAA+B,EAC/BtC,MAAsB,EACC;EACvB,IAAIwE,OAAgC,GAAG,CAAC,CAAC;EACzC;EACA,IAAI,OAAOzB,KAAK,CAACA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,CAAC3F,MAAM,KAAK,QAAQ,EAAE;IACvE;IACA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC9B;MACA4C,MAAM,GAAGsC,KAAK;MACdA,KAAK,GAAGsB,IAAI;MACZA,IAAI,GAAGxG,MAAM;MACb;MACAA,MAAM,GAAG2F,KAAK,CAAC3F,MAAM,CAAC,CAAC;MACvB;MACA2F,KAAK,GAAGA,KAAK,CAACA,KAAK,CAAC,CAAC;IACvB,CAAC,MAAM;MACL;MACA3F,MAAM,GAAG2F,KAAK,CAAC3F,MAAM,CAAC,CAAC;MACvB;MACA2F,KAAK,GAAGA,KAAK,CAACA,KAAK,CAAC,CAAC;IACvB;EACF,CAAC,MAAM,IAAI,OAAO3F,MAAM,KAAK,QAAQ,EAAE;IACrC;IACA4C,MAAM,GAAGsC,KAAK;IACdA,KAAK,GAAGsB,IAAI;IACZA,IAAI,GAAGxG,MAAM;IACbA,MAAM,GAAG,CAAC;EACZ;EACA;EACA,IAAI,IAAAkQ,QAAA,CAAAxQ,OAAA,EAAO8G,IAAI,MAAK,QAAQ,EAAE;IAC5B;IACAY,OAAO,GAAGZ,IAAI;IACd;IACA5D,MAAM,GAAGsC,KAAK;EAChB,CAAC,MAAM,IAAI,OAAOsB,IAAI,KAAK,QAAQ,EAAE;IACnC,IAAItB,KAAK,IAAI,IAAI,EAAE;MACjBkC,OAAO,CAACZ,IAAI,CAAC,GAAGtB,KAAK;IACvB,CAAC,MAAM;MACL;MACAtC,MAAM,GAAG4D,IAAI;IACf;EACF;EACA;EACA5D,MAAM,GAAGA,MAAM,IAAI9B,gBAAO,CAACsD,OAAO,CAACsC,GAAG;EACtC;EACA,OAAO,CAACf,KAAK,EAAE3F,MAAM,EAAEoH,OAAO,EAAExE,MAAM,CAAC;AACzC;AASA,SAASiN,UAAUA,CACjBlJ,KAAY,EACZhB,KAAqB,EACrBwK,cAAuC,EACvCvN,MAAsB,EACtB;EACA,IAAM5C,MAAM,GAAG,OAAOmQ,cAAc,KAAK,QAAQ,GAAGA,cAAc,GAAG,CAAC;EACtE,IAAIxJ,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI;EAC9B,IAAIyJ,KAAK;EACT,IAAIC,GAAG;EACP;EACA,IAAI1K,KAAK,IAAI,OAAOA,KAAK,CAAC2K,iBAAiB,KAAK,UAAU,EAAE;IAAA,IAAAC,IAAA,GAC3C,CAAC5J,KAAK,CAAChB,KAAK,EAAEgB,KAAK,CAAChB,KAAK,GAAGgB,KAAK,CAAC3G,MAAM,CAAC,CAACwQ,GAAG,CAAE,UAAAC,GAAG;MAAA;QAC/D;QACA9K,KAAK,CAAC2K,iBAAiB,CAACG,GAAG,EAAE7N,MAAM,KAAK9B,gBAAO,CAACsD,OAAO,CAACC,IAAI;MAC9D;IAAA,EAAC;IAAA,IAAAqM,KAAA,OAAAvN,eAAA,CAAAzD,OAAA,EAAA6Q,IAAA;IAHAH,KAAK,GAAAM,KAAA;IAAEL,GAAG,GAAAK,KAAA;EAIb,CAAC,MAAM;IAAA,IAAAC,KAAA,GACU,CAAChK,KAAK,CAAChB,KAAK,EAAEgB,KAAK,CAAChB,KAAK,GAAGgB,KAAK,CAAC3G,MAAM,CAAC,CAACwQ,GAAG,CAAE,UAAAC,GAAG,EAAK;MACpE;MACA,IAAIA,GAAG,GAAG9K,KAAK,IAAK8K,GAAG,KAAK9K,KAAK,IAAI/C,MAAM,KAAK9B,gBAAO,CAACsD,OAAO,CAACC,IAAK,EACnE,OAAOoM,GAAG;MACZ,IAAIzQ,MAAM,IAAI,CAAC,EAAE;QACf,OAAOyQ,GAAG,GAAGzQ,MAAM;MACrB;MACA;MACA,OAAO4L,IAAI,CAACC,GAAG,CAAClG,KAAK,EAAE8K,GAAG,GAAGzQ,MAAM,CAAC;IACtC,CAAC,CAAC;IAAA,IAAA4Q,KAAA,OAAAzN,eAAA,CAAAzD,OAAA,EAAAiR,KAAA;IATDP,KAAK,GAAAQ,KAAA;IAAEP,GAAG,GAAAO,KAAA;EAUb;EACA,OAAO,IAAIjF,gBAAK,CAACyE,KAAK,EAAEC,GAAG,GAAGD,KAAK,CAAC;AACtC", "ignoreList": []}]}