{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\info-summary\\index.vue?vue&type=template&id=7e184638&scoped=true", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\info-summary\\index.vue", "mtime": 1749104047640}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749104421286}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImFwcC1jb250YWluZXIiPgogIDwhLS0g5pON5L2c5oyJ6ZKu5Yy65Z+fIC0tPgogIDxkaXYgY2xhc3M9ImFjdGlvbi1idXR0b25zIj4KICAgIDxlbC1idXR0b24gdHlwZT0icHJpbWFyeSIgaWNvbj0iZWwtaWNvbi1tZXNzYWdlIiBAY2xpY2s9InNob3dTZW5kQWxlcnREaWFsb2ciPuWPkemAgemihOitpjwvZWwtYnV0dG9uPgogICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBpY29uPSJlbC1pY29uLXBsdXMiIEBjbGljaz0ic2hvd0NyZWF0ZVBsYW5EaWFsb2ciPuaWsOW7uuaWueahiDwvZWwtYnV0dG9uPgogICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBpY29uPSJlbC1pY29uLWZvbGRlci1hZGQiIEBjbGljaz0ic2hvd0FkZFRvQWxlcnRNYXRlcmlhbERpYWxvZyI+5Yqg5YWl6Iez5oql5ZGK57Sg5p2QPC9lbC1idXR0b24+CiAgICA8ZWwtYnV0dG9uIHR5cGU9InByaW1hcnkiIGljb249ImVsLWljb24tc2hhcmUiIEBjbGljaz0ic2hvd0luZm9HcmFwaERpYWxvZyI+5L+h5oGv5Zu+6LCxPC9lbC1idXR0b24+CiAgICA8ZWwtYnV0dG9uIHR5cGU9InByaW1hcnkiIGljb249ImVsLWljb24tZG9jdW1lbnQtY2hlY2tlZCIgQGNsaWNrPSJzaG93T3JpZ2luYWxQcm9vZnJlYWRpbmdEaWFsb2ciPuWOn+eov+agoeWvuTwvZWwtYnV0dG9uPgogIDwvZGl2PgoKICA8ZGl2IGNsYXNzPSJpbmZvLXN1bW1hcnktY29udGFpbmVyIj4KICAgIDwhLS0g5bem5L6n5a+86Iiq5qCPIC0tPgogICAgPGRpdiBjbGFzcz0ibGVmdC1zaWRlYmFyIj4KICAgICAgPGRpdiBjbGFzcz0ic2lkZWJhci1oZWFkZXIiPgogICAgICAgIDxzcGFuIGNsYXNzPSJzaWRlYmFyLXRpdGxlIj7mlrnlpKo8L3NwYW4+CiAgICAgICAgPGkgY2xhc3M9ImVsLWljb24tYXJyb3ctcmlnaHQiPjwvaT4KICAgICAgPC9kaXY+CiAgICAgIDxkaXYgY2xhc3M9InNpZGViYXItbWVudSI+CiAgICAgICAgPGVsLW1lbnUKICAgICAgICAgIDpkZWZhdWx0LWFjdGl2ZT0iYWN0aXZlTWVudSIKICAgICAgICAgIGNsYXNzPSJlbC1tZW51LXZlcnRpY2FsIgogICAgICAgID4KICAgICAgICAgIDxlbC1tZW51LWl0ZW0gaW5kZXg9IjEiIEBjbGljaz0iaGFuZGxlTWVudUNsaWNrKCfmgLvop4gnKSI+CiAgICAgICAgICAgIDxpIGNsYXNzPSJlbC1pY29uLXMtaG9tZSI+PC9pPgogICAgICAgICAgICA8c3BhbiBzbG90PSJ0aXRsZSI+5oC76KeIPC9zcGFuPgogICAgICAgICAgPC9lbC1tZW51LWl0ZW0+CiAgICAgICAgICA8ZWwtbWVudS1pdGVtIGluZGV4PSIyIiBAY2xpY2s9ImhhbmRsZU1lbnVDbGljaygn5ZOB54mMJykiPgogICAgICAgICAgICA8aSBjbGFzcz0iZWwtaWNvbi1zLWdvb2RzIj48L2k+CiAgICAgICAgICAgIDxzcGFuIHNsb3Q9InRpdGxlIj7lk4HniYwoMSk8L3NwYW4+CiAgICAgICAgICA8L2VsLW1lbnUtaXRlbT4KICAgICAgICAgIDxlbC1tZW51LWl0ZW0gaW5kZXg9IjMiIEBjbGljaz0iaGFuZGxlTWVudUNsaWNrKCfkurrniaknKSI+CiAgICAgICAgICAgIDxpIGNsYXNzPSJlbC1pY29uLXVzZXIiPjwvaT4KICAgICAgICAgICAgPHNwYW4gc2xvdD0idGl0bGUiPuS6uueJqSgwKTwvc3Bhbj4KICAgICAgICAgIDwvZWwtbWVudS1pdGVtPgogICAgICAgICAgPGVsLW1lbnUtaXRlbSBpbmRleD0iNCIgQGNsaWNrPSJoYW5kbGVNZW51Q2xpY2soJ+acuuaehCcpIj4KICAgICAgICAgICAgPGkgY2xhc3M9ImVsLWljb24tb2ZmaWNlLWJ1aWxkaW5nIj48L2k+CiAgICAgICAgICAgIDxzcGFuIHNsb3Q9InRpdGxlIj7mnLrmnoQoMCk8L3NwYW4+CiAgICAgICAgICA8L2VsLW1lbnUtaXRlbT4KICAgICAgICAgIDxlbC1tZW51LWl0ZW0gaW5kZXg9IjUiIEBjbGljaz0iaGFuZGxlTWVudUNsaWNrKCfkuqflk4EnKSI+CiAgICAgICAgICAgIDxpIGNsYXNzPSJlbC1pY29uLXNob3BwaW5nLWJhZy0xIj48L2k+CiAgICAgICAgICAgIDxzcGFuIHNsb3Q9InRpdGxlIj7kuqflk4EoMCk8L3NwYW4+CiAgICAgICAgICA8L2VsLW1lbnUtaXRlbT4KICAgICAgICAgIDxlbC1tZW51LWl0ZW0gaW5kZXg9IjYiIEBjbGljaz0iaGFuZGxlTWVudUNsaWNrKCfkuovku7YnKSI+CiAgICAgICAgICAgIDxpIGNsYXNzPSJlbC1pY29uLWJlbGwiPjwvaT4KICAgICAgICAgICAgPHNwYW4gc2xvdD0idGl0bGUiPuS6i+S7tigwKTwvc3Bhbj4KICAgICAgICAgIDwvZWwtbWVudS1pdGVtPgogICAgICAgICAgPGVsLW1lbnUtaXRlbSBpbmRleD0iNyIgQGNsaWNrPSJoYW5kbGVNZW51Q2xpY2soJ+ivnemimCcpIj4KICAgICAgICAgICAgPGkgY2xhc3M9ImVsLWljb24tY2hhdC1kb3Qtc3F1YXJlIj48L2k+CiAgICAgICAgICAgIDxzcGFuIHNsb3Q9InRpdGxlIj7or53popgoMCk8L3NwYW4+CiAgICAgICAgICA8L2VsLW1lbnUtaXRlbT4KICAgICAgICA8L2VsLW1lbnU+CiAgICAgIDwvZGl2PgogICAgPC9kaXY+CgogICAgPCEtLSDlj7PkvqflhoXlrrnljLrln58gLS0+CiAgICA8ZGl2IGNsYXNzPSJyaWdodC1jb250ZW50Ij4KICAgICAgPCEtLSDmoIfpopjlkozmk43kvZzljLrln58gLS0+CiAgICAgIDxkaXYgY2xhc3M9ImNvbnRlbnQtaGVhZGVyIj4KICAgICAgICA8ZGl2IGNsYXNzPSJlbnRpdHktdGl0bGUiPgogICAgICAgICAgPHNwYW4gY2xhc3M9ImVudGl0eS1uYW1lIj7mlrnlpKo8L3NwYW4+CiAgICAgICAgICA8aSBjbGFzcz0iZWwtaWNvbi1hcnJvdy1yaWdodCI+PC9pPgogICAgICAgIDwvZGl2PgogICAgICAgIDxkaXYgY2xhc3M9InZpZXctYWN0aW9ucyI+CiAgICAgICAgICA8ZWwtYnV0dG9uLWdyb3VwPgogICAgICAgICAgICA8ZWwtYnV0dG9uIHNpemU9InNtYWxsIiB0eXBlPSJwcmltYXJ5IiBpY29uPSJlbC1pY29uLXMtZ3JpZCI+PC9lbC1idXR0b24+CiAgICAgICAgICAgIDxlbC1idXR0b24gc2l6ZT0ic21hbGwiIGljb249ImVsLWljb24tbWVudSI+PC9lbC1idXR0b24+CiAgICAgICAgICAgIDxlbC1idXR0b24gc2l6ZT0ic21hbGwiIGljb249ImVsLWljb24tcy11bmZvbGQiPjwvZWwtYnV0dG9uPgogICAgICAgICAgPC9lbC1idXR0b24tZ3JvdXA+CiAgICAgICAgPC9kaXY+CiAgICAgIDwvZGl2PgoKICAgICAgPCEtLSDmoIfnrb7pobUgLS0+CiAgICAgIDxkaXYgY2xhc3M9InRhYnMtY29udGFpbmVyIj4KICAgICAgICA8ZGl2IGNsYXNzPSJmaWx0ZXItdGFicyI+CiAgICAgICAgICA8ZWwtcmFkaW8tZ3JvdXAgdi1tb2RlbD0iYWN0aXZlVGFiIiBzaXplPSJzbWFsbCI+CiAgICAgICAgICAgIDxlbC1yYWRpby1idXR0b24gbGFiZWw9InRvZGF5Ij7ku4rlpKk8L2VsLXJhZGlvLWJ1dHRvbj4KICAgICAgICAgICAgPGVsLXJhZGlvLWJ1dHRvbiBsYWJlbD0ieWVzdGVyZGF5Ij7mmKjlpKk8L2VsLXJhZGlvLWJ1dHRvbj4KICAgICAgICAgICAgPGVsLXJhZGlvLWJ1dHRvbiBsYWJlbD0iYmVmb3JlX3llc3RlcmRheSI+5YmN5aSpPC9lbC1yYWRpby1idXR0b24+CiAgICAgICAgICAgIDxlbC1yYWRpby1idXR0b24gbGFiZWw9ImVhcmxpZXIiPuabtOaXqTwvZWwtcmFkaW8tYnV0dG9uPgogICAgICAgICAgICA8ZWwtcmFkaW8tYnV0dG9uIGxhYmVsPSJjdXN0b20iPuiHquWumuS5iTwvZWwtcmFkaW8tYnV0dG9uPgogICAgICAgICAgPC9lbC1yYWRpby1ncm91cD4KICAgICAgICA8L2Rpdj4KICAgICAgPC9kaXY+CgogICAgICA8IS0tIOS/oeaBr+exu+Wei+etm+mAiSAtLT4KICAgICAgPGRpdiBjbGFzcz0iZmlsdGVyLXNlY3Rpb24iPgogICAgICAgIDxkaXYgY2xhc3M9ImZpbHRlci1yb3ciPgogICAgICAgICAgPHNwYW4gY2xhc3M9ImZpbHRlci1sYWJlbCI+5bmz5Y+w5YiG57G7Ojwvc3Bhbj4KICAgICAgICAgIDxlbC1jaGVja2JveC1ncm91cCB2LW1vZGVsPSJwbGF0Zm9ybVR5cGVzIiBzaXplPSJzbWFsbCI+CiAgICAgICAgICAgIDxlbC1jaGVja2JveCBsYWJlbD0ibmV3cyI+5paw6Ze7ICg0Ni8yMTcpPC9lbC1jaGVja2JveD4KICAgICAgICAgICAgPGVsLWNoZWNrYm94IGxhYmVsPSJ3ZWlibyI+5b6u5Y2aICg1LzM0KTwvZWwtY2hlY2tib3g+CiAgICAgICAgICAgIDxlbC1jaGVja2JveCBsYWJlbD0id2VjaGF0Ij7lvq7kv6EgKDE0LzU0KTwvZWwtY2hlY2tib3g+CiAgICAgICAgICAgIDxlbC1jaGVja2JveCBsYWJlbD0idmlkZW8iPuinhumikSAoMi83OCk8L2VsLWNoZWNrYm94PgogICAgICAgICAgICA8ZWwtY2hlY2tib3ggbGFiZWw9ImFwcCI+QVBQICgxLzI5KTwvZWwtY2hlY2tib3g+CiAgICAgICAgICAgIDxlbC1jaGVja2JveCBsYWJlbD0iZm9ydW0iPuiuuuWdmyAoMC85KTwvZWwtY2hlY2tib3g+CiAgICAgICAgICAgIDxlbC1jaGVja2JveCBsYWJlbD0iZWNvbW1lcmNlIj7nlLXllYYgKDQ0LzQ0Nik8L2VsLWNoZWNrYm94PgogICAgICAgICAgICA8ZWwtY2hlY2tib3ggbGFiZWw9InFhIj7pl67nrZQgKDYvMjEpPC9lbC1jaGVja2JveD4KICAgICAgICAgICAgPGVsLWNoZWNrYm94IGxhYmVsPSJvdGhlciI+5YW25LuWICgxLzIpPC9lbC1jaGVja2JveD4KICAgICAgICAgIDwvZWwtY2hlY2tib3gtZ3JvdXA+CiAgICAgICAgPC9kaXY+CgogICAgICAgIDxkaXYgY2xhc3M9ImZpbHRlci1yb3ciPgogICAgICAgICAgPHNwYW4gY2xhc3M9ImZpbHRlci1sYWJlbCI+5oOF5oSf5YC+5ZCROjwvc3Bhbj4KICAgICAgICAgIDxlbC1jaGVja2JveC1ncm91cCB2LW1vZGVsPSJzZW50aW1lbnRUeXBlcyIgc2l6ZT0ic21hbGwiPgogICAgICAgICAgICA8ZWwtY2hlY2tib3ggbGFiZWw9InBvc2l0aXZlIj7mraPpnaIgKDQ2LzIxNyk8L2VsLWNoZWNrYm94PgogICAgICAgICAgICA8ZWwtY2hlY2tib3ggbGFiZWw9Im5ldXRyYWwiPuS4reaApyAoNy84KTwvZWwtY2hlY2tib3g+CiAgICAgICAgICAgIDxlbC1jaGVja2JveCBsYWJlbD0ibmVnYXRpdmUiPui0n+mdoiAoMy81Nyk8L2VsLWNoZWNrYm94PgogICAgICAgICAgICA8ZWwtY2hlY2tib3ggbGFiZWw9Im90aGVyIj7lhbbku5YgKDEvMyk8L2VsLWNoZWNrYm94PgogICAgICAgICAgPC9lbC1jaGVja2JveC1ncm91cD4KICAgICAgICA8L2Rpdj4KCiAgICAgICAgPGRpdiBjbGFzcz0iZmlsdGVyLXJvdyI+CiAgICAgICAgICA8c3BhbiBjbGFzcz0iZmlsdGVyLWxhYmVsIj7kv6Hmga/lsZ7mgKc6PC9zcGFuPgogICAgICAgICAgPGVsLWNoZWNrYm94LWdyb3VwIHYtbW9kZWw9ImluZm9BdHRyaWJ1dGVzIiBzaXplPSJzbWFsbCI+CiAgICAgICAgICAgIDxlbC1jaGVja2JveCBsYWJlbD0ib2ZmaWNpYWwiPuWumOaWueWPkeW4gzwvZWwtY2hlY2tib3g+CiAgICAgICAgICAgIDxlbC1jaGVja2JveCBsYWJlbD0ibWVkaWEiPuWqkuS9k+aKpemBkzwvZWwtY2hlY2tib3g+CiAgICAgICAgICAgIDxlbC1jaGVja2JveCBsYWJlbD0idXNlciI+55So5oi36K+E5Lu3PC9lbC1jaGVja2JveD4KICAgICAgICAgICAgPGVsLWNoZWNrYm94IGxhYmVsPSJjb21wZXRpdG9yIj7nq57lk4Hkv6Hmga88L2VsLWNoZWNrYm94PgogICAgICAgICAgICA8ZWwtY2hlY2tib3ggbGFiZWw9ImluZHVzdHJ5Ij7ooYzkuJrliqjmgIE8L2VsLWNoZWNrYm94PgogICAgICAgICAgICA8ZWwtY2hlY2tib3ggbGFiZWw9InBvbGljeSI+5pS/562W5rOV6KeEPC9lbC1jaGVja2JveD4KICAgICAgICAgIDwvZWwtY2hlY2tib3gtZ3JvdXA+CiAgICAgICAgPC9kaXY+CgogICAgICAgIDxkaXYgY2xhc3M9ImZpbHRlci1hY3Rpb25zIj4KICAgICAgICAgIDxlbC1idXR0b24gc2l6ZT0ic21hbGwiIHR5cGU9InByaW1hcnkiPuetm+mAiTwvZWwtYnV0dG9uPgogICAgICAgICAgPGVsLWJ1dHRvbiBzaXplPSJzbWFsbCI+6YeN572uPC9lbC1idXR0b24+CiAgICAgICAgPC9kaXY+CiAgICAgIDwvZGl2PgoKICAgICAgPCEtLSDmk43kvZzmoI8gLS0+CiAgICAgIDxkaXYgY2xhc3M9ImFjdGlvbi1iYXIiPgogICAgICAgIDxkaXYgY2xhc3M9ImxlZnQtYWN0aW9ucyI+CiAgICAgICAgICA8ZWwtYnV0dG9uIHNpemU9InNtYWxsIiB0eXBlPSJwcmltYXJ5Ij7lhajpgIk8L2VsLWJ1dHRvbj4KICAgICAgICAgIDxlbC1idXR0b24gc2l6ZT0ic21hbGwiPuWvvOWHujwvZWwtYnV0dG9uPgogICAgICAgIDwvZGl2PgogICAgICAgIDxkaXYgY2xhc3M9InJpZ2h0LWFjdGlvbnMiPgogICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLmkJzntKLlhbPplK7or40iCiAgICAgICAgICAgIHByZWZpeC1pY29uPSJlbC1pY29uLXNlYXJjaCIKICAgICAgICAgICAgdi1tb2RlbD0ic2VhcmNoS2V5d29yZCIKICAgICAgICAgICAgc2l6ZT0ic21hbGwiCiAgICAgICAgICAgIGNsZWFyYWJsZQogICAgICAgICAgICBjbGFzcz0ic2VhcmNoLWlucHV0IgogICAgICAgICAgICBAa2V5dXAuZW50ZXI9ImhhbmRsZVNlYXJjaEtleXdvcmQiCiAgICAgICAgICA+CiAgICAgICAgICAgIDxlbC1idXR0b24gc2xvdD0iYXBwZW5kIiBpY29uPSJlbC1pY29uLXNlYXJjaCIgQGNsaWNrPSJoYW5kbGVTZWFyY2hLZXl3b3JkIj48L2VsLWJ1dHRvbj4KICAgICAgICAgIDwvZWwtaW5wdXQ+CiAgICAgICAgICA8ZWwtZHJvcGRvd24gc2l6ZT0ic21hbGwiIHNwbGl0LWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBAY29tbWFuZD0iaGFuZGxlQ29tbWFuZCI+CiAgICAgICAgICAgIOaOkuW6jwogICAgICAgICAgICA8ZWwtZHJvcGRvd24tbWVudSBzbG90PSJkcm9wZG93biI+CiAgICAgICAgICAgICAgPGVsLWRyb3Bkb3duLWl0ZW0gY29tbWFuZD0idGltZV9kZXNjIj7ml7bpl7TpmY3luo88L2VsLWRyb3Bkb3duLWl0ZW0+CiAgICAgICAgICAgICAgPGVsLWRyb3Bkb3duLWl0ZW0gY29tbWFuZD0idGltZV9hc2MiPuaXtumXtOWNh+W6jzwvZWwtZHJvcGRvd24taXRlbT4KICAgICAgICAgICAgICA8ZWwtZHJvcGRvd24taXRlbSBjb21tYW5kPSJyZWxldmFuY2UiPuebuOWFs+aApzwvZWwtZHJvcGRvd24taXRlbT4KICAgICAgICAgICAgPC9lbC1kcm9wZG93bi1tZW51PgogICAgICAgICAgPC9lbC1kcm9wZG93bj4KICAgICAgICA8L2Rpdj4KICAgICAgPC9kaXY+CgogICAgICA8IS0tIOS/oeaBr+WIl+ihqCAtLT4KICAgICAgPGRpdiBjbGFzcz0iaW5mby1saXN0Ij4KICAgICAgICA8ZGl2IHYtZm9yPSIoaXRlbSwgaW5kZXgpIGluIHBhZ2luYXRlZExpc3QiIDprZXk9ImluZGV4IiBjbGFzcz0iaW5mby1pdGVtIj4KICAgICAgICAgIDxlbC1jaGVja2JveCB2LW1vZGVsPSJpdGVtLnNlbGVjdGVkIiBjbGFzcz0iaXRlbS1jaGVja2JveCI+PC9lbC1jaGVja2JveD4KICAgICAgICAgIDxkaXYgY2xhc3M9ImluZm8tY29udGVudCI+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9ImluZm8taGVhZGVyIj4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJpbmZvLXRpdGxlIiB2LWh0bWw9Iml0ZW0udGl0bGUiPjwvZGl2PgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImluZm8tYWN0aW9ucyI+CiAgICAgICAgICAgICAgICA8ZWwtYnV0dG9uIHR5cGU9InRleHQiIGljb249ImVsLWljb24tc3Rhci1vZmYiPjwvZWwtYnV0dG9uPgogICAgICAgICAgICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJ0ZXh0IiBpY29uPSJlbC1pY29uLXNoYXJlIj48L2VsLWJ1dHRvbj4KICAgICAgICAgICAgICAgIDxlbC1idXR0b24gdHlwZT0idGV4dCIgaWNvbj0iZWwtaWNvbi1tb3JlIj48L2VsLWJ1dHRvbj4KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9ImluZm8tc3VtbWFyeSIgdi1odG1sPSJpdGVtLmNvbnRlbnQiPjwvZGl2PgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJpbmZvLWZvb3RlciI+CiAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9ImluZm8tc291cmNlIj57eyBpdGVtLnNvdXJjZSB9fTwvc3Bhbj4KICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0iaW5mby10aW1lIj57eyBpdGVtLnRpbWUgfX08L3NwYW4+CiAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9ImluZm8tc2VudGltZW50IiA6Y2xhc3M9IidzZW50aW1lbnQtJyArIGl0ZW0uc2VudGltZW50Ij4KICAgICAgICAgICAgICAgIDxlbC1idXR0b24KICAgICAgICAgICAgICAgICAgOnR5cGU9Iml0ZW0uc2VudGltZW50ID09PSAncG9zaXRpdmUnID8gJ3N1Y2Nlc3MnIDogaXRlbS5zZW50aW1lbnQgPT09ICduZWdhdGl2ZScgPyAnZGFuZ2VyJyA6ICdpbmZvJyIKICAgICAgICAgICAgICAgICAgc2l6ZT0ibWluaSIKICAgICAgICAgICAgICAgICAgQGNsaWNrPSJoYW5kbGVTZW50aW1lbnRDbGljayhpdGVtKSIKICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgICAgPGkgOmNsYXNzPSJnZXRTZW50aW1lbnRJY29uKGl0ZW0uc2VudGltZW50KSI+PC9pPgogICAgICAgICAgICAgICAgICB7eyBnZXRTZW50aW1lbnRUZXh0KGl0ZW0uc2VudGltZW50KSB9fQogICAgICAgICAgICAgICAgPC9lbC1idXR0b24+CiAgICAgICAgICAgICAgPC9zcGFuPgogICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJpbmZvLXZpZXdzIj4KICAgICAgICAgICAgICAgIDxpIGNsYXNzPSJlbC1pY29uLXZpZXciPjwvaT4ge3sgaXRlbS52aWV3cyB9fQogICAgICAgICAgICAgIDwvc3Bhbj4KICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0iaW5mby1jb21tZW50cyI+CiAgICAgICAgICAgICAgICA8aSBjbGFzcz0iZWwtaWNvbi1jaGF0LWxpbmUtc3F1YXJlIj48L2k+IHt7IGl0ZW0uY29tbWVudHMgfX0KICAgICAgICAgICAgICA8L3NwYW4+CiAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9ImluZm8taW5kZXgiPnt7IChjdXJyZW50UGFnZSAtIDEpICogcGFnZVNpemUgKyBpbmRleCArIDEgfX08L3NwYW4+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJpbmZvLWltYWdlcyIgdi1pZj0iaXRlbS5pbWFnZXMgJiYgaXRlbS5pbWFnZXMubGVuZ3RoID4gMCI+CiAgICAgICAgICAgICAgPGltZyB2LWZvcj0iKGltZywgaW1nSW5kZXgpIGluIGl0ZW0uaW1hZ2VzIiA6a2V5PSJpbWdJbmRleCIgOnNyYz0iaW1nIiBjbGFzcz0iaW5mby1pbWFnZSIgLz4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L2Rpdj4KICAgICAgPC9kaXY+CgogICAgICA8IS0tIOWIhumhtSAtLT4KICAgICAgPGRpdiBjbGFzcz0icGFnaW5hdGlvbi1jb250YWluZXIiPgogICAgICAgIDxlbC1wYWdpbmF0aW9uCiAgICAgICAgICBAc2l6ZS1jaGFuZ2U9ImhhbmRsZVNpemVDaGFuZ2UiCiAgICAgICAgICBAY3VycmVudC1jaGFuZ2U9ImhhbmRsZUN1cnJlbnRDaGFuZ2UiCiAgICAgICAgICA6Y3VycmVudC1wYWdlPSJjdXJyZW50UGFnZSIKICAgICAgICAgIDpwYWdlLXNpemVzPSJbMTAsIDIwLCAzMCwgNTBdIgogICAgICAgICAgOnBhZ2Utc2l6ZT0icGFnZVNpemUiCiAgICAgICAgICBsYXlvdXQ9InRvdGFsLCBzaXplcywgcHJldiwgcGFnZXIsIG5leHQsIGp1bXBlciIKICAgICAgICAgIDp0b3RhbD0idG90YWxJdGVtcyIKICAgICAgICAgIGJhY2tncm91bmQKICAgICAgICA+PC9lbC1wYWdpbmF0aW9uPgogICAgICA8L2Rpdj4KICAgIDwvZGl2PgogIDwvZGl2PgoKICA8IS0tIOaWsOW7uuaWueahiOWvueivneahhiAtLT4KICA8ZWwtZGlhbG9nCiAgICB0aXRsZT0i5paw5bu65pa55qGIIgogICAgOnZpc2libGUuc3luYz0iY3JlYXRlUGxhbkRpYWxvZ1Zpc2libGUiCiAgICB3aWR0aD0iNTAlIgogICAgOmNsb3NlLW9uLWNsaWNrLW1vZGFsPSJmYWxzZSIKICAgIDpjbG9zZS1vbi1wcmVzcy1lc2NhcGU9ImZhbHNlIgogICAgYXBwZW5kLXRvLWJvZHkKICAgIGN1c3RvbS1jbGFzcz0iY3JlYXRlLXBsYW4tZGlhbG9nIgogID4KICAgIDxlbC10YWJzIHYtbW9kZWw9InBsYW5BY3RpdmVUYWIiPgogICAgICA8ZWwtdGFiLXBhbmUgbGFiZWw9Iuebkea1i+aWueW8jyIgbmFtZT0ic3RhbmRhcmQiPgogICAgICAgIDxlbC1mb3JtIDptb2RlbD0icGxhbkZvcm0iIGxhYmVsLXdpZHRoPSI3MHB4IiBzaXplPSJzbWFsbCI+CiAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLmlrnmoYjlkI3np7AiPgogICAgICAgICAgICA8ZWwtaW5wdXQgdi1tb2RlbD0icGxhbkZvcm0ubmFtZSIgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeaWueahiOWQjeensCI+PC9lbC1pbnB1dD4KICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgoKICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuS9nOeUqOiMg+WbtCI+CiAgICAgICAgICAgIDxlbC1zZWxlY3Qgdi1tb2RlbD0icGxhbkZvcm0uc2NvcGUiIHBsYWNlaG9sZGVyPSLor7fpgInmi6kiIHN0eWxlPSJ3aWR0aDogMTAwJSI+CiAgICAgICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i5YWo6YOoIiB2YWx1ZT0iYWxsIj48L2VsLW9wdGlvbj4KICAgICAgICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLpgInpobkxIiB2YWx1ZT0ib3B0aW9uMSI+PC9lbC1vcHRpb24+CiAgICAgICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i6YCJ6aG5MiIgdmFsdWU9Im9wdGlvbjIiPjwvZWwtb3B0aW9uPgogICAgICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgoKICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9Iuebkea1i+WvueixoSI+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9Im1vbml0b3Itb2JqZWN0LXNlbGVjdCI+CiAgICAgICAgICAgICAgPGVsLWlucHV0IHYtbW9kZWw9InBsYW5Gb3JtLm1vbml0b3JPYmplY3QiIHBsYWNlaG9sZGVyPSLor7fovpPlhaXnm5HmtYvlr7nosaEiPjwvZWwtaW5wdXQ+CiAgICAgICAgICAgICAgPGVsLWRyb3Bkb3duIHRyaWdnZXI9ImNsaWNrIiBAY29tbWFuZD0iaGFuZGxlTW9uaXRvck9iamVjdENvbW1hbmQiPgogICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9ImVsLWRyb3Bkb3duLWxpbmsiPgogICAgICAgICAgICAgICAgICA8aSBjbGFzcz0iZWwtaWNvbi1hcnJvdy1kb3duIGVsLWljb24tLXJpZ2h0Ij48L2k+CiAgICAgICAgICAgICAgICA8L3NwYW4+CiAgICAgICAgICAgICAgICA8ZWwtZHJvcGRvd24tbWVudSBzbG90PSJkcm9wZG93biI+CiAgICAgICAgICAgICAgICAgIDxlbC1kcm9wZG93bi1pdGVtIGNvbW1hbmQ9Im9wdGlvbjEiPumAiemhuTE8L2VsLWRyb3Bkb3duLWl0ZW0+CiAgICAgICAgICAgICAgICAgIDxlbC1kcm9wZG93bi1pdGVtIGNvbW1hbmQ9Im9wdGlvbjIiPumAiemhuTI8L2VsLWRyb3Bkb3duLWl0ZW0+CiAgICAgICAgICAgICAgICAgIDxlbC1kcm9wZG93bi1pdGVtIGNvbW1hbmQ9Im9wdGlvbjMiPumAiemhuTM8L2VsLWRyb3Bkb3duLWl0ZW0+CiAgICAgICAgICAgICAgICA8L2VsLWRyb3Bkb3duLW1lbnU+CiAgICAgICAgICAgICAgPC9lbC1kcm9wZG93bj4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KCiAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLlnLDln58iPgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJsb2NhdGlvbi1zZWxlY3QiPgogICAgICAgICAgICAgIDxlbC1pbnB1dCB2LW1vZGVsPSJwbGFuRm9ybS5sb2NhdGlvbiIgcGxhY2Vob2xkZXI9Iuivt+mAieaLqeWcsOWfnyIgcmVhZG9ubHk+PC9lbC1pbnB1dD4KICAgICAgICAgICAgICA8ZWwtYnV0dG9uIHR5cGU9InRleHQiIGNsYXNzPSJsb2NhdGlvbi1idG4iIEBjbGljaz0ic2hvd0xvY2F0aW9uTWFwIj4KICAgICAgICAgICAgICAgIDxpIGNsYXNzPSJlbC1pY29uLWxvY2F0aW9uIj48L2k+CiAgICAgICAgICAgICAgPC9lbC1idXR0b24+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CgogICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5Li76aKYIj4KICAgICAgICAgICAgPGRpdiBjbGFzcz0idGhlbWUtcm93Ij4KICAgICAgICAgICAgICA8ZWwtdGFnCiAgICAgICAgICAgICAgICB2LWZvcj0iKHRhZywgaW5kZXgpIGluIHBsYW5Gb3JtLnRoZW1lcyIKICAgICAgICAgICAgICAgIDprZXk9ImluZGV4IgogICAgICAgICAgICAgICAgY2xvc2FibGUKICAgICAgICAgICAgICAgIEBjbG9zZT0iaGFuZGxlUmVtb3ZlVGhlbWUodGFnKSIKICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICB7eyB0YWcgfX0KICAgICAgICAgICAgICA8L2VsLXRhZz4KICAgICAgICAgICAgICA8ZWwtaW5wdXQKICAgICAgICAgICAgICAgIGNsYXNzPSJ0aGVtZS1pbnB1dCIKICAgICAgICAgICAgICAgIHYtaWY9InRoZW1lSW5wdXRWaXNpYmxlIgogICAgICAgICAgICAgICAgdi1tb2RlbD0idGhlbWVJbnB1dFZhbHVlIgogICAgICAgICAgICAgICAgcmVmPSJ0aGVtZUlucHV0IgogICAgICAgICAgICAgICAgc2l6ZT0ic21hbGwiCiAgICAgICAgICAgICAgICBAa2V5dXAuZW50ZXIubmF0aXZlPSJoYW5kbGVBZGRUaGVtZSIKICAgICAgICAgICAgICAgIEBibHVyPSJoYW5kbGVBZGRUaGVtZSIKICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgPC9lbC1pbnB1dD4KICAgICAgICAgICAgICA8ZWwtYnV0dG9uIHYtZWxzZSBjbGFzcz0idGhlbWUtYnV0dG9uIiBzaXplPSJzbWFsbCIgQGNsaWNrPSJzaG93VGhlbWVJbnB1dCI+KyDmt7vliqDkuLvpopg8L2VsLWJ1dHRvbj4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KCiAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLooYzkuJrliIbnsbsiPgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJpbmR1c3RyeS1yb3ciPgogICAgICAgICAgICAgIDxlbC10YWcKICAgICAgICAgICAgICAgIHYtaWY9InBsYW5Gb3JtLmluZHVzdHJ5IgogICAgICAgICAgICAgICAgY2xvc2FibGUKICAgICAgICAgICAgICAgIEBjbG9zZT0icGxhbkZvcm0uaW5kdXN0cnkgPSAnJyIKICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICB7eyBwbGFuRm9ybS5pbmR1c3RyeSB9fQogICAgICAgICAgICAgIDwvZWwtdGFnPgogICAgICAgICAgICAgIDxlbC1idXR0b24gdi1pZj0iIXBsYW5Gb3JtLmluZHVzdHJ5IiBjbGFzcz0iaW5kdXN0cnktYnV0dG9uIiBzaXplPSJzbWFsbCIgQGNsaWNrPSJzaG93SW5kdXN0cnlTZWxlY3QiPisg5re75Yqg6KGM5Lia5YiG57G7PC9lbC1idXR0b24+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CgogICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5pe26Ze05q61Ij4KICAgICAgICAgICAgPGVsLWRhdGUtcGlja2VyCiAgICAgICAgICAgICAgdi1tb2RlbD0icGxhbkZvcm0udGltZVJhbmdlIgogICAgICAgICAgICAgIHR5cGU9ImRhdGVyYW5nZSIKICAgICAgICAgICAgICByYW5nZS1zZXBhcmF0b3I9IuiHsyIKICAgICAgICAgICAgICBzdGFydC1wbGFjZWhvbGRlcj0i5byA5aeL5pel5pyfIgogICAgICAgICAgICAgIGVuZC1wbGFjZWhvbGRlcj0i57uT5p2f5pel5pyfIgogICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogMTAwJSIKICAgICAgICAgICAgPgogICAgICAgICAgICA8L2VsLWRhdGUtcGlja2VyPgogICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CgogICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5rig6YGTIj4KICAgICAgICAgICAgPGVsLWNoZWNrYm94LWdyb3VwIHYtbW9kZWw9InBsYW5Gb3JtLmNoYW5uZWxzIj4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJjaGFubmVscy1yb3ciPgogICAgICAgICAgICAgICAgPGVsLWNoZWNrYm94IGxhYmVsPSJuZXdzIj7mlrDpl7s8L2VsLWNoZWNrYm94PgogICAgICAgICAgICAgICAgPGVsLWNoZWNrYm94IGxhYmVsPSJ3ZWlibyI+5b6u5Y2aPC9lbC1jaGVja2JveD4KICAgICAgICAgICAgICAgIDxlbC1jaGVja2JveCBsYWJlbD0id2VjaGF0Ij7lvq7kv6E8L2VsLWNoZWNrYm94PgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNoYW5uZWxzLXJvdyI+CiAgICAgICAgICAgICAgICA8ZWwtY2hlY2tib3ggbGFiZWw9InZpZGVvIj7op4bpopE8L2VsLWNoZWNrYm94PgogICAgICAgICAgICAgICAgPGVsLWNoZWNrYm94IGxhYmVsPSJhcHAiPkFQUDwvZWwtY2hlY2tib3g+CiAgICAgICAgICAgICAgICA8ZWwtY2hlY2tib3ggbGFiZWw9ImZvcnVtIj7orrrlnZs8L2VsLWNoZWNrYm94PgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2VsLWNoZWNrYm94LWdyb3VwPgogICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgPC9lbC1mb3JtPgogICAgICA8L2VsLXRhYi1wYW5lPgoKICAgICAgPGVsLXRhYi1wYW5lIGxhYmVsPSLpq5jnuqfmlrnlvI8iIG5hbWU9ImFkdmFuY2VkIj4KICAgICAgICA8ZWwtZm9ybSA6bW9kZWw9ImFkdmFuY2VkUGxhbkZvcm0iIGxhYmVsLXdpZHRoPSI3MHB4IiBzaXplPSJzbWFsbCI+CiAgICAgICAgICA8IS0tIOmrmOe6p+aooeW8j+eahOihqOWNleWGheWuuSAtLT4KICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuaWueahiOWQjeensCI+CiAgICAgICAgICAgIDxlbC1pbnB1dCB2LW1vZGVsPSJhZHZhbmNlZFBsYW5Gb3JtLm5hbWUiIHBsYWNlaG9sZGVyPSLor7fovpPlhaXmlrnmoYjlkI3np7AiPjwvZWwtaW5wdXQ+CiAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KCiAgICAgICAgICA8IS0tIOWFtuS7lumrmOe6p+mAiemhuSAtLT4KICAgICAgICA8L2VsLWZvcm0+CiAgICAgIDwvZWwtdGFiLXBhbmU+CiAgICA8L2VsLXRhYnM+CgogICAgPGRpdiBzbG90PSJmb290ZXIiIGNsYXNzPSJkaWFsb2ctZm9vdGVyIj4KICAgICAgPGVsLWJ1dHRvbiBAY2xpY2s9ImNyZWF0ZVBsYW5EaWFsb2dWaXNpYmxlID0gZmFsc2UiPuWPlua2iDwvZWwtYnV0dG9uPgogICAgICA8ZWwtYnV0dG9uIHR5cGU9InByaW1hcnkiIEBjbGljaz0ic2F2ZVBsYW4iPuS/neWtmDwvZWwtYnV0dG9uPgogICAgPC9kaXY+CiAgPC9lbC1kaWFsb2c+CgogIDwhLS0g6KGM5Lia5YiG57G75by556qXIC0tPgogIDxlbC1kaWFsb2cKICAgIHRpdGxlPSLooYzkuJrliIbnsbsiCiAgICA6dmlzaWJsZS5zeW5jPSJpbmR1c3RyeURpYWxvZ1Zpc2libGUiCiAgICB3aWR0aD0iNDAlIgogICAgOmNsb3NlLW9uLWNsaWNrLW1vZGFsPSJ0cnVlIgogICAgOmNsb3NlLW9uLXByZXNzLWVzY2FwZT0idHJ1ZSIKICAgIGFwcGVuZC10by1ib2R5CiAgICBjdXN0b20tY2xhc3M9ImluZHVzdHJ5LWRpYWxvZyIKICA+CiAgICA8ZGl2IGNsYXNzPSJpbmR1c3RyeS1kaWFsb2ctY29udGVudCI+CiAgICAgIDxkaXYgY2xhc3M9ImluZHVzdHJ5LXRyZWUtY29udGFpbmVyIj4KICAgICAgICA8ZWwtdHJlZQogICAgICAgICAgOmRhdGE9ImluZHVzdHJ5VHJlZURhdGEiCiAgICAgICAgICA6cHJvcHM9ImluZHVzdHJ5VHJlZVByb3BzIgogICAgICAgICAgbm9kZS1rZXk9ImlkIgogICAgICAgICAgZGVmYXVsdC1leHBhbmQtYWxsCiAgICAgICAgICBoaWdobGlnaHQtY3VycmVudAogICAgICAgICAgQG5vZGUtY2xpY2s9ImhhbmRsZUluZHVzdHJ5Tm9kZUNsaWNrIgogICAgICAgIC8+CiAgICAgIDwvZGl2PgogICAgICA8ZGl2IGNsYXNzPSJpbmR1c3RyeS1zZWxlY3RlZC1jb250YWluZXIiPgogICAgICAgIDxkaXYgY2xhc3M9ImluZHVzdHJ5LXNlbGVjdGVkLXRpdGxlIj4KICAgICAgICAgIHt7IHNlbGVjdGVkSW5kdXN0cnkgPyBzZWxlY3RlZEluZHVzdHJ5LmxhYmVsIDogJ+ivt+mAieaLqeihjOS4muWIhuexuycgfX0KICAgICAgICA8L2Rpdj4KICAgICAgPC9kaXY+CiAgICA8L2Rpdj4KICAgIDxkaXYgc2xvdD0iZm9vdGVyIiBjbGFzcz0iZGlhbG9nLWZvb3RlciI+CiAgICAgIDxlbC1idXR0b24gQGNsaWNrPSJpbmR1c3RyeURpYWxvZ1Zpc2libGUgPSBmYWxzZSI+5Y+W5raIPC9lbC1idXR0b24+CiAgICAgIDxlbC1idXR0b24gdHlwZT0icHJpbWFyeSIgQGNsaWNrPSJjb25maXJtSW5kdXN0cnlTZWxlY3QiPuehruWumjwvZWwtYnV0dG9uPgogICAgPC9kaXY+CiAgPC9lbC1kaWFsb2c+CgogIDwhLS0g5Y+R6YCB6aKE6K2m5by556qXIC0tPgogIDxlbC1kaWFsb2cKICAgIHRpdGxlPSLlj5HpgIHpooToraYiCiAgICA6dmlzaWJsZS5zeW5jPSJzZW5kQWxlcnREaWFsb2dWaXNpYmxlIgogICAgd2lkdGg9IjQwJSIKICAgIDpjbG9zZS1vbi1jbGljay1tb2RhbD0idHJ1ZSIKICAgIDpjbG9zZS1vbi1wcmVzcy1lc2NhcGU9InRydWUiCiAgICBhcHBlbmQtdG8tYm9keQogICAgY3VzdG9tLWNsYXNzPSJzZW5kLWFsZXJ0LWRpYWxvZyIKICA+CiAgICA8ZGl2IGNsYXNzPSJzZW5kLWFsZXJ0LWNvbnRlbnQiPgogICAgICA8ZWwtZm9ybSA6bW9kZWw9ImFsZXJ0Rm9ybSIgbGFiZWwtd2lkdGg9IjgwcHgiIHNpemU9InNtYWxsIj4KICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLpooTorabmoIfpopgiPgogICAgICAgICAgPGVsLWlucHV0IHYtbW9kZWw9ImFsZXJ0Rm9ybS50aXRsZSIgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpemihOitpuagh+mimCI+PC9lbC1pbnB1dD4KICAgICAgICA8L2VsLWZvcm0taXRlbT4KCiAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5o6l5pS25Lq6Ij4KICAgICAgICAgIDxkaXYgY2xhc3M9InJlY2VpdmVyLWxpc3QiPgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJyZWNlaXZlci1pdGVtIiB2LWZvcj0iKHJlY2VpdmVyLCBpbmRleCkgaW4gcmVjZWl2ZXJzIiA6a2V5PSJpbmRleCI+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0icmVjZWl2ZXItdHlwZSI+e3sgcmVjZWl2ZXIudHlwZSB9fTwvZGl2PgogICAgICAgICAgICAgIDxlbC1zZWxlY3QKICAgICAgICAgICAgICAgIHYtbW9kZWw9ImFsZXJ0Rm9ybS5zZWxlY3RlZFJlY2VpdmVyc1tpbmRleF0iCiAgICAgICAgICAgICAgICA6cGxhY2Vob2xkZXI9Iifor7fpgInmi6knICsgcmVjZWl2ZXIudHlwZSIKICAgICAgICAgICAgICAgIGNsYXNzPSJyZWNlaXZlci1zZWxlY3QiCiAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgPGVsLW9wdGlvbgogICAgICAgICAgICAgICAgICB2LWZvcj0icGVyc29uIGluIHJlY2VpdmVyLnBlcnNvbnMiCiAgICAgICAgICAgICAgICAgIDprZXk9InBlcnNvbiIKICAgICAgICAgICAgICAgICAgOmxhYmVsPSJwZXJzb24iCiAgICAgICAgICAgICAgICAgIDp2YWx1ZT0icGVyc29uIgogICAgICAgICAgICAgICAgPjwvZWwtb3B0aW9uPgogICAgICAgICAgICAgIDwvZWwtc2VsZWN0PgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICA8L2VsLWZvcm0+CiAgICA8L2Rpdj4KICAgIDxkaXYgc2xvdD0iZm9vdGVyIiBjbGFzcz0iZGlhbG9nLWZvb3RlciI+CiAgICAgIDxlbC1idXR0b24gQGNsaWNrPSJjYW5jZWxTZW5kQWxlcnQiPuWPlua2iDwvZWwtYnV0dG9uPgogICAgICA8ZWwtYnV0dG9uIHR5cGU9InByaW1hcnkiIEBjbGljaz0iY29uZmlybVNlbmRBbGVydCI+56Gu5a6aPC9lbC1idXR0b24+CiAgICA8L2Rpdj4KICA8L2VsLWRpYWxvZz4KCiAgPCEtLSDmraPpnaLmg4XmhJ/kv6Hmga/lvLnnqpcgLS0+CiAgPGVsLWRpYWxvZwogICAgdGl0bGU9IuaDheaEn+WxnuaAp+e6oOmUmSIKICAgIDp2aXNpYmxlLnN5bmM9InBvc2l0aXZlU2VudGltZW50RGlhbG9nVmlzaWJsZSIKICAgIHdpZHRoPSI1MCUiCiAgICBhcHBlbmQtdG8tYm9keQogICAgY3VzdG9tLWNsYXNzPSJwb3NpdGl2ZS1zZW50aW1lbnQtZGlhbG9nIgogID4KICAgIDxlbC1yYWRpby1ncm91cCB2LW1vZGVsPSJzZWxlY3RlZFNlbnRpbWVudCIgc2l6ZT0ic21hbGwiPgogICAgICA8ZWwtcmFkaW8tYnV0dG9uIGxhYmVsPSJwb3NpdGl2ZSI+5q2j6Z2iPC9lbC1yYWRpby1idXR0b24+CiAgICAgIDxlbC1yYWRpby1idXR0b24gbGFiZWw9Im5ldXRyYWwiPuS4reaApzwvZWwtcmFkaW8tYnV0dG9uPgogICAgICA8ZWwtcmFkaW8tYnV0dG9uIGxhYmVsPSJuZWdhdGl2ZSI+6LSf6Z2iPC9lbC1yYWRpby1idXR0b24+CiAgICA8L2VsLXJhZGlvLWdyb3VwPgogICAgPHNwYW4gc2xvdD0iZm9vdGVyIiBjbGFzcz0iZGlhbG9nLWZvb3RlciI+CiAgICAgIDxlbC1idXR0b24gQGNsaWNrPSJwb3NpdGl2ZVNlbnRpbWVudERpYWxvZ1Zpc2libGUgPSBmYWxzZSI+5Y+W5raIPC9lbC1idXR0b24+CiAgICAgIDxlbC1idXR0b24gdHlwZT0icHJpbWFyeSIgQGNsaWNrPSJoYW5kbGVQb3NpdGl2ZURpYWxvZ0NvbmZpcm0iPuehruWumjwvZWwtYnV0dG9uPgogICAgPC9zcGFuPgogIDwvZWwtZGlhbG9nPgoKICA8IS0tIOWKoOWFpeiHs+aKpeWRiue0oOadkOWvueivneahhiAtLT4KICA8ZWwtZGlhbG9nCiAgICB0aXRsZT0i5Yqg5YWl6Iez5oql5ZGK57Sg5p2QIgogICAgOnZpc2libGUuc3luYz0iYWRkVG9BbGVydE1hdGVyaWFsRGlhbG9nVmlzaWJsZSIKICAgIHdpZHRoPSIzMCUiCiAgICA6Y2xvc2Utb24tY2xpY2stbW9kYWw9ImZhbHNlIgogICAgOmNsb3NlLW9uLXByZXNzLWVzY2FwZT0iZmFsc2UiCiAgICBhcHBlbmQtdG8tYm9keQogICAgY3VzdG9tLWNsYXNzPSJhZGQtdG8tYWxlcnQtbWF0ZXJpYWwtZGlhbG9nIgogID4KICAgIDxkaXYgY2xhc3M9ImFkZC10by1hbGVydC1tYXRlcmlhbC1jb250ZW50Ij4KICAgICAgPGVsLWZvcm0gbGFiZWwtd2lkdGg9IjgwcHgiIHNpemU9InNtYWxsIj4KICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLpgInmi6nntKDmnZDlupMiPgogICAgICAgICAgPGVsLXNlbGVjdCB2LW1vZGVsPSJzZWxlY3RlZE1hdGVyaWFsTGlicmFyeSIgcGxhY2Vob2xkZXI9Iuivt+mAieaLqee0oOadkOW6kyIgc3R5bGU9IndpZHRoOiAxMDAlIj4KICAgICAgICAgICAgPCEtLSDov5nph4zpnIDopoHmoLnmja7lrp7pmYXmlbDmja7loavlhYXpgInpobkgLS0+CiAgICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9Iue0oOadkOW6kzEiIHZhbHVlPSJsaWJyYXJ5MSI+PC9lbC1vcHRpb24+CiAgICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9Iue0oOadkOW6kzIiIHZhbHVlPSJsaWJyYXJ5MiI+PC9lbC1vcHRpb24+CiAgICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgPC9lbC1mb3JtPgogICAgPC9kaXY+CiAgICA8ZGl2IHNsb3Q9ImZvb3RlciIgY2xhc3M9ImRpYWxvZy1mb290ZXIiPgogICAgICA8ZWwtYnV0dG9uIEBjbGljaz0iY2FuY2VsQWRkVG9BbGVydE1hdGVyaWFsIj7lj5bmtog8L2VsLWJ1dHRvbj4KICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBAY2xpY2s9ImNvbmZpcm1BZGRUb0FsZXJ0TWF0ZXJpYWwiPuehruWumjwvZWwtYnV0dG9uPgogICAgPC9kaXY+CiAgPC9lbC1kaWFsb2c+CjwvZGl2Pgo="}, null]}