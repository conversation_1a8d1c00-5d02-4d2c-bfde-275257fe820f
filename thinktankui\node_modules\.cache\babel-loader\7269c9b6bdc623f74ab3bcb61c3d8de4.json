{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\ui\\tooltip.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\ui\\tooltip.js", "mtime": 1749104422791}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["isScrollable", "el", "_getComputedStyle", "getComputedStyle", "overflowY", "<PERSON><PERSON><PERSON>", "quill", "boundsContainer", "_this", "_classCallCheck2", "default", "document", "body", "root", "addContainer", "innerHTML", "constructor", "TEMPLATE", "addEventListener", "style", "marginTop", "concat", "scrollTop", "hide", "_createClass2", "key", "value", "classList", "add", "position", "reference", "left", "width", "offsetWidth", "top", "bottom", "remove", "containerBounds", "getBoundingClientRect", "rootBounds", "shift", "right", "height", "verticalShift", "show", "_default", "exports"], "sources": ["../../src/ui/tooltip.ts"], "sourcesContent": ["import type Quill from '../core.js';\nimport type { Bounds } from '../core/selection.js';\n\nconst isScrollable = (el: Element) => {\n  const { overflowY } = getComputedStyle(el, null);\n  return overflowY !== 'visible' && overflowY !== 'clip';\n};\n\nclass Tooltip {\n  quill: Quill;\n  boundsContainer: HTMLElement;\n  root: HTMLDivElement;\n\n  constructor(quill: Quill, boundsContainer?: HTMLElement) {\n    this.quill = quill;\n    this.boundsContainer = boundsContainer || document.body;\n    this.root = quill.addContainer('ql-tooltip');\n    // @ts-expect-error\n    this.root.innerHTML = this.constructor.TEMPLATE;\n    if (isScrollable(this.quill.root)) {\n      this.quill.root.addEventListener('scroll', () => {\n        this.root.style.marginTop = `${-1 * this.quill.root.scrollTop}px`;\n      });\n    }\n    this.hide();\n  }\n\n  hide() {\n    this.root.classList.add('ql-hidden');\n  }\n\n  position(reference: Bounds) {\n    const left =\n      reference.left + reference.width / 2 - this.root.offsetWidth / 2;\n    // root.scrollTop should be 0 if scrollContainer !== root\n    const top = reference.bottom + this.quill.root.scrollTop;\n    this.root.style.left = `${left}px`;\n    this.root.style.top = `${top}px`;\n    this.root.classList.remove('ql-flip');\n    const containerBounds = this.boundsContainer.getBoundingClientRect();\n    const rootBounds = this.root.getBoundingClientRect();\n    let shift = 0;\n    if (rootBounds.right > containerBounds.right) {\n      shift = containerBounds.right - rootBounds.right;\n      this.root.style.left = `${left + shift}px`;\n    }\n    if (rootBounds.left < containerBounds.left) {\n      shift = containerBounds.left - rootBounds.left;\n      this.root.style.left = `${left + shift}px`;\n    }\n    if (rootBounds.bottom > containerBounds.bottom) {\n      const height = rootBounds.bottom - rootBounds.top;\n      const verticalShift = reference.bottom - reference.top + height;\n      this.root.style.top = `${top - verticalShift}px`;\n      this.root.classList.add('ql-flip');\n    }\n    return shift;\n  }\n\n  show() {\n    this.root.classList.remove('ql-editing');\n    this.root.classList.remove('ql-hidden');\n  }\n}\n\nexport default Tooltip;\n"], "mappings": ";;;;;;;;;AAGA,IAAMA,YAAY,GAAI,SAAhBA,YAAYA,CAAIC,EAAW,EAAK;EACpC,IAAAC,iBAAA,GAAsBC,gBAAgB,CAACF,EAAE,EAAE,IAAI,CAAC;IAAxCG,SAAA,GAAAF,iBAAA,CAAAE,SAAA;EACR,OAAOA,SAAS,KAAK,SAAS,IAAIA,SAAS,KAAK,MAAM;AACxD,CAAC;AAAA,IAEKC,OAAO;EAKX,SAAAA,QAAYC,KAAY,EAAEC,eAA6B,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAL,OAAA;IACvD,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,eAAe,GAAGA,eAAe,IAAII,QAAQ,CAACC,IAAI;IACvD,IAAI,CAACC,IAAI,GAAGP,KAAK,CAACQ,YAAY,CAAC,YAAY,CAAC;IAC5C;IACA,IAAI,CAACD,IAAI,CAACE,SAAS,GAAG,IAAI,CAACC,WAAW,CAACC,QAAQ;IAC/C,IAAIjB,YAAY,CAAC,IAAI,CAACM,KAAK,CAACO,IAAI,CAAC,EAAE;MACjC,IAAI,CAACP,KAAK,CAACO,IAAI,CAACK,gBAAgB,CAAC,QAAQ,EAAE,YAAM;QAC/CV,KAAI,CAACK,IAAI,CAACM,KAAK,CAACC,SAAS,MAAAC,MAAA,CAAM,CAAC,CAAC,GAAGb,KAAI,CAACF,KAAK,CAACO,IAAI,CAACS,SAAU,OAAG;MACnE,CAAC,CAAC;IACJ;IACA,IAAI,CAACC,IAAI,CAAC,CAAC;EACb;EAAA,WAAAC,aAAA,CAAAd,OAAA,EAAAL,OAAA;IAAAoB,GAAA;IAAAC,KAAA,EAEA,SAAAH,IAAIA,CAAA,EAAG;MACL,IAAI,CAACV,IAAI,CAACc,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;IACtC;EAAA;IAAAH,GAAA;IAAAC,KAAA,EAEA,SAAAG,QAAQA,CAACC,SAAiB,EAAE;MAC1B,IAAMC,IAAI,GACRD,SAAS,CAACC,IAAI,GAAGD,SAAS,CAACE,KAAK,GAAG,CAAC,GAAG,IAAI,CAACnB,IAAI,CAACoB,WAAW,GAAG,CAAC;MAClE;MACA,IAAMC,GAAG,GAAGJ,SAAS,CAACK,MAAM,GAAG,IAAI,CAAC7B,KAAK,CAACO,IAAI,CAACS,SAAS;MACxD,IAAI,CAACT,IAAI,CAACM,KAAK,CAACY,IAAI,MAAAV,MAAA,CAAMU,IAAK,OAAG;MAClC,IAAI,CAAClB,IAAI,CAACM,KAAK,CAACe,GAAG,MAAAb,MAAA,CAAMa,GAAI,OAAG;MAChC,IAAI,CAACrB,IAAI,CAACc,SAAS,CAACS,MAAM,CAAC,SAAS,CAAC;MACrC,IAAMC,eAAe,GAAG,IAAI,CAAC9B,eAAe,CAAC+B,qBAAqB,CAAC,CAAC;MACpE,IAAMC,UAAU,GAAG,IAAI,CAAC1B,IAAI,CAACyB,qBAAqB,CAAC,CAAC;MACpD,IAAIE,KAAK,GAAG,CAAC;MACb,IAAID,UAAU,CAACE,KAAK,GAAGJ,eAAe,CAACI,KAAK,EAAE;QAC5CD,KAAK,GAAGH,eAAe,CAACI,KAAK,GAAGF,UAAU,CAACE,KAAK;QAChD,IAAI,CAAC5B,IAAI,CAACM,KAAK,CAACY,IAAI,MAAAV,MAAA,CAAMU,IAAI,GAAGS,KAAM,OAAG;MAC5C;MACA,IAAID,UAAU,CAACR,IAAI,GAAGM,eAAe,CAACN,IAAI,EAAE;QAC1CS,KAAK,GAAGH,eAAe,CAACN,IAAI,GAAGQ,UAAU,CAACR,IAAI;QAC9C,IAAI,CAAClB,IAAI,CAACM,KAAK,CAACY,IAAI,MAAAV,MAAA,CAAMU,IAAI,GAAGS,KAAM,OAAG;MAC5C;MACA,IAAID,UAAU,CAACJ,MAAM,GAAGE,eAAe,CAACF,MAAM,EAAE;QAC9C,IAAMO,MAAM,GAAGH,UAAU,CAACJ,MAAM,GAAGI,UAAU,CAACL,GAAG;QACjD,IAAMS,aAAa,GAAGb,SAAS,CAACK,MAAM,GAAGL,SAAS,CAACI,GAAG,GAAGQ,MAAM;QAC/D,IAAI,CAAC7B,IAAI,CAACM,KAAK,CAACe,GAAG,MAAAb,MAAA,CAAMa,GAAG,GAAGS,aAAc,OAAG;QAChD,IAAI,CAAC9B,IAAI,CAACc,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC;MACpC;MACA,OAAOY,KAAK;IACd;EAAA;IAAAf,GAAA;IAAAC,KAAA,EAEA,SAAAkB,IAAIA,CAAA,EAAG;MACL,IAAI,CAAC/B,IAAI,CAACc,SAAS,CAACS,MAAM,CAAC,YAAY,CAAC;MACxC,IAAI,CAACvB,IAAI,CAACc,SAAS,CAACS,MAAM,CAAC,WAAW,CAAC;IACzC;EAAA;AAAA;AAAA,IAAAS,QAAA,GAAAC,OAAA,CAAApC,OAAA,GAGaL,OAAO", "ignoreList": []}]}