{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\layout\\components\\InnerLink\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\layout\\components\\InnerLink\\index.vue", "mtime": 1749104047626}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIHByb3BzOiB7DQogICAgc3JjOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAiLyINCiAgICB9LA0KICAgIGlmcmFtZUlkOiB7DQogICAgICB0eXBlOiBTdHJpbmcNCiAgICB9DQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGxvYWRpbmc6IGZhbHNlLA0KICAgICAgaGVpZ2h0OiBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xpZW50SGVpZ2h0IC0gOTQuNSArICJweDsiDQogICAgfTsNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB2YXIgX3RoaXMgPSB0aGlzOw0KICAgIGNvbnN0IGlmcmFtZUlkID0gKCIjIiArIHRoaXMuaWZyYW1lSWQpLnJlcGxhY2UoL1wvL2csICJcXC8iKTsNCiAgICBjb25zdCBpZnJhbWUgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKGlmcmFtZUlkKTsNCiAgICAvLyBpZnJhbWXpobXpnaJsb2FkaW5n5o6n5Yi2DQogICAgaWYgKGlmcmFtZS5hdHRhY2hFdmVudCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGlmcmFtZS5hdHRhY2hFdmVudCgib25sb2FkIiwgZnVuY3Rpb24gKCkgew0KICAgICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9IGVsc2Ugew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGlmcmFtZS5vbmxvYWQgPSBmdW5jdGlvbiAoKSB7DQogICAgICAgIF90aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH07DQogICAgfQ0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/InnerLink", "sourcesContent": ["<template>\r\n  <div :style=\"'height:' + height\" v-loading=\"loading\" element-loading-text=\"正在加载页面，请稍候！\">\r\n    <iframe\r\n      :id=\"iframeId\"\r\n      style=\"width: 100%; height: 100%\"\r\n      :src=\"src\"\r\n      frameborder=\"no\"\r\n    ></iframe>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  props: {\r\n    src: {\r\n      type: String,\r\n      default: \"/\"\r\n    },\r\n    iframeId: {\r\n      type: String\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      height: document.documentElement.clientHeight - 94.5 + \"px;\"\r\n    };\r\n  },\r\n  mounted() {\r\n    var _this = this;\r\n    const iframeId = (\"#\" + this.iframeId).replace(/\\//g, \"\\\\/\");\r\n    const iframe = document.querySelector(iframeId);\r\n    // iframe页面loading控制\r\n    if (iframe.attachEvent) {\r\n      this.loading = true;\r\n      iframe.attachEvent(\"onload\", function () {\r\n        _this.loading = false;\r\n      });\r\n    } else {\r\n      this.loading = true;\r\n      iframe.onload = function () {\r\n        _this.loading = false;\r\n      };\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}