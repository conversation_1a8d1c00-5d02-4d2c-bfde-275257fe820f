{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\system\\dict\\data.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\system\\dict\\data.vue", "mtime": 1749104047647}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0RGF0YSwgZ2V0RGF0YSwgZGVsRGF0YSwgYWRkRGF0YSwgdXBkYXRlRGF0YSB9IGZyb20gIkAvYXBpL3N5c3RlbS9kaWN0L2RhdGEiOw0KaW1wb3J0IHsgb3B0aW9uc2VsZWN0IGFzIGdldERpY3RPcHRpb25zZWxlY3QsIGdldFR5cGUgfSBmcm9tICJAL2FwaS9zeXN0ZW0vZGljdC90eXBlIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiRGF0YSIsDQogIGRpY3RzOiBbJ3N5c19ub3JtYWxfZGlzYWJsZSddLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgIGlkczogW10sDQogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g5a2X5YW46KGo5qC85pWw5o2uDQogICAgICBkYXRhTGlzdDogW10sDQogICAgICAvLyDpu5jorqTlrZflhbjnsbvlnosNCiAgICAgIGRlZmF1bHREaWN0VHlwZTogIiIsDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvLyDmlbDmja7moIfnrb7lm57mmL7moLflvI8NCiAgICAgIGxpc3RDbGFzc09wdGlvbnM6IFsNCiAgICAgICAgew0KICAgICAgICAgIHZhbHVlOiAiZGVmYXVsdCIsDQogICAgICAgICAgbGFiZWw6ICLpu5jorqQiDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB2YWx1ZTogInByaW1hcnkiLA0KICAgICAgICAgIGxhYmVsOiAi5Li76KaBIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdmFsdWU6ICJzdWNjZXNzIiwNCiAgICAgICAgICBsYWJlbDogIuaIkOWKnyINCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHZhbHVlOiAiaW5mbyIsDQogICAgICAgICAgbGFiZWw6ICLkv6Hmga8iDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB2YWx1ZTogIndhcm5pbmciLA0KICAgICAgICAgIGxhYmVsOiAi6K2m5ZGKIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdmFsdWU6ICJkYW5nZXIiLA0KICAgICAgICAgIGxhYmVsOiAi5Y2x6ZmpIg0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgLy8g57G75Z6L5pWw5o2u5a2X5YW4DQogICAgICB0eXBlT3B0aW9uczogW10sDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgZGljdFR5cGU6IHVuZGVmaW5lZCwNCiAgICAgICAgZGljdExhYmVsOiB1bmRlZmluZWQsDQogICAgICAgIHN0YXR1czogdW5kZWZpbmVkDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgZGljdExhYmVsOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaVsOaNruagh+etvuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICAgIGRpY3RWYWx1ZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmlbDmja7plK7lgLzkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBkaWN0U29ydDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmlbDmja7pobrluo/kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdDQogICAgICB9DQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICBjb25zdCBkaWN0SWQgPSB0aGlzLiRyb3V0ZS5wYXJhbXMgJiYgdGhpcy4kcm91dGUucGFyYW1zLmRpY3RJZDsNCiAgICB0aGlzLmdldFR5cGUoZGljdElkKTsNCiAgICB0aGlzLmdldFR5cGVMaXN0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKiog5p+l6K+i5a2X5YW457G75Z6L6K+m57uGICovDQogICAgZ2V0VHlwZShkaWN0SWQpIHsNCiAgICAgIGdldFR5cGUoZGljdElkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5kaWN0VHlwZSA9IHJlc3BvbnNlLmRhdGEuZGljdFR5cGU7DQogICAgICAgIHRoaXMuZGVmYXVsdERpY3RUeXBlID0gcmVzcG9uc2UuZGF0YS5kaWN0VHlwZTsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDmn6Xor6LlrZflhbjnsbvlnovliJfooaggKi8NCiAgICBnZXRUeXBlTGlzdCgpIHsNCiAgICAgIGdldERpY3RPcHRpb25zZWxlY3QoKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy50eXBlT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDmn6Xor6LlrZflhbjmlbDmja7liJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxpc3REYXRhKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmRhdGFMaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5Y+W5raI5oyJ6ZKuDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgfSwNCiAgICAvLyDooajljZXph43nva4NCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgZGljdENvZGU6IHVuZGVmaW5lZCwNCiAgICAgICAgZGljdExhYmVsOiB1bmRlZmluZWQsDQogICAgICAgIGRpY3RWYWx1ZTogdW5kZWZpbmVkLA0KICAgICAgICBjc3NDbGFzczogdW5kZWZpbmVkLA0KICAgICAgICBsaXN0Q2xhc3M6ICdkZWZhdWx0JywNCiAgICAgICAgZGljdFNvcnQ6IDAsDQogICAgICAgIHN0YXR1czogIjAiLA0KICAgICAgICByZW1hcms6IHVuZGVmaW5lZA0KICAgICAgfTsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7DQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6L+U5Zue5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQ2xvc2UoKSB7DQogICAgICBjb25zdCBvYmogPSB7IHBhdGg6ICIvc3lzdGVtL2RpY3QiIH07DQogICAgICB0aGlzLiR0YWIuY2xvc2VPcGVuUGFnZShvYmopOw0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmRpY3RUeXBlID0gdGhpcy5kZWZhdWx0RGljdFR5cGU7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg5a2X5YW45pWw5o2uIjsNCiAgICAgIHRoaXMuZm9ybS5kaWN0VHlwZSA9IHRoaXMucXVlcnlQYXJhbXMuZGljdFR5cGU7DQogICAgfSwNCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmRpY3RDb2RlKQ0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoIT0xDQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICBjb25zdCBkaWN0Q29kZSA9IHJvdy5kaWN0Q29kZSB8fCB0aGlzLmlkcw0KICAgICAgZ2V0RGF0YShkaWN0Q29kZSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS55a2X5YW45pWw5o2uIjsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm06IGZ1bmN0aW9uKCkgew0KICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5kaWN0Q29kZSAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgIHVwZGF0ZURhdGEodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2RpY3QvcmVtb3ZlRGljdCcsIHRoaXMucXVlcnlQYXJhbXMuZGljdFR5cGUpOw0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGFkZERhdGEodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2RpY3QvcmVtb3ZlRGljdCcsIHRoaXMucXVlcnlQYXJhbXMuZGljdFR5cGUpOw0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICBjb25zdCBkaWN0Q29kZXMgPSByb3cuZGljdENvZGUgfHwgdGhpcy5pZHM7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTlrZflhbjnvJbnoIHkuLoiJyArIGRpY3RDb2RlcyArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbigpIHsNCiAgICAgICAgcmV0dXJuIGRlbERhdGEoZGljdENvZGVzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdkaWN0L3JlbW92ZURpY3QnLCB0aGlzLnF1ZXJ5UGFyYW1zLmRpY3RUeXBlKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICB0aGlzLmRvd25sb2FkKCdzeXN0ZW0vZGljdC9kYXRhL2V4cG9ydCcsIHsNCiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcw0KICAgICAgfSwgYGRhdGFfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApDQogICAgfQ0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["data.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgMA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "data.vue", "sourceRoot": "src/views/system/dict", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"字典名称\" prop=\"dictType\">\r\n        <el-select v-model=\"queryParams.dictType\">\r\n          <el-option\r\n            v-for=\"item in typeOptions\"\r\n            :key=\"item.dictId\"\r\n            :label=\"item.dictName\"\r\n            :value=\"item.dictType\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"字典标签\" prop=\"dictLabel\">\r\n        <el-input\r\n          v-model=\"queryParams.dictLabel\"\r\n          placeholder=\"请输入字典标签\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" placeholder=\"数据状态\" clearable>\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_normal_disable\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['system:dict:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['system:dict:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['system:dict:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['system:dict:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-close\"\r\n          size=\"mini\"\r\n          @click=\"handleClose\"\r\n        >关闭</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"dataList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"字典编码\" align=\"center\" prop=\"dictCode\" />\r\n      <el-table-column label=\"字典标签\" align=\"center\" prop=\"dictLabel\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"(scope.row.listClass == '' || scope.row.listClass == 'default') && (scope.row.cssClass == '' || scope.row.cssClass == null)\">{{ scope.row.dictLabel }}</span>\r\n          <el-tag v-else :type=\"scope.row.listClass == 'primary' ? '' : scope.row.listClass\" :class=\"scope.row.cssClass\">{{ scope.row.dictLabel }}</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"字典键值\" align=\"center\" prop=\"dictValue\" />\r\n      <el-table-column label=\"字典排序\" align=\"center\" prop=\"dictSort\" />\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['system:dict:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['system:dict:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改参数配置对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"字典类型\">\r\n          <el-input v-model=\"form.dictType\" :disabled=\"true\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"数据标签\" prop=\"dictLabel\">\r\n          <el-input v-model=\"form.dictLabel\" placeholder=\"请输入数据标签\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"数据键值\" prop=\"dictValue\">\r\n          <el-input v-model=\"form.dictValue\" placeholder=\"请输入数据键值\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"样式属性\" prop=\"cssClass\">\r\n          <el-input v-model=\"form.cssClass\" placeholder=\"请输入样式属性\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"显示排序\" prop=\"dictSort\">\r\n          <el-input-number v-model=\"form.dictSort\" controls-position=\"right\" :min=\"0\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"回显样式\" prop=\"listClass\">\r\n          <el-select v-model=\"form.listClass\">\r\n            <el-option\r\n              v-for=\"item in listClassOptions\"\r\n              :key=\"item.value\"\r\n              :label=\"item.label + '(' + item.value + ')'\"\r\n              :value=\"item.value\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\" prop=\"status\">\r\n          <el-radio-group v-model=\"form.status\">\r\n            <el-radio\r\n              v-for=\"dict in dict.type.sys_normal_disable\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.value\"\r\n            >{{dict.label}}</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listData, getData, delData, addData, updateData } from \"@/api/system/dict/data\";\r\nimport { optionselect as getDictOptionselect, getType } from \"@/api/system/dict/type\";\r\n\r\nexport default {\r\n  name: \"Data\",\r\n  dicts: ['sys_normal_disable'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 字典表格数据\r\n      dataList: [],\r\n      // 默认字典类型\r\n      defaultDictType: \"\",\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 数据标签回显样式\r\n      listClassOptions: [\r\n        {\r\n          value: \"default\",\r\n          label: \"默认\"\r\n        },\r\n        {\r\n          value: \"primary\",\r\n          label: \"主要\"\r\n        },\r\n        {\r\n          value: \"success\",\r\n          label: \"成功\"\r\n        },\r\n        {\r\n          value: \"info\",\r\n          label: \"信息\"\r\n        },\r\n        {\r\n          value: \"warning\",\r\n          label: \"警告\"\r\n        },\r\n        {\r\n          value: \"danger\",\r\n          label: \"危险\"\r\n        }\r\n      ],\r\n      // 类型数据字典\r\n      typeOptions: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        dictType: undefined,\r\n        dictLabel: undefined,\r\n        status: undefined\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        dictLabel: [\r\n          { required: true, message: \"数据标签不能为空\", trigger: \"blur\" }\r\n        ],\r\n        dictValue: [\r\n          { required: true, message: \"数据键值不能为空\", trigger: \"blur\" }\r\n        ],\r\n        dictSort: [\r\n          { required: true, message: \"数据顺序不能为空\", trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    const dictId = this.$route.params && this.$route.params.dictId;\r\n    this.getType(dictId);\r\n    this.getTypeList();\r\n  },\r\n  methods: {\r\n    /** 查询字典类型详细 */\r\n    getType(dictId) {\r\n      getType(dictId).then(response => {\r\n        this.queryParams.dictType = response.data.dictType;\r\n        this.defaultDictType = response.data.dictType;\r\n        this.getList();\r\n      });\r\n    },\r\n    /** 查询字典类型列表 */\r\n    getTypeList() {\r\n      getDictOptionselect().then(response => {\r\n        this.typeOptions = response.data;\r\n      });\r\n    },\r\n    /** 查询字典数据列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listData(this.queryParams).then(response => {\r\n        this.dataList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        dictCode: undefined,\r\n        dictLabel: undefined,\r\n        dictValue: undefined,\r\n        cssClass: undefined,\r\n        listClass: 'default',\r\n        dictSort: 0,\r\n        status: \"0\",\r\n        remark: undefined\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 返回按钮操作 */\r\n    handleClose() {\r\n      const obj = { path: \"/system/dict\" };\r\n      this.$tab.closeOpenPage(obj);\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.queryParams.dictType = this.defaultDictType;\r\n      this.handleQuery();\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加字典数据\";\r\n      this.form.dictType = this.queryParams.dictType;\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.dictCode)\r\n      this.single = selection.length!=1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const dictCode = row.dictCode || this.ids\r\n      getData(dictCode).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改字典数据\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.dictCode != undefined) {\r\n            updateData(this.form).then(response => {\r\n              this.$store.dispatch('dict/removeDict', this.queryParams.dictType);\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addData(this.form).then(response => {\r\n              this.$store.dispatch('dict/removeDict', this.queryParams.dictType);\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const dictCodes = row.dictCode || this.ids;\r\n      this.$modal.confirm('是否确认删除字典编码为\"' + dictCodes + '\"的数据项？').then(function() {\r\n        return delData(dictCodes);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n        this.$store.dispatch('dict/removeDict', this.queryParams.dictType);\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/dict/data/export', {\r\n        ...this.queryParams\r\n      }, `data_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script>"]}]}