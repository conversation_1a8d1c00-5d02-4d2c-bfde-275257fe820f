{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\account\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\account\\index.vue", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_userManagement", "_interopRequireDefault", "require", "name", "components", "UserManagement", "watch", "selectAllFavorites", "val", "watchSelectAllFavorites", "data", "activeMenuItem", "currentSettingsTab", "userInfo", "userId", "phoneNumber", "registerDate", "category", "plan", "totalPoints", "pointsLevel", "availablePoints", "estimatedDays", "expirationDate", "remainingCount", "settings", "auto", "manual", "downloadImages", "backup", "realTimeSync", "humanReview", "autoPublish", "keywords", "highQuality", "multiPlatform", "autoSave", "batchProcess", "imageProcess", "autoOn", "autoOff", "downloadList", "id", "dataSize", "createTime", "status", "total", "currentPage", "pageSize", "jumpPage", "selectedDownloads", "versionList", "version", "date", "selectedVersion", "notes", "addMaterialDialogVisible", "newMaterialName", "materialList", "showPasswordChange", "passwordForm", "oldPassword", "newPassword", "confirmPassword", "selectedFavorites", "favoriteStartDate", "favoriteEndDate", "favoriteSearchKeyword", "favoriteList", "selected", "tag", "title", "time", "source", "favoritesTotal", "favoritesCurrentPage", "favoritesPageSize", "favoritesJumpPage", "contactActiveTab", "contactList", "phone", "email", "location", "addContactDialogVisible", "contactForm", "contactFormRules", "required", "message", "trigger", "pattern", "type", "methods", "handleMenuSelect", "index", "showAccountInfo", "showPasswordForm", "changePassword", "$message", "warning", "success", "switchSettingsTab", "tab", "saveSettings", "handleSelectionChange", "selection", "batchDownload", "length", "concat", "batchDelete", "_this", "$confirm", "confirmButtonText", "cancelButtonText", "then", "catch", "info", "handleDownload", "row", "handleDelete", "_this2", "handleCurrentChange", "handleJumpPage", "page", "parseInt", "isNaN", "Math", "ceil", "selectVersion", "versionData", "find", "v", "showAddMaterialDialog", "addMaterial", "trim", "push", "Date", "now", "toLocaleString", "handleFavoriteSelect", "filter", "item", "for<PERSON>ach", "_toConsumableArray2", "default", "batchCancelFavorite", "_this3", "selectedIds", "map", "includes", "cancelFavorite", "_this4", "i", "showAddContactDialog", "submitContactForm", "_this5", "$refs", "validate", "valid", "findIndex", "year", "month", "day", "hour", "minute", "second", "hour12", "replace", "splice", "newContact", "_objectSpread2", "max", "apply", "handleEditContact", "JSON", "parse", "stringify", "handleDeleteContact", "_this6", "exportFavorites", "handleFavoritePageChange", "handleFavoriteJumpPage"], "sources": ["src/views/account/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"page-layout\">\r\n      <!-- 左侧导航栏 -->\r\n      <div class=\"left-sidebar\">\r\n        <div class=\"user-info\">\r\n          <div class=\"avatar\">\r\n            <img src=\"@/assets/images/profile.jpg\" alt=\"用户头像\">\r\n          </div>\r\n          <div class=\"user-id\">***********</div>\r\n          <div class=\"register-date\">2023-04-28注册</div>\r\n        </div>\r\n\r\n        <div class=\"sidebar-menu\">\r\n          <el-menu\r\n            :default-active=\"activeMenuItem\"\r\n            class=\"sidebar-menu-list\"\r\n            @select=\"handleMenuSelect\"\r\n          >\r\n            <el-menu-item index=\"account\">\r\n              <i class=\"el-icon-user\"></i>\r\n              <span>我的账号</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"favorite\">\r\n              <i class=\"el-icon-star-on\"></i>\r\n              <span>我的收藏</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"download\">\r\n              <i class=\"el-icon-download\"></i>\r\n              <span>我的下载</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"contact\">\r\n              <i class=\"el-icon-notebook-1\"></i>\r\n              <span>我的联系人</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"material\">\r\n              <i class=\"el-icon-chat-line-round\"></i>\r\n              <span>素材库</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"privacy\">\r\n              <i class=\"el-icon-setting\"></i>\r\n              <span>系统设置</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"user-management\">\r\n              <i class=\"el-icon-user\"></i>\r\n              <span>用户管理</span>\r\n            </el-menu-item>\r\n          </el-menu>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧内容区 -->\r\n      <div class=\"content\">\r\n        <!-- 账户信息内容 -->\r\n        <div v-if=\"activeMenuItem === 'account'\" class=\"account-container\">\r\n          <div class=\"account-header\">\r\n            <div class=\"title\">我的账号</div>\r\n            <div class=\"actions\">\r\n              <el-button size=\"small\" @click=\"showAccountInfo\">账号安全</el-button>\r\n              <el-button type=\"primary\" size=\"small\" @click=\"showPasswordForm\">修改密码</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 账户信息内容 -->\r\n          <div v-if=\"!showPasswordChange\" class=\"account-info\">\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">用户账号：</div>\r\n              <div class=\"value\">***********</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">手机号码：</div>\r\n              <div class=\"value\">***********</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">创建时间：</div>\r\n              <div class=\"value\">2023-04-28</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">专业认证分类：</div>\r\n              <div class=\"value\">互联网+</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">方案：</div>\r\n              <div class=\"value\">【免费】+【限时】</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">总积分值：</div>\r\n              <div class=\"value\">2000</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">积分等级：</div>\r\n              <div class=\"value\">初级VIP</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">剩余可用积分：</div>\r\n              <div class=\"value\">【250积分 = 600次】</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">预计可用天数：</div>\r\n              <div class=\"value\">365</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">预计到期日：</div>\r\n              <div class=\"value\">365</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">剩余次数：</div>\r\n              <div class=\"value\">10000次</div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 修改密码表单 -->\r\n          <div v-if=\"showPasswordChange\" class=\"password-form\">\r\n            <div class=\"form-group\">\r\n              <div class=\"form-label\">旧密码：</div>\r\n              <el-input\r\n                v-model=\"passwordForm.oldPassword\"\r\n                type=\"password\"\r\n                placeholder=\"请输入旧密码\"\r\n                show-password\r\n              ></el-input>\r\n            </div>\r\n\r\n            <div class=\"form-group\">\r\n              <div class=\"form-label\">新密码：</div>\r\n              <el-input\r\n                v-model=\"passwordForm.newPassword\"\r\n                type=\"password\"\r\n                placeholder=\"8-16 密码必须同时包含数字、大小写字母和符号\"\r\n                show-password\r\n              ></el-input>\r\n            </div>\r\n\r\n            <div class=\"form-group\">\r\n              <div class=\"form-label\">确认新密码：</div>\r\n              <el-input\r\n                v-model=\"passwordForm.confirmPassword\"\r\n                type=\"password\"\r\n                placeholder=\"请再次输入新密码\"\r\n                show-password\r\n              ></el-input>\r\n            </div>\r\n\r\n            <div class=\"form-actions\">\r\n              <el-button type=\"primary\" @click=\"changePassword\">确认修改</el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 我的下载内容 -->\r\n        <div v-if=\"activeMenuItem === 'download'\" class=\"download-container\">\r\n          <div class=\"download-header\">\r\n            <div class=\"title\">我的下载</div>\r\n            <div class=\"actions\">\r\n              <el-button type=\"primary\" size=\"small\" @click=\"batchDownload\">批量下载</el-button>\r\n              <el-button type=\"danger\" size=\"small\" @click=\"batchDelete\">批量删除</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"download-content\">\r\n            <el-table\r\n              :data=\"downloadList\"\r\n              style=\"width: 100%\"\r\n              @selection-change=\"handleSelectionChange\">\r\n              <el-table-column\r\n                type=\"selection\"\r\n                width=\"55\">\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"name\"\r\n                label=\"名称\"\r\n                width=\"300\">\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"dataSize\"\r\n                label=\"数据量\"\r\n                width=\"100\">\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"createTime\"\r\n                label=\"生成时间\"\r\n                width=\"180\">\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"status\"\r\n                label=\"下载状态\"\r\n                width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <span class=\"download-status\">{{ scope.row.status }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                label=\"操作\"\r\n                width=\"120\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    icon=\"el-icon-download\"\r\n                    @click=\"handleDownload(scope.row)\">\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    icon=\"el-icon-delete\"\r\n                    @click=\"handleDelete(scope.row)\">\r\n                  </el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <div class=\"pagination-container\">\r\n              <span>共 {{ total }} 条记录</span>\r\n              <el-pagination\r\n                background\r\n                layout=\"prev, pager, next, jumper\"\r\n                :total=\"total\"\r\n                :current-page.sync=\"currentPage\"\r\n                :page-size=\"pageSize\"\r\n                @current-change=\"handleCurrentChange\">\r\n              </el-pagination>\r\n              <span>前往第</span>\r\n              <el-input\r\n                v-model=\"jumpPage\"\r\n                size=\"mini\"\r\n                class=\"jump-page-input\">\r\n              </el-input>\r\n              <span>页</span>\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"primary\"\r\n                @click=\"handleJumpPage\">\r\n                确定\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 我的收藏内容 -->\r\n        <div v-if=\"activeMenuItem === 'favorite'\" class=\"favorite-container\">\r\n          <div class=\"favorite-header\">\r\n            <div class=\"title\">我的收藏</div>\r\n          </div>\r\n\r\n          <div class=\"favorite-toolbar\">\r\n            <div class=\"toolbar-left\">\r\n              <el-checkbox v-model=\"selectAllFavorites\">全选</el-checkbox>\r\n              <el-button size=\"small\" type=\"danger\" :disabled=\"selectedFavorites.length === 0\" @click=\"batchCancelFavorite\">批量取消收藏</el-button>\r\n              <el-button size=\"small\" icon=\"el-icon-refresh\">刷新</el-button>\r\n            </div>\r\n\r\n            <div class=\"toolbar-right\">\r\n              <div class=\"date-filter\">\r\n                <el-date-picker\r\n                  v-model=\"favoriteStartDate\"\r\n                  type=\"date\"\r\n                  placeholder=\"开始日期\"\r\n                  size=\"small\"\r\n                  format=\"yyyy-MM-dd\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                  style=\"width: 130px;\"\r\n                ></el-date-picker>\r\n                <span class=\"date-separator\">-</span>\r\n                <el-date-picker\r\n                  v-model=\"favoriteEndDate\"\r\n                  type=\"date\"\r\n                  placeholder=\"结束日期\"\r\n                  size=\"small\"\r\n                  format=\"yyyy-MM-dd\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                  style=\"width: 130px;\"\r\n                ></el-date-picker>\r\n              </div>\r\n\r\n              <div class=\"search-box\">\r\n                <el-input\r\n                  v-model=\"favoriteSearchKeyword\"\r\n                  placeholder=\"搜索内容\"\r\n                  size=\"small\"\r\n                  prefix-icon=\"el-icon-search\"\r\n                  clearable\r\n                  style=\"width: 200px;\"\r\n                ></el-input>\r\n              </div>\r\n\r\n              <el-button type=\"primary\" size=\"small\" icon=\"el-icon-download\" @click=\"exportFavorites\">全部下载</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"favorite-content\">\r\n            <div class=\"favorite-list\">\r\n              <div v-for=\"item in favoriteList\" :key=\"item.id\" class=\"favorite-item\">\r\n                <div class=\"item-checkbox\">\r\n                  <el-checkbox v-model=\"item.selected\" @change=\"handleFavoriteSelect\"></el-checkbox>\r\n                </div>\r\n                <div class=\"item-content\">\r\n                  <div class=\"item-title\">\r\n                    <span class=\"title-tag\">{{ item.tag }}</span>\r\n                    <span class=\"title-text\">{{ item.title }}</span>\r\n                  </div>\r\n                  <div class=\"item-info\">\r\n                    <span class=\"info-time\">收藏时间: {{ item.time }}</span>\r\n                    <span class=\"info-source\">来源: {{ item.source }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"item-actions\">\r\n                  <el-button\r\n                    type=\"text\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-delete\"\r\n                    class=\"cancel-favorite-btn\"\r\n                    @click=\"cancelFavorite(item)\">\r\n                    取消收藏\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"favorite-pagination\">\r\n              <div class=\"pagination-info\">\r\n                共 <span>{{ favoritesTotal }}</span> 条记录\r\n              </div>\r\n              <div class=\"pagination-controls\">\r\n                <el-pagination\r\n                  background\r\n                  layout=\"prev, pager, next\"\r\n                  :total=\"favoritesTotal\"\r\n                  :current-page.sync=\"favoritesCurrentPage\"\r\n                  :page-size=\"favoritesPageSize\"\r\n                  @current-change=\"handleFavoritePageChange\">\r\n                </el-pagination>\r\n              </div>\r\n              <div class=\"pagination-jump\">\r\n                <span>前往</span>\r\n                <el-input\r\n                  v-model=\"favoritesJumpPage\"\r\n                  size=\"mini\"\r\n                  class=\"jump-page-input\">\r\n                </el-input>\r\n                <span>页</span>\r\n                <el-button\r\n                  size=\"mini\"\r\n                  type=\"primary\"\r\n                  @click=\"handleFavoriteJumpPage\">\r\n                  确定\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 素材库内容 -->\r\n        <div v-if=\"activeMenuItem === 'material'\" class=\"material-container\">\r\n          <div class=\"material-header\">\r\n            <div class=\"title\">素材库</div>\r\n          </div>\r\n\r\n          <div class=\"material-content\">\r\n            <div class=\"material-add-box\" @click=\"showAddMaterialDialog\">\r\n              <div class=\"add-icon\">\r\n                <i class=\"el-icon-plus\"></i>\r\n              </div>\r\n              <div class=\"add-text\">新建素材包</div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 新建素材对话框 -->\r\n          <el-dialog\r\n            title=\"新建素材包\"\r\n            :visible.sync=\"addMaterialDialogVisible\"\r\n            width=\"400px\"\r\n            center\r\n            :show-close=\"false\"\r\n            custom-class=\"material-dialog\"\r\n          >\r\n            <div class=\"material-form\">\r\n              <div class=\"form-item\">\r\n                <div class=\"form-label\">素材包名称：</div>\r\n                <el-input v-model=\"newMaterialName\" placeholder=\"请输入素材包名称\"></el-input>\r\n              </div>\r\n            </div>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n              <el-button type=\"primary\" @click=\"addMaterial\">确定</el-button>\r\n              <el-button @click=\"addMaterialDialogVisible = false\">取消</el-button>\r\n            </div>\r\n          </el-dialog>\r\n        </div>\r\n\r\n        <!-- 我的联系人内容 -->\r\n        <div v-if=\"activeMenuItem === 'contact'\" class=\"contact-container\">\r\n          <div class=\"contact-header\">\r\n            <div class=\"title\">我的联系人</div>\r\n            <div class=\"contact-tabs\">\r\n              <el-radio-group v-model=\"contactActiveTab\" size=\"small\">\r\n                <el-radio-button label=\"wechat\">微信</el-radio-button>\r\n                <el-radio-button label=\"sms\">短信</el-radio-button>\r\n                <el-radio-button label=\"email\">邮箱</el-radio-button>\r\n                <el-radio-button label=\"qwechat\">企微群</el-radio-button>\r\n                <el-radio-button label=\"dingtalk\">钉钉群</el-radio-button>\r\n                <el-radio-button label=\"feishu\">飞书群</el-radio-button>\r\n              </el-radio-group>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"contact-toolbar\">\r\n            <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"showAddContactDialog\">添加/导入联系人</el-button>\r\n          </div>\r\n\r\n          <div class=\"contact-content\">\r\n            <el-table\r\n              :data=\"contactList\"\r\n              style=\"width: 100%\"\r\n              border\r\n              stripe\r\n              :header-cell-style=\"{background:'#f5f7fa', color:'#606266'}\"\r\n            >\r\n              <el-table-column\r\n                label=\"头像\"\r\n                width=\"80\"\r\n                align=\"center\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <el-avatar :size=\"40\" icon=\"el-icon-user\"></el-avatar>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"name\"\r\n                label=\"姓名\"\r\n                width=\"120\"\r\n                align=\"center\"\r\n              />\r\n              <el-table-column\r\n                prop=\"createTime\"\r\n                label=\"创建/更新\"\r\n                width=\"180\"\r\n                align=\"center\"\r\n              />\r\n              <el-table-column\r\n                prop=\"location\"\r\n                label=\"位置/时间\"\r\n                width=\"180\"\r\n                align=\"center\"\r\n              />\r\n              <el-table-column\r\n                label=\"操作\"\r\n                align=\"center\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    icon=\"el-icon-edit\"\r\n                    @click=\"handleEditContact(scope.row)\"\r\n                  >编辑</el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    icon=\"el-icon-delete\"\r\n                    class=\"delete-btn\"\r\n                    @click=\"handleDeleteContact(scope.row)\"\r\n                  >删除</el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n\r\n          <!-- 添加联系人对话框 -->\r\n          <el-dialog title=\"添加联系人\" :visible.sync=\"addContactDialogVisible\" width=\"500px\" center>\r\n            <el-form ref=\"contactForm\" :model=\"contactForm\" :rules=\"contactFormRules\" label-width=\"80px\">\r\n              <el-form-item label=\"姓名\" prop=\"name\">\r\n                <el-input v-model=\"contactForm.name\" placeholder=\"请输入姓名\" />\r\n              </el-form-item>\r\n              <el-form-item label=\"手机号码\" prop=\"phone\">\r\n                <el-input v-model=\"contactForm.phone\" placeholder=\"请输入手机号码\" />\r\n              </el-form-item>\r\n              <el-form-item label=\"邮箱\" prop=\"email\">\r\n                <el-input v-model=\"contactForm.email\" placeholder=\"请输入邮箱\" />\r\n              </el-form-item>\r\n              <el-form-item label=\"位置\" prop=\"location\">\r\n                <el-input v-model=\"contactForm.location\" placeholder=\"请输入位置\" />\r\n              </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n              <el-button @click=\"addContactDialogVisible = false\">取消</el-button>\r\n              <el-button type=\"primary\" @click=\"submitContactForm\">确定</el-button>\r\n            </div>\r\n          </el-dialog>\r\n        </div>\r\n\r\n        <!-- 用户管理内容 -->\r\n        <div v-if=\"activeMenuItem === 'user-management'\">\r\n          <user-management />\r\n        </div>\r\n\r\n        <!-- 系统设置内容 -->\r\n        <div v-if=\"activeMenuItem === 'privacy'\" class=\"settings-container\">\r\n          <div class=\"settings-header\">\r\n            <div class=\"title\">系统设置</div>\r\n          </div>\r\n\r\n          <div class=\"settings-content\">\r\n            <!-- 左侧设置菜单 -->\r\n            <div class=\"settings-sidebar\">\r\n              <div class=\"settings-menu\">\r\n                <div :class=\"['menu-item', currentSettingsTab === 'basic' ? 'active' : '']\" @click=\"switchSettingsTab('basic')\">\r\n                  <i class=\"el-icon-setting\"></i>\r\n                  <span>基础设置</span>\r\n                </div>\r\n                <div :class=\"['menu-item', currentSettingsTab === 'updates' ? 'active' : '']\" @click=\"switchSettingsTab('updates')\">\r\n                  <i class=\"el-icon-document\"></i>\r\n                  <span>更新说明</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 右侧设置内容 -->\r\n            <div class=\"settings-main\">\r\n              <!-- 基础设置内容 -->\r\n              <div v-if=\"currentSettingsTab === 'basic'\" class=\"settings-section\">\r\n                <div class=\"section-title\">文章管理选项：</div>\r\n                <div class=\"options-group\">\r\n                  <el-checkbox v-model=\"settings.auto\">自动</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.manual\">手动</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.downloadImages\">下载图片</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.backup\">备份</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.realTimeSync\">实时同步</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.humanReview\">人工审核</el-checkbox>\r\n                </div>\r\n\r\n                <div class=\"options-group\">\r\n                  <el-checkbox v-model=\"settings.autoPublish\">自动发布</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.keywords\">关键词</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.highQuality\">高质量</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.multiPlatform\">多平台发布</el-checkbox>\r\n                </div>\r\n\r\n                <div class=\"options-group\">\r\n                  <el-checkbox v-model=\"settings.autoSave\">自动保存</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.batchProcess\">批量处理</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.imageProcess\">图片处理</el-checkbox>\r\n                </div>\r\n\r\n                <div class=\"section-title\">下载设置：</div>\r\n                <div class=\"download-settings\">\r\n                  <div class=\"setting-item\">\r\n                    <span class=\"label\">下载路径：</span>\r\n                    <span class=\"value\">自动生成文件夹</span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"section-title\">高级自动化设置：</div>\r\n                <div class=\"options-group\">\r\n                  <el-checkbox v-model=\"settings.autoOn\">开启</el-checkbox>\r\n                  <el-checkbox v-model=\"settings.autoOff\">关闭</el-checkbox>\r\n                </div>\r\n\r\n                <div class=\"save-button\">\r\n                  <el-button type=\"primary\" @click=\"saveSettings\">保存</el-button>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 更新说明内容 -->\r\n              <div v-if=\"currentSettingsTab === 'updates'\" class=\"updates-section\">\r\n                <div class=\"updates-header\">\r\n                  <div class=\"version-title\">{{ selectedVersion.version }} 版本更新说明</div>\r\n                  <div class=\"version-date\">{{ selectedVersion.date }}</div>\r\n                </div>\r\n\r\n                <div class=\"updates-content\">\r\n                  <div class=\"version-list\">\r\n                    <div\r\n                      v-for=\"version in versionList\"\r\n                      :key=\"version.version\"\r\n                      :class=\"['version-item', selectedVersion.version === version.version ? 'active' : '']\"\r\n                      @click=\"selectVersion(version.version)\"\r\n                    >\r\n                      {{ version.version }}\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div class=\"update-details\">\r\n                    <div class=\"update-notes\">\r\n                      <div v-for=\"(note, index) in selectedVersion.notes\" :key=\"index\" class=\"note-item\">\r\n                        <div class=\"note-number\">{{ index + 1 }}. </div>\r\n                        <div class=\"note-content\" v-html=\"note\"></div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UserManagement from './user-management.vue';\r\n\r\nexport default {\r\n  name: \"AccountManagement\",\r\n  components: {\r\n    UserManagement\r\n  },\r\n  watch: {\r\n    selectAllFavorites(val) {\r\n      this.watchSelectAllFavorites(val);\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      activeMenuItem: 'account', // 默认选中我的账号\r\n      currentSettingsTab: 'basic', // 默认选中基础设置\r\n      // 用户信息\r\n      userInfo: {\r\n        userId: '***********',\r\n        phoneNumber: '***********',\r\n        registerDate: '2023-04-28',\r\n        category: '互联网+',\r\n        plan: '【免费】+【限时】',\r\n        totalPoints: '2000',\r\n        pointsLevel: '初级VIP',\r\n        availablePoints: '【250积分 = 600次】',\r\n        estimatedDays: '365',\r\n        expirationDate: '365',\r\n        remainingCount: '10000次'\r\n      },\r\n      // 系统设置\r\n      settings: {\r\n        auto: true,\r\n        manual: true,\r\n        downloadImages: true,\r\n        backup: true,\r\n        realTimeSync: false,\r\n        humanReview: true,\r\n        autoPublish: false,\r\n        keywords: true,\r\n        highQuality: false,\r\n        multiPlatform: true,\r\n        autoSave: true,\r\n        batchProcess: true,\r\n        imageProcess: true,\r\n        autoOn: true,\r\n        autoOff: false\r\n      },\r\n      // 下载列表\r\n      downloadList: [\r\n        {\r\n          id: 1,\r\n          name: '方案_20240428163743_1',\r\n          dataSize: '1323',\r\n          createTime: '2024-04-28 16:37:57',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '方案_20240428163743_1',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-28 16:37:57',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '方案_20240427173742_4',\r\n          dataSize: '1893',\r\n          createTime: '2024-04-27 17:37:42',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 4,\r\n          name: '方案_20240427173742_3',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-27 17:37:42',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 5,\r\n          name: '方案_20240427173742_2',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-27 17:37:42',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 6,\r\n          name: '方案_20240427173742_2',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-27 17:37:42',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 7,\r\n          name: '方案_20240427173742_1',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-27 17:37:42',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 8,\r\n          name: '台账_20240427173129_5',\r\n          dataSize: '1281',\r\n          createTime: '2024-04-27 17:31:29',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 9,\r\n          name: '台账_20240427173129_4',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-27 17:31:29',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 10,\r\n          name: '台账_20240427173129_3',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-27 17:31:29',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 11,\r\n          name: '台账_20240427173129_2',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-27 17:31:29',\r\n          status: '已完成'\r\n        },\r\n        {\r\n          id: 12,\r\n          name: '台账_20240427173129_1',\r\n          dataSize: '2000',\r\n          createTime: '2024-04-27 17:31:29',\r\n          status: '已完成'\r\n        }\r\n      ],\r\n      // 分页相关\r\n      total: 12,\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      jumpPage: '',\r\n      // 选中的下载项\r\n      selectedDownloads: [],\r\n      // 版本列表\r\n      versionList: [\r\n        { version: '6.2.9', date: '2024.01.19' },\r\n        { version: '6.2.8', date: '2023.12.15' },\r\n        { version: '6.2.7', date: '2023.11.20' },\r\n        { version: '6.2.6', date: '2023.10.18' },\r\n        { version: '6.2.5', date: '2023.09.25' },\r\n        { version: '6.2.4', date: '2023.08.30' },\r\n        { version: '6.2.3', date: '2023.07.28' },\r\n        { version: '6.2.2', date: '2023.06.22' },\r\n        { version: '6.2.1', date: '2023.05.15' },\r\n        { version: '6.2.0', date: '2023.04.10' }\r\n      ],\r\n      // 当前选中的版本\r\n      selectedVersion: {\r\n        version: '6.2.9',\r\n        date: '2024.01.19',\r\n        notes: [\r\n          '1. 【新增功能】新增<b>个人中心</b>功能，用户可以查看和管理个人信息。',\r\n          '2. 【功能优化】优化了<b>搜索引擎</b>的性能，提高了搜索速度和准确性。',\r\n          '3. 【界面调整】调整了<b>首页布局</b>，使界面更加简洁美观。',\r\n          '4. 【问题修复】修复了在某些情况下<b>\"数据导出\"</b>功能失效的问题。',\r\n          '5. 【安全增强】增强了系统的<b>安全性</b>，提高了数据保护能力。'\r\n        ]\r\n      },\r\n      // 素材库相关\r\n      addMaterialDialogVisible: false,\r\n      newMaterialName: '',\r\n      materialList: [],\r\n      // 修改密码相关\r\n      showPasswordChange: false,\r\n      passwordForm: {\r\n        oldPassword: '',\r\n        newPassword: '',\r\n        confirmPassword: ''\r\n      },\r\n      // 我的收藏相关\r\n      selectAllFavorites: false,\r\n      selectedFavorites: [],\r\n      favoriteStartDate: '',\r\n      favoriteEndDate: '',\r\n      favoriteSearchKeyword: '',\r\n      favoriteList: [\r\n        {\r\n          id: 1,\r\n          selected: false,\r\n          tag: 'HOT',\r\n          title: '新产品市场分析报告',\r\n          time: '2023-04-29 20:37:51',\r\n          source: '市场调查部门 A 数据库'\r\n        },\r\n        {\r\n          id: 2,\r\n          selected: false,\r\n          tag: 'NEW',\r\n          title: '2024年第一季度行业趋势分析',\r\n          time: '2024-04-28 15:22:36',\r\n          source: '行业研究中心'\r\n        },\r\n        {\r\n          id: 3,\r\n          selected: false,\r\n          tag: 'HOT',\r\n          title: '竞品分析与市场定位策略',\r\n          time: '2024-04-27 09:15:42',\r\n          source: '战略规划部'\r\n        },\r\n        {\r\n          id: 4,\r\n          selected: false,\r\n          tag: 'TOP',\r\n          title: '用户行为数据分析报告',\r\n          time: '2024-04-26 14:30:18',\r\n          source: '数据分析部门'\r\n        },\r\n        {\r\n          id: 5,\r\n          selected: false,\r\n          tag: 'NEW',\r\n          title: '新媒体营销策略白皮书',\r\n          time: '2024-04-25 11:45:23',\r\n          source: '营销部门'\r\n        },\r\n        {\r\n          id: 6,\r\n          selected: false,\r\n          tag: 'HOT',\r\n          title: '产品迭代计划与路线图',\r\n          time: '2024-04-24 16:20:37',\r\n          source: '产品部门'\r\n        },\r\n        {\r\n          id: 7,\r\n          selected: false,\r\n          tag: 'TOP',\r\n          title: '行业政策解读与影响分析',\r\n          time: '2024-04-23 10:05:12',\r\n          source: '政策研究中心'\r\n        }\r\n      ],\r\n      favoritesTotal: 7,\r\n      favoritesCurrentPage: 1,\r\n      favoritesPageSize: 10,\r\n      favoritesJumpPage: '',\r\n\r\n      // 我的联系人相关\r\n      contactActiveTab: 'wechat', // 默认选中微信标签\r\n      contactList: [\r\n        {\r\n          id: 1,\r\n          name: '张三',\r\n          phone: '13800138001',\r\n          email: '<EMAIL>',\r\n          location: '北京市朝阳区',\r\n          createTime: '2024-04-28 10:30:45'\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '李四',\r\n          phone: '13800138002',\r\n          email: '<EMAIL>',\r\n          location: '上海市浦东新区',\r\n          createTime: '2024-04-27 15:20:36'\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '王五',\r\n          phone: '13800138003',\r\n          email: '<EMAIL>',\r\n          location: '广州市天河区',\r\n          createTime: '2024-04-26 09:15:22'\r\n        }\r\n      ],\r\n      addContactDialogVisible: false,\r\n      contactForm: {\r\n        id: null,\r\n        name: '',\r\n        phone: '',\r\n        email: '',\r\n        location: ''\r\n      },\r\n      contactFormRules: {\r\n        name: [\r\n          { required: true, message: '请输入姓名', trigger: 'blur' }\r\n        ],\r\n        phone: [\r\n          { required: true, message: '请输入手机号码', trigger: 'blur' },\r\n          { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }\r\n        ],\r\n        email: [\r\n          { required: true, message: '请输入邮箱', trigger: 'blur' },\r\n          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  methods: {\r\n    // 处理菜单项选择\r\n    handleMenuSelect(index) {\r\n      this.activeMenuItem = index;\r\n    },\r\n    // 显示账户信息\r\n    showAccountInfo() {\r\n      this.showPasswordChange = false;\r\n    },\r\n    // 显示修改密码表单\r\n    showPasswordForm() {\r\n      this.showPasswordChange = true;\r\n      this.passwordForm = {\r\n        oldPassword: '',\r\n        newPassword: '',\r\n        confirmPassword: ''\r\n      };\r\n    },\r\n    // 修改密码\r\n    changePassword() {\r\n      // 表单验证\r\n      if (!this.passwordForm.oldPassword) {\r\n        this.$message.warning('请输入旧密码');\r\n        return;\r\n      }\r\n      if (!this.passwordForm.newPassword) {\r\n        this.$message.warning('请输入新密码');\r\n        return;\r\n      }\r\n      if (!this.passwordForm.confirmPassword) {\r\n        this.$message.warning('请确认新密码');\r\n        return;\r\n      }\r\n      if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {\r\n        this.$message.warning('两次输入的新密码不一致');\r\n        return;\r\n      }\r\n\r\n      // 提交修改密码请求\r\n      this.$message.success('密码修改成功');\r\n      this.showPasswordChange = false;\r\n    },\r\n    // 切换设置选项卡\r\n    switchSettingsTab(tab) {\r\n      this.currentSettingsTab = tab;\r\n    },\r\n    // 保存设置\r\n    saveSettings() {\r\n      this.$message.success('设置已保存');\r\n    },\r\n    // 处理表格选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedDownloads = selection;\r\n    },\r\n    // 批量下载\r\n    batchDownload() {\r\n      if (this.selectedDownloads.length === 0) {\r\n        this.$message.warning('请选择要下载的文件');\r\n        return;\r\n      }\r\n      this.$message.success(`已开始下载${this.selectedDownloads.length}个文件`);\r\n    },\r\n    // 批量删除\r\n    batchDelete() {\r\n      if (this.selectedDownloads.length === 0) {\r\n        this.$message.warning('请选择要删除的文件');\r\n        return;\r\n      }\r\n      this.$confirm('确定要删除选中的文件吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$message.success(`已删除${this.selectedDownloads.length}个文件`);\r\n        // 实际应用中这里需要调用接口删除文件\r\n      }).catch(() => {\r\n        this.$message.info('已取消删除');\r\n      });\r\n    },\r\n    // 下载单个文件\r\n    handleDownload(row) {\r\n      this.$message.success(`开始下载: ${row.name}`);\r\n    },\r\n    // 删除单个文件\r\n    handleDelete(row) {\r\n      this.$confirm('确定要删除该文件吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$message.success(`已删除: ${row.name}`);\r\n        // 实际应用中这里需要调用接口删除文件\r\n      }).catch(() => {\r\n        this.$message.info('已取消删除');\r\n      });\r\n    },\r\n    // 处理页码变化\r\n    handleCurrentChange(val) {\r\n      this.currentPage = val;\r\n      // 实际应用中这里需要调用接口获取对应页的数据\r\n    },\r\n    // 处理跳转页面\r\n    handleJumpPage() {\r\n      if (!this.jumpPage) {\r\n        return;\r\n      }\r\n      const page = parseInt(this.jumpPage);\r\n      if (isNaN(page) || page < 1 || page > Math.ceil(this.total / this.pageSize)) {\r\n        this.$message.warning('请输入有效的页码');\r\n        return;\r\n      }\r\n      this.currentPage = page;\r\n      // 实际应用中这里需要调用接口获取对应页的数据\r\n    },\r\n    // 选择版本\r\n    selectVersion(version) {\r\n      const versionData = this.versionList.find(v => v.version === version);\r\n      if (versionData) {\r\n        // 根据版本号获取对应的更新说明\r\n        let notes = [];\r\n        if (version === '6.2.9') {\r\n          notes = [\r\n            '1. 【新增功能】新增<b>个人中心</b>功能，用户可以查看和管理个人信息。',\r\n            '2. 【功能优化】优化了<b>搜索引擎</b>的性能，提高了搜索速度和准确性。',\r\n            '3. 【界面调整】调整了<b>首页布局</b>，使界面更加简洁美观。',\r\n            '4. 【问题修复】修复了在某些情况下<b>\"数据导出\"</b>功能失效的问题。',\r\n            '5. 【安全增强】增强了系统的<b>安全性</b>，提高了数据保护能力。'\r\n          ];\r\n        } else if (version === '6.2.8') {\r\n          notes = [\r\n            '1. 【新增功能】新增<b>数据分析</b>模块，提供更全面的数据统计。',\r\n            '2. 【功能优化】优化了<b>文件上传</b>功能，支持更多文件格式。',\r\n            '3. 【问题修复】修复了部分用户<b>无法登录</b>的问题。'\r\n          ];\r\n        } else {\r\n          notes = [\r\n            '1. 【功能优化】优化系统性能，提升用户体验。',\r\n            '2. 【问题修复】修复已知问题，提高系统稳定性。'\r\n          ];\r\n        }\r\n\r\n        this.selectedVersion = {\r\n          version: versionData.version,\r\n          date: versionData.date,\r\n          notes: notes\r\n        };\r\n      }\r\n    },\r\n    // 显示添加素材对话框\r\n    showAddMaterialDialog() {\r\n      this.newMaterialName = '';\r\n      this.addMaterialDialogVisible = true;\r\n    },\r\n    // 添加素材\r\n    addMaterial() {\r\n      if (!this.newMaterialName.trim()) {\r\n        this.$message.warning('请输入素材名称');\r\n        return;\r\n      }\r\n\r\n      // 添加新素材\r\n      this.materialList.push({\r\n        id: Date.now(),\r\n        name: this.newMaterialName,\r\n        createTime: new Date().toLocaleString()\r\n      });\r\n\r\n      this.$message.success(`素材\"${this.newMaterialName}\"创建成功`);\r\n      this.addMaterialDialogVisible = false;\r\n    },\r\n\r\n    // 处理收藏项选择\r\n    handleFavoriteSelect() {\r\n      this.selectedFavorites = this.favoriteList.filter(item => item.selected);\r\n      // 检查是否全选\r\n      this.selectAllFavorites = this.selectedFavorites.length === this.favoriteList.length;\r\n    },\r\n\r\n    // 全选/取消全选收藏项\r\n    watchSelectAllFavorites(val) {\r\n      this.favoriteList.forEach(item => {\r\n        item.selected = val;\r\n      });\r\n      this.selectedFavorites = val ? [...this.favoriteList] : [];\r\n    },\r\n\r\n    // 批量取消收藏\r\n    batchCancelFavorite() {\r\n      if (this.selectedFavorites.length === 0) {\r\n        this.$message.warning('请选择要取消收藏的项');\r\n        return;\r\n      }\r\n\r\n      this.$confirm('确定要取消选中的收藏吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 实际应用中这里需要调用接口取消收藏\r\n        const selectedIds = this.selectedFavorites.map(item => item.id);\r\n        this.favoriteList = this.favoriteList.filter(item => !selectedIds.includes(item.id));\r\n        this.selectedFavorites = [];\r\n        this.selectAllFavorites = false;\r\n        this.$message.success('已取消收藏');\r\n      }).catch(() => {\r\n        this.$message.info('已取消操作');\r\n      });\r\n    },\r\n\r\n    // 取消单个收藏\r\n    cancelFavorite(item) {\r\n      this.$confirm('确定要取消收藏\"' + item.title + '\"吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 实际应用中这里需要调用接口取消收藏\r\n        this.favoriteList = this.favoriteList.filter(i => i.id !== item.id);\r\n        // 更新选中的收藏项\r\n        this.selectedFavorites = this.selectedFavorites.filter(i => i.id !== item.id);\r\n        // 更新总数\r\n        this.favoritesTotal = this.favoriteList.length;\r\n        this.$message.success('已取消收藏');\r\n      }).catch(() => {\r\n        this.$message.info('已取消操作');\r\n      });\r\n    },\r\n\r\n    // 显示添加联系人对话框\r\n    showAddContactDialog() {\r\n      this.contactForm = {\r\n        id: null,\r\n        name: '',\r\n        phone: '',\r\n        email: '',\r\n        location: ''\r\n      };\r\n      this.addContactDialogVisible = true;\r\n    },\r\n\r\n    // 提交联系人表单\r\n    submitContactForm() {\r\n      this.$refs.contactForm.validate(valid => {\r\n        if (valid) {\r\n          if (this.contactForm.id) {\r\n            // 编辑联系人\r\n            const index = this.contactList.findIndex(item => item.id === this.contactForm.id);\r\n            if (index !== -1) {\r\n              // 更新创建时间\r\n              this.contactForm.createTime = new Date().toLocaleString('zh-CN', {\r\n                year: 'numeric',\r\n                month: '2-digit',\r\n                day: '2-digit',\r\n                hour: '2-digit',\r\n                minute: '2-digit',\r\n                second: '2-digit',\r\n                hour12: false\r\n              }).replace(/\\//g, '-');\r\n              this.contactList.splice(index, 1, this.contactForm);\r\n              this.$message.success('联系人修改成功');\r\n            }\r\n          } else {\r\n            // 添加联系人\r\n            const newContact = {\r\n              ...this.contactForm,\r\n              id: this.contactList.length > 0 ? Math.max(...this.contactList.map(item => item.id)) + 1 : 1,\r\n              createTime: new Date().toLocaleString('zh-CN', {\r\n                year: 'numeric',\r\n                month: '2-digit',\r\n                day: '2-digit',\r\n                hour: '2-digit',\r\n                minute: '2-digit',\r\n                second: '2-digit',\r\n                hour12: false\r\n              }).replace(/\\//g, '-')\r\n            };\r\n            this.contactList.push(newContact);\r\n            this.$message.success('联系人添加成功');\r\n          }\r\n          this.addContactDialogVisible = false;\r\n        }\r\n      });\r\n    },\r\n\r\n    // 编辑联系人\r\n    handleEditContact(row) {\r\n      this.contactForm = JSON.parse(JSON.stringify(row));\r\n      this.addContactDialogVisible = true;\r\n    },\r\n\r\n    // 删除联系人\r\n    handleDeleteContact(row) {\r\n      this.$confirm(`确定要删除联系人\"${row.name}\"吗？`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 实际应用中这里需要调用接口删除联系人\r\n        this.contactList = this.contactList.filter(item => item.id !== row.id);\r\n        this.$message.success('联系人删除成功');\r\n      }).catch(() => {\r\n        this.$message.info('已取消删除');\r\n      });\r\n    },\r\n\r\n    // 导出收藏\r\n    exportFavorites() {\r\n      this.$message.success('开始导出收藏');\r\n      // 实际应用中这里需要调用接口导出收藏\r\n    },\r\n\r\n    // 处理收藏分页变化\r\n    handleFavoritePageChange(val) {\r\n      this.favoritesCurrentPage = val;\r\n      // 实际应用中这里需要调用接口获取对应页的数据\r\n    },\r\n\r\n    // 处理收藏跳转页面\r\n    handleFavoriteJumpPage() {\r\n      if (!this.favoritesJumpPage) {\r\n        return;\r\n      }\r\n\r\n      const page = parseInt(this.favoritesJumpPage);\r\n      if (isNaN(page) || page < 1 || page > Math.ceil(this.favoritesTotal / this.favoritesPageSize)) {\r\n        this.$message.warning('请输入有效的页码');\r\n        return;\r\n      }\r\n\r\n      this.favoritesCurrentPage = page;\r\n      // 实际应用中这里需要调用接口获取对应页的数据\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background-color: #f5f5f5;\r\n  min-height: 100vh;\r\n  padding: 20px;\r\n}\r\n\r\n.page-layout {\r\n  display: flex;\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.left-sidebar {\r\n  width: 200px;\r\n  background-color: #fff;\r\n  border-right: 1px solid #e6e6e6;\r\n}\r\n\r\n.user-info {\r\n  padding: 20px;\r\n  text-align: center;\r\n  border-bottom: 1px solid #e6e6e6;\r\n\r\n  .avatar {\r\n    width: 60px;\r\n    height: 60px;\r\n    margin: 0 auto 10px;\r\n    border-radius: 50%;\r\n    overflow: hidden;\r\n\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n      object-fit: cover;\r\n    }\r\n  }\r\n\r\n  .user-id {\r\n    font-size: 16px;\r\n    font-weight: bold;\r\n    margin-bottom: 5px;\r\n  }\r\n\r\n  .register-date {\r\n    font-size: 12px;\r\n    color: #999;\r\n  }\r\n}\r\n\r\n.sidebar-menu {\r\n  .sidebar-menu-list {\r\n    border-right: none;\r\n  }\r\n\r\n  .el-menu-item {\r\n    height: 50px;\r\n    line-height: 50px;\r\n\r\n    i {\r\n      margin-right: 5px;\r\n      color: #666;\r\n    }\r\n  }\r\n\r\n  .el-menu-item.is-active {\r\n    background-color: #f0f9eb;\r\n    color: #67c23a;\r\n\r\n    i {\r\n      color: #67c23a;\r\n    }\r\n  }\r\n}\r\n\r\n.content {\r\n  flex: 1;\r\n  padding: 20px;\r\n}\r\n\r\n.account-container {\r\n  background-color: #fff;\r\n}\r\n\r\n.account-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-bottom: 20px;\r\n  margin-bottom: 20px;\r\n  border-bottom: 1px solid #eee;\r\n\r\n  .title {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n.account-info {\r\n  .info-item {\r\n    display: flex;\r\n    margin-bottom: 20px;\r\n    line-height: 24px;\r\n\r\n    .label {\r\n      width: 120px;\r\n      color: #666;\r\n      text-align: right;\r\n      padding-right: 10px;\r\n    }\r\n\r\n    .value {\r\n      flex: 1;\r\n      color: #333;\r\n    }\r\n  }\r\n}\r\n\r\n/* 系统设置样式 */\r\n.settings-container {\r\n  background-color: #fff;\r\n}\r\n\r\n.settings-header {\r\n  padding-bottom: 15px;\r\n  margin-bottom: 15px;\r\n\r\n  .title {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n.settings-content {\r\n  display: flex;\r\n}\r\n\r\n.settings-sidebar {\r\n  width: 120px;\r\n  border-right: 1px solid #eee;\r\n  padding-right: 10px;\r\n}\r\n\r\n.settings-menu {\r\n  .menu-item {\r\n    padding: 10px 0;\r\n    cursor: pointer;\r\n    display: flex;\r\n    align-items: center;\r\n    color: #666;\r\n    font-size: 14px;\r\n\r\n    i {\r\n      margin-right: 8px;\r\n      color: #1890ff;\r\n    }\r\n\r\n    &.active {\r\n      color: #1890ff;\r\n      font-weight: bold;\r\n    }\r\n\r\n    &:hover {\r\n      color: #1890ff;\r\n    }\r\n  }\r\n}\r\n\r\n.settings-main {\r\n  flex: 1;\r\n  padding-left: 20px;\r\n}\r\n\r\n.settings-section {\r\n  .section-title {\r\n    font-weight: bold;\r\n    margin: 15px 0 10px;\r\n    color: #333;\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n.options-group {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin-bottom: 15px;\r\n\r\n  .el-checkbox {\r\n    margin-right: 15px;\r\n    margin-bottom: 10px;\r\n    min-width: 80px;\r\n  }\r\n}\r\n\r\n.download-settings {\r\n  margin-bottom: 15px;\r\n\r\n  .setting-item {\r\n    display: flex;\r\n    margin-bottom: 10px;\r\n    font-size: 14px;\r\n\r\n    .label {\r\n      width: 80px;\r\n      color: #666;\r\n    }\r\n\r\n    .value {\r\n      flex: 1;\r\n      color: #333;\r\n    }\r\n  }\r\n}\r\n\r\n.save-button {\r\n  margin-top: 20px;\r\n\r\n  .el-button {\r\n    background-color: #1890ff;\r\n    border-color: #1890ff;\r\n    padding: 8px 20px;\r\n  }\r\n}\r\n\r\n/* 我的下载样式 */\r\n.download-container {\r\n  background-color: #fff;\r\n}\r\n\r\n.download-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-bottom: 15px;\r\n  margin-bottom: 15px;\r\n\r\n  .title {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .actions {\r\n    .el-button {\r\n      margin-left: 10px;\r\n    }\r\n  }\r\n}\r\n\r\n.download-content {\r\n  .el-table {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .download-status {\r\n    color: #67c23a;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-top: 20px;\r\n\r\n  span {\r\n    margin: 0 5px;\r\n  }\r\n\r\n  .jump-page-input {\r\n    width: 50px;\r\n    margin: 0 5px;\r\n  }\r\n}\r\n\r\n/* 修改密码表单样式 */\r\n.password-form {\r\n  max-width: 500px;\r\n  margin: 20px 0;\r\n\r\n  .form-group {\r\n    margin-bottom: 20px;\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .form-label {\r\n      width: 100px;\r\n      text-align: right;\r\n      margin-right: 15px;\r\n      color: #606266;\r\n    }\r\n\r\n    .el-input {\r\n      width: 300px;\r\n    }\r\n  }\r\n\r\n  .form-actions {\r\n    margin-top: 30px;\r\n    padding-left: 115px;\r\n\r\n    .el-button {\r\n      width: 100px;\r\n    }\r\n  }\r\n}\r\n\r\n/* 我的收藏样式 */\r\n.favorite-container {\r\n  background-color: #fff;\r\n}\r\n\r\n.favorite-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-bottom: 15px;\r\n  margin-bottom: 15px;\r\n  border-bottom: 1px solid #eee;\r\n\r\n  .title {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n.favorite-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n\r\n  .toolbar-left {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .el-checkbox {\r\n      margin-right: 15px;\r\n    }\r\n\r\n    .el-button {\r\n      margin-right: 10px;\r\n    }\r\n  }\r\n\r\n  .toolbar-right {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .date-filter {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-right: 15px;\r\n\r\n      .date-separator {\r\n        margin: 0 5px;\r\n      }\r\n    }\r\n\r\n    .search-box {\r\n      margin-right: 15px;\r\n    }\r\n  }\r\n}\r\n\r\n.favorite-content {\r\n  margin-top: 20px;\r\n\r\n  .favorite-list {\r\n    border: 1px solid #ebeef5;\r\n    border-radius: 4px;\r\n\r\n    .favorite-item {\r\n      display: flex;\r\n      padding: 15px;\r\n      border-bottom: 1px solid #ebeef5;\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      .item-checkbox {\r\n        margin-right: 15px;\r\n        display: flex;\r\n        align-items: center;\r\n      }\r\n\r\n      .item-content {\r\n        flex: 1;\r\n\r\n        .item-title {\r\n          margin-bottom: 8px;\r\n\r\n          .title-tag {\r\n            display: inline-block;\r\n            padding: 2px 6px;\r\n            font-size: 12px;\r\n            color: #fff;\r\n            background-color: #f56c6c;\r\n            border-radius: 2px;\r\n            margin-right: 8px;\r\n          }\r\n\r\n          .title-text {\r\n            font-size: 16px;\r\n            font-weight: 500;\r\n            color: #303133;\r\n          }\r\n        }\r\n\r\n        .item-info {\r\n          font-size: 13px;\r\n          color: #909399;\r\n\r\n          .info-time {\r\n            margin-right: 15px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .item-actions {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-left: 15px;\r\n\r\n        .cancel-favorite-btn {\r\n          color: #f56c6c;\r\n\r\n          &:hover {\r\n            color: #ff7c7c;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .favorite-pagination {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-top: 20px;\r\n    padding: 10px 0;\r\n\r\n    .pagination-info {\r\n      font-size: 14px;\r\n      color: #606266;\r\n\r\n      span {\r\n        font-weight: bold;\r\n        color: #303133;\r\n      }\r\n    }\r\n\r\n    .pagination-controls {\r\n      flex: 1;\r\n      text-align: center;\r\n    }\r\n\r\n    .pagination-jump {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      span {\r\n        margin: 0 5px;\r\n        font-size: 14px;\r\n        color: #606266;\r\n      }\r\n\r\n      .jump-page-input {\r\n        width: 50px;\r\n        margin: 0 5px;\r\n      }\r\n\r\n      .el-button {\r\n        margin-left: 5px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 我的联系人样式 */\r\n.contact-container {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.contact-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n\r\n  .title {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .contact-tabs {\r\n    .el-radio-group {\r\n      .el-radio-button {\r\n        margin-right: -1px;\r\n\r\n        &:first-child .el-radio-button__inner {\r\n          border-radius: 4px 0 0 4px;\r\n        }\r\n\r\n        &:last-child .el-radio-button__inner {\r\n          border-radius: 0 4px 4px 0;\r\n        }\r\n\r\n        .el-radio-button__inner {\r\n          padding: 8px 15px;\r\n          font-size: 13px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.contact-toolbar {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.contact-content {\r\n  .el-table {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .delete-btn {\r\n    color: #f56c6c;\r\n  }\r\n}\r\n\r\n/* 素材库样式 */\r\n.material-container {\r\n  background-color: #fff;\r\n}\r\n\r\n.material-header {\r\n  padding-bottom: 15px;\r\n  margin-bottom: 15px;\r\n\r\n  .title {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n.material-content {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n\r\n  .material-add-box {\r\n    width: 200px;\r\n    height: 150px;\r\n    border: 1px dashed #d9d9d9;\r\n    border-radius: 4px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n    cursor: pointer;\r\n    margin-right: 20px;\r\n    margin-bottom: 20px;\r\n\r\n    &:hover {\r\n      border-color: #1890ff;\r\n\r\n      .add-icon, .add-text {\r\n        color: #1890ff;\r\n      }\r\n    }\r\n\r\n    .add-icon {\r\n      font-size: 30px;\r\n      color: #8c8c8c;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .add-text {\r\n      font-size: 14px;\r\n      color: #8c8c8c;\r\n    }\r\n  }\r\n}\r\n\r\n.material-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px;\r\n    text-align: center;\r\n    border-bottom: 1px solid #f0f0f0;\r\n  }\r\n\r\n  .material-form {\r\n    padding: 20px 0;\r\n\r\n    .form-item {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 15px;\r\n\r\n      .form-label {\r\n        width: 80px;\r\n        text-align: right;\r\n        margin-right: 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .dialog-footer {\r\n    text-align: center;\r\n\r\n    .el-button {\r\n      width: 80px;\r\n    }\r\n  }\r\n}\r\n\r\n/* 更新说明样式 */\r\n.updates-section {\r\n  .updates-header {\r\n    margin-bottom: 20px;\r\n\r\n    .version-title {\r\n      font-size: 16px;\r\n      font-weight: bold;\r\n      margin-bottom: 5px;\r\n    }\r\n\r\n    .version-date {\r\n      font-size: 12px;\r\n      color: #999;\r\n    }\r\n  }\r\n\r\n  .updates-content {\r\n    display: flex;\r\n\r\n    .version-list {\r\n      width: 100px;\r\n      border-right: 1px solid #eee;\r\n      padding-right: 15px;\r\n      margin-right: 20px;\r\n\r\n      .version-item {\r\n        padding: 8px 0;\r\n        cursor: pointer;\r\n        color: #666;\r\n        font-size: 14px;\r\n        text-align: center;\r\n        border-radius: 4px;\r\n        margin-bottom: 5px;\r\n\r\n        &:hover {\r\n          background-color: #f5f7fa;\r\n        }\r\n\r\n        &.active {\r\n          background-color: #e6f7ff;\r\n          color: #1890ff;\r\n          font-weight: bold;\r\n        }\r\n      }\r\n    }\r\n\r\n    .update-details {\r\n      flex: 1;\r\n\r\n      .update-notes {\r\n        .note-item {\r\n          display: flex;\r\n          margin-bottom: 10px;\r\n          line-height: 1.6;\r\n\r\n          .note-number {\r\n            margin-right: 5px;\r\n            color: #666;\r\n          }\r\n\r\n          .note-content {\r\n            flex: 1;\r\n\r\n            b {\r\n              color: #1890ff;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAimBA,IAAAA,eAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,UAAA;IACAC,cAAA,EAAAA;EACA;EACAC,KAAA;IACAC,kBAAA,WAAAA,mBAAAC,GAAA;MACA,KAAAC,uBAAA,CAAAD,GAAA;IACA;EACA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,cAAA;MAAA;MACAC,kBAAA;MAAA;MACA;MACAC,QAAA;QACAC,MAAA;QACAC,WAAA;QACAC,YAAA;QACAC,QAAA;QACAC,IAAA;QACAC,WAAA;QACAC,WAAA;QACAC,eAAA;QACAC,aAAA;QACAC,cAAA;QACAC,cAAA;MACA;MACA;MACAC,QAAA;QACAC,IAAA;QACAC,MAAA;QACAC,cAAA;QACAC,MAAA;QACAC,YAAA;QACAC,WAAA;QACAC,WAAA;QACAC,QAAA;QACAC,WAAA;QACAC,aAAA;QACAC,QAAA;QACAC,YAAA;QACAC,YAAA;QACAC,MAAA;QACAC,OAAA;MACA;MACA;MACAC,YAAA,GACA;QACAC,EAAA;QACAvC,IAAA;QACAwC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA,GACA;QACAH,EAAA;QACAvC,IAAA;QACAwC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA,GACA;QACAH,EAAA;QACAvC,IAAA;QACAwC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA,GACA;QACAH,EAAA;QACAvC,IAAA;QACAwC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA,GACA;QACAH,EAAA;QACAvC,IAAA;QACAwC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA,GACA;QACAH,EAAA;QACAvC,IAAA;QACAwC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA,GACA;QACAH,EAAA;QACAvC,IAAA;QACAwC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA,GACA;QACAH,EAAA;QACAvC,IAAA;QACAwC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA,GACA;QACAH,EAAA;QACAvC,IAAA;QACAwC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA,GACA;QACAH,EAAA;QACAvC,IAAA;QACAwC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA,GACA;QACAH,EAAA;QACAvC,IAAA;QACAwC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA,GACA;QACAH,EAAA;QACAvC,IAAA;QACAwC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA,EACA;MACA;MACAC,KAAA;MACAC,WAAA;MACAC,QAAA;MACAC,QAAA;MACA;MACAC,iBAAA;MACA;MACAC,WAAA,GACA;QAAAC,OAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,OAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,OAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,OAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,OAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,OAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,OAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,OAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,OAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,OAAA;QAAAC,IAAA;MAAA,EACA;MACA;MACAC,eAAA;QACAF,OAAA;QACAC,IAAA;QACAE,KAAA,GACA,2CACA,2CACA,sCACA,2CACA;MAEA;MACA;MACAC,wBAAA;MACAC,eAAA;MACAC,YAAA;MACA;MACAC,kBAAA;MACAC,YAAA;QACAC,WAAA;QACAC,WAAA;QACAC,eAAA;MACA;MACA;MACAxD,kBAAA;MACAyD,iBAAA;MACAC,iBAAA;MACAC,eAAA;MACAC,qBAAA;MACAC,YAAA,GACA;QACA1B,EAAA;QACA2B,QAAA;QACAC,GAAA;QACAC,KAAA;QACAC,IAAA;QACAC,MAAA;MACA,GACA;QACA/B,EAAA;QACA2B,QAAA;QACAC,GAAA;QACAC,KAAA;QACAC,IAAA;QACAC,MAAA;MACA,GACA;QACA/B,EAAA;QACA2B,QAAA;QACAC,GAAA;QACAC,KAAA;QACAC,IAAA;QACAC,MAAA;MACA,GACA;QACA/B,EAAA;QACA2B,QAAA;QACAC,GAAA;QACAC,KAAA;QACAC,IAAA;QACAC,MAAA;MACA,GACA;QACA/B,EAAA;QACA2B,QAAA;QACAC,GAAA;QACAC,KAAA;QACAC,IAAA;QACAC,MAAA;MACA,GACA;QACA/B,EAAA;QACA2B,QAAA;QACAC,GAAA;QACAC,KAAA;QACAC,IAAA;QACAC,MAAA;MACA,GACA;QACA/B,EAAA;QACA2B,QAAA;QACAC,GAAA;QACAC,KAAA;QACAC,IAAA;QACAC,MAAA;MACA,EACA;MACAC,cAAA;MACAC,oBAAA;MACAC,iBAAA;MACAC,iBAAA;MAEA;MACAC,gBAAA;MAAA;MACAC,WAAA,GACA;QACArC,EAAA;QACAvC,IAAA;QACA6E,KAAA;QACAC,KAAA;QACAC,QAAA;QACAtC,UAAA;MACA,GACA;QACAF,EAAA;QACAvC,IAAA;QACA6E,KAAA;QACAC,KAAA;QACAC,QAAA;QACAtC,UAAA;MACA,GACA;QACAF,EAAA;QACAvC,IAAA;QACA6E,KAAA;QACAC,KAAA;QACAC,QAAA;QACAtC,UAAA;MACA,EACA;MACAuC,uBAAA;MACAC,WAAA;QACA1C,EAAA;QACAvC,IAAA;QACA6E,KAAA;QACAC,KAAA;QACAC,QAAA;MACA;MACAG,gBAAA;QACAlF,IAAA,GACA;UAAAmF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,KAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,OAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,KAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAE,IAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAG,OAAA;IACA;IACAC,gBAAA,WAAAA,iBAAAC,KAAA;MACA,KAAAlF,cAAA,GAAAkF,KAAA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAAnC,kBAAA;IACA;IACA;IACAoC,gBAAA,WAAAA,iBAAA;MACA,KAAApC,kBAAA;MACA,KAAAC,YAAA;QACAC,WAAA;QACAC,WAAA;QACAC,eAAA;MACA;IACA;IACA;IACAiC,cAAA,WAAAA,eAAA;MACA;MACA,UAAApC,YAAA,CAAAC,WAAA;QACA,KAAAoC,QAAA,CAAAC,OAAA;QACA;MACA;MACA,UAAAtC,YAAA,CAAAE,WAAA;QACA,KAAAmC,QAAA,CAAAC,OAAA;QACA;MACA;MACA,UAAAtC,YAAA,CAAAG,eAAA;QACA,KAAAkC,QAAA,CAAAC,OAAA;QACA;MACA;MACA,SAAAtC,YAAA,CAAAE,WAAA,UAAAF,YAAA,CAAAG,eAAA;QACA,KAAAkC,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,KAAAD,QAAA,CAAAE,OAAA;MACA,KAAAxC,kBAAA;IACA;IACA;IACAyC,iBAAA,WAAAA,kBAAAC,GAAA;MACA,KAAAzF,kBAAA,GAAAyF,GAAA;IACA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAL,QAAA,CAAAE,OAAA;IACA;IACA;IACAI,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAtD,iBAAA,GAAAsD,SAAA;IACA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA,SAAAvD,iBAAA,CAAAwD,MAAA;QACA,KAAAT,QAAA,CAAAC,OAAA;QACA;MACA;MACA,KAAAD,QAAA,CAAAE,OAAA,kCAAAQ,MAAA,MAAAzD,iBAAA,CAAAwD,MAAA;IACA;IACA;IACAE,WAAA,WAAAA,YAAA;MAAA,IAAAC,KAAA;MACA,SAAA3D,iBAAA,CAAAwD,MAAA;QACA,KAAAT,QAAA,CAAAC,OAAA;QACA;MACA;MACA,KAAAY,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAtB,IAAA;MACA,GAAAuB,IAAA;QACAJ,KAAA,CAAAZ,QAAA,CAAAE,OAAA,sBAAAQ,MAAA,CAAAE,KAAA,CAAA3D,iBAAA,CAAAwD,MAAA;QACA;MACA,GAAAQ,KAAA;QACAL,KAAA,CAAAZ,QAAA,CAAAkB,IAAA;MACA;IACA;IACA;IACAC,cAAA,WAAAA,eAAAC,GAAA;MACA,KAAApB,QAAA,CAAAE,OAAA,8BAAAQ,MAAA,CAAAU,GAAA,CAAAlH,IAAA;IACA;IACA;IACAmH,YAAA,WAAAA,aAAAD,GAAA;MAAA,IAAAE,MAAA;MACA,KAAAT,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAtB,IAAA;MACA,GAAAuB,IAAA;QACAM,MAAA,CAAAtB,QAAA,CAAAE,OAAA,wBAAAQ,MAAA,CAAAU,GAAA,CAAAlH,IAAA;QACA;MACA,GAAA+G,KAAA;QACAK,MAAA,CAAAtB,QAAA,CAAAkB,IAAA;MACA;IACA;IACA;IACAK,mBAAA,WAAAA,oBAAAhH,GAAA;MACA,KAAAuC,WAAA,GAAAvC,GAAA;MACA;IACA;IACA;IACAiH,cAAA,WAAAA,eAAA;MACA,UAAAxE,QAAA;QACA;MACA;MACA,IAAAyE,IAAA,GAAAC,QAAA,MAAA1E,QAAA;MACA,IAAA2E,KAAA,CAAAF,IAAA,KAAAA,IAAA,QAAAA,IAAA,GAAAG,IAAA,CAAAC,IAAA,MAAAhF,KAAA,QAAAE,QAAA;QACA,KAAAiD,QAAA,CAAAC,OAAA;QACA;MACA;MACA,KAAAnD,WAAA,GAAA2E,IAAA;MACA;IACA;IACA;IACAK,aAAA,WAAAA,cAAA3E,OAAA;MACA,IAAA4E,WAAA,QAAA7E,WAAA,CAAA8E,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAA9E,OAAA,KAAAA,OAAA;MAAA;MACA,IAAA4E,WAAA;QACA;QACA,IAAAzE,KAAA;QACA,IAAAH,OAAA;UACAG,KAAA,IACA,2CACA,2CACA,sCACA,2CACA,uCACA;QACA,WAAAH,OAAA;UACAG,KAAA,IACA,wCACA,uCACA,kCACA;QACA;UACAA,KAAA,IACA,2BACA,2BACA;QACA;QAEA,KAAAD,eAAA;UACAF,OAAA,EAAA4E,WAAA,CAAA5E,OAAA;UACAC,IAAA,EAAA2E,WAAA,CAAA3E,IAAA;UACAE,KAAA,EAAAA;QACA;MACA;IACA;IACA;IACA4E,qBAAA,WAAAA,sBAAA;MACA,KAAA1E,eAAA;MACA,KAAAD,wBAAA;IACA;IACA;IACA4E,WAAA,WAAAA,YAAA;MACA,UAAA3E,eAAA,CAAA4E,IAAA;QACA,KAAApC,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,KAAAxC,YAAA,CAAA4E,IAAA;QACA5F,EAAA,EAAA6F,IAAA,CAAAC,GAAA;QACArI,IAAA,OAAAsD,eAAA;QACAb,UAAA,MAAA2F,IAAA,GAAAE,cAAA;MACA;MAEA,KAAAxC,QAAA,CAAAE,OAAA,kBAAAQ,MAAA,MAAAlD,eAAA;MACA,KAAAD,wBAAA;IACA;IAEA;IACAkF,oBAAA,WAAAA,qBAAA;MACA,KAAA1E,iBAAA,QAAAI,YAAA,CAAAuE,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAvE,QAAA;MAAA;MACA;MACA,KAAA9D,kBAAA,QAAAyD,iBAAA,CAAA0C,MAAA,UAAAtC,YAAA,CAAAsC,MAAA;IACA;IAEA;IACAjG,uBAAA,WAAAA,wBAAAD,GAAA;MACA,KAAA4D,YAAA,CAAAyE,OAAA,WAAAD,IAAA;QACAA,IAAA,CAAAvE,QAAA,GAAA7D,GAAA;MACA;MACA,KAAAwD,iBAAA,GAAAxD,GAAA,OAAAsI,mBAAA,CAAAC,OAAA,OAAA3E,YAAA;IACA;IAEA;IACA4E,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA,SAAAjF,iBAAA,CAAA0C,MAAA;QACA,KAAAT,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAAY,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAtB,IAAA;MACA,GAAAuB,IAAA;QACA;QACA,IAAAiC,WAAA,GAAAD,MAAA,CAAAjF,iBAAA,CAAAmF,GAAA,WAAAP,IAAA;UAAA,OAAAA,IAAA,CAAAlG,EAAA;QAAA;QACAuG,MAAA,CAAA7E,YAAA,GAAA6E,MAAA,CAAA7E,YAAA,CAAAuE,MAAA,WAAAC,IAAA;UAAA,QAAAM,WAAA,CAAAE,QAAA,CAAAR,IAAA,CAAAlG,EAAA;QAAA;QACAuG,MAAA,CAAAjF,iBAAA;QACAiF,MAAA,CAAA1I,kBAAA;QACA0I,MAAA,CAAAhD,QAAA,CAAAE,OAAA;MACA,GAAAe,KAAA;QACA+B,MAAA,CAAAhD,QAAA,CAAAkB,IAAA;MACA;IACA;IAEA;IACAkC,cAAA,WAAAA,eAAAT,IAAA;MAAA,IAAAU,MAAA;MACA,KAAAxC,QAAA,cAAA8B,IAAA,CAAArE,KAAA;QACAwC,iBAAA;QACAC,gBAAA;QACAtB,IAAA;MACA,GAAAuB,IAAA;QACA;QACAqC,MAAA,CAAAlF,YAAA,GAAAkF,MAAA,CAAAlF,YAAA,CAAAuE,MAAA,WAAAY,CAAA;UAAA,OAAAA,CAAA,CAAA7G,EAAA,KAAAkG,IAAA,CAAAlG,EAAA;QAAA;QACA;QACA4G,MAAA,CAAAtF,iBAAA,GAAAsF,MAAA,CAAAtF,iBAAA,CAAA2E,MAAA,WAAAY,CAAA;UAAA,OAAAA,CAAA,CAAA7G,EAAA,KAAAkG,IAAA,CAAAlG,EAAA;QAAA;QACA;QACA4G,MAAA,CAAA5E,cAAA,GAAA4E,MAAA,CAAAlF,YAAA,CAAAsC,MAAA;QACA4C,MAAA,CAAArD,QAAA,CAAAE,OAAA;MACA,GAAAe,KAAA;QACAoC,MAAA,CAAArD,QAAA,CAAAkB,IAAA;MACA;IACA;IAEA;IACAqC,oBAAA,WAAAA,qBAAA;MACA,KAAApE,WAAA;QACA1C,EAAA;QACAvC,IAAA;QACA6E,KAAA;QACAC,KAAA;QACAC,QAAA;MACA;MACA,KAAAC,uBAAA;IACA;IAEA;IACAsE,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAAvE,WAAA,CAAAwE,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAtE,WAAA,CAAA1C,EAAA;YACA;YACA,IAAAmD,KAAA,GAAA6D,MAAA,CAAA3E,WAAA,CAAA+E,SAAA,WAAAlB,IAAA;cAAA,OAAAA,IAAA,CAAAlG,EAAA,KAAAgH,MAAA,CAAAtE,WAAA,CAAA1C,EAAA;YAAA;YACA,IAAAmD,KAAA;cACA;cACA6D,MAAA,CAAAtE,WAAA,CAAAxC,UAAA,OAAA2F,IAAA,GAAAE,cAAA;gBACAsB,IAAA;gBACAC,KAAA;gBACAC,GAAA;gBACAC,IAAA;gBACAC,MAAA;gBACAC,MAAA;gBACAC,MAAA;cACA,GAAAC,OAAA;cACAZ,MAAA,CAAA3E,WAAA,CAAAwF,MAAA,CAAA1E,KAAA,KAAA6D,MAAA,CAAAtE,WAAA;cACAsE,MAAA,CAAAzD,QAAA,CAAAE,OAAA;YACA;UACA;YACA;YACA,IAAAqE,UAAA,OAAAC,cAAA,CAAA1B,OAAA,MAAA0B,cAAA,CAAA1B,OAAA,MACAW,MAAA,CAAAtE,WAAA;cACA1C,EAAA,EAAAgH,MAAA,CAAA3E,WAAA,CAAA2B,MAAA,OAAAmB,IAAA,CAAA6C,GAAA,CAAAC,KAAA,CAAA9C,IAAA,MAAAiB,mBAAA,CAAAC,OAAA,EAAAW,MAAA,CAAA3E,WAAA,CAAAoE,GAAA,WAAAP,IAAA;gBAAA,OAAAA,IAAA,CAAAlG,EAAA;cAAA;cACAE,UAAA,MAAA2F,IAAA,GAAAE,cAAA;gBACAsB,IAAA;gBACAC,KAAA;gBACAC,GAAA;gBACAC,IAAA;gBACAC,MAAA;gBACAC,MAAA;gBACAC,MAAA;cACA,GAAAC,OAAA;YAAA,EACA;YACAZ,MAAA,CAAA3E,WAAA,CAAAuD,IAAA,CAAAkC,UAAA;YACAd,MAAA,CAAAzD,QAAA,CAAAE,OAAA;UACA;UACAuD,MAAA,CAAAvE,uBAAA;QACA;MACA;IACA;IAEA;IACAyF,iBAAA,WAAAA,kBAAAvD,GAAA;MACA,KAAAjC,WAAA,GAAAyF,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAA1D,GAAA;MACA,KAAAlC,uBAAA;IACA;IAEA;IACA6F,mBAAA,WAAAA,oBAAA3D,GAAA;MAAA,IAAA4D,MAAA;MACA,KAAAnE,QAAA,sDAAAH,MAAA,CAAAU,GAAA,CAAAlH,IAAA;QACA4G,iBAAA;QACAC,gBAAA;QACAtB,IAAA;MACA,GAAAuB,IAAA;QACA;QACAgE,MAAA,CAAAlG,WAAA,GAAAkG,MAAA,CAAAlG,WAAA,CAAA4D,MAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAlG,EAAA,KAAA2E,GAAA,CAAA3E,EAAA;QAAA;QACAuI,MAAA,CAAAhF,QAAA,CAAAE,OAAA;MACA,GAAAe,KAAA;QACA+D,MAAA,CAAAhF,QAAA,CAAAkB,IAAA;MACA;IACA;IAEA;IACA+D,eAAA,WAAAA,gBAAA;MACA,KAAAjF,QAAA,CAAAE,OAAA;MACA;IACA;IAEA;IACAgF,wBAAA,WAAAA,yBAAA3K,GAAA;MACA,KAAAmE,oBAAA,GAAAnE,GAAA;MACA;IACA;IAEA;IACA4K,sBAAA,WAAAA,uBAAA;MACA,UAAAvG,iBAAA;QACA;MACA;MAEA,IAAA6C,IAAA,GAAAC,QAAA,MAAA9C,iBAAA;MACA,IAAA+C,KAAA,CAAAF,IAAA,KAAAA,IAAA,QAAAA,IAAA,GAAAG,IAAA,CAAAC,IAAA,MAAApD,cAAA,QAAAE,iBAAA;QACA,KAAAqB,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAAvB,oBAAA,GAAA+C,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}