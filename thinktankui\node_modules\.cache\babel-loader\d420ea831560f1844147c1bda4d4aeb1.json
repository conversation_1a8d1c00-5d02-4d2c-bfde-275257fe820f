{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\system\\role\\selectUser.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\system\\role\\selectUser.vue", "mtime": 1749104047647}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_role", "require", "dicts", "props", "roleId", "type", "Number", "String", "data", "visible", "userIds", "total", "userList", "queryParams", "pageNum", "pageSize", "undefined", "userName", "phonenumber", "methods", "show", "getList", "clickRow", "row", "$refs", "table", "toggleRowSelection", "handleSelectionChange", "selection", "map", "item", "userId", "_this", "unallocatedUserList", "then", "res", "rows", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectUser", "_this2", "join", "$modal", "msgError", "authUserSelectAll", "msgSuccess", "msg", "$emit"], "sources": ["src/views/system/role/selectUser.vue"], "sourcesContent": ["<template>\r\n  <!-- 授权用户 -->\r\n  <el-dialog title=\"选择用户\" :visible.sync=\"visible\" width=\"800px\" top=\"5vh\" append-to-body>\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\">\r\n      <el-form-item label=\"用户名称\" prop=\"userName\">\r\n        <el-input\r\n          v-model=\"queryParams.userName\"\r\n          placeholder=\"请输入用户名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"手机号码\" prop=\"phonenumber\">\r\n        <el-input\r\n          v-model=\"queryParams.phonenumber\"\r\n          placeholder=\"请输入手机号码\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n    <el-row>\r\n      <el-table @row-click=\"clickRow\" ref=\"table\" :data=\"userList\" @selection-change=\"handleSelectionChange\" height=\"260px\">\r\n        <el-table-column type=\"selection\" width=\"55\"></el-table-column>\r\n        <el-table-column label=\"用户名称\" prop=\"userName\" :show-overflow-tooltip=\"true\" />\r\n        <el-table-column label=\"用户昵称\" prop=\"nickName\" :show-overflow-tooltip=\"true\" />\r\n        <el-table-column label=\"邮箱\" prop=\"email\" :show-overflow-tooltip=\"true\" />\r\n        <el-table-column label=\"手机\" prop=\"phonenumber\" :show-overflow-tooltip=\"true\" />\r\n        <el-table-column label=\"状态\" align=\"center\" prop=\"status\">\r\n          <template slot-scope=\"scope\">\r\n            <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.createTime) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <pagination\r\n        v-show=\"total>0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\"\r\n      />\r\n    </el-row>\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button type=\"primary\" @click=\"handleSelectUser\">确 定</el-button>\r\n      <el-button @click=\"visible = false\">取 消</el-button>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport { unallocatedUserList, authUserSelectAll } from \"@/api/system/role\";\r\nexport default {\r\n  dicts: ['sys_normal_disable'],\r\n  props: {\r\n    // 角色编号\r\n    roleId: {\r\n      type: [Number, String]\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      visible: false,\r\n      // 选中数组值\r\n      userIds: [],\r\n      // 总条数\r\n      total: 0,\r\n      // 未授权用户数据\r\n      userList: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        roleId: undefined,\r\n        userName: undefined,\r\n        phonenumber: undefined\r\n      }\r\n    };\r\n  },\r\n  methods: {\r\n    // 显示弹框\r\n    show() {\r\n      this.queryParams.roleId = this.roleId;\r\n      this.getList();\r\n      this.visible = true;\r\n    },\r\n    clickRow(row) {\r\n      this.$refs.table.toggleRowSelection(row);\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.userIds = selection.map(item => item.userId);\r\n    },\r\n    // 查询表数据\r\n    getList() {\r\n      unallocatedUserList(this.queryParams).then(res => {\r\n        this.userList = res.rows;\r\n        this.total = res.total;\r\n      });\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 选择授权用户操作 */\r\n    handleSelectUser() {\r\n      const roleId = this.queryParams.roleId;\r\n      const userIds = this.userIds.join(\",\");\r\n      if (userIds == \"\") {\r\n        this.$modal.msgError(\"请选择要分配的用户\");\r\n        return;\r\n      }\r\n      authUserSelectAll({ roleId: roleId, userIds: userIds }).then(res => {\r\n        this.$modal.msgSuccess(res.msg);\r\n        this.visible = false;\r\n        this.$emit(\"ok\");\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;AA2DA,IAAAA,KAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAC,KAAA;EACAC,KAAA;IACA;IACAC,MAAA;MACAC,IAAA,GAAAC,MAAA,EAAAC,MAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,OAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAX,MAAA,EAAAY,SAAA;QACAC,QAAA,EAAAD,SAAA;QACAE,WAAA,EAAAF;MACA;IACA;EACA;EACAG,OAAA;IACA;IACAC,IAAA,WAAAA,KAAA;MACA,KAAAP,WAAA,CAAAT,MAAA,QAAAA,MAAA;MACA,KAAAiB,OAAA;MACA,KAAAZ,OAAA;IACA;IACAa,QAAA,WAAAA,SAAAC,GAAA;MACA,KAAAC,KAAA,CAAAC,KAAA,CAAAC,kBAAA,CAAAH,GAAA;IACA;IACA;IACAI,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAlB,OAAA,GAAAkB,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,MAAA;MAAA;IACA;IACA;IACAV,OAAA,WAAAA,QAAA;MAAA,IAAAW,KAAA;MACA,IAAAC,yBAAA,OAAApB,WAAA,EAAAqB,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAApB,QAAA,GAAAuB,GAAA,CAAAC,IAAA;QACAJ,KAAA,CAAArB,KAAA,GAAAwB,GAAA,CAAAxB,KAAA;MACA;IACA;IACA,aACA0B,WAAA,WAAAA,YAAA;MACA,KAAAxB,WAAA,CAAAC,OAAA;MACA,KAAAO,OAAA;IACA;IACA,aACAiB,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IACA,eACAG,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,IAAArC,MAAA,QAAAS,WAAA,CAAAT,MAAA;MACA,IAAAM,OAAA,QAAAA,OAAA,CAAAgC,IAAA;MACA,IAAAhC,OAAA;QACA,KAAAiC,MAAA,CAAAC,QAAA;QACA;MACA;MACA,IAAAC,uBAAA;QAAAzC,MAAA,EAAAA,MAAA;QAAAM,OAAA,EAAAA;MAAA,GAAAwB,IAAA,WAAAC,GAAA;QACAM,MAAA,CAAAE,MAAA,CAAAG,UAAA,CAAAX,GAAA,CAAAY,GAAA;QACAN,MAAA,CAAAhC,OAAA;QACAgC,MAAA,CAAAO,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}