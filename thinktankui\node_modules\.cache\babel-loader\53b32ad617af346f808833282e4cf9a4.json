{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\monitor\\online\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\monitor\\online\\index.vue", "mtime": 1749104047642}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfb25saW5lID0gcmVxdWlyZSgiQC9hcGkvbW9uaXRvci9vbmxpbmUiKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICJPbmxpbmUiLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDooajmoLzmlbDmja4KICAgICAgbGlzdDogW10sCiAgICAgIHBhZ2VOdW06IDEsCiAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgaXBhZGRyOiB1bmRlZmluZWQsCiAgICAgICAgdXNlck5hbWU6IHVuZGVmaW5lZAogICAgICB9CiAgICB9OwogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOafpeivoueZu+W9leaXpeW/l+WIl+ihqCAqL2dldExpc3Q6IGZ1bmN0aW9uIGdldExpc3QoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgICgwLCBfb25saW5lLmxpc3QpKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMubGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgX3RoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi9oYW5kbGVRdWVyeTogZnVuY3Rpb24gaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi9yZXNldFF1ZXJ5OiBmdW5jdGlvbiByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvKiog5by66YCA5oyJ6ZKu5pON5L2cICovaGFuZGxlRm9yY2VMb2dvdXQ6IGZ1bmN0aW9uIGhhbmRsZUZvcmNlTG9nb3V0KHJvdykgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5by66YCA5ZCN56ew5Li6IicgKyByb3cudXNlck5hbWUgKyAnIueahOeUqOaIt++8nycpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiAoMCwgX29ubGluZS5mb3JjZUxvZ291dCkocm93LnRva2VuSWQpOwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczIuZ2V0TGlzdCgpOwogICAgICAgIF90aGlzMi4kbW9kYWwubXNnU3VjY2Vzcygi5by66YCA5oiQ5YqfIik7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHt9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_online", "require", "name", "data", "loading", "total", "list", "pageNum", "pageSize", "queryParams", "ipaddr", "undefined", "userName", "created", "getList", "methods", "_this", "then", "response", "rows", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleForceLogout", "row", "_this2", "$modal", "confirm", "forceLogout", "tokenId", "msgSuccess", "catch"], "sources": ["src/views/monitor/online/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\r\n      <el-form-item label=\"登录地址\" prop=\"ipaddr\">\r\n        <el-input\r\n          v-model=\"queryParams.ipaddr\"\r\n          placeholder=\"请输入登录地址\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"用户名称\" prop=\"userName\">\r\n        <el-input\r\n          v-model=\"queryParams.userName\"\r\n          placeholder=\"请输入用户名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n\r\n    </el-form>\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"list.slice((pageNum-1)*pageSize,pageNum*pageSize)\"\r\n      style=\"width: 100%;\"\r\n    >\r\n      <el-table-column label=\"序号\" type=\"index\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{(pageNum - 1) * pageSize + scope.$index + 1}}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"会话编号\" align=\"center\" prop=\"tokenId\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"登录名称\" align=\"center\" prop=\"userName\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"部门名称\" align=\"center\" prop=\"deptName\" />\r\n      <el-table-column label=\"主机\" align=\"center\" prop=\"ipaddr\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"登录地点\" align=\"center\" prop=\"loginLocation\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"浏览器\" align=\"center\" prop=\"browser\" />\r\n      <el-table-column label=\"操作系统\" align=\"center\" prop=\"os\" />\r\n      <el-table-column label=\"登录时间\" align=\"center\" prop=\"loginTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.loginTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleForceLogout(scope.row)\"\r\n            v-hasPermi=\"['monitor:online:forceLogout']\"\r\n          >强退</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total>0\" :total=\"total\" :page.sync=\"pageNum\" :limit.sync=\"pageSize\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { list, forceLogout } from \"@/api/monitor/online\";\r\n\r\nexport default {\r\n  name: \"Online\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 表格数据\r\n      list: [],\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      // 查询参数\r\n      queryParams: {\r\n        ipaddr: undefined,\r\n        userName: undefined\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询登录日志列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      list(this.queryParams).then(response => {\r\n        this.list = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 强退按钮操作 */\r\n    handleForceLogout(row) {\r\n      this.$modal.confirm('是否确认强退名称为\"' + row.userName + '\"的用户？').then(function() {\r\n        return forceLogout(row.tokenId);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"强退成功\");\r\n      }).catch(() => {});\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n"], "mappings": ";;;;;;AAiEA,IAAAA,OAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACAC,OAAA;MACAC,QAAA;MACA;MACAC,WAAA;QACAC,MAAA,EAAAC,SAAA;QACAC,QAAA,EAAAD;MACA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,eACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAZ,OAAA;MACA,IAAAE,YAAA,OAAAG,WAAA,EAAAQ,IAAA,WAAAC,QAAA;QACAF,KAAA,CAAAV,IAAA,GAAAY,QAAA,CAAAC,IAAA;QACAH,KAAA,CAAAX,KAAA,GAAAa,QAAA,CAAAb,KAAA;QACAW,KAAA,CAAAZ,OAAA;MACA;IACA;IACA,aACAgB,WAAA,WAAAA,YAAA;MACA,KAAAb,OAAA;MACA,KAAAO,OAAA;IACA;IACA,aACAO,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IACA,aACAG,iBAAA,WAAAA,kBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,MAAA,CAAAC,OAAA,gBAAAH,GAAA,CAAAZ,QAAA,YAAAK,IAAA;QACA,WAAAW,mBAAA,EAAAJ,GAAA,CAAAK,OAAA;MACA,GAAAZ,IAAA;QACAQ,MAAA,CAAAX,OAAA;QACAW,MAAA,CAAAC,MAAA,CAAAI,UAAA;MACA,GAAAC,KAAA;IACA;EACA;AACA", "ignoreList": []}]}