{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\report-center\\index.vue?vue&type=template&id=572a4832&scoped=true", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\report-center\\index.vue", "mtime": 1749104047642}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749104421286}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}