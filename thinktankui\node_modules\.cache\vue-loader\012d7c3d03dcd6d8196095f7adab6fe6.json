{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\meta-search\\index.vue?vue&type=style&index=0&id=102ebc49&scoped=true&lang=css", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\meta-search\\index.vue", "mtime": 1749104047640}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749104419119}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749104421224}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749104419872}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5tZXRhLXNlYXJjaC1jb250YWluZXIgew0KICBwYWRkaW5nOiAyMHB4Ow0KICBiYWNrZ3JvdW5kOiAjZjVmN2ZhOw0KICBtaW4taGVpZ2h0OiAxMDB2aDsNCn0NCg0KLnNlYXJjaC1oZWFkZXIgew0KICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBwYWRkaW5nOiAyMHB4Ow0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICBib3gtc2hhZG93OiAwIDJweCAxMnB4IDAgcmdiYSgwLCAwLCAwLCAwLjEpOw0KfQ0KDQouc2VhcmNoLXRhYnMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KfQ0KDQoudGFiIHsNCiAgcGFkZGluZzogOHB4IDE2cHg7DQogIG1hcmdpbi1yaWdodDogMTBweDsNCiAgY3Vyc29yOiBwb2ludGVyOw0KICBib3JkZXItYm90dG9tOiAycHggc29saWQgdHJhbnNwYXJlbnQ7DQp9DQoNCi50YWIuYWN0aXZlIHsNCiAgY29sb3I6ICM0MDlFRkY7DQogIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjNDA5RUZGOw0KICBmb250LXdlaWdodDogYm9sZDsNCn0NCg0KLnNlYXJjaC1ib3ggew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KfQ0KDQouc2VhcmNoLWlucHV0IHsNCiAgZmxleDogMTsNCiAgaGVpZ2h0OiA0MHB4Ow0KICBwYWRkaW5nOiAwIDE1cHg7DQogIGJvcmRlcjogMXB4IHNvbGlkICNkY2RmZTY7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgbWFyZ2luLXJpZ2h0OiAxMHB4Ow0KICBmb250LXNpemU6IDE0cHg7DQp9DQoNCi5zZWFyY2gtYnRuIHsNCiAgaGVpZ2h0OiA0MHB4Ow0KfQ0KDQouc2VhcmNoLXJlc3VsdHMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KfQ0KDQouc2VhcmNoLWVuZ2luZXMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBwYWRkaW5nOiAxNXB4Ow0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICBib3gtc2hhZG93OiAwIDJweCAxMnB4IDAgcmdiYSgwLCAwLCAwLCAwLjEpOw0KfQ0KDQouZW5naW5lLWl0ZW0gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBwYWRkaW5nOiA4cHggMTZweDsNCiAgbWFyZ2luLXJpZ2h0OiAxNXB4Ow0KICBjdXJzb3I6IHBvaW50ZXI7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3M7DQogIGJvcmRlcjogMXB4IHNvbGlkICNkY2RmZTY7DQogIGJhY2tncm91bmQ6ICNmZmY7DQp9DQoNCi5lbmdpbmUtaXRlbTpob3ZlciB7DQogIGJvcmRlci1jb2xvcjogI2M2ZTJmZjsNCn0NCg0KLmVuZ2luZS1pdGVtLmFjdGl2ZSB7DQogIGJhY2tncm91bmQ6ICNlY2Y1ZmY7DQogIGNvbG9yOiAjNDA5RUZGOw0KICBib3JkZXItY29sb3I6ICM0MDlFRkY7DQp9DQoNCi5jaGVja2JveCB7DQogIHdpZHRoOiAxNnB4Ow0KICBoZWlnaHQ6IDE2cHg7DQogIGJvcmRlcjogMXB4IHNvbGlkICNkY2RmZTY7DQogIGJvcmRlci1yYWRpdXM6IDJweDsNCiAgbWFyZ2luLXJpZ2h0OiA4cHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBjb2xvcjogI2ZmZjsNCiAgYmFja2dyb3VuZC1jb2xvcjogIzQwOUVGRjsNCn0NCg0KLmVuZ2luZS1pY29uIHsNCiAgd2lkdGg6IDE2cHg7DQogIGhlaWdodDogMTZweDsNCiAgbWFyZ2luLXJpZ2h0OiA4cHg7DQp9DQoNCi5yZXN1bHRzLWNvbnRhaW5lciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGdhcDogMjBweDsNCn0NCg0KLnJlc3VsdC1jb2x1bW4gew0KICBmbGV4OiAxOw0KICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBib3gtc2hhZG93OiAwIDJweCAxMnB4IDAgcmdiYSgwLCAwLCAwLCAwLjEpOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KfQ0KDQoucmVzdWx0LWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIHBhZGRpbmc6IDE1cHg7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZWJlZWY1Ow0KICBiYWNrZ3JvdW5kOiAjZjVmN2ZhOw0KfQ0KDQoucmVzdWx0LWljb24gew0KICB3aWR0aDogMTZweDsNCiAgaGVpZ2h0OiAxNnB4Ow0KICBtYXJnaW4tcmlnaHQ6IDhweDsNCn0NCg0KLnJlc3VsdC10aXRsZSB7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBjb2xvcjogIzMwMzEzMzsNCn0NCg0KLnJlc3VsdC1saXN0IHsNCiAgcGFkZGluZzogMTVweDsNCn0NCg0KLnJlc3VsdC1pdGVtIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgcGFkZGluZy1ib3R0b206IDE1cHg7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZWJlZWY1Ow0KfQ0KDQoucmVzdWx0LWl0ZW06bGFzdC1jaGlsZCB7DQogIG1hcmdpbi1ib3R0b206IDA7DQogIHBhZGRpbmctYm90dG9tOiAwOw0KICBib3JkZXItYm90dG9tOiBub25lOw0KfQ0KDQouaXRlbS10aXRsZSB7DQogIG1hcmdpbjogMCAwIDhweCAwOw0KICBmb250LXNpemU6IDE2cHg7DQp9DQoNCi5pdGVtLXRpdGxlIGEgew0KICBjb2xvcjogIzAzNjZkNjsNCiAgdGV4dC1kZWNvcmF0aW9uOiBub25lOw0KfQ0KDQouaXRlbS10aXRsZSBhOmhvdmVyIHsNCiAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7DQp9DQoNCi5pdGVtLXVybCB7DQogIGNvbG9yOiAjNjdjMjNhOw0KICBmb250LXNpemU6IDEycHg7DQogIG1hcmdpbi1ib3R0b206IDhweDsNCn0NCg0KLml0ZW0tZGVzYyB7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBmb250LXNpemU6IDE0cHg7DQogIGxpbmUtaGVpZ2h0OiAxLjU7DQp9DQoNCi8qIOWFqOaWh+ajgOe0ouagt+W8jyAqLw0KLmZ1bGx0ZXh0LXJlc3VsdHMgew0KICBtYXJnaW4tdG9wOiAyMHB4Ow0KfQ0KDQouZmlsdGVyLXNlY3Rpb24gew0KICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgcGFkZGluZzogMjBweDsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBib3gtc2hhZG93OiAwIDJweCAxMnB4IDAgcmdiYSgwLCAwLCAwLCAwLjEpOw0KfQ0KDQouZmlsdGVyLXJvdyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQp9DQoNCi5maWx0ZXItcm93Omxhc3QtY2hpbGQgew0KICBtYXJnaW4tYm90dG9tOiAwOw0KfQ0KDQouZmlsdGVyLWxhYmVsIHsNCiAgd2lkdGg6IDgwcHg7DQogIGNvbG9yOiAjNjY2Ow0KICBmb250LXNpemU6IDE0cHg7DQp9DQoNCi5maWx0ZXItb3B0aW9ucyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtd3JhcDogd3JhcDsNCiAgZ2FwOiAxMHB4Ow0KfQ0KDQouY291bnQgew0KICBjb2xvcjogIzk5OTsNCiAgZm9udC1zaXplOiAxMnB4Ow0KfQ0KDQoucmVzdWx0LXN0YXRzIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgcGFkZGluZzogMTVweCAyMHB4Ow0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIGJveC1zaGFkb3c6IDAgMnB4IDEycHggMCByZ2JhKDAsIDAsIDAsIDAuMSk7DQp9DQoNCi5hY3Rpb24tYnV0dG9ucyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGdhcDogMTBweDsNCn0NCg0KLnJlc3VsdHMtbGlzdCB7DQogIGJhY2tncm91bmQ6IHdoaXRlOw0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQogIGJveC1zaGFkb3c6IDAgMnB4IDEycHggMCByZ2JhKDAsIDAsIDAsIDAuMSk7DQp9DQoNCi5yZXN1bHRzLWxpc3QgLnJlc3VsdC1pdGVtIHsNCiAgcGFkZGluZzogMjBweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7DQp9DQoNCi5yZXN1bHRzLWxpc3QgLnJlc3VsdC1pdGVtOmxhc3QtY2hpbGQgew0KICBib3JkZXItYm90dG9tOiBub25lOw0KfQ0KDQoucmVzdWx0LWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQp9DQoNCi5yZXN1bHQtdGl0bGUgew0KICBmb250LXNpemU6IDE2cHg7DQogIGNvbG9yOiAjMzMzOw0KICBtYXJnaW46IDA7DQogIGxpbmUtaGVpZ2h0OiAxLjQ7DQogIGZsZXg6IDE7DQogIG1hcmdpbi1yaWdodDogMjBweDsNCn0NCg0KLnJlc3VsdC1tZXRhIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC13cmFwOiB3cmFwOw0KICBnYXA6IDE1cHg7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgY29sb3I6ICM5OTk7DQp9DQoNCi5tZXRhLWl0ZW0gew0KICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KfQ0KDQoucmVzdWx0LWNvbnRlbnQgew0KICBjb2xvcjogIzY2NjsNCiAgbGluZS1oZWlnaHQ6IDEuNjsNCiAgZm9udC1zaXplOiAxNHB4Ow0KfQ0KDQoucGFnaW5hdGlvbi1jb250YWluZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgYmFja2dyb3VuZDogd2hpdGU7DQogIHBhZGRpbmc6IDIwcHg7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgYm94LXNoYWRvdzogMCAycHggMTJweCAwIHJnYmEoMCwgMCwgMCwgMC4xKTsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8ZA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/meta-search", "sourcesContent": ["<template>\r\n  <div class=\"meta-search-container\">\r\n    <!-- 顶部搜索区域 -->\r\n    <div class=\"search-header\">\r\n      <div class=\"search-tabs\">\r\n        <div\r\n          class=\"tab\"\r\n          :class=\"{ active: activeTab === 'fulltext' }\"\r\n          @click=\"switchTab('fulltext')\"\r\n        >\r\n          全文检索\r\n        </div>\r\n        <div\r\n          class=\"tab\"\r\n          :class=\"{ active: activeTab === 'meta' }\"\r\n          @click=\"switchTab('meta')\"\r\n        >\r\n          元搜索\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"search-box\">\r\n        <input\r\n          type=\"text\"\r\n          class=\"search-input\"\r\n          v-model=\"searchKeyword\"\r\n          placeholder=\"请输入搜索关键词\"\r\n          @keyup.enter=\"handleSearch\"\r\n        />\r\n        <el-button type=\"primary\" class=\"search-btn\" @click=\"handleSearch\">搜索</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 全文检索结果 -->\r\n    <div v-if=\"activeTab === 'fulltext' && hasSearched\" class=\"fulltext-results\">\r\n      <!-- 筛选条件区域 -->\r\n      <div class=\"filter-section\">\r\n        <!-- 时间筛选 -->\r\n        <div class=\"filter-row\">\r\n          <span class=\"filter-label\">时间范围:</span>\r\n          <div class=\"filter-options\">\r\n            <el-button\r\n              v-for=\"time in timeOptions\"\r\n              :key=\"time.value\"\r\n              :type=\"selectedTime === time.value ? 'primary' : ''\"\r\n              size=\"small\"\r\n              @click=\"selectTime(time.value)\"\r\n            >\r\n              {{ time.label }}\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 平台筛选 -->\r\n        <div class=\"filter-row\">\r\n          <span class=\"filter-label\">平台类型:</span>\r\n          <div class=\"filter-options\">\r\n            <el-button\r\n              v-for=\"platform in platformOptions\"\r\n              :key=\"platform.value\"\r\n              :type=\"selectedPlatform === platform.value ? 'primary' : ''\"\r\n              size=\"small\"\r\n              @click=\"selectPlatform(platform.value)\"\r\n            >\r\n              {{ platform.label }}\r\n              <span v-if=\"platform.count\" class=\"count\">({{ platform.count }})</span>\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 情感筛选 -->\r\n        <div class=\"filter-row\">\r\n          <span class=\"filter-label\">情感类型:</span>\r\n          <div class=\"filter-options\">\r\n            <el-button\r\n              v-for=\"emotion in emotionOptions\"\r\n              :key=\"emotion.value\"\r\n              :type=\"selectedEmotion === emotion.value ? 'primary' : ''\"\r\n              size=\"small\"\r\n              @click=\"selectEmotion(emotion.value)\"\r\n            >\r\n              {{ emotion.label }}\r\n              <span v-if=\"emotion.count\" class=\"count\">({{ emotion.count }})</span>\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 结果统计 -->\r\n      <div class=\"result-stats\">\r\n        <span>共{{ totalResults }}条结果</span>\r\n        <div class=\"action-buttons\">\r\n          <el-button size=\"small\">导出</el-button>\r\n          <el-button type=\"primary\" size=\"small\">分析</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 搜索结果列表 -->\r\n      <div class=\"results-list\">\r\n        <div v-for=\"(item, index) in searchResults\" :key=\"index\" class=\"result-item\">\r\n          <div class=\"result-header\">\r\n            <h3 class=\"result-title\">{{ item.title }}</h3>\r\n            <div class=\"result-actions\">\r\n              <el-button type=\"text\" icon=\"el-icon-view\"></el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"result-meta\">\r\n            <span class=\"meta-item\">{{ item.source }}</span>\r\n            <span class=\"meta-item\">{{ item.publishTime }}</span>\r\n            <span class=\"meta-item\">{{ item.author }}</span>\r\n            <span class=\"meta-item\">{{ item.platform }}</span>\r\n            <span class=\"meta-item\">阅读量: {{ item.readCount }}</span>\r\n            <span class=\"meta-item\">{{ item.location }}</span>\r\n            <span class=\"meta-item\">{{ item.category }}</span>\r\n          </div>\r\n\r\n          <div class=\"result-content\">\r\n            {{ item.content }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          background\r\n          layout=\"prev, pager, next\"\r\n          :total=\"totalResults\"\r\n          :current-page.sync=\"currentPage\"\r\n          :page-size=\"pageSize\"\r\n          @current-change=\"handlePageChange\"\r\n        ></el-pagination>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 元搜索结果 -->\r\n    <div v-if=\"activeTab === 'meta' && hasSearched\" class=\"meta-results\">\r\n      <!-- 搜索引擎选项卡 -->\r\n      <div class=\"search-engines\">\r\n        <div\r\n          v-for=\"engine in searchEngines\"\r\n          :key=\"engine.id\"\r\n          class=\"engine-item\"\r\n          :class=\"{ active: engine.id === activeEngine }\"\r\n          @click=\"toggleEngine(engine.id)\"\r\n        >\r\n          <div class=\"checkbox\">\r\n            <i class=\"el-icon-check\" v-if=\"selectedEngines.includes(engine.id)\"></i>\r\n          </div>\r\n          <img :src=\"engine.icon\" :alt=\"engine.name\" class=\"engine-icon\" />\r\n          <span class=\"engine-name\">{{ engine.name }}</span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 搜索结果展示区 -->\r\n      <div class=\"results-container\">\r\n        <!-- 左侧搜索结果 -->\r\n        <div class=\"result-column\" v-if=\"selectedEngines.includes('bing')\">\r\n          <div class=\"result-header\">\r\n            <img src=\"https://www.bing.com/favicon.ico\" alt=\"Microsoft Bing\" class=\"result-icon\" />\r\n            <span class=\"result-title\">Microsoft Bing</span>\r\n          </div>\r\n          <div class=\"result-list\">\r\n            <div class=\"result-item\" v-for=\"(item, index) in bingResults\" :key=\"'bing-'+index\">\r\n              <h3 class=\"item-title\">\r\n                <a :href=\"item.link\" target=\"_blank\">{{ item.title }}</a>\r\n              </h3>\r\n              <div class=\"item-url\">{{ item.url }}</div>\r\n              <div class=\"item-desc\">{{ item.description }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 中间搜索结果 -->\r\n        <div class=\"result-column\" v-if=\"selectedEngines.includes('baidu')\">\r\n          <div class=\"result-header\">\r\n            <img src=\"https://www.baidu.com/favicon.ico\" alt=\"百度搜索\" class=\"result-icon\" />\r\n            <span class=\"result-title\">百度搜索</span>\r\n          </div>\r\n          <div class=\"result-list\">\r\n            <div class=\"result-item\" v-for=\"(item, index) in baiduResults\" :key=\"'baidu-'+index\">\r\n              <h3 class=\"item-title\">\r\n                <a :href=\"item.link\" target=\"_blank\">{{ item.title }}</a>\r\n              </h3>\r\n              <div class=\"item-url\">{{ item.url }}</div>\r\n              <div class=\"item-desc\">{{ item.description }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 右侧搜索结果 -->\r\n        <div class=\"result-column\" v-if=\"selectedEngines.includes('360')\">\r\n          <div class=\"result-header\">\r\n            <img src=\"https://www.so.com/favicon.ico\" alt=\"360搜索\" class=\"result-icon\" />\r\n            <span class=\"result-title\">360搜索</span>\r\n          </div>\r\n          <div class=\"result-list\">\r\n            <div class=\"result-item\" v-for=\"(item, index) in so360Results\" :key=\"'360-'+index\">\r\n              <h3 class=\"item-title\">\r\n                <a :href=\"item.link\" target=\"_blank\">{{ item.title }}</a>\r\n              </h3>\r\n              <div class=\"item-url\">{{ item.url }}</div>\r\n              <div class=\"item-desc\">{{ item.description }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'MetaSearch',\r\n  data() {\r\n    return {\r\n      activeTab: 'fulltext', // 默认显示全文检索\r\n      searchKeyword: '方太',\r\n      hasSearched: true,\r\n\r\n      // 全文检索相关数据\r\n      selectedTime: '24h',\r\n      selectedPlatform: 'all',\r\n      selectedEmotion: 'all',\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      totalResults: 10000,\r\n\r\n      timeOptions: [\r\n        { label: '24小时', value: '24h' },\r\n        { label: '一周', value: '1w' },\r\n        { label: '半年', value: '6m' },\r\n        { label: '一年', value: '1y' },\r\n        { label: '自定义', value: 'custom' }\r\n      ],\r\n\r\n      platformOptions: [\r\n        { label: '全部', value: 'all', count: 10540 },\r\n        { label: '微信', value: 'wechat', count: 1847 },\r\n        { label: '微博', value: 'weibo', count: 2008 },\r\n        { label: '客户端', value: 'app', count: 1748 },\r\n        { label: '论坛', value: 'forum', count: 673 }\r\n      ],\r\n\r\n      emotionOptions: [\r\n        { label: '全部', value: 'all' },\r\n        { label: '正面', value: 'positive' },\r\n        { label: '负面', value: 'negative' },\r\n        { label: '中性', value: 'neutral' }\r\n      ],\r\n\r\n      searchResults: [\r\n        {\r\n          title: '从政府部门44三个个体的热点问题，到媒体1，覆盖的（含义），斗争主义口号，牛年主要的问题，共同人工智能的问题人员，如山山的问题的主要问题，新工人，打工，用友三家...',\r\n          source: '新华网',\r\n          publishTime: '2022-06-29 20:07:04',\r\n          author: '77人讨论',\r\n          platform: '平台来源',\r\n          readCount: '无',\r\n          location: '无所在地',\r\n          category: '新闻',\r\n          content: '从政府部门44三个个体的热点问题，到媒体1，覆盖的（含义），斗争主义口号，牛年主要的问题，共同人工智能的问题人员，如山山的问题的主要问题，新工人，打工，用友三家...'\r\n        },\r\n        {\r\n          title: '中大-论文发表(2025年中大论文发表)自然指数中大-论文发表世界地位论坛-2025年的价值中国论文',\r\n          source: '中大论文发表',\r\n          publishTime: '2022-06-29 20:07:04',\r\n          author: '77人讨论',\r\n          platform: '平台来源',\r\n          readCount: '无',\r\n          location: '无所在地',\r\n          category: '论文',\r\n          content: '中大-论文发表(2025年中大论文发表)自然指数中大-论文发表世界地位论坛-2025年的价值中国论文...'\r\n        },\r\n        {\r\n          title: '转发微博#中#大学生，人情世故。',\r\n          source: '微博',\r\n          publishTime: '2022-06-29 20:07:04',\r\n          author: '77人讨论',\r\n          platform: '微博',\r\n          readCount: '1000',\r\n          location: '北京',\r\n          category: '社交媒体',\r\n          content: '转发微博#中#大学生，人情世故。这是一条关于大学生人际关系的微博内容...'\r\n        }\r\n      ],\r\n\r\n      // 元搜索相关数据\r\n      activeEngine: 'bing',\r\n      selectedEngines: ['bing', 'baidu', '360'],\r\n      searchEngines: [\r\n        { id: 'bing', name: 'Microsoft Bing', icon: 'https://www.bing.com/favicon.ico' },\r\n        { id: 'baidu', name: '百度搜索', icon: 'https://www.baidu.com/favicon.ico' },\r\n        { id: '360', name: '360搜索', icon: 'https://www.so.com/favicon.ico' }\r\n      ],\r\n      bingResults: [\r\n        {\r\n          title: '方太官网_高端全场景厨电',\r\n          url: 'https://www.fotile.com',\r\n          link: 'https://www.fotile.com',\r\n          description: '方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'\r\n        },\r\n        {\r\n          title: '【方太集团官网】',\r\n          url: 'https://www.fotile.com/about',\r\n          link: 'https://www.fotile.com/about',\r\n          description: '方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'\r\n        },\r\n        {\r\n          title: '不平凡',\r\n          url: 'https://www.fotile.com/product',\r\n          link: 'https://www.fotile.com/product',\r\n          description: '方太高端厨电，专注高端厨房电器20年，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'\r\n        }\r\n      ],\r\n      baiduResults: [\r\n        {\r\n          title: '方太官网_高端全场景厨电',\r\n          url: 'https://www.fotile.com',\r\n          link: 'https://www.fotile.com',\r\n          description: '方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'\r\n        },\r\n        {\r\n          title: '方太厨电 高端集成厨房电器品牌',\r\n          url: 'https://www.fotile.com/product',\r\n          link: 'https://www.fotile.com/product',\r\n          description: '全国服务热线：400-315-0000 方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器...'\r\n        },\r\n        {\r\n          title: '方太 - 百度百科',\r\n          url: 'https://baike.baidu.com/item/方太/1830',\r\n          link: 'https://baike.baidu.com/item/方太/1830',\r\n          description: '方太，是中国高端厨电领导品牌，创立于1996年，总部位于浙江宁波，是一家集研发、生产、销售于一体的现代化企业，主要产品包括吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器...'\r\n        }\r\n      ],\r\n      so360Results: [\r\n        {\r\n          title: '方太官网_高端全场景厨电',\r\n          url: 'https://www.fotile.com',\r\n          link: 'https://www.fotile.com',\r\n          description: '方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'\r\n        },\r\n        {\r\n          title: '方太厨电旗舰店-天猫',\r\n          url: 'https://fotile.tmall.com',\r\n          link: 'https://fotile.tmall.com',\r\n          description: '方太厨电旗舰店,提供方太油烟机,方太燃气灶,方太消毒柜,方太洗碗机,方太蒸箱,方太烤箱,方太微波炉,方太水槽洗碗机等产品。天猫正品保障,提供...'\r\n        },\r\n        {\r\n          title: '方太集团有限公司',\r\n          url: 'https://www.fotile.com/about',\r\n          link: 'https://www.fotile.com/about',\r\n          description: '方太集团有限公司创立于1996年，总部位于浙江宁波，是一家集研发、生产、销售于一体的现代化企业，主要产品包括吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器...'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    // 标签页切换\r\n    switchTab(tab) {\r\n      this.activeTab = tab\r\n    },\r\n\r\n    // 搜索功能\r\n    handleSearch() {\r\n      this.hasSearched = true\r\n      // 实际项目中这里应该调用API获取搜索结果\r\n      console.log('搜索关键词:', this.searchKeyword)\r\n      this.$message.success(`搜索: ${this.searchKeyword}`)\r\n    },\r\n\r\n    // 全文检索筛选方法\r\n    selectTime(value) {\r\n      this.selectedTime = value\r\n      this.handleSearch()\r\n    },\r\n\r\n    selectPlatform(value) {\r\n      this.selectedPlatform = value\r\n      this.handleSearch()\r\n    },\r\n\r\n    selectEmotion(value) {\r\n      this.selectedEmotion = value\r\n      this.handleSearch()\r\n    },\r\n\r\n    handlePageChange(page) {\r\n      this.currentPage = page\r\n      // 加载对应页面数据\r\n    },\r\n\r\n    // 元搜索引擎切换\r\n    toggleEngine(engineId) {\r\n      // 切换选中状态\r\n      if (this.selectedEngines.includes(engineId)) {\r\n        // 如果已经选中，且不是最后一个选中的引擎，则取消选中\r\n        if (this.selectedEngines.length > 1) {\r\n          this.selectedEngines = this.selectedEngines.filter(id => id !== engineId)\r\n        }\r\n      } else {\r\n        // 如果未选中，则添加到选中列表\r\n        this.selectedEngines.push(engineId)\r\n      }\r\n\r\n      // 设置当前活动引擎\r\n      this.activeEngine = engineId\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.meta-search-container {\r\n  padding: 20px;\r\n  background: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n.search-header {\r\n  background: white;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.search-tabs {\r\n  display: flex;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.tab {\r\n  padding: 8px 16px;\r\n  margin-right: 10px;\r\n  cursor: pointer;\r\n  border-bottom: 2px solid transparent;\r\n}\r\n\r\n.tab.active {\r\n  color: #409EFF;\r\n  border-bottom: 2px solid #409EFF;\r\n  font-weight: bold;\r\n}\r\n\r\n.search-box {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.search-input {\r\n  flex: 1;\r\n  height: 40px;\r\n  padding: 0 15px;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  margin-right: 10px;\r\n  font-size: 14px;\r\n}\r\n\r\n.search-btn {\r\n  height: 40px;\r\n}\r\n\r\n.search-results {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.search-engines {\r\n  display: flex;\r\n  background: white;\r\n  border-radius: 8px;\r\n  padding: 15px;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.engine-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px 16px;\r\n  margin-right: 15px;\r\n  cursor: pointer;\r\n  border-radius: 4px;\r\n  transition: all 0.3s;\r\n  border: 1px solid #dcdfe6;\r\n  background: #fff;\r\n}\r\n\r\n.engine-item:hover {\r\n  border-color: #c6e2ff;\r\n}\r\n\r\n.engine-item.active {\r\n  background: #ecf5ff;\r\n  color: #409EFF;\r\n  border-color: #409EFF;\r\n}\r\n\r\n.checkbox {\r\n  width: 16px;\r\n  height: 16px;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 2px;\r\n  margin-right: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #fff;\r\n  background-color: #409EFF;\r\n}\r\n\r\n.engine-icon {\r\n  width: 16px;\r\n  height: 16px;\r\n  margin-right: 8px;\r\n}\r\n\r\n.results-container {\r\n  display: flex;\r\n  gap: 20px;\r\n}\r\n\r\n.result-column {\r\n  flex: 1;\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  background: #f5f7fa;\r\n}\r\n\r\n.result-icon {\r\n  width: 16px;\r\n  height: 16px;\r\n  margin-right: 8px;\r\n}\r\n\r\n.result-title {\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.result-list {\r\n  padding: 15px;\r\n}\r\n\r\n.result-item {\r\n  margin-bottom: 20px;\r\n  padding-bottom: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.result-item:last-child {\r\n  margin-bottom: 0;\r\n  padding-bottom: 0;\r\n  border-bottom: none;\r\n}\r\n\r\n.item-title {\r\n  margin: 0 0 8px 0;\r\n  font-size: 16px;\r\n}\r\n\r\n.item-title a {\r\n  color: #0366d6;\r\n  text-decoration: none;\r\n}\r\n\r\n.item-title a:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.item-url {\r\n  color: #67c23a;\r\n  font-size: 12px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.item-desc {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 全文检索样式 */\r\n.fulltext-results {\r\n  margin-top: 20px;\r\n}\r\n\r\n.filter-section {\r\n  background: white;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.filter-row {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.filter-row:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.filter-label {\r\n  width: 80px;\r\n  color: #666;\r\n  font-size: 14px;\r\n}\r\n\r\n.filter-options {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 10px;\r\n}\r\n\r\n.count {\r\n  color: #999;\r\n  font-size: 12px;\r\n}\r\n\r\n.result-stats {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: white;\r\n  padding: 15px 20px;\r\n  margin-bottom: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.results-list {\r\n  background: white;\r\n  border-radius: 8px;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.results-list .result-item {\r\n  padding: 20px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.results-list .result-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.result-title {\r\n  font-size: 16px;\r\n  color: #333;\r\n  margin: 0;\r\n  line-height: 1.4;\r\n  flex: 1;\r\n  margin-right: 20px;\r\n}\r\n\r\n.result-meta {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n  margin-bottom: 10px;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.meta-item {\r\n  white-space: nowrap;\r\n}\r\n\r\n.result-content {\r\n  color: #666;\r\n  line-height: 1.6;\r\n  font-size: 14px;\r\n}\r\n\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  background: white;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n</style>\r\n"]}]}