{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\login.vue?vue&type=style&index=0&id=7589b93f&rel=stylesheet%2Fscss&lang=scss", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\login.vue", "mtime": 1749104047640}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749104419119}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749104421224}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749104419872}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749104418479}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5sb2dpbiB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBoZWlnaHQ6IDEwMCU7DQogIGJhY2tncm91bmQtaW1hZ2U6IHVybCgiLi4vYXNzZXRzL2ltYWdlcy9sb2dpbi1iYWNrZ3JvdW5kLmpwZyIpOw0KICBiYWNrZ3JvdW5kLXNpemU6IGNvdmVyOw0KfQ0KLnRpdGxlIHsNCiAgbWFyZ2luOiAwcHggYXV0byAzMHB4IGF1dG87DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgY29sb3I6ICM3MDcwNzA7DQp9DQoNCi5sb2dpbi1mb3JtIHsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICBiYWNrZ3JvdW5kOiAjZmZmZmZmOw0KICB3aWR0aDogNDAwcHg7DQogIHBhZGRpbmc6IDI1cHggMjVweCA1cHggMjVweDsNCiAgLmVsLWlucHV0IHsNCiAgICBoZWlnaHQ6IDM4cHg7DQogICAgaW5wdXQgew0KICAgICAgaGVpZ2h0OiAzOHB4Ow0KICAgIH0NCiAgfQ0KICAuaW5wdXQtaWNvbiB7DQogICAgaGVpZ2h0OiAzOXB4Ow0KICAgIHdpZHRoOiAxNHB4Ow0KICAgIG1hcmdpbi1sZWZ0OiAycHg7DQogIH0NCn0NCi5sb2dpbi10aXAgew0KICBmb250LXNpemU6IDEzcHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgY29sb3I6ICNiZmJmYmY7DQp9DQoubG9naW4tY29kZSB7DQogIHdpZHRoOiAzMyU7DQogIGhlaWdodDogMzhweDsNCiAgZmxvYXQ6IHJpZ2h0Ow0KICBpbWcgew0KICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlOw0KICB9DQp9DQouZWwtbG9naW4tZm9vdGVyIHsNCiAgaGVpZ2h0OiA0MHB4Ow0KICBsaW5lLWhlaWdodDogNDBweDsNCiAgcG9zaXRpb246IGZpeGVkOw0KICBib3R0b206IDA7DQogIHdpZHRoOiAxMDAlOw0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGNvbG9yOiAjZmZmOw0KICBmb250LWZhbWlseTogQXJpYWw7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgbGV0dGVyLXNwYWNpbmc6IDFweDsNCn0NCi5sb2dpbi1jb2RlLWltZyB7DQogIGhlaWdodDogMzhweDsNCn0NCg=="}, {"version": 3, "sources": ["login.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+JA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "login.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"login\">\r\n    <el-form ref=\"loginForm\" :model=\"loginForm\" :rules=\"loginRules\" class=\"login-form\">\r\n      <h3 class=\"title\">金刚舆情智库系统</h3>\r\n      <el-form-item prop=\"username\">\r\n        <el-input\r\n          v-model=\"loginForm.username\"\r\n          type=\"text\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"账号\"\r\n        >\r\n          <svg-icon slot=\"prefix\" icon-class=\"user\" class=\"el-input__icon input-icon\" />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"password\">\r\n        <el-input\r\n          v-model=\"loginForm.password\"\r\n          type=\"password\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"密码\"\r\n          @keyup.enter.native=\"handleLogin\"\r\n        >\r\n          <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"code\" v-if=\"captchaEnabled\">\r\n        <el-input\r\n          v-model=\"loginForm.code\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"验证码\"\r\n          style=\"width: 63%\"\r\n          @keyup.enter.native=\"handleLogin\"\r\n        >\r\n          <svg-icon slot=\"prefix\" icon-class=\"validCode\" class=\"el-input__icon input-icon\" />\r\n        </el-input>\r\n        <div class=\"login-code\">\r\n          <img :src=\"codeUrl\" @click=\"getCode\" class=\"login-code-img\"/>\r\n        </div>\r\n      </el-form-item>\r\n      <el-checkbox v-model=\"loginForm.rememberMe\" style=\"margin:0px 0px 25px 0px;\">记住密码</el-checkbox>\r\n      <el-form-item style=\"width:100%;\">\r\n        <el-button\r\n          :loading=\"loading\"\r\n          size=\"medium\"\r\n          type=\"primary\"\r\n          style=\"width:100%;\"\r\n          @click.native.prevent=\"handleLogin\"\r\n        >\r\n          <span v-if=\"!loading\">登 录</span>\r\n          <span v-else>登 录 中...</span>\r\n        </el-button>\r\n        <div style=\"float: right;\" v-if=\"register\">\r\n          <router-link class=\"link-type\" :to=\"'/register'\">立即注册</router-link>\r\n        </div>\r\n      </el-form-item>\r\n    </el-form>\r\n    <!--  底部  -->\r\n    <div class=\"el-login-footer\">\r\n      <span>Copyright © 2024 insistence.tech All Rights Reserved.</span>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCodeImg } from \"@/api/login\";\r\nimport Cookies from \"js-cookie\";\r\nimport { encrypt, decrypt } from '@/utils/jsencrypt'\r\n\r\nexport default {\r\n  name: \"Login\",\r\n  data() {\r\n    return {\r\n      codeUrl: \"\",\r\n      loginForm: {\r\n        username: \"\",\r\n        password: \"\",\r\n        rememberMe: false,\r\n        code: \"\",\r\n        uuid: \"\"\r\n      },\r\n      loginRules: {\r\n        username: [\r\n          { required: true, trigger: \"blur\", message: \"请输入您的账号\" }\r\n        ],\r\n        password: [\r\n          { required: true, trigger: \"blur\", message: \"请输入您的密码\" }\r\n        ],\r\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }]\r\n      },\r\n      loading: false,\r\n      // 验证码开关\r\n      captchaEnabled: true,\r\n      // 注册开关\r\n      register: false,\r\n      redirect: undefined\r\n    };\r\n  },\r\n  watch: {\r\n    $route: {\r\n      handler: function(route) {\r\n        this.redirect = route.query && route.query.redirect;\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  created() {\r\n    this.getCode();\r\n    this.getCookie();\r\n  },\r\n  methods: {\r\n    getCode() {\r\n      getCodeImg().then(res => {\r\n        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled;\r\n        this.register = res.registerEnabled === undefined ? false : res.registerEnabled;\r\n        if (this.captchaEnabled) {\r\n          this.codeUrl = \"data:image/gif;base64,\" + res.img;\r\n          this.loginForm.uuid = res.uuid;\r\n        }\r\n      });\r\n    },\r\n    getCookie() {\r\n      const username = Cookies.get(\"username\");\r\n      const password = Cookies.get(\"password\");\r\n      const rememberMe = Cookies.get('rememberMe')\r\n      this.loginForm = {\r\n        username: username === undefined ? this.loginForm.username : username,\r\n        password: password === undefined ? this.loginForm.password : decrypt(password),\r\n        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)\r\n      };\r\n    },\r\n    handleLogin() {\r\n      this.$refs.loginForm.validate(valid => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          if (this.loginForm.rememberMe) {\r\n            Cookies.set(\"username\", this.loginForm.username, { expires: 30 });\r\n            Cookies.set(\"password\", encrypt(this.loginForm.password), { expires: 30 });\r\n            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 });\r\n          } else {\r\n            Cookies.remove(\"username\");\r\n            Cookies.remove(\"password\");\r\n            Cookies.remove('rememberMe');\r\n          }\r\n          this.$store.dispatch(\"Login\", this.loginForm).then(() => {\r\n            this.$router.push({ path: this.redirect || \"/\" }).catch(()=>{});\r\n          }).catch(() => {\r\n            this.loading = false;\r\n            if (this.captchaEnabled) {\r\n              this.getCode();\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style rel=\"stylesheet/scss\" lang=\"scss\">\r\n.login {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100%;\r\n  background-image: url(\"../assets/images/login-background.jpg\");\r\n  background-size: cover;\r\n}\r\n.title {\r\n  margin: 0px auto 30px auto;\r\n  text-align: center;\r\n  color: #707070;\r\n}\r\n\r\n.login-form {\r\n  border-radius: 6px;\r\n  background: #ffffff;\r\n  width: 400px;\r\n  padding: 25px 25px 5px 25px;\r\n  .el-input {\r\n    height: 38px;\r\n    input {\r\n      height: 38px;\r\n    }\r\n  }\r\n  .input-icon {\r\n    height: 39px;\r\n    width: 14px;\r\n    margin-left: 2px;\r\n  }\r\n}\r\n.login-tip {\r\n  font-size: 13px;\r\n  text-align: center;\r\n  color: #bfbfbf;\r\n}\r\n.login-code {\r\n  width: 33%;\r\n  height: 38px;\r\n  float: right;\r\n  img {\r\n    cursor: pointer;\r\n    vertical-align: middle;\r\n  }\r\n}\r\n.el-login-footer {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  position: fixed;\r\n  bottom: 0;\r\n  width: 100%;\r\n  text-align: center;\r\n  color: #fff;\r\n  font-family: Arial;\r\n  font-size: 12px;\r\n  letter-spacing: 1px;\r\n}\r\n.login-code-img {\r\n  height: 38px;\r\n}\r\n</style>\r\n"]}]}