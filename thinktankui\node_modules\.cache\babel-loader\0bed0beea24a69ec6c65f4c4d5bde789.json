{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\utils\\dict\\DictData.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\utils\\dict\\DictData.js", "mtime": 1749104047634}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749104418875}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfY3JlYXRlQ2xhc3MyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvY3JlYXRlQ2xhc3MuanMiKSk7CnZhciBfY2xhc3NDYWxsQ2hlY2syID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvY2xhc3NDYWxsQ2hlY2suanMiKSk7Ci8qKg0KICogQGNsYXNzZGVzYyDlrZflhbjmlbDmja4NCiAqIEBwcm9wZXJ0eSB7U3RyaW5nfSBsYWJlbCDmoIfnrb4NCiAqIEBwcm9wZXJ0eSB7Kn0gdmFsdWUg5qCH562+DQogKiBAcHJvcGVydHkge09iamVjdH0gcmF3IOWOn+Wni+aVsOaNrg0KICovCnZhciBEaWN0RGF0YSA9IGV4cG9ydHMuZGVmYXVsdCA9IC8qI19fUFVSRV9fKi8oMCwgX2NyZWF0ZUNsYXNzMi5kZWZhdWx0KShmdW5jdGlvbiBEaWN0RGF0YShsYWJlbCwgdmFsdWUsIHJhdykgewogICgwLCBfY2xhc3NDYWxsQ2hlY2syLmRlZmF1bHQpKHRoaXMsIERpY3REYXRhKTsKICB0aGlzLmxhYmVsID0gbGFiZWw7CiAgdGhpcy52YWx1ZSA9IHZhbHVlOwogIHRoaXMucmF3ID0gcmF3Owp9KTs="}, {"version": 3, "names": ["DictData", "exports", "default", "_createClass2", "label", "value", "raw", "_classCallCheck2"], "sources": ["D:/thinktank/thinktankui/src/utils/dict/DictData.js"], "sourcesContent": ["/**\r\n * @classdesc 字典数据\r\n * @property {String} label 标签\r\n * @property {*} value 标签\r\n * @property {Object} raw 原始数据\r\n */\r\nexport default class DictData {\r\n  constructor(label, value, raw) {\r\n    this.label = label\r\n    this.value = value\r\n    this.raw = raw\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AALA,IAMqBA,QAAQ,GAAAC,OAAA,CAAAC,OAAA,oBAAAC,aAAA,CAAAD,OAAA,EAC3B,SAAAF,SAAYI,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAE;EAAA,IAAAC,gBAAA,CAAAL,OAAA,QAAAF,QAAA;EAC7B,IAAI,CAACI,KAAK,GAAGA,KAAK;EAClB,IAAI,CAACC,KAAK,GAAGA,KAAK;EAClB,IAAI,CAACC,GAAG,GAAGA,GAAG;AAChB,CAAC", "ignoreList": []}]}