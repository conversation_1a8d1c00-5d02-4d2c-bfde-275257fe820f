{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\register.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\register.vue", "mtime": 1749104047642}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["register.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "register.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"register\">\r\n    <el-form ref=\"registerForm\" :model=\"registerForm\" :rules=\"registerRules\" class=\"register-form\">\r\n      <h3 class=\"title\">vfadmin后台管理系统</h3>\r\n      <el-form-item prop=\"username\">\r\n        <el-input v-model=\"registerForm.username\" type=\"text\" auto-complete=\"off\" placeholder=\"账号\">\r\n          <svg-icon slot=\"prefix\" icon-class=\"user\" class=\"el-input__icon input-icon\" />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"password\">\r\n        <el-input\r\n          v-model=\"registerForm.password\"\r\n          type=\"password\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"密码\"\r\n          @keyup.enter.native=\"handleRegister\"\r\n        >\r\n          <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"confirmPassword\">\r\n        <el-input\r\n          v-model=\"registerForm.confirmPassword\"\r\n          type=\"password\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"确认密码\"\r\n          @keyup.enter.native=\"handleRegister\"\r\n        >\r\n          <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"code\" v-if=\"captchaEnabled\">\r\n        <el-input\r\n          v-model=\"registerForm.code\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"验证码\"\r\n          style=\"width: 63%\"\r\n          @keyup.enter.native=\"handleRegister\"\r\n        >\r\n          <svg-icon slot=\"prefix\" icon-class=\"validCode\" class=\"el-input__icon input-icon\" />\r\n        </el-input>\r\n        <div class=\"register-code\">\r\n          <img :src=\"codeUrl\" @click=\"getCode\" class=\"register-code-img\"/>\r\n        </div>\r\n      </el-form-item>\r\n      <el-form-item style=\"width:100%;\">\r\n        <el-button\r\n          :loading=\"loading\"\r\n          size=\"medium\"\r\n          type=\"primary\"\r\n          style=\"width:100%;\"\r\n          @click.native.prevent=\"handleRegister\"\r\n        >\r\n          <span v-if=\"!loading\">注 册</span>\r\n          <span v-else>注 册 中...</span>\r\n        </el-button>\r\n        <div style=\"float: right;\">\r\n          <router-link class=\"link-type\" :to=\"'/login'\">使用已有账户登录</router-link>\r\n        </div>\r\n      </el-form-item>\r\n    </el-form>\r\n    <!--  底部  -->\r\n    <div class=\"el-register-footer\">\r\n      <span>Copyright © 2024 insistence.tech All Rights Reserved.</span>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCodeImg, register } from \"@/api/login\";\r\n\r\nexport default {\r\n  name: \"Register\",\r\n  data() {\r\n    const equalToPassword = (rule, value, callback) => {\r\n      if (this.registerForm.password !== value) {\r\n        callback(new Error(\"两次输入的密码不一致\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    return {\r\n      codeUrl: \"\",\r\n      registerForm: {\r\n        username: \"\",\r\n        password: \"\",\r\n        confirmPassword: \"\",\r\n        code: \"\",\r\n        uuid: \"\"\r\n      },\r\n      registerRules: {\r\n        username: [\r\n          { required: true, trigger: \"blur\", message: \"请输入您的账号\" },\r\n          { min: 2, max: 20, message: '用户账号长度必须介于 2 和 20 之间', trigger: 'blur' }\r\n        ],\r\n        password: [\r\n          { required: true, trigger: \"blur\", message: \"请输入您的密码\" },\r\n          { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' },\r\n          { pattern: /^[^<>\"'|\\\\]+$/, message: \"不能包含非法字符：< > \\\" ' \\\\\\ |\", trigger: \"blur\" }\r\n        ],\r\n        confirmPassword: [\r\n          { required: true, trigger: \"blur\", message: \"请再次输入您的密码\" },\r\n          { required: true, validator: equalToPassword, trigger: \"blur\" }\r\n        ],\r\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }]\r\n      },\r\n      loading: false,\r\n      captchaEnabled: true\r\n    };\r\n  },\r\n  created() {\r\n    this.getCode();\r\n  },\r\n  methods: {\r\n    getCode() {\r\n      getCodeImg().then(res => {\r\n        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled;\r\n        if (this.captchaEnabled) {\r\n          this.codeUrl = \"data:image/gif;base64,\" + res.img;\r\n          this.registerForm.uuid = res.uuid;\r\n        }\r\n      });\r\n    },\r\n    handleRegister() {\r\n      this.$refs.registerForm.validate(valid => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          register(this.registerForm).then(res => {\r\n            const username = this.registerForm.username;\r\n            this.$alert(\"<font color='red'>恭喜你，您的账号 \" + username + \" 注册成功！</font>\", '系统提示', {\r\n              dangerouslyUseHTMLString: true,\r\n              type: 'success'\r\n            }).then(() => {\r\n              this.$router.push(\"/login\");\r\n            }).catch(() => {});\r\n          }).catch(() => {\r\n            this.loading = false;\r\n            if (this.captchaEnabled) {\r\n              this.getCode();\r\n            }\r\n          })\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style rel=\"stylesheet/scss\" lang=\"scss\">\r\n.register {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100%;\r\n  background-image: url(\"../assets/images/login-background.jpg\");\r\n  background-size: cover;\r\n}\r\n.title {\r\n  margin: 0px auto 30px auto;\r\n  text-align: center;\r\n  color: #707070;\r\n}\r\n\r\n.register-form {\r\n  border-radius: 6px;\r\n  background: #ffffff;\r\n  width: 400px;\r\n  padding: 25px 25px 5px 25px;\r\n  .el-input {\r\n    height: 38px;\r\n    input {\r\n      height: 38px;\r\n    }\r\n  }\r\n  .input-icon {\r\n    height: 39px;\r\n    width: 14px;\r\n    margin-left: 2px;\r\n  }\r\n}\r\n.register-tip {\r\n  font-size: 13px;\r\n  text-align: center;\r\n  color: #bfbfbf;\r\n}\r\n.register-code {\r\n  width: 33%;\r\n  height: 38px;\r\n  float: right;\r\n  img {\r\n    cursor: pointer;\r\n    vertical-align: middle;\r\n  }\r\n}\r\n.el-register-footer {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  position: fixed;\r\n  bottom: 0;\r\n  width: 100%;\r\n  text-align: center;\r\n  color: #fff;\r\n  font-family: Arial;\r\n  font-size: 12px;\r\n  letter-spacing: 1px;\r\n}\r\n.register-code-img {\r\n  height: 38px;\r\n}\r\n</style>\r\n"]}]}