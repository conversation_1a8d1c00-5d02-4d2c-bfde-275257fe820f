{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\Breadcrumb\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\Breadcrumb\\index.vue", "mtime": 1749104047617}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Breadcrumb", "sourcesContent": ["<template>\r\n  <el-breadcrumb class=\"app-breadcrumb\" separator=\"/\">\r\n    <transition-group name=\"breadcrumb\">\r\n      <el-breadcrumb-item v-for=\"(item, index) in levelList\" :key=\"item.path\">\r\n        <span v-if=\"item.redirect === 'noRedirect' || index == levelList.length - 1\" class=\"no-redirect\">{{ item.meta.title }}</span>\r\n        <a v-else @click.prevent=\"handleLink(item)\">{{ item.meta.title }}</a>\r\n      </el-breadcrumb-item>\r\n    </transition-group>\r\n  </el-breadcrumb>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      levelList: null\r\n    }\r\n  },\r\n  watch: {\r\n    $route(route) {\r\n      // if you go to the redirect page, do not update the breadcrumbs\r\n      if (route.path.startsWith('/redirect/')) {\r\n        return\r\n      }\r\n      this.getBreadcrumb()\r\n    }\r\n  },\r\n  created() {\r\n    this.getBreadcrumb()\r\n  },\r\n  methods: {\r\n    getBreadcrumb() {\r\n      // only show routes with meta.title\r\n      let matched = []\r\n      const router = this.$route\r\n      const pathNum = this.findPathNum(router.path)\r\n      // multi-level menu\r\n      if (pathNum > 2) {\r\n        const reg = /\\/\\w+/gi\r\n        const pathList = router.path.match(reg).map((item, index) => {\r\n          if (index !== 0) item = item.slice(1)\r\n          return item\r\n        })\r\n        this.getMatched(pathList, this.$store.getters.defaultRoutes, matched)\r\n      } else {\r\n        matched = router.matched.filter(item => item.meta && item.meta.title)\r\n      }\r\n      // 判断是否为首页\r\n      if (!this.isDashboard(matched[0])) {\r\n        matched = [{ path: \"/index\", meta: { title: \"首页\" } }].concat(matched)\r\n      }\r\n      this.levelList = matched.filter(item => item.meta && item.meta.title && item.meta.breadcrumb !== false)\r\n    },\r\n    findPathNum(str, char = \"/\") {\r\n      let index = str.indexOf(char)\r\n      let num = 0\r\n      while (index !== -1) {\r\n        num++\r\n        index = str.indexOf(char, index + 1)\r\n      }\r\n      return num\r\n    },\r\n    getMatched(pathList, routeList, matched) {\r\n      let data = routeList.find(item => item.path == pathList[0] || (item.name += '').toLowerCase() == pathList[0])\r\n      if (data) {\r\n        matched.push(data)\r\n        if (data.children && pathList.length) {\r\n          pathList.shift()\r\n          this.getMatched(pathList, data.children, matched)\r\n        }\r\n      }\r\n    },\r\n    isDashboard(route) {\r\n      const name = route && route.name\r\n      if (!name) {\r\n        return false\r\n      }\r\n      return name.trim() === 'Index'\r\n    },\r\n    handleLink(item) {\r\n      const { redirect, path } = item\r\n      if (redirect) {\r\n        this.$router.push(redirect)\r\n        return\r\n      }\r\n      this.$router.push(path)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-breadcrumb.el-breadcrumb {\r\n  display: inline-block;\r\n  font-size: 14px;\r\n  line-height: 50px;\r\n  margin-left: 8px;\r\n  .no-redirect {\r\n    color: #97a8be;\r\n    cursor: text;\r\n  }\r\n}\r\n</style>\r\n"]}]}