{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\search-results\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\search-results\\index.vue", "mtime": 1749104047642}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "originalTopNav", "undefined", "searchQuery", "selectedTime", "selectedPlatform", "selectedEmotion", "currentPage", "pageSize", "totalResults", "timeOptions", "label", "value", "platformOptions", "count", "emotionOptions", "searchResults", "title", "source", "publishTime", "author", "platform", "readCount", "location", "category", "content", "mounted", "$store", "state", "settings", "topNav", "dispatch", "key", "$route", "query", "q", "handleSearch", "<PERSON><PERSON><PERSON><PERSON>", "methods", "$message", "success", "concat", "selectTime", "selectPlatform", "selectEmotion", "handlePageChange", "page", "goBack", "window", "history", "length", "$router", "go", "push"], "sources": ["src/views/search-results/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"search-results-container\">\r\n    <!-- 搜索区域 -->\r\n    <div class=\"search-section\">\r\n      <div class=\"search-header\">\r\n        <el-button type=\"text\" icon=\"el-icon-arrow-left\" @click=\"goBack\" class=\"back-button\">\r\n          返回\r\n        </el-button>\r\n        <div class=\"search-tabs\">\r\n          <div class=\"tab-item active\">全文检索</div>\r\n          <div class=\"tab-item\">可视化</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"search-box\">\r\n        <el-input\r\n          v-model=\"searchQuery\"\r\n          placeholder=\"请输入搜索关键词\"\r\n          class=\"search-input\"\r\n          @keyup.enter=\"handleSearch\"\r\n        >\r\n          <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"handleSearch\"></el-button>\r\n        </el-input>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 筛选条件区域 -->\r\n    <div class=\"filter-section\">\r\n      <!-- 时间筛选 -->\r\n      <div class=\"filter-row\">\r\n        <span class=\"filter-label\">时间范围:</span>\r\n        <div class=\"filter-options\">\r\n          <el-button\r\n            v-for=\"time in timeOptions\"\r\n            :key=\"time.value\"\r\n            :type=\"selectedTime === time.value ? 'primary' : ''\"\r\n            size=\"small\"\r\n            @click=\"selectTime(time.value)\"\r\n          >\r\n            {{ time.label }}\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 平台筛选 -->\r\n      <div class=\"filter-row\">\r\n        <span class=\"filter-label\">平台类型:</span>\r\n        <div class=\"filter-options\">\r\n          <el-button\r\n            v-for=\"platform in platformOptions\"\r\n            :key=\"platform.value\"\r\n            :type=\"selectedPlatform === platform.value ? 'primary' : ''\"\r\n            size=\"small\"\r\n            @click=\"selectPlatform(platform.value)\"\r\n          >\r\n            {{ platform.label }}\r\n            <span v-if=\"platform.count\" class=\"count\">({{ platform.count }})</span>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 情感筛选 -->\r\n      <div class=\"filter-row\">\r\n        <span class=\"filter-label\">情感类型:</span>\r\n        <div class=\"filter-options\">\r\n          <el-button\r\n            v-for=\"emotion in emotionOptions\"\r\n            :key=\"emotion.value\"\r\n            :type=\"selectedEmotion === emotion.value ? 'primary' : ''\"\r\n            size=\"small\"\r\n            @click=\"selectEmotion(emotion.value)\"\r\n          >\r\n            {{ emotion.label }}\r\n            <span v-if=\"emotion.count\" class=\"count\">({{ emotion.count }})</span>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 其他筛选 -->\r\n      <div class=\"filter-row\">\r\n        <span class=\"filter-label\">其他筛选:</span>\r\n        <div class=\"filter-options\">\r\n          <el-button size=\"small\">作者类型</el-button>\r\n          <el-button size=\"small\">地域</el-button>\r\n          <el-button size=\"small\">影响力</el-button>\r\n          <el-button size=\"small\">传播量</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 结果统计 -->\r\n    <div class=\"result-stats\">\r\n      <span>共{{ totalResults }}条结果</span>\r\n      <div class=\"action-buttons\">\r\n        <el-button size=\"small\">导出</el-button>\r\n        <el-button type=\"primary\" size=\"small\">分析</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索结果列表 -->\r\n    <div class=\"results-list\">\r\n      <div v-for=\"(item, index) in searchResults\" :key=\"index\" class=\"result-item\">\r\n        <div class=\"result-header\">\r\n          <h3 class=\"result-title\">{{ item.title }}</h3>\r\n          <div class=\"result-actions\">\r\n            <el-button type=\"text\" icon=\"el-icon-view\"></el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"result-meta\">\r\n          <span class=\"meta-item\">{{ item.source }}</span>\r\n          <span class=\"meta-item\">{{ item.publishTime }}</span>\r\n          <span class=\"meta-item\">{{ item.author }}</span>\r\n          <span class=\"meta-item\">{{ item.platform }}</span>\r\n          <span class=\"meta-item\">阅读量: {{ item.readCount }}</span>\r\n          <span class=\"meta-item\">{{ item.location }}</span>\r\n          <span class=\"meta-item\">{{ item.category }}</span>\r\n        </div>\r\n\r\n        <div class=\"result-content\">\r\n          {{ item.content }}\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 分页 -->\r\n    <div class=\"pagination-container\">\r\n      <el-pagination\r\n        background\r\n        layout=\"prev, pager, next\"\r\n        :total=\"totalResults\"\r\n        :current-page.sync=\"currentPage\"\r\n        :page-size=\"pageSize\"\r\n        @current-change=\"handlePageChange\"\r\n      ></el-pagination>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"SearchResults\",\r\n  data() {\r\n    return {\r\n      originalTopNav: undefined,\r\n      searchQuery: \"今日 万元\",\r\n      selectedTime: \"24h\",\r\n      selectedPlatform: \"all\",\r\n      selectedEmotion: \"all\",\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      totalResults: 10000,\r\n\r\n      timeOptions: [\r\n        { label: \"24小时\", value: \"24h\" },\r\n        { label: \"一周\", value: \"1w\" },\r\n        { label: \"半年\", value: \"6m\" },\r\n        { label: \"一年\", value: \"1y\" },\r\n        { label: \"自定义\", value: \"custom\" }\r\n      ],\r\n\r\n      platformOptions: [\r\n        { label: \"全部\", value: \"all\", count: 10540 },\r\n        { label: \"微信\", value: \"wechat\", count: 1847 },\r\n        { label: \"微博\", value: \"weibo\", count: 2008 },\r\n        { label: \"客户端\", value: \"app\", count: 1748 },\r\n        { label: \"论坛\", value: \"forum\", count: 673 }\r\n      ],\r\n\r\n      emotionOptions: [\r\n        { label: \"全部\", value: \"all\" },\r\n        { label: \"正面\", value: \"positive\" },\r\n        { label: \"负面\", value: \"negative\" },\r\n        { label: \"中性\", value: \"neutral\" }\r\n      ],\r\n\r\n      searchResults: [\r\n        {\r\n          title: \"从政府部门44三个个体的热点问题，到媒体1，覆盖的（含义），斗争主义口号，牛年主要的问题，共同人工智能的问题人员，如山山的问题的主要问题，新工人，打工，用友三家...\",\r\n          source: \"新华网\",\r\n          publishTime: \"2022-06-29 20:07:04\",\r\n          author: \"77人讨论\",\r\n          platform: \"平台来源\",\r\n          readCount: \"无\",\r\n          location: \"无所在地\",\r\n          category: \"新闻\",\r\n          content: \"从政府部门44三个个体的热点问题，到媒体1，覆盖的（含义），斗争主义口号，牛年主要的问题，共同人工智能的问题人员，如山山的问题的主要问题，新工人，打工，用友三家...\"\r\n        },\r\n        {\r\n          title: \"中大-论文发表(2025年中大论文发表)自然指数中大-论文发表世界地位论坛-2025年的价值中国论文\",\r\n          source: \"中大论文发表\",\r\n          publishTime: \"2022-06-29 20:07:04\",\r\n          author: \"77人讨论\",\r\n          platform: \"平台来源\",\r\n          readCount: \"无\",\r\n          location: \"无所在地\",\r\n          category: \"论文\",\r\n          content: \"中大-论文发表(2025年中大论文发表)自然指数中大-论文发表世界地位论坛-2025年的价值中国论文...\"\r\n        },\r\n        {\r\n          title: \"转发微博#中#大学生，人情世故。\",\r\n          source: \"微博\",\r\n          publishTime: \"2022-06-29 20:07:04\",\r\n          author: \"77人讨论\",\r\n          platform: \"微博\",\r\n          readCount: \"1000\",\r\n          location: \"北京\",\r\n          category: \"社交媒体\",\r\n          content: \"转发微博#中#大学生，人情世故。这是一条关于大学生人际关系的微博内容...\"\r\n        }\r\n      ]\r\n    };\r\n  },\r\n  mounted() {\r\n    // 隐藏顶部导航栏\r\n    this.originalTopNav = this.$store.state.settings.topNav\r\n    this.$store.dispatch('settings/changeSetting', {\r\n      key: 'topNav',\r\n      value: false\r\n    })\r\n\r\n    // 获取URL参数中的搜索关键词\r\n    if (this.$route.query.q) {\r\n      this.searchQuery = this.$route.query.q;\r\n      this.handleSearch();\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    // 恢复顶部导航栏设置\r\n    if (this.originalTopNav !== undefined) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'topNav',\r\n        value: this.originalTopNav\r\n      })\r\n    }\r\n  },\r\n  methods: {\r\n    handleSearch() {\r\n      this.$message.success(`搜索: ${this.searchQuery}`);\r\n      // 实际搜索逻辑\r\n    },\r\n\r\n    selectTime(value) {\r\n      this.selectedTime = value;\r\n      this.handleSearch();\r\n    },\r\n\r\n    selectPlatform(value) {\r\n      this.selectedPlatform = value;\r\n      this.handleSearch();\r\n    },\r\n\r\n    selectEmotion(value) {\r\n      this.selectedEmotion = value;\r\n      this.handleSearch();\r\n    },\r\n\r\n    handlePageChange(page) {\r\n      this.currentPage = page;\r\n      // 加载对应页面数据\r\n    },\r\n\r\n    goBack() {\r\n      // 返回上一页，如果没有历史记录则返回信息汇总页面\r\n      if (window.history.length > 1) {\r\n        this.$router.go(-1);\r\n      } else {\r\n        this.$router.push('/info-summary');\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.search-results-container {\r\n  padding: 20px;\r\n  background-color: #f5f5f5;\r\n  min-height: 100vh;\r\n}\r\n\r\n.search-section {\r\n  background: white;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.search-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.back-button {\r\n  margin-right: 20px;\r\n  color: #409EFF;\r\n  font-size: 14px;\r\n}\r\n\r\n.back-button:hover {\r\n  color: #66b1ff;\r\n}\r\n\r\n.search-tabs {\r\n  display: flex;\r\n}\r\n\r\n.tab-item {\r\n  padding: 8px 20px;\r\n  cursor: pointer;\r\n  border-bottom: 2px solid transparent;\r\n  color: #666;\r\n}\r\n\r\n.tab-item.active {\r\n  color: #409EFF;\r\n  border-bottom-color: #409EFF;\r\n}\r\n\r\n.search-box {\r\n  max-width: 600px;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n}\r\n\r\n.filter-section {\r\n  background: white;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.filter-row {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.filter-row:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.filter-label {\r\n  width: 80px;\r\n  color: #666;\r\n  font-size: 14px;\r\n}\r\n\r\n.filter-options {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 10px;\r\n}\r\n\r\n.count {\r\n  color: #999;\r\n  font-size: 12px;\r\n}\r\n\r\n.result-stats {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: white;\r\n  padding: 15px 20px;\r\n  margin-bottom: 20px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.results-list {\r\n  background: white;\r\n  border-radius: 4px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.result-item {\r\n  padding: 20px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.result-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.result-title {\r\n  font-size: 16px;\r\n  color: #333;\r\n  margin: 0;\r\n  line-height: 1.4;\r\n  flex: 1;\r\n  margin-right: 20px;\r\n}\r\n\r\n.result-meta {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n  margin-bottom: 10px;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.meta-item {\r\n  white-space: nowrap;\r\n}\r\n\r\n.result-content {\r\n  color: #666;\r\n  line-height: 1.6;\r\n  font-size: 14px;\r\n}\r\n\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  background: white;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCA4IA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,cAAA,EAAAC,SAAA;MACAC,WAAA;MACAC,YAAA;MACAC,gBAAA;MACAC,eAAA;MACAC,WAAA;MACAC,QAAA;MACAC,YAAA;MAEAC,WAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MAEAC,eAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAE,KAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAE,KAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAE,KAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAE,KAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAE,KAAA;MAAA,EACA;MAEAC,cAAA,GACA;QAAAJ,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MAEAI,aAAA,GACA;QACAC,KAAA;QACAC,MAAA;QACAC,WAAA;QACAC,MAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,OAAA;MACA,GACA;QACAR,KAAA;QACAC,MAAA;QACAC,WAAA;QACAC,MAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,OAAA;MACA,GACA;QACAR,KAAA;QACAC,MAAA;QACAC,WAAA;QACAC,MAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,OAAA;MACA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAzB,cAAA,QAAA0B,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,MAAA;IACA,KAAAH,MAAA,CAAAI,QAAA;MACAC,GAAA;MACApB,KAAA;IACA;;IAEA;IACA,SAAAqB,MAAA,CAAAC,KAAA,CAAAC,CAAA;MACA,KAAAhC,WAAA,QAAA8B,MAAA,CAAAC,KAAA,CAAAC,CAAA;MACA,KAAAC,YAAA;IACA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA;IACA,SAAApC,cAAA,KAAAC,SAAA;MACA,KAAAyB,MAAA,CAAAI,QAAA;QACAC,GAAA;QACApB,KAAA,OAAAX;MACA;IACA;EACA;EACAqC,OAAA;IACAF,YAAA,WAAAA,aAAA;MACA,KAAAG,QAAA,CAAAC,OAAA,kBAAAC,MAAA,MAAAtC,WAAA;MACA;IACA;IAEAuC,UAAA,WAAAA,WAAA9B,KAAA;MACA,KAAAR,YAAA,GAAAQ,KAAA;MACA,KAAAwB,YAAA;IACA;IAEAO,cAAA,WAAAA,eAAA/B,KAAA;MACA,KAAAP,gBAAA,GAAAO,KAAA;MACA,KAAAwB,YAAA;IACA;IAEAQ,aAAA,WAAAA,cAAAhC,KAAA;MACA,KAAAN,eAAA,GAAAM,KAAA;MACA,KAAAwB,YAAA;IACA;IAEAS,gBAAA,WAAAA,iBAAAC,IAAA;MACA,KAAAvC,WAAA,GAAAuC,IAAA;MACA;IACA;IAEAC,MAAA,WAAAA,OAAA;MACA;MACA,IAAAC,MAAA,CAAAC,OAAA,CAAAC,MAAA;QACA,KAAAC,OAAA,CAAAC,EAAA;MACA;QACA,KAAAD,OAAA,CAAAE,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}