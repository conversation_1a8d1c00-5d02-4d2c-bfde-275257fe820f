{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\directive\\permission\\hasRole.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\directive\\permission\\hasRole.js", "mtime": 1749104047626}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749104418875}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5lcnJvci5jYXVzZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuaW5jbHVkZXMuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pbmNsdWRlcy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3Iuc29tZS5qcyIpOwp2YXIgX3N0b3JlID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3N0b3JlIikpOwovKioNCiogdi1oYXNSb2xlIOinkuiJsuadg+mZkOWkhOeQhg0KKiBDb3B5cmlnaHQgKGMpIDIwMTkgcnVveWkNCiovCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBpbnNlcnRlZDogZnVuY3Rpb24gaW5zZXJ0ZWQoZWwsIGJpbmRpbmcsIHZub2RlKSB7CiAgICB2YXIgdmFsdWUgPSBiaW5kaW5nLnZhbHVlOwogICAgdmFyIHN1cGVyX2FkbWluID0gImFkbWluIjsKICAgIHZhciByb2xlcyA9IF9zdG9yZS5kZWZhdWx0LmdldHRlcnMgJiYgX3N0b3JlLmRlZmF1bHQuZ2V0dGVycy5yb2xlczsKICAgIGlmICh2YWx1ZSAmJiB2YWx1ZSBpbnN0YW5jZW9mIEFycmF5ICYmIHZhbHVlLmxlbmd0aCA+IDApIHsKICAgICAgdmFyIHJvbGVGbGFnID0gdmFsdWU7CiAgICAgIHZhciBoYXNSb2xlID0gcm9sZXMuc29tZShmdW5jdGlvbiAocm9sZSkgewogICAgICAgIHJldHVybiBzdXBlcl9hZG1pbiA9PT0gcm9sZSB8fCByb2xlRmxhZy5pbmNsdWRlcyhyb2xlKTsKICAgICAgfSk7CiAgICAgIGlmICghaGFzUm9sZSkgewogICAgICAgIGVsLnBhcmVudE5vZGUgJiYgZWwucGFyZW50Tm9kZS5yZW1vdmVDaGlsZChlbCk7CiAgICAgIH0KICAgIH0gZWxzZSB7CiAgICAgIHRocm93IG5ldyBFcnJvcigiXHU4QkY3XHU4QkJFXHU3RjZFXHU4OUQyXHU4MjcyXHU2NzQzXHU5NjUwXHU2ODA3XHU3QjdFXHU1MDNDXCIiKTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_store", "_interopRequireDefault", "require", "_default", "exports", "default", "inserted", "el", "binding", "vnode", "value", "super_admin", "roles", "store", "getters", "Array", "length", "roleFlag", "hasRole", "some", "role", "includes", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "Error"], "sources": ["D:/thinktank/thinktankui/src/directive/permission/hasRole.js"], "sourcesContent": [" /**\r\n * v-hasRole 角色权限处理\r\n * Copyright (c) 2019 ruoyi\r\n */\r\n\r\nimport store from '@/store'\r\n\r\nexport default {\r\n  inserted(el, binding, vnode) {\r\n    const { value } = binding\r\n    const super_admin = \"admin\";\r\n    const roles = store.getters && store.getters.roles\r\n\r\n    if (value && value instanceof Array && value.length > 0) {\r\n      const roleFlag = value\r\n\r\n      const hasRole = roles.some(role => {\r\n        return super_admin === role || roleFlag.includes(role)\r\n      })\r\n\r\n      if (!hasRole) {\r\n        el.parentNode && el.parentNode.removeChild(el)\r\n      }\r\n    } else {\r\n      throw new Error(`请设置角色权限标签值\"`)\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;AAKA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AALC;AACD;AACA;AACA;AAHC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAOc;EACbC,QAAQ,WAARA,QAAQA,CAACC,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAE;IAC3B,IAAQC,KAAK,GAAKF,OAAO,CAAjBE,KAAK;IACb,IAAMC,WAAW,GAAG,OAAO;IAC3B,IAAMC,KAAK,GAAGC,cAAK,CAACC,OAAO,IAAID,cAAK,CAACC,OAAO,CAACF,KAAK;IAElD,IAAIF,KAAK,IAAIA,KAAK,YAAYK,KAAK,IAAIL,KAAK,CAACM,MAAM,GAAG,CAAC,EAAE;MACvD,IAAMC,QAAQ,GAAGP,KAAK;MAEtB,IAAMQ,OAAO,GAAGN,KAAK,CAACO,IAAI,CAAC,UAAAC,IAAI,EAAI;QACjC,OAAOT,WAAW,KAAKS,IAAI,IAAIH,QAAQ,CAACI,QAAQ,CAACD,IAAI,CAAC;MACxD,CAAC,CAAC;MAEF,IAAI,CAACF,OAAO,EAAE;QACZX,EAAE,CAACe,UAAU,IAAIf,EAAE,CAACe,UAAU,CAACC,WAAW,CAAChB,EAAE,CAAC;MAChD;IACF,CAAC,MAAM;MACL,MAAM,IAAIiB,KAAK,iEAAc,CAAC;IAChC;EACF;AACF,CAAC", "ignoreList": []}]}