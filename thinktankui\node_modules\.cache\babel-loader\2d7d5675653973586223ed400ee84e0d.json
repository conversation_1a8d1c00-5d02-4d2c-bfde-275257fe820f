{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\image.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\image.js", "mtime": 1749104421973}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_parchment", "require", "_link", "ATTRIBUTES", "Image", "_EmbedBlot", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "key", "value", "format", "name", "indexOf", "domNode", "setAttribute", "removeAttribute", "_superPropGet2", "create", "node", "sanitize", "formats", "reduce", "attribute", "hasAttribute", "getAttribute", "match", "url", "test", "EmbedBlot", "_defineProperty2", "_default", "exports"], "sources": ["../../src/formats/image.ts"], "sourcesContent": ["import { EmbedBlot } from 'parchment';\nimport { sanitize } from './link.js';\n\nconst ATTRIBUTES = ['alt', 'height', 'width'];\n\nclass Image extends EmbedBlot {\n  static blotName = 'image';\n  static tagName = 'IMG';\n\n  static create(value: string) {\n    const node = super.create(value) as Element;\n    if (typeof value === 'string') {\n      node.setAttribute('src', this.sanitize(value));\n    }\n    return node;\n  }\n\n  static formats(domNode: Element) {\n    return ATTRIBUTES.reduce(\n      (formats: Record<string, string | null>, attribute) => {\n        if (domNode.hasAttribute(attribute)) {\n          formats[attribute] = domNode.getAttribute(attribute);\n        }\n        return formats;\n      },\n      {},\n    );\n  }\n\n  static match(url: string) {\n    return /\\.(jpe?g|gif|png)$/.test(url) || /^data:image\\/.+;base64/.test(url);\n  }\n\n  static sanitize(url: string) {\n    return sanitize(url, ['http', 'https', 'data']) ? url : '//:0';\n  }\n\n  static value(domNode: Element) {\n    return domNode.getAttribute('src');\n  }\n\n  domNode: HTMLImageElement;\n\n  format(name: string, value: string) {\n    if (ATTRIBUTES.indexOf(name) > -1) {\n      if (value) {\n        this.domNode.setAttribute(name, value);\n      } else {\n        this.domNode.removeAttribute(name);\n      }\n    } else {\n      super.format(name, value);\n    }\n  }\n}\n\nexport default Image;\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAEA,IAAME,UAAU,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC;AAAA,IAEvCC,KAAK,0BAAAC,UAAA;EAAA,SAAAD,MAAA;IAAA,IAAAE,gBAAA,CAAAC,OAAA,QAAAH,KAAA;IAAA,WAAAI,WAAA,CAAAD,OAAA,QAAAH,KAAA,EAAAK,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAH,KAAA,EAAAC,UAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAH,KAAA;IAAAQ,GAAA;IAAAC,KAAA,EAsCT,SAAAC,MAAMA,CAACC,IAAY,EAAEF,KAAa,EAAE;MAClC,IAAIV,UAAU,CAACa,OAAO,CAACD,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;QACjC,IAAIF,KAAK,EAAE;UACT,IAAI,CAACI,OAAO,CAACC,YAAY,CAACH,IAAI,EAAEF,KAAK,CAAC;QACxC,CAAC,MAAM;UACL,IAAI,CAACI,OAAO,CAACE,eAAe,CAACJ,IAAI,CAAC;QACpC;MACF,CAAC,MAAM;QACL,IAAAK,cAAA,CAAAb,OAAA,EAAAH,KAAA,sBAAaW,IAAI,EAAEF,KAAK;MAC1B;IACF;EAAA;IAAAD,GAAA;IAAAC,KAAA,EA5CA,SAAOQ,MAAMA,CAACR,KAAa,EAAE;MAC3B,IAAMS,IAAI,OAAAF,cAAA,CAAAb,OAAA,EAAAH,KAAA,sBAAgBS,KAAK,EAAY;MAC3C,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7BS,IAAI,CAACJ,YAAY,CAAC,KAAK,EAAE,IAAI,CAACK,QAAQ,CAACV,KAAK,CAAC,CAAC;MAChD;MACA,OAAOS,IAAI;IACb;EAAA;IAAAV,GAAA;IAAAC,KAAA,EAEA,SAAOW,OAAOA,CAACP,OAAgB,EAAE;MAC/B,OAAOd,UAAU,CAACsB,MAAM,CACtB,UAACD,OAAsC,EAAEE,SAAS,EAAK;QACrD,IAAIT,OAAO,CAACU,YAAY,CAACD,SAAS,CAAC,EAAE;UACnCF,OAAO,CAACE,SAAS,CAAC,GAAGT,OAAO,CAACW,YAAY,CAACF,SAAS,CAAC;QACtD;QACA,OAAOF,OAAO;MAChB,CAAC,EACD,CAAC,CACH,CAAC;IACH;EAAA;IAAAZ,GAAA;IAAAC,KAAA,EAEA,SAAOgB,KAAKA,CAACC,GAAW,EAAE;MACxB,OAAO,oBAAoB,CAACC,IAAI,CAACD,GAAG,CAAC,IAAI,wBAAwB,CAACC,IAAI,CAACD,GAAG,CAAC;IAC7E;EAAA;IAAAlB,GAAA;IAAAC,KAAA,EAEA,SAAOU,QAAQA,CAACO,GAAW,EAAE;MAC3B,OAAO,IAAAP,cAAQ,EAACO,GAAG,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,GAAGA,GAAG,GAAG,MAAM;IAChE;EAAA;IAAAlB,GAAA;IAAAC,KAAA,EAEA,SAAOA,KAAKA,CAACI,OAAgB,EAAE;MAC7B,OAAOA,OAAO,CAACW,YAAY,CAAC,KAAK,CAAC;IACpC;EAAA;AAAA,EAlCkBI,oBAAS;AAAA,IAAAC,gBAAA,CAAA1B,OAAA,EAAvBH,KAAK,cACS,OAAO;AAAA,IAAA6B,gBAAA,CAAA1B,OAAA,EADrBH,KAAK,aAEQ,KAAK;AAAA,IAAA8B,QAAA,GAAAC,OAAA,CAAA5B,OAAA,GAiDTH,KAAK", "ignoreList": []}]}