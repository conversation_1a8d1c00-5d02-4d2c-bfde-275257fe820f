{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\info-summary\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\info-summary\\index.vue", "mtime": 1749104047640}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdJbmZvU3VtbWFyeScsDQogIGNvbXB1dGVkOiB7DQogICAgLy8g5qC55o2u5b2T5YmN6aG156CB5ZKM5q+P6aG15pi+56S65pWw6YeP6K6h566X5b2T5YmN6aG155qE5pWw5o2uDQogICAgcGFnaW5hdGVkTGlzdCgpIHsNCiAgICAgIGNvbnN0IHN0YXJ0ID0gKHRoaXMuY3VycmVudFBhZ2UgLSAxKSAqIHRoaXMucGFnZVNpemU7DQogICAgICAvLyDlrp7pmYXlupTnlKjkuK3vvIzov5nph4zlupTor6XmmK/ku47lkI7nq6/ojrflj5bliIbpobXmlbDmja4NCiAgICAgIC8vIOi/memHjOS4uuS6hua8lOekuu+8jOaIkeS7rOS7juacrOWcsOaVsOaNruS4reaIquWPluS4gOmDqOWIhg0KICAgICAgcmV0dXJuIHRoaXMuaW5mb0xpc3Quc2xpY2UoMCwgTWF0aC5taW4odGhpcy5pbmZvTGlzdC5sZW5ndGgsIHRoaXMucGFnZVNpemUpKTsNCiAgICB9DQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmhtemdouWfuuehgOaVsOaNrg0KICAgICAgb3JpZ2luYWxUb3BOYXY6IHVuZGVmaW5lZCwgLy8g5a2Y5YKo5Y6f5aeL55qEdG9wTmF254q25oCBDQogICAgICBzZWFyY2hLZXl3b3JkOiAnJywNCiAgICAgIHBvc2l0aXZlU2VudGltZW50RGlhbG9nVmlzaWJsZTogZmFsc2UsIC8vIOaOp+WItuato+mdouaDheaEn+W8ueeql+eahOaYvuekug0KICAgICAgc2VsZWN0ZWRTZW50aW1lbnQ6ICcnLCAvLyDnlKjkuo7mg4XmhJ/lsZ7mgKfnuqDplJnlvLnnqpfnmoTpgInkuK3lgLwNCiAgICAgIGVkaXRpbmdJdGVtOiBudWxsLCAvLyDlvZPliY3nvJbovpHnmoTmg4XmhJ/mnaHnm64NCiAgICAgIGFjdGl2ZU1lbnU6ICcyJywgLy8g6buY6K6k6YCJ5Lit5ZOB54mMDQogICAgICBhY3RpdmVUYWI6ICd0b2RheScsIC8vIOm7mOiupOmAieS4reS7iuWkqQ0KICAgICAgcGxhdGZvcm1UeXBlczogWyduZXdzJywgJ3dlaWJvJywgJ3dlY2hhdCcsICd2aWRlbycsICdhcHAnXSwgLy8g6buY6K6k6YCJ5Lit55qE5bmz5Y+w57G75Z6LDQogICAgICBzZW50aW1lbnRUeXBlczogWydwb3NpdGl2ZScsICduZXV0cmFsJ10sIC8vIOm7mOiupOmAieS4reeahOaDheaEn+exu+Weiw0KICAgICAgaW5mb0F0dHJpYnV0ZXM6IFsnb2ZmaWNpYWwnLCAnbWVkaWEnXSwgLy8g6buY6K6k6YCJ5Lit55qE5L+h5oGv5bGe5oCnDQoNCiAgICAgIC8vIOWIhumhteebuOWFs+aVsOaNrg0KICAgICAgY3VycmVudFBhZ2U6IDEsDQogICAgICBwYWdlU2l6ZTogMTAsDQogICAgICB0b3RhbEl0ZW1zOiAxMjAsIC8vIOWBh+iuvuaAu+WFseaciTEyMOadoeaVsOaNrg0KDQogICAgICAvLyDmlrDlu7rmlrnmoYjlr7nor53moYbnm7jlhbPmlbDmja4NCiAgICAgIGNyZWF0ZVBsYW5EaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHBsYW5BY3RpdmVUYWI6ICdzdGFuZGFyZCcsDQogICAgICB0aGVtZUlucHV0VmlzaWJsZTogZmFsc2UsDQogICAgICB0aGVtZUlucHV0VmFsdWU6ICcnLA0KICAgICAgcGxhbkZvcm06IHsNCiAgICAgICAgbmFtZTogJycsDQogICAgICAgIHNjb3BlOiAnYWxsJywNCiAgICAgICAgbW9uaXRvck9iamVjdDogJycsDQogICAgICAgIGxvY2F0aW9uOiAnJywNCiAgICAgICAgdGhlbWVzOiBbXSwNCiAgICAgICAgaW5kdXN0cnk6ICcnLA0KICAgICAgICB0aW1lUmFuZ2U6ICcnLA0KICAgICAgICBjaGFubmVsczogWyduZXdzJywgJ3dlaWJvJywgJ3dlY2hhdCddDQogICAgICB9LA0KICAgICAgYWR2YW5jZWRQbGFuRm9ybTogew0KICAgICAgICBuYW1lOiAnJw0KICAgICAgfSwNCg0KICAgICAgLy8g6KGM5Lia5YiG57G75by556qX55u45YWz5pWw5o2uDQogICAgICBpbmR1c3RyeURpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgc2VsZWN0ZWRJbmR1c3RyeTogbnVsbCwNCiAgICAgIGluZHVzdHJ5VHJlZVByb3BzOiB7DQogICAgICAgIGxhYmVsOiAnbGFiZWwnLA0KICAgICAgICBjaGlsZHJlbjogJ2NoaWxkcmVuJw0KICAgICAgfSwNCg0KICAgICAgLy8g5Y+R6YCB6aKE6K2m5by556qX55u45YWz5pWw5o2uDQogICAgICBzZW5kQWxlcnREaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGFkZFRvQWxlcnRNYXRlcmlhbERpYWxvZ1Zpc2libGU6IGZhbHNlLCAvLyDmlrDlop7vvJrmjqfliLbliqDlhaXmiqXlkYrntKDmnZDlvLnnqpfnmoTmmL7npLoNCiAgICAgIGFsZXJ0Rm9ybTogew0KICAgICAgICB0aXRsZTogJycsDQogICAgICAgIHNlbGVjdGVkUmVjZWl2ZXJzOiBbJycsICcnLCAnJywgJycsICcnLCAnJ10NCiAgICAgIH0sDQogICAgICByZWNlaXZlcnM6IFsNCiAgICAgICAgew0KICAgICAgICAgIHR5cGU6ICfmgLvnm5EnLA0KICAgICAgICAgIHBlcnNvbnM6IFsn546L5oC755uRJywgJ+adjuaAu+ebkScsICflvKDmgLvnm5EnXQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdHlwZTogJ+e7j+eQhicsDQogICAgICAgICAgcGVyc29uczogWyfnjovnu4/nkIYnLCAn5p2O57uP55CGJywgJ+W8oOe7j+eQhiddDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0eXBlOiAn5Li7566hJywNCiAgICAgICAgICBwZXJzb25zOiBbJ+eOi+S4u+euoScsICfmnY7kuLvnrqEnLCAn5byg5Li7566hJ10NCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHR5cGU6ICflkZjlt6UnLA0KICAgICAgICAgIHBlcnNvbnM6IFsn546L5ZGY5belJywgJ+adjuWRmOW3pScsICflvKDlkZjlt6UnXQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdHlwZTogJ+WklumDqOS6uuWRmCcsDQogICAgICAgICAgcGVyc29uczogWyflpJbpg6jkurrlkZgxJywgJ+WklumDqOS6uuWRmDInLCAn5aSW6YOo5Lq65ZGYMyddDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0eXBlOiAn5YW25LuWJywNCiAgICAgICAgICBwZXJzb25zOiBbJ+WFtuS7luS6uuWRmDEnLCAn5YW25LuW5Lq65ZGYMicsICflhbbku5bkurrlkZgzJ10NCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIGluZHVzdHJ5VHJlZURhdGE6IFsNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAxLA0KICAgICAgICAgIGxhYmVsOiAn5Yi26YCgJywNCiAgICAgICAgICBjaGlsZHJlbjogW10NCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAyLA0KICAgICAgICAgIGxhYmVsOiAn5YWs5YWxJywNCiAgICAgICAgICBjaGlsZHJlbjogW10NCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAzLA0KICAgICAgICAgIGxhYmVsOiAn5pWZ6IKyJywNCiAgICAgICAgICBjaGlsZHJlbjogW10NCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiA0LA0KICAgICAgICAgIGxhYmVsOiAn5bel5Lia6K6+5aSHJywNCiAgICAgICAgICBjaGlsZHJlbjogW10NCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiA1LA0KICAgICAgICAgIGxhYmVsOiAn546v5L+d6K6+5aSHJywNCiAgICAgICAgICBjaGlsZHJlbjogW10NCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiA2LA0KICAgICAgICAgIGxhYmVsOiAn6YeR6J6NJywNCiAgICAgICAgICBjaGlsZHJlbjogW10NCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiA3LA0KICAgICAgICAgIGxhYmVsOiAn5ZWG5LiaJywNCiAgICAgICAgICBjaGlsZHJlbjogW10NCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiA4LA0KICAgICAgICAgIGxhYmVsOiAn5rCR55So5LiO5ZWG55SoJywNCiAgICAgICAgICBjaGlsZHJlbjogW10NCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiA5LA0KICAgICAgICAgIGxhYmVsOiAn5pS/5bqc6YOo6ZeoJywNCiAgICAgICAgICBjaGlsZHJlbjogW10NCiAgICAgICAgfQ0KICAgICAgXSwNCg0KICAgICAgLy8g5L+h5oGv5YiX6KGo5pWw5o2uDQogICAgICBpbmZvTGlzdDogWw0KICAgICAgICB7DQogICAgICAgICAgc2VsZWN0ZWQ6IGZhbHNlLA0KICAgICAgICAgIHRpdGxlOiAn5paw77yMPHNwYW4gY2xhc3M9ImhpZ2hsaWdodCI+6auY5Lu35YC86IGU5ZCNPC9zcGFuPuaJk+mAoDxzcGFuIGNsYXNzPSJoaWdobGlnaHQiPuaWueWkqjwvc3Bhbj4oRm90aWxlKeWOqOeUteaWsOWTgeeJjCcsDQogICAgICAgICAgY29udGVudDogJ+aNrjxzcGFuIGNsYXNzPSJoaWdobGlnaHQiPuaWueWkqjwvc3Bhbj4oRm90aWxlKeWumOaWuea2iOaBr++8jDxzcGFuIGNsYXNzPSJoaWdobGlnaHQiPuaWueWkqjwvc3Bhbj7kuI7orr7orqHluIjogZTlkI3miZPpgKDkuobkuIDns7vliJfpq5jnq6/ljqjnlLXkuqflk4HvvIzlvJXpoobljqjmiL/mlrDmva7mtYEuLi4nLA0KICAgICAgICAgIHNvdXJjZTogJ+aWsOa1qui0oue7jycsDQogICAgICAgICAgdGltZTogJzIwMjMtMDQtMjkgMjA6Mjc6NTEnLA0KICAgICAgICAgIHNlbnRpbWVudDogJ3Bvc2l0aXZlJywNCiAgICAgICAgICB2aWV3czogNjQzNywNCiAgICAgICAgICBjb21tZW50czogODksDQogICAgICAgICAgaW1hZ2VzOiBbXQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgc2VsZWN0ZWQ6IGZhbHNlLA0KICAgICAgICAgIHRpdGxlOiAn5oub6IGY5LiA5L2N44CQPHNwYW4gY2xhc3M9ImhpZ2hsaWdodCI+5pa55aSqPC9zcGFuPumbhuWboihGb3RpbGUpLTExMSA8c3BhbiBjbGFzcz0iaGlnaGxpZ2h0Ij7mlrnlpKo8L3NwYW4+6ZuG5ZuiKEZvdGlsZSktNDAwLjY4MDguNjU177yM5LiA6ZSu5ZG85Y+r77yM5pyN5Yqh5ZON5bqU5LiA5LiA44CR55qEPHNwYW4gY2xhc3M9ImhpZ2hsaWdodCI+5pa55aSqPC9zcGFuPumbhuWboihGb3RpbGUp57u05L+u5biI5YKFJywNCiAgICAgICAgICBjb250ZW50OiAn5oub6IGY5LiA5L2N44CQPHNwYW4gY2xhc3M9ImhpZ2hsaWdodCI+5pa55aSqPC9zcGFuPumbhuWboihGb3RpbGUp5a6Y5pa56K6k6K+B55qEPHNwYW4gY2xhc3M9ImhpZ2hsaWdodCI+5pa55aSqPC9zcGFuPumbhuWboihGb3RpbGUpLTExMTxzcGFuIGNsYXNzPSJoaWdobGlnaHQiPuaWueWkqjwvc3Bhbj7pm4blm6IoRm90aWxlKTQwMC42ODA4LjY1Ne+8jOS4gOmUruWRvOWPq++8jOacjeWKoeWTjeW6lOS4gOS4gOOAkeeahDxzcGFuIGNsYXNzPSJoaWdobGlnaHQiPuaWueWkqjwvc3Bhbj7pm4blm6IoRm90aWxlKee7tOS/ruW4iOWChS4uLicsDQogICAgICAgICAgc291cmNlOiAn5aS05p2h5paw6Ze7JywNCiAgICAgICAgICB0aW1lOiAnMjAyMy0wNC0yOSAyMDoyNDoyMicsDQogICAgICAgICAgc2VudGltZW50OiAnbmV1dHJhbCcsDQogICAgICAgICAgdmlld3M6IDUzMjEsDQogICAgICAgICAgY29tbWVudHM6IDQyLA0KICAgICAgICAgIGltYWdlczogWycvaW1hZ2VzL3NhbXBsZTEuanBnJywgJy9pbWFnZXMvc2FtcGxlMi5qcGcnXQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgc2VsZWN0ZWQ6IGZhbHNlLA0KICAgICAgICAgIHRpdGxlOiAnPHNwYW4gY2xhc3M9ImhpZ2hsaWdodCI+5pa55aSqPC9zcGFuPumbhuWboihGb3RpbGUp5a6Y5pa55pyN5Yqh5Lit5b+DLTxzcGFuIGNsYXNzPSJoaWdobGlnaHQiPuaWueWkqjwvc3Bhbj7pm4blm6IoRm90aWxlKS0xMTEgPHNwYW4gY2xhc3M9ImhpZ2hsaWdodCI+5pa55aSqPC9zcGFuPumbhuWboihGb3RpbGUpIDQwMC42ODA4LjY1Ne+8jOS4gOmUruWRvOWPq++8jOacjeWKoeWTjeW6lOS4gOS4gOeahDxzcGFuIGNsYXNzPSJoaWdobGlnaHQiPuaWueWkqjwvc3Bhbj7pm4blm6IoRm90aWxlKee7tOS/ruW4iOWChScsDQogICAgICAgICAgY29udGVudDogJzxzcGFuIGNsYXNzPSJoaWdobGlnaHQiPuaWueWkqjwvc3Bhbj7pm4blm6IoRm90aWxlKeWumOaWueacjeWKoeS4reW/gy08c3BhbiBjbGFzcz0iaGlnaGxpZ2h0Ij7mlrnlpKo8L3NwYW4+6ZuG5ZuiKEZvdGlsZSktMTExPHNwYW4gY2xhc3M9ImhpZ2hsaWdodCI+5pa55aSqPC9zcGFuPumbhuWboihGb3RpbGUpNDAwLjY4MDguNjU177yMPHNwYW4gY2xhc3M9ImhpZ2hsaWdodCI+5pa55aSqPC9zcGFuPumbhuWboihGb3RpbGUp57u05L+u6YOo44CC5Y+v6aKE57qm5LiK6Zeo77yM5LiA56uZ5byP6Kej5YazPHNwYW4gY2xhc3M9ImhpZ2hsaWdodCI+5pa55aSqPC9zcGFuPuWOqOeUteaVhemanOmXrumimC4uLicsDQogICAgICAgICAgc291cmNlOiAn5LuK5pel5aS05p2hJywNCiAgICAgICAgICB0aW1lOiAnMjAyMy0wNC0yOSAyMDoxNDowMycsDQogICAgICAgICAgc2VudGltZW50OiAnbmV1dHJhbCcsDQogICAgICAgICAgdmlld3M6IDM2NDIsDQogICAgICAgICAgY29tbWVudHM6IDMzLA0KICAgICAgICAgIGltYWdlczogWycvaW1hZ2VzL3NhbXBsZTMuanBnJywgJy9pbWFnZXMvc2FtcGxlNC5qcGcnXQ0KICAgICAgICB9DQogICAgICBdDQogICAgfTsNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICAvLyDpmpDol4/pobbpg6jlr7zoiKrmoI8NCiAgICB0aGlzLm9yaWdpbmFsVG9wTmF2ID0gdGhpcy4kc3RvcmUuc3RhdGUuc2V0dGluZ3MudG9wTmF2DQogICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ3NldHRpbmdzL2NoYW5nZVNldHRpbmcnLCB7DQogICAgICBrZXk6ICd0b3BOYXYnLA0KICAgICAgdmFsdWU6IGZhbHNlDQogICAgfSkNCiAgfSwNCiAgYmVmb3JlRGVzdHJveSgpIHsNCiAgICAvLyDmgaLlpI3pobbpg6jlr7zoiKrmoI/orr7nva4NCiAgICBpZiAodGhpcy5vcmlnaW5hbFRvcE5hdiAhPT0gdW5kZWZpbmVkKSB7DQogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnc2V0dGluZ3MvY2hhbmdlU2V0dGluZycsIHsNCiAgICAgICAga2V5OiAndG9wTmF2JywNCiAgICAgICAgdmFsdWU6IHRoaXMub3JpZ2luYWxUb3BOYXYNCiAgICAgIH0pDQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQoNCiAgICAvLyDoj5zljZXngrnlh7vkuovku7YNCiAgICBoYW5kbGVNZW51Q2xpY2sobWVudU5hbWUpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfpgInmi6noj5zljZU6JywgbWVudU5hbWUpOw0KICAgIH0sDQoNCiAgICAvLyDojrflj5bmg4XmhJ/lm77moIcNCiAgICBnZXRTZW50aW1lbnRJY29uKHNlbnRpbWVudCkgew0KICAgICAgY29uc3QgaWNvbnMgPSB7DQogICAgICAgIHBvc2l0aXZlOiAnZWwtaWNvbi1zdW5ueScsDQogICAgICAgIG5ldXRyYWw6ICdlbC1pY29uLXBhcnRseS1jbG91ZHknLA0KICAgICAgICBuZWdhdGl2ZTogJ2VsLWljb24tY2xvdWR5Jw0KICAgICAgfTsNCiAgICAgIHJldHVybiBpY29uc1tzZW50aW1lbnRdIHx8ICdlbC1pY29uLXF1ZXN0aW9uJzsNCiAgICB9LA0KDQogICAgLy8g6I635Y+W5oOF5oSf5paH5pysDQogICAgZ2V0U2VudGltZW50VGV4dChzZW50aW1lbnQpIHsNCiAgICAgIGNvbnN0IHRleHRzID0gew0KICAgICAgICBwb3NpdGl2ZTogJ+ato+mdoicsDQogICAgICAgIG5ldXRyYWw6ICfkuK3mgKcnLA0KICAgICAgICBuZWdhdGl2ZTogJ+i0n+mdoicNCiAgICAgIH07DQogICAgICByZXR1cm4gdGV4dHNbc2VudGltZW50XSB8fCAn5pyq55+lJzsNCiAgICB9LA0KDQogICAgLy8g5LiL5ouJ6I+c5Y2V5ZG95Luk5aSE55CGDQogICAgaGFuZGxlQ29tbWFuZChjb21tYW5kKSB7DQogICAgICBjb25zb2xlLmxvZygn5omn6KGM5ZG95LukOicsIGNvbW1hbmQpOw0KICAgIH0sDQoNCiAgICAvLyDlpITnkIbmkJzntKLlhbPplK7or40NCiAgICBoYW5kbGVTZWFyY2hLZXl3b3JkKCkgew0KICAgICAgaWYgKHRoaXMuc2VhcmNoS2V5d29yZC50cmltKCkpIHsNCiAgICAgICAgLy8g6Lez6L2s5Yiw5pCc57Si57uT5p6c6aG16Z2i77yM5bm25Lyg6YCS5pCc57Si5YWz6ZSu6K+NDQogICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsNCiAgICAgICAgICBwYXRoOiAnL3NlYXJjaC1yZXN1bHRzJywNCiAgICAgICAgICBxdWVyeTogew0KICAgICAgICAgICAgcTogdGhpcy5zZWFyY2hLZXl3b3JkLnRyaW0oKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+i+k+WFpeaQnOe0ouWFs+mUruivjScpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDliIbpobXnm7jlhbPmlrnms5UNCiAgICBoYW5kbGVTaXplQ2hhbmdlKHNpemUpIHsNCiAgICAgIHRoaXMucGFnZVNpemUgPSBzaXplOw0KICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IDE7IC8vIOWIh+aNouavj+mhteaYvuekuuaVsOmHj+aXtu+8jOmHjee9ruS4uuesrOS4gOmhtQ0KICAgICAgY29uc29sZS5sb2coJ+avj+mhteaYvuekuuaVsOmHjzonLCBzaXplKTsNCiAgICB9LA0KDQogICAgaGFuZGxlQ3VycmVudENoYW5nZShwYWdlKSB7DQogICAgICB0aGlzLmN1cnJlbnRQYWdlID0gcGFnZTsNCiAgICAgIGNvbnNvbGUubG9nKCflvZPliY3pobXnoIE6JywgcGFnZSk7DQogICAgfSwNCg0KICAgIC8vIOaWsOW7uuaWueahiOebuOWFs+aWueazlQ0KICAgIHNob3dDcmVhdGVQbGFuRGlhbG9nKCkgew0KICAgICAgdGhpcy5jcmVhdGVQbGFuRGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgfSwNCg0KICAgIC8vIOS4u+mimOagh+etvuebuOWFs+aWueazlQ0KICAgIGhhbmRsZVJlbW92ZVRoZW1lKHRhZykgew0KICAgICAgdGhpcy5wbGFuRm9ybS50aGVtZXMuc3BsaWNlKHRoaXMucGxhbkZvcm0udGhlbWVzLmluZGV4T2YodGFnKSwgMSk7DQogICAgfSwNCg0KICAgIHNob3dUaGVtZUlucHV0KCkgew0KICAgICAgdGhpcy50aGVtZUlucHV0VmlzaWJsZSA9IHRydWU7DQogICAgICB0aGlzLiRuZXh0VGljayhfID0+IHsNCiAgICAgICAgdGhpcy4kcmVmcy50aGVtZUlucHV0LiRyZWZzLmlucHV0LmZvY3VzKCk7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgaGFuZGxlQWRkVGhlbWUoKSB7DQogICAgICBsZXQgaW5wdXRWYWx1ZSA9IHRoaXMudGhlbWVJbnB1dFZhbHVlOw0KICAgICAgaWYgKGlucHV0VmFsdWUpIHsNCiAgICAgICAgaWYgKCF0aGlzLnBsYW5Gb3JtLnRoZW1lcy5pbmNsdWRlcyhpbnB1dFZhbHVlKSkgew0KICAgICAgICAgIHRoaXMucGxhbkZvcm0udGhlbWVzLnB1c2goaW5wdXRWYWx1ZSk7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHRoaXMudGhlbWVJbnB1dFZpc2libGUgPSBmYWxzZTsNCiAgICAgIHRoaXMudGhlbWVJbnB1dFZhbHVlID0gJyc7DQogICAgfSwNCg0KICAgIC8vIOebkea1i+WvueixoeS4i+aLieiPnOWNleWRveS7pOWkhOeQhg0KICAgIGhhbmRsZU1vbml0b3JPYmplY3RDb21tYW5kKGNvbW1hbmQpIHsNCiAgICAgIHRoaXMucGxhbkZvcm0ubW9uaXRvck9iamVjdCA9IGNvbW1hbmQ7DQogICAgfSwNCg0KICAgIC8vIOaYvuekuuWcsOWfn+mAieaLqeWcsOWbvg0KICAgIHNob3dMb2NhdGlvbk1hcCgpIHsNCiAgICAgIC8vIOi/memHjOWPr+S7peWunueOsOWcsOWbvumAieaLqeWKn+iDvQ0KICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCfmmL7npLrlnLDln5/pgInmi6nlnLDlm74nKTsNCiAgICB9LA0KDQogICAgLy8g5pi+56S66KGM5Lia5YiG57G76YCJ5oupDQogICAgc2hvd0luZHVzdHJ5U2VsZWN0KCkgew0KICAgICAgdGhpcy5pbmR1c3RyeURpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgIH0sDQoNCiAgICAvLyDlpITnkIbooYzkuJrliIbnsbvmoJHoioLngrnngrnlh7sNCiAgICBoYW5kbGVJbmR1c3RyeU5vZGVDbGljayhkYXRhKSB7DQogICAgICB0aGlzLnNlbGVjdGVkSW5kdXN0cnkgPSBkYXRhOw0KICAgIH0sDQoNCiAgICAvLyDnoa7orqTooYzkuJrliIbnsbvpgInmi6kNCiAgICBjb25maXJtSW5kdXN0cnlTZWxlY3QoKSB7DQogICAgICBpZiAodGhpcy5zZWxlY3RlZEluZHVzdHJ5KSB7DQogICAgICAgIHRoaXMucGxhbkZvcm0uaW5kdXN0cnkgPSB0aGlzLnNlbGVjdGVkSW5kdXN0cnkubGFiZWw7DQogICAgICB9DQogICAgICB0aGlzLmluZHVzdHJ5RGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KICAgIH0sDQoNCiAgICAvLyDkv53lrZjmlrnmoYgNCiAgICBzYXZlUGxhbigpIHsNCiAgICAgIC8vIOagueaNruW9k+WJjea/gOa0u+eahOagh+etvumhtemAieaLqeS4jeWQjOeahOihqOWNleaVsOaNrg0KICAgICAgY29uc3QgZm9ybURhdGEgPSB0aGlzLnBsYW5BY3RpdmVUYWIgPT09ICdzdGFuZGFyZCcgPyB0aGlzLnBsYW5Gb3JtIDogdGhpcy5hZHZhbmNlZFBsYW5Gb3JtOw0KDQogICAgICBjb25zb2xlLmxvZygn5L+d5a2Y5pa55qGIOicsIGZvcm1EYXRhKTsNCiAgICAgIC8vIOi/memHjOWPr+S7pea3u+WKoOihqOWNlemqjOivgeWSjOaPkOS6pOWIsOWQjuerr+eahOmAu+i+kQ0KDQogICAgICAvLyDlhbPpl63lr7nor53moYYNCiAgICAgIHRoaXMuY3JlYXRlUGxhbkRpYWxvZ1Zpc2libGUgPSBmYWxzZTsNCg0KICAgICAgLy8g6YeN572u6KGo5Y2VDQogICAgICBpZiAodGhpcy5wbGFuQWN0aXZlVGFiID09PSAnc3RhbmRhcmQnKSB7DQogICAgICAgIHRoaXMucGxhbkZvcm0gPSB7DQogICAgICAgICAgbmFtZTogJycsDQogICAgICAgICAgc2NvcGU6ICdhbGwnLA0KICAgICAgICAgIG1vbml0b3JPYmplY3Q6ICcnLA0KICAgICAgICAgIGxvY2F0aW9uOiAnJywNCiAgICAgICAgICB0aGVtZXM6IFtdLA0KICAgICAgICAgIGluZHVzdHJ5OiAnJywNCiAgICAgICAgICB0aW1lUmFuZ2U6ICcnLA0KICAgICAgICAgIGNoYW5uZWxzOiBbJ25ld3MnLCAnd2VpYm8nLCAnd2VjaGF0J10NCiAgICAgICAgfTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuYWR2YW5jZWRQbGFuRm9ybSA9IHsNCiAgICAgICAgICBuYW1lOiAnJw0KICAgICAgICB9Ow0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDmmL7npLrlj5HpgIHpooTorablvLnnqpcNCiAgICBzaG93U2VuZEFsZXJ0RGlhbG9nKCkgew0KICAgICAgdGhpcy5zZW5kQWxlcnREaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICB9LA0KDQogICAgLy8g5Y+W5raI5Y+R6YCB6aKE6K2mDQogICAgY2FuY2VsU2VuZEFsZXJ0KCkgew0KICAgICAgdGhpcy5zZW5kQWxlcnREaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgICAvLyDph43nva7ooajljZUNCiAgICAgIHRoaXMuYWxlcnRGb3JtID0gew0KICAgICAgICB0aXRsZTogJycsDQogICAgICAgIHNlbGVjdGVkUmVjZWl2ZXJzOiBbJycsICcnLCAnJywgJycsICcnLCAnJ10NCiAgICAgIH07DQogICAgfSwNCiAgICBoYW5kbGVTZW50aW1lbnRDbGljayhpdGVtKSB7DQogICAgICB0aGlzLmVkaXRpbmdJdGVtID0gaXRlbTsNCiAgICAgIHRoaXMuc2VsZWN0ZWRTZW50aW1lbnQgPSBpdGVtLnNlbnRpbWVudDsNCiAgICAgIHRoaXMucG9zaXRpdmVTZW50aW1lbnREaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICB9LA0KICAgIGhhbmRsZVBvc2l0aXZlRGlhbG9nQ29uZmlybSgpIHsNCiAgICAgIGlmICh0aGlzLmVkaXRpbmdJdGVtKSB7DQogICAgICAgIHRoaXMuZWRpdGluZ0l0ZW0uc2VudGltZW50ID0gdGhpcy5zZWxlY3RlZFNlbnRpbWVudDsNCiAgICAgICAgLy8g5Zyo5a6e6ZmF5bqU55So5Lit77yM6L+Z6YeM5Y+v6IO96ZyA6KaB6LCD55SoQVBJ5bCG5pu05pS55L+d5a2Y5Yiw5ZCO56uvDQogICAgICAgIC8vIOS+i+WmgjogdGhpcy51cGRhdGVTZW50aW1lbnRBcGkodGhpcy5lZGl0aW5nSXRlbS5pZCwgdGhpcy5zZWxlY3RlZFNlbnRpbWVudCk7DQogICAgICB9DQogICAgICB0aGlzLnBvc2l0aXZlU2VudGltZW50RGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KICAgIH0sDQoNCiAgICAvLyDnoa7orqTlj5HpgIHpooToraYNCiAgICBjb25maXJtU2VuZEFsZXJ0KCkgew0KICAgICAgLy8g5Zyo6L+Z6YeM5aSE55CG5Y+R6YCB6aKE6K2m55qE6YC76L6RDQogICAgICBjb25zb2xlLmxvZygn5Y+R6YCB6aKE6K2mOicsIHRoaXMuYWxlcnRGb3JtKTsNCiAgICAgIHRoaXMuc2VuZEFsZXJ0RGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgLy8g5riF56m66KGo5Y2VDQogICAgICB0aGlzLmFsZXJ0Rm9ybSA9IHsNCiAgICAgICAgdGl0bGU6ICcnLA0KICAgICAgICBzZWxlY3RlZFJlY2VpdmVyczogWycnLCAnJywgJycsICcnLCAnJywgJyddDQogICAgICB9Ow0KICAgIH0sDQoNCiAgICAvLyDliqDlhaXoh7PmiqXlkYrntKDmnZDlr7nor53moYbnm7jlhbPmlrnms5UNCiAgICBzaG93QWRkVG9BbGVydE1hdGVyaWFsRGlhbG9nKCkgew0KICAgICAgdGhpcy5hZGRUb0FsZXJ0TWF0ZXJpYWxEaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICB9LA0KICAgIGNhbmNlbEFkZFRvQWxlcnRNYXRlcmlhbCgpIHsNCiAgICAgIHRoaXMuYWRkVG9BbGVydE1hdGVyaWFsRGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgdGhpcy5zZWxlY3RlZE1hdGVyaWFsTGlicmFyeSA9ICcnOyAvLyDmuIXnqbrpgInkuK3lgLwNCiAgICB9LA0KICAgIGNvbmZpcm1BZGRUb0FsZXJ0TWF0ZXJpYWwoKSB7DQogICAgICAvLyDov5nph4zmt7vliqDnoa7orqTpgLvovpHvvIzkvovlpoLmj5DkuqTpgInkuK3nmoTntKDmnZDlupMNCiAgICAgIGNvbnNvbGUubG9nKCdTZWxlY3RlZCBNYXRlcmlhbCBMaWJyYXJ5OicsIHRoaXMuc2VsZWN0ZWRNYXRlcmlhbExpYnJhcnkpOw0KICAgICAgdGhpcy5hZGRUb0FsZXJ0TWF0ZXJpYWxEaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgICB0aGlzLnNlbGVjdGVkTWF0ZXJpYWxMaWJyYXJ5ID0gJyc7IC8vIOa4heepuumAieS4reWAvA0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4dA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/info-summary", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 操作按钮区域 -->\r\n    <div class=\"action-buttons\">\r\n      <el-button type=\"primary\" icon=\"el-icon-message\" @click=\"showSendAlertDialog\">发送预警</el-button>\r\n      <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"showCreatePlanDialog\">新建方案</el-button>\r\n      <el-button type=\"primary\" icon=\"el-icon-folder-add\" @click=\"showAddToAlertMaterialDialog\">加入至报告素材</el-button>\r\n      <el-button type=\"primary\" icon=\"el-icon-share\" @click=\"showInfoGraphDialog\">信息图谱</el-button>\r\n      <el-button type=\"primary\" icon=\"el-icon-document-checked\" @click=\"showOriginalProofreadingDialog\">原稿校对</el-button>\r\n    </div>\r\n\r\n    <div class=\"info-summary-container\">\r\n      <!-- 左侧导航栏 -->\r\n      <div class=\"left-sidebar\">\r\n        <div class=\"sidebar-header\">\r\n          <span class=\"sidebar-title\">方太</span>\r\n          <i class=\"el-icon-arrow-right\"></i>\r\n        </div>\r\n        <div class=\"sidebar-menu\">\r\n          <el-menu\r\n            :default-active=\"activeMenu\"\r\n            class=\"el-menu-vertical\"\r\n          >\r\n            <el-menu-item index=\"1\" @click=\"handleMenuClick('总览')\">\r\n              <i class=\"el-icon-s-home\"></i>\r\n              <span slot=\"title\">总览</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"2\" @click=\"handleMenuClick('品牌')\">\r\n              <i class=\"el-icon-s-goods\"></i>\r\n              <span slot=\"title\">品牌(1)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"3\" @click=\"handleMenuClick('人物')\">\r\n              <i class=\"el-icon-user\"></i>\r\n              <span slot=\"title\">人物(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"4\" @click=\"handleMenuClick('机构')\">\r\n              <i class=\"el-icon-office-building\"></i>\r\n              <span slot=\"title\">机构(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"5\" @click=\"handleMenuClick('产品')\">\r\n              <i class=\"el-icon-shopping-bag-1\"></i>\r\n              <span slot=\"title\">产品(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"6\" @click=\"handleMenuClick('事件')\">\r\n              <i class=\"el-icon-bell\"></i>\r\n              <span slot=\"title\">事件(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"7\" @click=\"handleMenuClick('话题')\">\r\n              <i class=\"el-icon-chat-dot-square\"></i>\r\n              <span slot=\"title\">话题(0)</span>\r\n            </el-menu-item>\r\n          </el-menu>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧内容区域 -->\r\n      <div class=\"right-content\">\r\n        <!-- 标题和操作区域 -->\r\n        <div class=\"content-header\">\r\n          <div class=\"entity-title\">\r\n            <span class=\"entity-name\">方太</span>\r\n            <i class=\"el-icon-arrow-right\"></i>\r\n          </div>\r\n          <div class=\"view-actions\">\r\n            <el-button-group>\r\n              <el-button size=\"small\" type=\"primary\" icon=\"el-icon-s-grid\"></el-button>\r\n              <el-button size=\"small\" icon=\"el-icon-menu\"></el-button>\r\n              <el-button size=\"small\" icon=\"el-icon-s-unfold\"></el-button>\r\n            </el-button-group>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 标签页 -->\r\n        <div class=\"tabs-container\">\r\n          <div class=\"filter-tabs\">\r\n            <el-radio-group v-model=\"activeTab\" size=\"small\">\r\n              <el-radio-button label=\"today\">今天</el-radio-button>\r\n              <el-radio-button label=\"yesterday\">昨天</el-radio-button>\r\n              <el-radio-button label=\"before_yesterday\">前天</el-radio-button>\r\n              <el-radio-button label=\"earlier\">更早</el-radio-button>\r\n              <el-radio-button label=\"custom\">自定义</el-radio-button>\r\n            </el-radio-group>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 信息类型筛选 -->\r\n        <div class=\"filter-section\">\r\n          <div class=\"filter-row\">\r\n            <span class=\"filter-label\">平台分类:</span>\r\n            <el-checkbox-group v-model=\"platformTypes\" size=\"small\">\r\n              <el-checkbox label=\"news\">新闻 (46/217)</el-checkbox>\r\n              <el-checkbox label=\"weibo\">微博 (5/34)</el-checkbox>\r\n              <el-checkbox label=\"wechat\">微信 (14/54)</el-checkbox>\r\n              <el-checkbox label=\"video\">视频 (2/78)</el-checkbox>\r\n              <el-checkbox label=\"app\">APP (1/29)</el-checkbox>\r\n              <el-checkbox label=\"forum\">论坛 (0/9)</el-checkbox>\r\n              <el-checkbox label=\"ecommerce\">电商 (44/446)</el-checkbox>\r\n              <el-checkbox label=\"qa\">问答 (6/21)</el-checkbox>\r\n              <el-checkbox label=\"other\">其他 (1/2)</el-checkbox>\r\n            </el-checkbox-group>\r\n          </div>\r\n\r\n          <div class=\"filter-row\">\r\n            <span class=\"filter-label\">情感倾向:</span>\r\n            <el-checkbox-group v-model=\"sentimentTypes\" size=\"small\">\r\n              <el-checkbox label=\"positive\">正面 (46/217)</el-checkbox>\r\n              <el-checkbox label=\"neutral\">中性 (7/8)</el-checkbox>\r\n              <el-checkbox label=\"negative\">负面 (3/57)</el-checkbox>\r\n              <el-checkbox label=\"other\">其他 (1/3)</el-checkbox>\r\n            </el-checkbox-group>\r\n          </div>\r\n\r\n          <div class=\"filter-row\">\r\n            <span class=\"filter-label\">信息属性:</span>\r\n            <el-checkbox-group v-model=\"infoAttributes\" size=\"small\">\r\n              <el-checkbox label=\"official\">官方发布</el-checkbox>\r\n              <el-checkbox label=\"media\">媒体报道</el-checkbox>\r\n              <el-checkbox label=\"user\">用户评价</el-checkbox>\r\n              <el-checkbox label=\"competitor\">竞品信息</el-checkbox>\r\n              <el-checkbox label=\"industry\">行业动态</el-checkbox>\r\n              <el-checkbox label=\"policy\">政策法规</el-checkbox>\r\n            </el-checkbox-group>\r\n          </div>\r\n\r\n          <div class=\"filter-actions\">\r\n            <el-button size=\"small\" type=\"primary\">筛选</el-button>\r\n            <el-button size=\"small\">重置</el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 操作栏 -->\r\n        <div class=\"action-bar\">\r\n          <div class=\"left-actions\">\r\n            <el-button size=\"small\" type=\"primary\">全选</el-button>\r\n            <el-button size=\"small\">导出</el-button>\r\n          </div>\r\n          <div class=\"right-actions\">\r\n            <el-input\r\n              placeholder=\"搜索关键词\"\r\n              prefix-icon=\"el-icon-search\"\r\n              v-model=\"searchKeyword\"\r\n              size=\"small\"\r\n              clearable\r\n              class=\"search-input\"\r\n              @keyup.enter=\"handleSearchKeyword\"\r\n            >\r\n              <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"handleSearchKeyword\"></el-button>\r\n            </el-input>\r\n            <el-dropdown size=\"small\" split-button type=\"primary\" @command=\"handleCommand\">\r\n              排序\r\n              <el-dropdown-menu slot=\"dropdown\">\r\n                <el-dropdown-item command=\"time_desc\">时间降序</el-dropdown-item>\r\n                <el-dropdown-item command=\"time_asc\">时间升序</el-dropdown-item>\r\n                <el-dropdown-item command=\"relevance\">相关性</el-dropdown-item>\r\n              </el-dropdown-menu>\r\n            </el-dropdown>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 信息列表 -->\r\n        <div class=\"info-list\">\r\n          <div v-for=\"(item, index) in paginatedList\" :key=\"index\" class=\"info-item\">\r\n            <el-checkbox v-model=\"item.selected\" class=\"item-checkbox\"></el-checkbox>\r\n            <div class=\"info-content\">\r\n              <div class=\"info-header\">\r\n                <div class=\"info-title\" v-html=\"item.title\"></div>\r\n                <div class=\"info-actions\">\r\n                  <el-button type=\"text\" icon=\"el-icon-star-off\"></el-button>\r\n                  <el-button type=\"text\" icon=\"el-icon-share\"></el-button>\r\n                  <el-button type=\"text\" icon=\"el-icon-more\"></el-button>\r\n                </div>\r\n              </div>\r\n              <div class=\"info-summary\" v-html=\"item.content\"></div>\r\n              <div class=\"info-footer\">\r\n                <span class=\"info-source\">{{ item.source }}</span>\r\n                <span class=\"info-time\">{{ item.time }}</span>\r\n                <span class=\"info-sentiment\" :class=\"'sentiment-' + item.sentiment\">\r\n                  <el-button\r\n                    :type=\"item.sentiment === 'positive' ? 'success' : item.sentiment === 'negative' ? 'danger' : 'info'\"\r\n                    size=\"mini\"\r\n                    @click=\"handleSentimentClick(item)\"\r\n                  >\r\n                    <i :class=\"getSentimentIcon(item.sentiment)\"></i>\r\n                    {{ getSentimentText(item.sentiment) }}\r\n                  </el-button>\r\n                </span>\r\n                <span class=\"info-views\">\r\n                  <i class=\"el-icon-view\"></i> {{ item.views }}\r\n                </span>\r\n                <span class=\"info-comments\">\r\n                  <i class=\"el-icon-chat-line-square\"></i> {{ item.comments }}\r\n                </span>\r\n                <span class=\"info-index\">{{ (currentPage - 1) * pageSize + index + 1 }}</span>\r\n              </div>\r\n              <div class=\"info-images\" v-if=\"item.images && item.images.length > 0\">\r\n                <img v-for=\"(img, imgIndex) in item.images\" :key=\"imgIndex\" :src=\"img\" class=\"info-image\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-container\">\r\n          <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :current-page=\"currentPage\"\r\n            :page-sizes=\"[10, 20, 30, 50]\"\r\n            :page-size=\"pageSize\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"totalItems\"\r\n            background\r\n          ></el-pagination>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 新建方案对话框 -->\r\n    <el-dialog\r\n      title=\"新建方案\"\r\n      :visible.sync=\"createPlanDialogVisible\"\r\n      width=\"50%\"\r\n      :close-on-click-modal=\"false\"\r\n      :close-on-press-escape=\"false\"\r\n      append-to-body\r\n      custom-class=\"create-plan-dialog\"\r\n    >\r\n      <el-tabs v-model=\"planActiveTab\">\r\n        <el-tab-pane label=\"监测方式\" name=\"standard\">\r\n          <el-form :model=\"planForm\" label-width=\"70px\" size=\"small\">\r\n            <el-form-item label=\"方案名称\">\r\n              <el-input v-model=\"planForm.name\" placeholder=\"请输入方案名称\"></el-input>\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"作用范围\">\r\n              <el-select v-model=\"planForm.scope\" placeholder=\"请选择\" style=\"width: 100%\">\r\n                <el-option label=\"全部\" value=\"all\"></el-option>\r\n                <el-option label=\"选项1\" value=\"option1\"></el-option>\r\n                <el-option label=\"选项2\" value=\"option2\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"监测对象\">\r\n              <div class=\"monitor-object-select\">\r\n                <el-input v-model=\"planForm.monitorObject\" placeholder=\"请输入监测对象\"></el-input>\r\n                <el-dropdown trigger=\"click\" @command=\"handleMonitorObjectCommand\">\r\n                  <span class=\"el-dropdown-link\">\r\n                    <i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n                  </span>\r\n                  <el-dropdown-menu slot=\"dropdown\">\r\n                    <el-dropdown-item command=\"option1\">选项1</el-dropdown-item>\r\n                    <el-dropdown-item command=\"option2\">选项2</el-dropdown-item>\r\n                    <el-dropdown-item command=\"option3\">选项3</el-dropdown-item>\r\n                  </el-dropdown-menu>\r\n                </el-dropdown>\r\n              </div>\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"地域\">\r\n              <div class=\"location-select\">\r\n                <el-input v-model=\"planForm.location\" placeholder=\"请选择地域\" readonly></el-input>\r\n                <el-button type=\"text\" class=\"location-btn\" @click=\"showLocationMap\">\r\n                  <i class=\"el-icon-location\"></i>\r\n                </el-button>\r\n              </div>\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"主题\">\r\n              <div class=\"theme-row\">\r\n                <el-tag\r\n                  v-for=\"(tag, index) in planForm.themes\"\r\n                  :key=\"index\"\r\n                  closable\r\n                  @close=\"handleRemoveTheme(tag)\"\r\n                >\r\n                  {{ tag }}\r\n                </el-tag>\r\n                <el-input\r\n                  class=\"theme-input\"\r\n                  v-if=\"themeInputVisible\"\r\n                  v-model=\"themeInputValue\"\r\n                  ref=\"themeInput\"\r\n                  size=\"small\"\r\n                  @keyup.enter.native=\"handleAddTheme\"\r\n                  @blur=\"handleAddTheme\"\r\n                >\r\n                </el-input>\r\n                <el-button v-else class=\"theme-button\" size=\"small\" @click=\"showThemeInput\">+ 添加主题</el-button>\r\n              </div>\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"行业分类\">\r\n              <div class=\"industry-row\">\r\n                <el-tag\r\n                  v-if=\"planForm.industry\"\r\n                  closable\r\n                  @close=\"planForm.industry = ''\"\r\n                >\r\n                  {{ planForm.industry }}\r\n                </el-tag>\r\n                <el-button v-if=\"!planForm.industry\" class=\"industry-button\" size=\"small\" @click=\"showIndustrySelect\">+ 添加行业分类</el-button>\r\n              </div>\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"时间段\">\r\n              <el-date-picker\r\n                v-model=\"planForm.timeRange\"\r\n                type=\"daterange\"\r\n                range-separator=\"至\"\r\n                start-placeholder=\"开始日期\"\r\n                end-placeholder=\"结束日期\"\r\n                style=\"width: 100%\"\r\n              >\r\n              </el-date-picker>\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"渠道\">\r\n              <el-checkbox-group v-model=\"planForm.channels\">\r\n                <div class=\"channels-row\">\r\n                  <el-checkbox label=\"news\">新闻</el-checkbox>\r\n                  <el-checkbox label=\"weibo\">微博</el-checkbox>\r\n                  <el-checkbox label=\"wechat\">微信</el-checkbox>\r\n                </div>\r\n                <div class=\"channels-row\">\r\n                  <el-checkbox label=\"video\">视频</el-checkbox>\r\n                  <el-checkbox label=\"app\">APP</el-checkbox>\r\n                  <el-checkbox label=\"forum\">论坛</el-checkbox>\r\n                </div>\r\n              </el-checkbox-group>\r\n            </el-form-item>\r\n          </el-form>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"高级方式\" name=\"advanced\">\r\n          <el-form :model=\"advancedPlanForm\" label-width=\"70px\" size=\"small\">\r\n            <!-- 高级模式的表单内容 -->\r\n            <el-form-item label=\"方案名称\">\r\n              <el-input v-model=\"advancedPlanForm.name\" placeholder=\"请输入方案名称\"></el-input>\r\n            </el-form-item>\r\n\r\n            <!-- 其他高级选项 -->\r\n          </el-form>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"createPlanDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"savePlan\">保存</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 行业分类弹窗 -->\r\n    <el-dialog\r\n      title=\"行业分类\"\r\n      :visible.sync=\"industryDialogVisible\"\r\n      width=\"40%\"\r\n      :close-on-click-modal=\"true\"\r\n      :close-on-press-escape=\"true\"\r\n      append-to-body\r\n      custom-class=\"industry-dialog\"\r\n    >\r\n      <div class=\"industry-dialog-content\">\r\n        <div class=\"industry-tree-container\">\r\n          <el-tree\r\n            :data=\"industryTreeData\"\r\n            :props=\"industryTreeProps\"\r\n            node-key=\"id\"\r\n            default-expand-all\r\n            highlight-current\r\n            @node-click=\"handleIndustryNodeClick\"\r\n          />\r\n        </div>\r\n        <div class=\"industry-selected-container\">\r\n          <div class=\"industry-selected-title\">\r\n            {{ selectedIndustry ? selectedIndustry.label : '请选择行业分类' }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"industryDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmIndustrySelect\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 发送预警弹窗 -->\r\n    <el-dialog\r\n      title=\"发送预警\"\r\n      :visible.sync=\"sendAlertDialogVisible\"\r\n      width=\"40%\"\r\n      :close-on-click-modal=\"true\"\r\n      :close-on-press-escape=\"true\"\r\n      append-to-body\r\n      custom-class=\"send-alert-dialog\"\r\n    >\r\n      <div class=\"send-alert-content\">\r\n        <el-form :model=\"alertForm\" label-width=\"80px\" size=\"small\">\r\n          <el-form-item label=\"预警标题\">\r\n            <el-input v-model=\"alertForm.title\" placeholder=\"请输入预警标题\"></el-input>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"接收人\">\r\n            <div class=\"receiver-list\">\r\n              <div class=\"receiver-item\" v-for=\"(receiver, index) in receivers\" :key=\"index\">\r\n                <div class=\"receiver-type\">{{ receiver.type }}</div>\r\n                <el-select\r\n                  v-model=\"alertForm.selectedReceivers[index]\"\r\n                  :placeholder=\"'请选择' + receiver.type\"\r\n                  class=\"receiver-select\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"person in receiver.persons\"\r\n                    :key=\"person\"\r\n                    :label=\"person\"\r\n                    :value=\"person\"\r\n                  ></el-option>\r\n                </el-select>\r\n              </div>\r\n            </div>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelSendAlert\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmSendAlert\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 正面情感信息弹窗 -->\r\n    <el-dialog\r\n      title=\"情感属性纠错\"\r\n      :visible.sync=\"positiveSentimentDialogVisible\"\r\n      width=\"50%\"\r\n      append-to-body\r\n      custom-class=\"positive-sentiment-dialog\"\r\n    >\r\n      <el-radio-group v-model=\"selectedSentiment\" size=\"small\">\r\n        <el-radio-button label=\"positive\">正面</el-radio-button>\r\n        <el-radio-button label=\"neutral\">中性</el-radio-button>\r\n        <el-radio-button label=\"negative\">负面</el-radio-button>\r\n      </el-radio-group>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"positiveSentimentDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"handlePositiveDialogConfirm\">确定</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 加入至报告素材对话框 -->\r\n    <el-dialog\r\n      title=\"加入至报告素材\"\r\n      :visible.sync=\"addToAlertMaterialDialogVisible\"\r\n      width=\"30%\"\r\n      :close-on-click-modal=\"false\"\r\n      :close-on-press-escape=\"false\"\r\n      append-to-body\r\n      custom-class=\"add-to-alert-material-dialog\"\r\n    >\r\n      <div class=\"add-to-alert-material-content\">\r\n        <el-form label-width=\"80px\" size=\"small\">\r\n          <el-form-item label=\"选择素材库\">\r\n            <el-select v-model=\"selectedMaterialLibrary\" placeholder=\"请选择素材库\" style=\"width: 100%\">\r\n              <!-- 这里需要根据实际数据填充选项 -->\r\n              <el-option label=\"素材库1\" value=\"library1\"></el-option>\r\n              <el-option label=\"素材库2\" value=\"library2\"></el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelAddToAlertMaterial\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmAddToAlertMaterial\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'InfoSummary',\r\n  computed: {\r\n    // 根据当前页码和每页显示数量计算当前页的数据\r\n    paginatedList() {\r\n      const start = (this.currentPage - 1) * this.pageSize;\r\n      // 实际应用中，这里应该是从后端获取分页数据\r\n      // 这里为了演示，我们从本地数据中截取一部分\r\n      return this.infoList.slice(0, Math.min(this.infoList.length, this.pageSize));\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // 页面基础数据\r\n      originalTopNav: undefined, // 存储原始的topNav状态\r\n      searchKeyword: '',\r\n      positiveSentimentDialogVisible: false, // 控制正面情感弹窗的显示\r\n      selectedSentiment: '', // 用于情感属性纠错弹窗的选中值\r\n      editingItem: null, // 当前编辑的情感条目\r\n      activeMenu: '2', // 默认选中品牌\r\n      activeTab: 'today', // 默认选中今天\r\n      platformTypes: ['news', 'weibo', 'wechat', 'video', 'app'], // 默认选中的平台类型\r\n      sentimentTypes: ['positive', 'neutral'], // 默认选中的情感类型\r\n      infoAttributes: ['official', 'media'], // 默认选中的信息属性\r\n\r\n      // 分页相关数据\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      totalItems: 120, // 假设总共有120条数据\r\n\r\n      // 新建方案对话框相关数据\r\n      createPlanDialogVisible: false,\r\n      planActiveTab: 'standard',\r\n      themeInputVisible: false,\r\n      themeInputValue: '',\r\n      planForm: {\r\n        name: '',\r\n        scope: 'all',\r\n        monitorObject: '',\r\n        location: '',\r\n        themes: [],\r\n        industry: '',\r\n        timeRange: '',\r\n        channels: ['news', 'weibo', 'wechat']\r\n      },\r\n      advancedPlanForm: {\r\n        name: ''\r\n      },\r\n\r\n      // 行业分类弹窗相关数据\r\n      industryDialogVisible: false,\r\n      selectedIndustry: null,\r\n      industryTreeProps: {\r\n        label: 'label',\r\n        children: 'children'\r\n      },\r\n\r\n      // 发送预警弹窗相关数据\r\n      sendAlertDialogVisible: false,\r\n      addToAlertMaterialDialogVisible: false, // 新增：控制加入报告素材弹窗的显示\r\n      alertForm: {\r\n        title: '',\r\n        selectedReceivers: ['', '', '', '', '', '']\r\n      },\r\n      receivers: [\r\n        {\r\n          type: '总监',\r\n          persons: ['王总监', '李总监', '张总监']\r\n        },\r\n        {\r\n          type: '经理',\r\n          persons: ['王经理', '李经理', '张经理']\r\n        },\r\n        {\r\n          type: '主管',\r\n          persons: ['王主管', '李主管', '张主管']\r\n        },\r\n        {\r\n          type: '员工',\r\n          persons: ['王员工', '李员工', '张员工']\r\n        },\r\n        {\r\n          type: '外部人员',\r\n          persons: ['外部人员1', '外部人员2', '外部人员3']\r\n        },\r\n        {\r\n          type: '其他',\r\n          persons: ['其他人员1', '其他人员2', '其他人员3']\r\n        }\r\n      ],\r\n      industryTreeData: [\r\n        {\r\n          id: 1,\r\n          label: '制造',\r\n          children: []\r\n        },\r\n        {\r\n          id: 2,\r\n          label: '公共',\r\n          children: []\r\n        },\r\n        {\r\n          id: 3,\r\n          label: '教育',\r\n          children: []\r\n        },\r\n        {\r\n          id: 4,\r\n          label: '工业设备',\r\n          children: []\r\n        },\r\n        {\r\n          id: 5,\r\n          label: '环保设备',\r\n          children: []\r\n        },\r\n        {\r\n          id: 6,\r\n          label: '金融',\r\n          children: []\r\n        },\r\n        {\r\n          id: 7,\r\n          label: '商业',\r\n          children: []\r\n        },\r\n        {\r\n          id: 8,\r\n          label: '民用与商用',\r\n          children: []\r\n        },\r\n        {\r\n          id: 9,\r\n          label: '政府部门',\r\n          children: []\r\n        }\r\n      ],\r\n\r\n      // 信息列表数据\r\n      infoList: [\r\n        {\r\n          selected: false,\r\n          title: '新，<span class=\"highlight\">高价值联名</span>打造<span class=\"highlight\">方太</span>(Fotile)厨电新品牌',\r\n          content: '据<span class=\"highlight\">方太</span>(Fotile)官方消息，<span class=\"highlight\">方太</span>与设计师联名打造了一系列高端厨电产品，引领厨房新潮流...',\r\n          source: '新浪财经',\r\n          time: '2023-04-29 20:27:51',\r\n          sentiment: 'positive',\r\n          views: 6437,\r\n          comments: 89,\r\n          images: []\r\n        },\r\n        {\r\n          selected: false,\r\n          title: '招聘一位【<span class=\"highlight\">方太</span>集团(Fotile)-111 <span class=\"highlight\">方太</span>集团(Fotile)-400.6808.655，一键呼叫，服务响应一一】的<span class=\"highlight\">方太</span>集团(Fotile)维修师傅',\r\n          content: '招聘一位【<span class=\"highlight\">方太</span>集团(Fotile)官方认证的<span class=\"highlight\">方太</span>集团(Fotile)-111<span class=\"highlight\">方太</span>集团(Fotile)400.6808.655，一键呼叫，服务响应一一】的<span class=\"highlight\">方太</span>集团(Fotile)维修师傅...',\r\n          source: '头条新闻',\r\n          time: '2023-04-29 20:24:22',\r\n          sentiment: 'neutral',\r\n          views: 5321,\r\n          comments: 42,\r\n          images: ['/images/sample1.jpg', '/images/sample2.jpg']\r\n        },\r\n        {\r\n          selected: false,\r\n          title: '<span class=\"highlight\">方太</span>集团(Fotile)官方服务中心-<span class=\"highlight\">方太</span>集团(Fotile)-111 <span class=\"highlight\">方太</span>集团(Fotile) 400.6808.655，一键呼叫，服务响应一一的<span class=\"highlight\">方太</span>集团(Fotile)维修师傅',\r\n          content: '<span class=\"highlight\">方太</span>集团(Fotile)官方服务中心-<span class=\"highlight\">方太</span>集团(Fotile)-111<span class=\"highlight\">方太</span>集团(Fotile)400.6808.655，<span class=\"highlight\">方太</span>集团(Fotile)维修部。可预约上门，一站式解决<span class=\"highlight\">方太</span>厨电故障问题...',\r\n          source: '今日头条',\r\n          time: '2023-04-29 20:14:03',\r\n          sentiment: 'neutral',\r\n          views: 3642,\r\n          comments: 33,\r\n          images: ['/images/sample3.jpg', '/images/sample4.jpg']\r\n        }\r\n      ]\r\n    };\r\n  },\r\n  mounted() {\r\n    // 隐藏顶部导航栏\r\n    this.originalTopNav = this.$store.state.settings.topNav\r\n    this.$store.dispatch('settings/changeSetting', {\r\n      key: 'topNav',\r\n      value: false\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    // 恢复顶部导航栏设置\r\n    if (this.originalTopNav !== undefined) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'topNav',\r\n        value: this.originalTopNav\r\n      })\r\n    }\r\n  },\r\n  methods: {\r\n\r\n    // 菜单点击事件\r\n    handleMenuClick(menuName) {\r\n      console.log('选择菜单:', menuName);\r\n    },\r\n\r\n    // 获取情感图标\r\n    getSentimentIcon(sentiment) {\r\n      const icons = {\r\n        positive: 'el-icon-sunny',\r\n        neutral: 'el-icon-partly-cloudy',\r\n        negative: 'el-icon-cloudy'\r\n      };\r\n      return icons[sentiment] || 'el-icon-question';\r\n    },\r\n\r\n    // 获取情感文本\r\n    getSentimentText(sentiment) {\r\n      const texts = {\r\n        positive: '正面',\r\n        neutral: '中性',\r\n        negative: '负面'\r\n      };\r\n      return texts[sentiment] || '未知';\r\n    },\r\n\r\n    // 下拉菜单命令处理\r\n    handleCommand(command) {\r\n      console.log('执行命令:', command);\r\n    },\r\n\r\n    // 处理搜索关键词\r\n    handleSearchKeyword() {\r\n      if (this.searchKeyword.trim()) {\r\n        // 跳转到搜索结果页面，并传递搜索关键词\r\n        this.$router.push({\r\n          path: '/search-results',\r\n          query: {\r\n            q: this.searchKeyword.trim()\r\n          }\r\n        });\r\n      } else {\r\n        this.$message.warning('请输入搜索关键词');\r\n      }\r\n    },\r\n\r\n    // 分页相关方法\r\n    handleSizeChange(size) {\r\n      this.pageSize = size;\r\n      this.currentPage = 1; // 切换每页显示数量时，重置为第一页\r\n      console.log('每页显示数量:', size);\r\n    },\r\n\r\n    handleCurrentChange(page) {\r\n      this.currentPage = page;\r\n      console.log('当前页码:', page);\r\n    },\r\n\r\n    // 新建方案相关方法\r\n    showCreatePlanDialog() {\r\n      this.createPlanDialogVisible = true;\r\n    },\r\n\r\n    // 主题标签相关方法\r\n    handleRemoveTheme(tag) {\r\n      this.planForm.themes.splice(this.planForm.themes.indexOf(tag), 1);\r\n    },\r\n\r\n    showThemeInput() {\r\n      this.themeInputVisible = true;\r\n      this.$nextTick(_ => {\r\n        this.$refs.themeInput.$refs.input.focus();\r\n      });\r\n    },\r\n\r\n    handleAddTheme() {\r\n      let inputValue = this.themeInputValue;\r\n      if (inputValue) {\r\n        if (!this.planForm.themes.includes(inputValue)) {\r\n          this.planForm.themes.push(inputValue);\r\n        }\r\n      }\r\n      this.themeInputVisible = false;\r\n      this.themeInputValue = '';\r\n    },\r\n\r\n    // 监测对象下拉菜单命令处理\r\n    handleMonitorObjectCommand(command) {\r\n      this.planForm.monitorObject = command;\r\n    },\r\n\r\n    // 显示地域选择地图\r\n    showLocationMap() {\r\n      // 这里可以实现地图选择功能\r\n      this.$message.info('显示地域选择地图');\r\n    },\r\n\r\n    // 显示行业分类选择\r\n    showIndustrySelect() {\r\n      this.industryDialogVisible = true;\r\n    },\r\n\r\n    // 处理行业分类树节点点击\r\n    handleIndustryNodeClick(data) {\r\n      this.selectedIndustry = data;\r\n    },\r\n\r\n    // 确认行业分类选择\r\n    confirmIndustrySelect() {\r\n      if (this.selectedIndustry) {\r\n        this.planForm.industry = this.selectedIndustry.label;\r\n      }\r\n      this.industryDialogVisible = false;\r\n    },\r\n\r\n    // 保存方案\r\n    savePlan() {\r\n      // 根据当前激活的标签页选择不同的表单数据\r\n      const formData = this.planActiveTab === 'standard' ? this.planForm : this.advancedPlanForm;\r\n\r\n      console.log('保存方案:', formData);\r\n      // 这里可以添加表单验证和提交到后端的逻辑\r\n\r\n      // 关闭对话框\r\n      this.createPlanDialogVisible = false;\r\n\r\n      // 重置表单\r\n      if (this.planActiveTab === 'standard') {\r\n        this.planForm = {\r\n          name: '',\r\n          scope: 'all',\r\n          monitorObject: '',\r\n          location: '',\r\n          themes: [],\r\n          industry: '',\r\n          timeRange: '',\r\n          channels: ['news', 'weibo', 'wechat']\r\n        };\r\n      } else {\r\n        this.advancedPlanForm = {\r\n          name: ''\r\n        };\r\n      }\r\n    },\r\n\r\n    // 显示发送预警弹窗\r\n    showSendAlertDialog() {\r\n      this.sendAlertDialogVisible = true;\r\n    },\r\n\r\n    // 取消发送预警\r\n    cancelSendAlert() {\r\n      this.sendAlertDialogVisible = false;\r\n      // 重置表单\r\n      this.alertForm = {\r\n        title: '',\r\n        selectedReceivers: ['', '', '', '', '', '']\r\n      };\r\n    },\r\n    handleSentimentClick(item) {\r\n      this.editingItem = item;\r\n      this.selectedSentiment = item.sentiment;\r\n      this.positiveSentimentDialogVisible = true;\r\n    },\r\n    handlePositiveDialogConfirm() {\r\n      if (this.editingItem) {\r\n        this.editingItem.sentiment = this.selectedSentiment;\r\n        // 在实际应用中，这里可能需要调用API将更改保存到后端\r\n        // 例如: this.updateSentimentApi(this.editingItem.id, this.selectedSentiment);\r\n      }\r\n      this.positiveSentimentDialogVisible = false;\r\n    },\r\n\r\n    // 确认发送预警\r\n    confirmSendAlert() {\r\n      // 在这里处理发送预警的逻辑\r\n      console.log('发送预警:', this.alertForm);\r\n      this.sendAlertDialogVisible = false;\r\n      // 清空表单\r\n      this.alertForm = {\r\n        title: '',\r\n        selectedReceivers: ['', '', '', '', '', '']\r\n      };\r\n    },\r\n\r\n    // 加入至报告素材对话框相关方法\r\n    showAddToAlertMaterialDialog() {\r\n      this.addToAlertMaterialDialogVisible = true;\r\n    },\r\n    cancelAddToAlertMaterial() {\r\n      this.addToAlertMaterialDialogVisible = false;\r\n      this.selectedMaterialLibrary = ''; // 清空选中值\r\n    },\r\n    confirmAddToAlertMaterial() {\r\n      // 这里添加确认逻辑，例如提交选中的素材库\r\n      console.log('Selected Material Library:', this.selectedMaterialLibrary);\r\n      this.addToAlertMaterialDialogVisible = false;\r\n      this.selectedMaterialLibrary = ''; // 清空选中值\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  position: relative;\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  background-color: #f0f2f5;\r\n}\r\n\r\n// 操作按钮区域样式\r\n.action-buttons {\r\n  position: absolute;\r\n  top: 15px;\r\n  left: 15px;\r\n  z-index: 10;\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.info-summary-container {\r\n  display: flex;\r\n  height: 100%;\r\n  padding-top: 60px; // 为新建方案按钮留出空间\r\n}\r\n\r\n// 左侧导航栏样式\r\n.left-sidebar {\r\n  width: 180px;\r\n  background-color: #fff;\r\n  border-right: 1px solid #e8e8e8;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow-y: auto;\r\n}\r\n\r\n.sidebar-header {\r\n  padding: 15px;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.sidebar-menu {\r\n  flex: 1;\r\n}\r\n\r\n.el-menu-vertical {\r\n  border-right: none;\r\n}\r\n\r\n// 右侧内容区域样式\r\n.right-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #fff;\r\n  overflow: hidden;\r\n  margin: 0 15px;\r\n  border-radius: 4px;\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.content-header {\r\n  padding: 15px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.entity-title {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n}\r\n\r\n.entity-name {\r\n  margin-right: 5px;\r\n}\r\n\r\n// 标签页样式\r\n.tabs-container {\r\n  padding: 10px 15px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.filter-tabs {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n// 筛选区域样式\r\n.filter-section {\r\n  padding: 15px;\r\n  background-color: #f9f9f9;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.filter-row {\r\n  margin-bottom: 10px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.filter-row:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.filter-label {\r\n  width: 80px;\r\n  color: #606266;\r\n  line-height: 28px;\r\n}\r\n\r\n.filter-actions {\r\n  margin-top: 10px;\r\n  padding-left: 80px;\r\n}\r\n\r\n// 操作栏样式\r\n.action-bar {\r\n  padding: 10px 15px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.left-actions, .right-actions {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.search-input {\r\n  width: 200px;\r\n  margin-right: 10px;\r\n}\r\n\r\n// 信息列表样式\r\n.info-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 0;\r\n}\r\n\r\n.info-item {\r\n  padding: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.item-checkbox {\r\n  margin-right: 10px;\r\n  margin-top: 3px;\r\n}\r\n\r\n.info-content {\r\n  flex: 1;\r\n}\r\n\r\n.info-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.info-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n\r\n.info-summary {\r\n  color: #606266;\r\n  margin-bottom: 8px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.info-footer {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #909399;\r\n  font-size: 13px;\r\n}\r\n\r\n.info-source, .info-time, .info-sentiment, .info-views, .info-comments {\r\n  margin-right: 15px;\r\n}\r\n\r\n.info-index {\r\n  margin-left: auto;\r\n}\r\n\r\n.sentiment-positive {\r\n  color: #67c23a;\r\n}\r\n\r\n.sentiment-neutral {\r\n  color: #909399;\r\n}\r\n\r\n.sentiment-negative {\r\n  color: #f56c6c;\r\n}\r\n\r\n.info-images {\r\n  display: flex;\r\n  margin-top: 10px;\r\n}\r\n\r\n.info-image {\r\n  width: 80px;\r\n  height: 80px;\r\n  object-fit: cover;\r\n  margin-right: 10px;\r\n  border-radius: 4px;\r\n}\r\n\r\n// 高亮样式\r\n:deep(.highlight) {\r\n  color: #409eff;\r\n  font-weight: bold;\r\n}\r\n\r\n// 新建方案对话框样式\r\n.create-plan-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n\r\n  .el-tabs__header {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .el-tabs__nav {\r\n    width: 100%;\r\n  }\r\n\r\n  .el-tabs__item {\r\n    flex: 1;\r\n    text-align: center;\r\n    height: 40px;\r\n    line-height: 40px;\r\n  }\r\n\r\n  .el-form-item {\r\n    margin-bottom: 15px;\r\n  }\r\n}\r\n\r\n.monitor-object-select {\r\n  display: flex;\r\n  align-items: center;\r\n  position: relative;\r\n\r\n  .el-dropdown {\r\n    position: absolute;\r\n    right: 10px;\r\n    top: 0;\r\n    height: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n.location-select {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.location-btn {\r\n  margin-left: 10px;\r\n}\r\n\r\n.industry-select-btn {\r\n  width: 100%;\r\n  text-align: left;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  padding: 8px 15px;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  color: #606266;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n\r\n  &:hover {\r\n    border-color: #c0c4cc;\r\n  }\r\n}\r\n\r\n.theme-row, .industry-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n}\r\n\r\n.theme-input, .industry-input {\r\n  width: 100px;\r\n  margin-left: 10px;\r\n  vertical-align: bottom;\r\n}\r\n\r\n.theme-button, .industry-button {\r\n  margin-left: 10px;\r\n  height: 32px;\r\n  line-height: 30px;\r\n  padding-top: 0;\r\n  padding-bottom: 0;\r\n}\r\n\r\n.el-tag {\r\n  margin-right: 10px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.channels-row {\r\n  display: flex;\r\n  margin-bottom: 10px;\r\n\r\n  .el-checkbox {\r\n    margin-right: 20px;\r\n  }\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n// 分页容器样式\r\n.pagination-container {\r\n  padding: 15px;\r\n  text-align: right;\r\n  background-color: #fff;\r\n  border-top: 1px solid #e8e8e8;\r\n}\r\n\r\n// 行业分类弹窗样式\r\n.industry-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n\r\n  .industry-dialog-content {\r\n    display: flex;\r\n    height: 400px;\r\n  }\r\n\r\n  .industry-tree-container {\r\n    flex: 1;\r\n    padding: 15px;\r\n    border-right: 1px solid #e8e8e8;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .industry-selected-container {\r\n    width: 200px;\r\n    padding: 15px;\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n\r\n  .industry-selected-title {\r\n    font-weight: bold;\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n\r\n// 发送预警弹窗样式\r\n.send-alert-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n\r\n  .send-alert-content {\r\n    padding: 20px;\r\n  }\r\n\r\n  .receiver-list {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n\r\n  .receiver-item {\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n\r\n  .receiver-type {\r\n    width: 80px;\r\n    margin-right: 10px;\r\n  }\r\n\r\n  .receiver-select {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n// 正面情感弹窗样式\r\n.positive-sentiment-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n  .el-dialog__body {\r\n    padding: 20px;\r\n  }\r\n}\r\n</style>\r\n\r\n<el-button type=\"success\" size=\"mini\" @click=\"handlePositiveClick\">正面</el-button>,\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  position: relative;\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  background-color: #f0f2f5;\r\n}\r\n\r\n// 操作按钮区域样式\r\n.action-buttons {\r\n  position: absolute;\r\n  top: 15px;\r\n  left: 15px;\r\n  z-index: 10;\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.info-summary-container {\r\n  display: flex;\r\n  height: 100%;\r\n  padding-top: 60px; // 为新建方案按钮留出空间\r\n}\r\n\r\n// 左侧导航栏样式\r\n.left-sidebar {\r\n  width: 180px;\r\n  background-color: #fff;\r\n  border-right: 1px solid #e8e8e8;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow-y: auto;\r\n}\r\n\r\n.sidebar-header {\r\n  padding: 15px;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.sidebar-menu {\r\n  flex: 1;\r\n}\r\n\r\n.el-menu-vertical {\r\n  border-right: none;\r\n}\r\n\r\n// 右侧内容区域样式\r\n.right-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #fff;\r\n  overflow: hidden;\r\n  margin: 0 15px;\r\n  border-radius: 4px;\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.content-header {\r\n  padding: 15px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.entity-title {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n}\r\n\r\n.entity-name {\r\n  margin-right: 5px;\r\n}\r\n\r\n// 标签页样式\r\n.tabs-container {\r\n  padding: 10px 15px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.filter-tabs {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n// 筛选区域样式\r\n.filter-section {\r\n  padding: 15px;\r\n  background-color: #f9f9f9;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.filter-row {\r\n  margin-bottom: 10px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.filter-row:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.filter-label {\r\n  width: 80px;\r\n  color: #606266;\r\n  line-height: 28px;\r\n}\r\n\r\n.filter-actions {\r\n  margin-top: 10px;\r\n  padding-left: 80px;\r\n}\r\n\r\n// 操作栏样式\r\n.action-bar {\r\n  padding: 10px 15px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.left-actions, .right-actions {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.search-input {\r\n  width: 200px;\r\n  margin-right: 10px;\r\n}\r\n\r\n// 信息列表样式\r\n.info-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 0;\r\n}\r\n\r\n.info-item {\r\n  padding: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.item-checkbox {\r\n  margin-right: 10px;\r\n  margin-top: 3px;\r\n}\r\n\r\n.info-content {\r\n  flex: 1;\r\n}\r\n\r\n.info-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.info-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n\r\n.info-summary {\r\n  color: #606266;\r\n  margin-bottom: 8px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.info-footer {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #909399;\r\n  font-size: 13px;\r\n}\r\n\r\n.info-source, .info-time, .info-sentiment, .info-views, .info-comments {\r\n  margin-right: 15px;\r\n}\r\n\r\n.info-index {\r\n  margin-left: auto;\r\n}\r\n\r\n.sentiment-positive {\r\n  color: #67c23a;\r\n}\r\n\r\n.sentiment-neutral {\r\n  color: #909399;\r\n}\r\n\r\n.sentiment-negative {\r\n  color: #f56c6c;\r\n}\r\n\r\n.info-images {\r\n  display: flex;\r\n  margin-top: 10px;\r\n}\r\n\r\n.info-image {\r\n  width: 80px;\r\n  height: 80px;\r\n  object-fit: cover;\r\n  margin-right: 10px;\r\n  border-radius: 4px;\r\n}\r\n\r\n// 高亮样式\r\n:deep(.highlight) {\r\n  color: #409eff;\r\n  font-weight: bold;\r\n}\r\n\r\n// 新建方案对话框样式\r\n.create-plan-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n\r\n  .el-tabs__header {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .el-tabs__nav {\r\n    width: 100%;\r\n  }\r\n\r\n  .el-tabs__item {\r\n    flex: 1;\r\n    text-align: center;\r\n    height: 40px;\r\n    line-height: 40px;\r\n  }\r\n\r\n  .el-form-item {\r\n    margin-bottom: 15px;\r\n  }\r\n}\r\n\r\n.monitor-object-select {\r\n  display: flex;\r\n  align-items: center;\r\n  position: relative;\r\n\r\n  .el-dropdown {\r\n    position: absolute;\r\n    right: 10px;\r\n    top: 0;\r\n    height: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n.location-select {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.location-btn {\r\n  margin-left: 10px;\r\n}\r\n\r\n.industry-select-btn {\r\n  width: 100%;\r\n  text-align: left;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  padding: 8px 15px;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  color: #606266;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n\r\n  &:hover {\r\n    border-color: #c0c4cc;\r\n  }\r\n}\r\n\r\n.theme-row, .industry-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n}\r\n\r\n.theme-input, .industry-input {\r\n  width: 100px;\r\n  margin-left: 10px;\r\n  vertical-align: bottom;\r\n}\r\n\r\n.theme-button, .industry-button {\r\n  margin-left: 10px;\r\n  height: 32px;\r\n  line-height: 30px;\r\n  padding-top: 0;\r\n  padding-bottom: 0;\r\n}\r\n\r\n.el-tag {\r\n  margin-right: 10px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.channels-row {\r\n  display: flex;\r\n  margin-bottom: 10px;\r\n\r\n  .el-checkbox {\r\n    margin-right: 20px;\r\n  }\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n// 分页容器样式\r\n.pagination-container {\r\n  padding: 15px;\r\n  text-align: right;\r\n  background-color: #fff;\r\n  border-top: 1px solid #e8e8e8;\r\n}\r\n\r\n// 行业分类弹窗样式\r\n.industry-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n\r\n  .industry-dialog-content {\r\n    display: flex;\r\n    height: 400px;\r\n  }\r\n\r\n  .industry-tree-container {\r\n    flex: 1;\r\n    padding: 15px;\r\n    border-right: 1px solid #e8e8e8;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .industry-selected-container {\r\n    width: 200px;\r\n    padding: 15px;\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n\r\n  .industry-selected-title {\r\n    font-weight: bold;\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n\r\n// 发送预警弹窗样式\r\n.send-alert-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n\r\n  .send-alert-content {\r\n    padding: 20px;\r\n  }\r\n\r\n  .receiver-list {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n\r\n  .receiver-item {\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n\r\n  .receiver-type {\r\n    width: 80px;\r\n    margin-right: 10px;\r\n  }\r\n\r\n  .receiver-select {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n// 正面情感弹窗样式\r\n.positive-sentiment-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n  .el-dialog__body {\r\n    padding: 20px;\r\n  }\r\n}\r\n</style>\r\n\r\nel-dialog\r\ntitle=\"信息图谱\"\r\n:visible.sync=\"infoGraphDialogVisible\"\r\nwidth=\"50%\"\r\nappend-to-body\r\ncustom-class=\"info-graph-dialog\"\r\n>\r\n<div class=\"info-graph-content\">\r\n<!-- 根据提供的图示调整内容布局 -->\r\n<div class=\"graph-container\">\r\n<div class=\"graph-node\">东木头人</div>\r\n<div class=\"graph-node\">永兴队</div>\r\n<!-- 添加更多节点 -->\r\n</div>\r\n</div>\r\n<div slot=\"footer\" class=\"dialog-footer\">\r\n<el-button @click=\"infoGraphDialogVisible = false\">关闭</el-button>\r\n</div>\r\n</el-dialog>\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  position: relative;\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  background-color: #f0f2f5;\r\n}\r\n\r\n// 操作按钮区域样式\r\n.action-buttons {\r\n  position: absolute;\r\n  top: 15px;\r\n  left: 15px;\r\n  z-index: 10;\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.info-summary-container {\r\n  display: flex;\r\n  height: 100%;\r\n  padding-top: 60px; // 为新建方案按钮留出空间\r\n}\r\n\r\n// 左侧导航栏样式\r\n.left-sidebar {\r\n  width: 180px;\r\n  background-color: #fff;\r\n  border-right: 1px solid #e8e8e8;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow-y: auto;\r\n}\r\n\r\n.sidebar-header {\r\n  padding: 15px;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.sidebar-menu {\r\n  flex: 1;\r\n}\r\n\r\n.el-menu-vertical {\r\n  border-right: none;\r\n}\r\n\r\n// 右侧内容区域样式\r\n.right-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #fff;\r\n  overflow: hidden;\r\n  margin: 0 15px;\r\n  border-radius: 4px;\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.content-header {\r\n  padding: 15px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.entity-title {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n}\r\n\r\n.entity-name {\r\n  margin-right: 5px;\r\n}\r\n\r\n// 标签页样式\r\n.tabs-container {\r\n  padding: 10px 15px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.filter-tabs {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n// 筛选区域样式\r\n.filter-section {\r\n  padding: 15px;\r\n  background-color: #f9f9f9;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.filter-row {\r\n  margin-bottom: 10px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.filter-row:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.filter-label {\r\n  width: 80px;\r\n  color: #606266;\r\n  line-height: 28px;\r\n}\r\n\r\n.filter-actions {\r\n  margin-top: 10px;\r\n  padding-left: 80px;\r\n}\r\n\r\n// 操作栏样式\r\n.action-bar {\r\n  padding: 10px 15px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.left-actions, .right-actions {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.search-input {\r\n  width: 200px;\r\n  margin-right: 10px;\r\n}\r\n\r\n// 信息列表样式\r\n.info-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 0;\r\n}\r\n\r\n.info-item {\r\n  padding: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.item-checkbox {\r\n  margin-right: 10px;\r\n  margin-top: 3px;\r\n}\r\n\r\n.info-content {\r\n  flex: 1;\r\n}\r\n\r\n.info-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.info-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n\r\n.info-summary {\r\n  color: #606266;\r\n  margin-bottom: 8px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.info-footer {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #909399;\r\n  font-size: 13px;\r\n}\r\n\r\n.info-source, .info-time, .info-sentiment, .info-views, .info-comments {\r\n  margin-right: 15px;\r\n}\r\n\r\n.info-index {\r\n  margin-left: auto;\r\n}\r\n\r\n.sentiment-positive {\r\n  color: #67c23a;\r\n}\r\n\r\n.sentiment-neutral {\r\n  color: #909399;\r\n}\r\n\r\n.sentiment-negative {\r\n  color: #f56c6c;\r\n}\r\n\r\n.info-images {\r\n  display: flex;\r\n  margin-top: 10px;\r\n}\r\n\r\n.info-image {\r\n  width: 80px;\r\n  height: 80px;\r\n  object-fit: cover;\r\n  margin-right: 10px;\r\n  border-radius: 4px;\r\n}\r\n\r\n// 高亮样式\r\n:deep(.highlight) {\r\n  color: #409eff;\r\n  font-weight: bold;\r\n}\r\n\r\n// 新建方案对话框样式\r\n.create-plan-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n\r\n  .el-tabs__header {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .el-tabs__nav {\r\n    width: 100%;\r\n  }\r\n\r\n  .el-tabs__item {\r\n    flex: 1;\r\n    text-align: center;\r\n    height: 40px;\r\n    line-height: 40px;\r\n  }\r\n\r\n  .el-form-item {\r\n    margin-bottom: 15px;\r\n  }\r\n}\r\n\r\n.monitor-object-select {\r\n  display: flex;\r\n  align-items: center;\r\n  position: relative;\r\n\r\n  .el-dropdown {\r\n    position: absolute;\r\n    right: 10px;\r\n    top: 0;\r\n    height: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n.location-select {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.location-btn {\r\n  margin-left: 10px;\r\n}\r\n\r\n.industry-select-btn {\r\n  width: 100%;\r\n  text-align: left;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  padding: 8px 15px;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  color: #606266;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n\r\n  &:hover {\r\n    border-color: #c0c4cc;\r\n  }\r\n}\r\n\r\n.theme-row, .industry-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n}\r\n\r\n.theme-input, .industry-input {\r\n  width: 100px;\r\n  margin-left: 10px;\r\n  vertical-align: bottom;\r\n}\r\n\r\n.theme-button, .industry-button {\r\n  margin-left: 10px;\r\n  height: 32px;\r\n  line-height: 30px;\r\n  padding-top: 0;\r\n  padding-bottom: 0;\r\n}\r\n\r\n.el-tag {\r\n  margin-right: 10px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.channels-row {\r\n  display: flex;\r\n  margin-bottom: 10px;\r\n\r\n  .el-checkbox {\r\n    margin-right: 20px;\r\n  }\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n// 分页容器样式\r\n.pagination-container {\r\n  padding: 15px;\r\n  text-align: right;\r\n  background-color: #fff;\r\n  border-top: 1px solid #e8e8e8;\r\n}\r\n\r\n// 行业分类弹窗样式\r\n.industry-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n\r\n  .industry-dialog-content {\r\n    display: flex;\r\n    height: 400px;\r\n  }\r\n\r\n  .industry-tree-container {\r\n    flex: 1;\r\n    padding: 15px;\r\n    border-right: 1px solid #e8e8e8;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .industry-selected-container {\r\n    width: 200px;\r\n    padding: 15px;\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n\r\n  .industry-selected-title {\r\n    font-weight: bold;\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n\r\n// 发送预警弹窗样式\r\n.send-alert-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n\r\n  .send-alert-content {\r\n    padding: 20px;\r\n  }\r\n\r\n  .receiver-list {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n\r\n  .receiver-item {\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n\r\n  .receiver-type {\r\n    width: 80px;\r\n    margin-right: 10px;\r\n  }\r\n\r\n  .receiver-select {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n// 正面情感弹窗样式\r\n.positive-sentiment-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n  .el-dialog__body {\r\n    padding: 20px;\r\n  }\r\n}\r\n</style>\r\n\r\n<el-button type=\"success\" size=\"mini\" @click=\"handlePositiveClick\">正面</el-button>,\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  position: relative;\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  background-color: #f0f2f5;\r\n}\r\n\r\n// 操作按钮区域样式\r\n.action-buttons {\r\n  position: absolute;\r\n  top: 15px;\r\n  left: 15px;\r\n  z-index: 10;\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.info-summary-container {\r\n  display: flex;\r\n  height: 100%;\r\n  padding-top: 60px; // 为新建方案按钮留出空间\r\n}\r\n\r\n// 左侧导航栏样式\r\n.left-sidebar {\r\n  width: 180px;\r\n  background-color: #fff;\r\n  border-right: 1px solid #e8e8e8;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow-y: auto;\r\n}\r\n\r\n.sidebar-header {\r\n  padding: 15px;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.sidebar-menu {\r\n  flex: 1;\r\n}\r\n\r\n.el-menu-vertical {\r\n  border-right: none;\r\n}\r\n\r\n// 右侧内容区域样式\r\n.right-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #fff;\r\n  overflow: hidden;\r\n  margin: 0 15px;\r\n  border-radius: 4px;\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.content-header {\r\n  padding: 15px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.entity-title {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n}\r\n\r\n.entity-name {\r\n  margin-right: 5px;\r\n}\r\n\r\n// 标签页样式\r\n.tabs-container {\r\n  padding: 10px 15px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.filter-tabs {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n// 筛选区域样式\r\n.filter-section {\r\n  padding: 15px;\r\n  background-color: #f9f9f9;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.filter-row {\r\n  margin-bottom: 10px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.filter-row:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.filter-label {\r\n  width: 80px;\r\n  color: #606266;\r\n  line-height: 28px;\r\n}\r\n\r\n.filter-actions {\r\n  margin-top: 10px;\r\n  padding-left: 80px;\r\n}\r\n\r\n// 操作栏样式\r\n.action-bar {\r\n  padding: 10px 15px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.left-actions, .right-actions {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.search-input {\r\n  width: 200px;\r\n  margin-right: 10px;\r\n}\r\n\r\n// 信息列表样式\r\n.info-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 0;\r\n}\r\n\r\n.info-item {\r\n  padding: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.item-checkbox {\r\n  margin-right: 10px;\r\n  margin-top: 3px;\r\n}\r\n\r\n.info-content {\r\n  flex: 1;\r\n}\r\n\r\n.info-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.info-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n\r\n.info-summary {\r\n  color: #606266;\r\n  margin-bottom: 8px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.info-footer {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #909399;\r\n  font-size: 13px;\r\n}\r\n\r\n.info-source, .info-time, .info-sentiment, .info-views, .info-comments {\r\n  margin-right: 15px;\r\n}\r\n\r\n.info-index {\r\n  margin-left: auto;\r\n}\r\n\r\n.sentiment-positive {\r\n  color: #67c23a;\r\n}\r\n\r\n.sentiment-neutral {\r\n  color: #909399;\r\n}\r\n\r\n.sentiment-negative {\r\n  color: #f56c6c;\r\n}\r\n\r\n.info-images {\r\n  display: flex;\r\n  margin-top: 10px;\r\n}\r\n\r\n.info-image {\r\n  width: 80px;\r\n  height: 80px;\r\n  object-fit: cover;\r\n  margin-right: 10px;\r\n  border-radius: 4px;\r\n}\r\n\r\n// 高亮样式\r\n:deep(.highlight) {\r\n  color: #409eff;\r\n  font-weight: bold;\r\n}\r\n\r\n// 新建方案对话框样式\r\n.create-plan-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n"]}]}