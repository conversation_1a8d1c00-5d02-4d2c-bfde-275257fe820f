{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\modules\\normalizeExternalHTML\\index.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\modules\\normalizeExternalHTML\\index.js", "mtime": 1749104422027}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnZhciBfZ29vZ2xlRG9jcyA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9ub3JtYWxpemVycy9nb29nbGVEb2NzLmpzIikpOwp2YXIgX21zV29yZCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9ub3JtYWxpemVycy9tc1dvcmQuanMiKSk7CnZhciBOT1JNQUxJWkVSUyA9IFtfbXNXb3JkLmRlZmF1bHQsIF9nb29nbGVEb2NzLmRlZmF1bHRdOwp2YXIgbm9ybWFsaXplRXh0ZXJuYWxIVE1MID0gZnVuY3Rpb24gbm9ybWFsaXplRXh0ZXJuYWxIVE1MKGRvYykgewogIGlmIChkb2MuZG9jdW1lbnRFbGVtZW50KSB7CiAgICBOT1JNQUxJWkVSUy5mb3JFYWNoKGZ1bmN0aW9uIChub3JtYWxpemUpIHsKICAgICAgbm9ybWFsaXplKGRvYyk7CiAgICB9KTsKICB9Cn07CnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IG5vcm1hbGl6ZUV4dGVybmFsSFRNTDs="}, {"version": 3, "names": ["_googleDocs", "_interopRequireDefault", "require", "_msWord", "NORMALIZERS", "msWord", "googleDocs", "normalizeExternalHTML", "doc", "documentElement", "for<PERSON>ach", "normalize", "_default", "exports", "default"], "sources": ["../../../src/modules/normalizeExternalHTML/index.ts"], "sourcesContent": ["import googleDocs from './normalizers/googleDocs.js';\nimport msWord from './normalizers/msWord.js';\n\nconst NORMALIZERS = [msWord, googleDocs];\n\nconst normalizeExternalHTML = (doc: Document) => {\n  if (doc.documentElement) {\n    NORMALIZERS.forEach((normalize) => {\n      normalize(doc);\n    });\n  }\n};\n\nexport default normalizeExternalHTML;\n"], "mappings": ";;;;;;;;AAAA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAF,sBAAA,CAAAC,OAAA;AAEA,IAAME,WAAW,GAAG,CAACC,eAAM,EAAEC,mBAAU,CAAC;AAExC,IAAMC,qBAAqB,GAAI,SAAzBA,qBAAqBA,CAAIC,GAAa,EAAK;EAC/C,IAAIA,GAAG,CAACC,eAAe,EAAE;IACvBL,WAAW,CAACM,OAAO,CAAE,UAAAC,SAAS,EAAK;MACjCA,SAAS,CAACH,GAAG,CAAC;IAChB,CAAC,CAAC;EACJ;AACF,CAAC;AAAA,IAAAI,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEcP,qBAAqB", "ignoreList": []}]}