{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\blots\\block.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\blots\\block.js", "mtime": 1749104420386}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_parchment", "require", "_quill<PERSON><PERSON><PERSON>", "_interopRequireDefault", "_break", "_inline", "_text", "NEWLINE_LENGTH", "Block", "exports", "default", "_BlockBlot", "_this", "_classCallCheck2", "_len", "arguments", "length", "args", "Array", "_key", "_callSuper2", "concat", "_defineProperty2", "_inherits2", "_createClass2", "key", "value", "delta", "cache", "blockDelta", "deleteAt", "index", "_superPropGet2", "formatAt", "name", "scroll", "query", "<PERSON><PERSON>", "BLOCK", "format", "Math", "min", "insertAt", "def", "lines", "split", "text", "shift", "children", "tail", "block", "reduce", "lineIndex", "line", "insertBefore", "blot", "ref", "head", "Break", "remove", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "target", "optimize", "context", "path", "<PERSON><PERSON><PERSON><PERSON>", "child", "force", "undefined", "clone", "parent", "next", "BlockBlot", "blotName", "tagName", "defaultChild", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Inline", "EmbedBlot", "TextBlot", "BlockEmbed", "_EmbedBlot", "attach", "attributes", "AttributorStore", "domNode", "Delta", "insert", "_objectSpread2", "formats", "values", "attribute", "BLOCK_ATTRIBUTE", "_this2", "pop", "blocks", "map", "create", "for<PERSON>ach", "scope", "BLOCK_BLOT", "filter", "descendants", "LeafBlot", "leaf", "bubbleFormats", "statics"], "sources": ["../../src/blots/block.ts"], "sourcesContent": ["import {\n  Attributor<PERSON><PERSON>,\n  BlockBlot,\n  EmbedBlot,\n  LeafBlot,\n  Scope,\n} from 'parchment';\nimport type { Blot, Parent } from 'parchment';\nimport Delta from 'quill-delta';\nimport Break from './break.js';\nimport Inline from './inline.js';\nimport TextBlot from './text.js';\n\nconst NEWLINE_LENGTH = 1;\n\nclass Block extends BlockBlot {\n  cache: { delta?: Delta | null; length?: number } = {};\n\n  delta(): Delta {\n    if (this.cache.delta == null) {\n      this.cache.delta = blockDelta(this);\n    }\n    return this.cache.delta;\n  }\n\n  deleteAt(index: number, length: number) {\n    super.deleteAt(index, length);\n    this.cache = {};\n  }\n\n  formatAt(index: number, length: number, name: string, value: unknown) {\n    if (length <= 0) return;\n    if (this.scroll.query(name, Scope.BLOCK)) {\n      if (index + length === this.length()) {\n        this.format(name, value);\n      }\n    } else {\n      super.formatAt(\n        index,\n        Math.min(length, this.length() - index - 1),\n        name,\n        value,\n      );\n    }\n    this.cache = {};\n  }\n\n  insertAt(index: number, value: string, def?: unknown) {\n    if (def != null) {\n      super.insertAt(index, value, def);\n      this.cache = {};\n      return;\n    }\n    if (value.length === 0) return;\n    const lines = value.split('\\n');\n    const text = lines.shift() as string;\n    if (text.length > 0) {\n      if (index < this.length() - 1 || this.children.tail == null) {\n        super.insertAt(Math.min(index, this.length() - 1), text);\n      } else {\n        this.children.tail.insertAt(this.children.tail.length(), text);\n      }\n      this.cache = {};\n    }\n    // TODO: Fix this next time the file is edited.\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    let block: Blot | this = this;\n    lines.reduce((lineIndex, line) => {\n      // @ts-expect-error Fix me later\n      block = block.split(lineIndex, true);\n      block.insertAt(0, line);\n      return line.length;\n    }, index + text.length);\n  }\n\n  insertBefore(blot: Blot, ref?: Blot | null) {\n    const { head } = this.children;\n    super.insertBefore(blot, ref);\n    if (head instanceof Break) {\n      head.remove();\n    }\n    this.cache = {};\n  }\n\n  length() {\n    if (this.cache.length == null) {\n      this.cache.length = super.length() + NEWLINE_LENGTH;\n    }\n    return this.cache.length;\n  }\n\n  moveChildren(target: Parent, ref?: Blot | null) {\n    super.moveChildren(target, ref);\n    this.cache = {};\n  }\n\n  optimize(context: { [key: string]: any }) {\n    super.optimize(context);\n    this.cache = {};\n  }\n\n  path(index: number) {\n    return super.path(index, true);\n  }\n\n  removeChild(child: Blot) {\n    super.removeChild(child);\n    this.cache = {};\n  }\n\n  split(index: number, force: boolean | undefined = false): Blot | null {\n    if (force && (index === 0 || index >= this.length() - NEWLINE_LENGTH)) {\n      const clone = this.clone();\n      if (index === 0) {\n        this.parent.insertBefore(clone, this);\n        return this;\n      }\n      this.parent.insertBefore(clone, this.next);\n      return clone;\n    }\n    const next = super.split(index, force);\n    this.cache = {};\n    return next;\n  }\n}\nBlock.blotName = 'block';\nBlock.tagName = 'P';\nBlock.defaultChild = Break;\nBlock.allowedChildren = [Break, Inline, EmbedBlot, TextBlot];\n\nclass BlockEmbed extends EmbedBlot {\n  attributes: AttributorStore;\n  domNode: HTMLElement;\n\n  attach() {\n    super.attach();\n    this.attributes = new AttributorStore(this.domNode);\n  }\n\n  delta() {\n    return new Delta().insert(this.value(), {\n      ...this.formats(),\n      ...this.attributes.values(),\n    });\n  }\n\n  format(name: string, value: unknown) {\n    const attribute = this.scroll.query(name, Scope.BLOCK_ATTRIBUTE);\n    if (attribute != null) {\n      // @ts-expect-error TODO: Scroll#query() should return Attributor when scope is attribute\n      this.attributes.attribute(attribute, value);\n    }\n  }\n\n  formatAt(index: number, length: number, name: string, value: unknown) {\n    this.format(name, value);\n  }\n\n  insertAt(index: number, value: string, def?: unknown) {\n    if (def != null) {\n      super.insertAt(index, value, def);\n      return;\n    }\n    const lines = value.split('\\n');\n    const text = lines.pop();\n    const blocks = lines.map((line) => {\n      const block = this.scroll.create(Block.blotName);\n      block.insertAt(0, line);\n      return block;\n    });\n    const ref = this.split(index);\n    blocks.forEach((block) => {\n      this.parent.insertBefore(block, ref);\n    });\n    if (text) {\n      this.parent.insertBefore(this.scroll.create('text', text), ref);\n    }\n  }\n}\nBlockEmbed.scope = Scope.BLOCK_BLOT;\n// It is important for cursor behavior BlockEmbeds use tags that are block level elements\n\nfunction blockDelta(blot: BlockBlot, filter = true) {\n  return blot\n    .descendants(LeafBlot)\n    .reduce((delta, leaf) => {\n      if (leaf.length() === 0) {\n        return delta;\n      }\n      return delta.insert(leaf.value(), bubbleFormats(leaf, {}, filter));\n    }, new Delta())\n    .insert('\\n', bubbleFormats(blot));\n}\n\nfunction bubbleFormats(\n  blot: Blot | null,\n  formats: Record<string, unknown> = {},\n  filter = true,\n): Record<string, unknown> {\n  if (blot == null) return formats;\n  if ('formats' in blot && typeof blot.formats === 'function') {\n    formats = {\n      ...formats,\n      ...blot.formats(),\n    };\n    if (filter) {\n      // exclude syntax highlighting from deltas and getFormat()\n      delete formats['code-token'];\n    }\n  }\n  if (\n    blot.parent == null ||\n    blot.parent.statics.blotName === 'scroll' ||\n    blot.parent.statics.scope !== blot.statics.scope\n  ) {\n    return formats;\n  }\n  return bubbleFormats(blot.parent, formats, filter);\n}\n\nexport { blockDelta, bubbleFormats, BlockEmbed, Block as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAQA,IAAAC,WAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,MAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,OAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,KAAA,GAAAH,sBAAA,CAAAF,OAAA;AAEA,IAAMM,cAAc,GAAG,CAAC;AAAA,IAElBC,KAAK,GAAAC,OAAA,CAAAC,OAAA,0BAAAC,UAAA;EAAA,SAAAH,MAAA;IAAA,IAAAI,KAAA;IAAA,IAAAC,gBAAA,CAAAH,OAAA,QAAAF,KAAA;IAAA,SAAAM,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAAF,IAAA,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAAAP,KAAA,OAAAQ,WAAA,CAAAV,OAAA,QAAAF,KAAA,KAAAa,MAAA,CAAAJ,IAAA;IAAA,IAAAK,gBAAA,CAAAZ,OAAA,EAAAE,KAAA,WAC0C,CAAC,CAAC;IAAA,OAAAA,KAAA;EAAA;EAAA,IAAAW,UAAA,CAAAb,OAAA,EAAAF,KAAA,EAAAG,UAAA;EAAA,WAAAa,aAAA,CAAAd,OAAA,EAAAF,KAAA;IAAAiB,GAAA;IAAAC,KAAA,EAErD,SAAAC,KAAKA,CAAA,EAAU;MACb,IAAI,IAAI,CAACC,KAAK,CAACD,KAAK,IAAI,IAAI,EAAE;QAC5B,IAAI,CAACC,KAAK,CAACD,KAAK,GAAGE,UAAU,CAAC,IAAI,CAAC;MACrC;MACA,OAAO,IAAI,CAACD,KAAK,CAACD,KAAK;IACzB;EAAA;IAAAF,GAAA;IAAAC,KAAA,EAEA,SAAAI,QAAQA,CAACC,KAAa,EAAEf,MAAc,EAAE;MACtC,IAAAgB,cAAA,CAAAtB,OAAA,EAAAF,KAAA,wBAAeuB,KAAK,EAAEf,MAAM;MAC5B,IAAI,CAACY,KAAK,GAAG,CAAC,CAAC;IACjB;EAAA;IAAAH,GAAA;IAAAC,KAAA,EAEA,SAAAO,QAAQA,CAACF,KAAa,EAAEf,MAAc,EAAEkB,IAAY,EAAER,KAAc,EAAE;MACpE,IAAIV,MAAM,IAAI,CAAC,EAAE;MACjB,IAAI,IAAI,CAACmB,MAAM,CAACC,KAAK,CAACF,IAAI,EAAEG,gBAAK,CAACC,KAAK,CAAC,EAAE;QACxC,IAAIP,KAAK,GAAGf,MAAM,KAAK,IAAI,CAACA,MAAM,CAAC,CAAC,EAAE;UACpC,IAAI,CAACuB,MAAM,CAACL,IAAI,EAAER,KAAK,CAAC;QAC1B;MACF,CAAC,MAAM;QACL,IAAAM,cAAA,CAAAtB,OAAA,EAAAF,KAAA,wBACEuB,KAAK,EACLS,IAAI,CAACC,GAAG,CAACzB,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC,GAAGe,KAAK,GAAG,CAAC,CAAC,EAC3CG,IAAI,EACJR,KACF;MACF;MACA,IAAI,CAACE,KAAK,GAAG,CAAC,CAAC;IACjB;EAAA;IAAAH,GAAA;IAAAC,KAAA,EAEA,SAAAgB,QAAQA,CAACX,KAAa,EAAEL,KAAa,EAAEiB,GAAa,EAAE;MACpD,IAAIA,GAAG,IAAI,IAAI,EAAE;QACf,IAAAX,cAAA,CAAAtB,OAAA,EAAAF,KAAA,wBAAeuB,KAAK,EAAEL,KAAK,EAAEiB,GAAG;QAChC,IAAI,CAACf,KAAK,GAAG,CAAC,CAAC;QACf;MACF;MACA,IAAIF,KAAK,CAACV,MAAM,KAAK,CAAC,EAAE;MACxB,IAAM4B,KAAK,GAAGlB,KAAK,CAACmB,KAAK,CAAC,IAAI,CAAC;MAC/B,IAAMC,IAAI,GAAGF,KAAK,CAACG,KAAK,CAAC,CAAW;MACpC,IAAID,IAAI,CAAC9B,MAAM,GAAG,CAAC,EAAE;QACnB,IAAIe,KAAK,GAAG,IAAI,CAACf,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAACgC,QAAQ,CAACC,IAAI,IAAI,IAAI,EAAE;UAC3D,IAAAjB,cAAA,CAAAtB,OAAA,EAAAF,KAAA,wBAAegC,IAAI,CAACC,GAAG,CAACV,KAAK,EAAE,IAAI,CAACf,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE8B,IAAI;QACzD,CAAC,MAAM;UACL,IAAI,CAACE,QAAQ,CAACC,IAAI,CAACP,QAAQ,CAAC,IAAI,CAACM,QAAQ,CAACC,IAAI,CAACjC,MAAM,CAAC,CAAC,EAAE8B,IAAI,CAAC;QAChE;QACA,IAAI,CAAClB,KAAK,GAAG,CAAC,CAAC;MACjB;MACA;MACA;MACA,IAAIsB,KAAkB,GAAG,IAAI;MAC7BN,KAAK,CAACO,MAAM,CAAC,UAACC,SAAS,EAAEC,IAAI,EAAK;QAChC;QACAH,KAAK,GAAGA,KAAK,CAACL,KAAK,CAACO,SAAS,EAAE,IAAI,CAAC;QACpCF,KAAK,CAACR,QAAQ,CAAC,CAAC,EAAEW,IAAI,CAAC;QACvB,OAAOA,IAAI,CAACrC,MAAM;MACpB,CAAC,EAAEe,KAAK,GAAGe,IAAI,CAAC9B,MAAM,CAAC;IACzB;EAAA;IAAAS,GAAA;IAAAC,KAAA,EAEA,SAAA4B,YAAYA,CAACC,IAAU,EAAEC,GAAiB,EAAE;MAC1C,IAAQC,IAAA,GAAS,IAAI,CAACT,QAAQ,CAAtBS,IAAA;MACR,IAAAzB,cAAA,CAAAtB,OAAA,EAAAF,KAAA,4BAAmB+C,IAAI,EAAEC,GAAG;MAC5B,IAAIC,IAAI,YAAYC,cAAK,EAAE;QACzBD,IAAI,CAACE,MAAM,CAAC,CAAC;MACf;MACA,IAAI,CAAC/B,KAAK,GAAG,CAAC,CAAC;IACjB;EAAA;IAAAH,GAAA;IAAAC,KAAA,EAEA,SAAAV,MAAMA,CAAA,EAAG;MACP,IAAI,IAAI,CAACY,KAAK,CAACZ,MAAM,IAAI,IAAI,EAAE;QAC7B,IAAI,CAACY,KAAK,CAACZ,MAAM,GAAG,IAAAgB,cAAA,CAAAtB,OAAA,EAAAF,KAAA,2BAAiBD,cAAc;MACrD;MACA,OAAO,IAAI,CAACqB,KAAK,CAACZ,MAAM;IAC1B;EAAA;IAAAS,GAAA;IAAAC,KAAA,EAEA,SAAAkC,YAAYA,CAACC,MAAc,EAAEL,GAAiB,EAAE;MAC9C,IAAAxB,cAAA,CAAAtB,OAAA,EAAAF,KAAA,4BAAmBqD,MAAM,EAAEL,GAAG;MAC9B,IAAI,CAAC5B,KAAK,GAAG,CAAC,CAAC;IACjB;EAAA;IAAAH,GAAA;IAAAC,KAAA,EAEA,SAAAoC,QAAQA,CAACC,OAA+B,EAAE;MACxC,IAAA/B,cAAA,CAAAtB,OAAA,EAAAF,KAAA,wBAAeuD,OAAO;MACtB,IAAI,CAACnC,KAAK,GAAG,CAAC,CAAC;IACjB;EAAA;IAAAH,GAAA;IAAAC,KAAA,EAEA,SAAAsC,IAAIA,CAACjC,KAAa,EAAE;MAClB,WAAAC,cAAA,CAAAtB,OAAA,EAAAF,KAAA,oBAAkBuB,KAAK,EAAE,IAAI;IAC/B;EAAA;IAAAN,GAAA;IAAAC,KAAA,EAEA,SAAAuC,WAAWA,CAACC,KAAW,EAAE;MACvB,IAAAlC,cAAA,CAAAtB,OAAA,EAAAF,KAAA,2BAAkB0D,KAAK;MACvB,IAAI,CAACtC,KAAK,GAAG,CAAC,CAAC;IACjB;EAAA;IAAAH,GAAA;IAAAC,KAAA,EAEA,SAAAmB,KAAKA,CAACd,KAAa,EAAmD;MAAA,IAAjDoC,KAA0B,GAAApD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAqD,SAAA,GAAArD,SAAA,MAAG,KAAK;MACrD,IAAIoD,KAAK,KAAKpC,KAAK,KAAK,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACf,MAAM,CAAC,CAAC,GAAGT,cAAc,CAAC,EAAE;QACrE,IAAM8D,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC;QAC1B,IAAItC,KAAK,KAAK,CAAC,EAAE;UACf,IAAI,CAACuC,MAAM,CAAChB,YAAY,CAACe,KAAK,EAAE,IAAI,CAAC;UACrC,OAAO,IAAI;QACb;QACA,IAAI,CAACC,MAAM,CAAChB,YAAY,CAACe,KAAK,EAAE,IAAI,CAACE,IAAI,CAAC;QAC1C,OAAOF,KAAK;MACd;MACA,IAAME,IAAI,OAAAvC,cAAA,CAAAtB,OAAA,EAAAF,KAAA,qBAAeuB,KAAK,EAAEoC,KAAK,EAAC;MACtC,IAAI,CAACvC,KAAK,GAAG,CAAC,CAAC;MACf,OAAO2C,IAAI;IACb;EAAA;AAAA,EA5GkBC,oBAAS;AA8G7BhE,KAAK,CAACiE,QAAQ,GAAG,OAAO;AACxBjE,KAAK,CAACkE,OAAO,GAAG,GAAG;AACnBlE,KAAK,CAACmE,YAAY,GAAGjB,cAAK;AAC1BlD,KAAK,CAACoE,eAAe,GAAG,CAAClB,cAAK,EAAEmB,eAAM,EAAEC,oBAAS,EAAEC,aAAQ,CAAC;AAAA,IAEtDC,UAAU,GAAAvE,OAAA,CAAAuE,UAAA,0BAAAC,UAAA;EAAA,SAAAD,WAAA;IAAA,IAAAnE,gBAAA,CAAAH,OAAA,QAAAsE,UAAA;IAAA,WAAA5D,WAAA,CAAAV,OAAA,QAAAsE,UAAA,EAAAjE,SAAA;EAAA;EAAA,IAAAQ,UAAA,CAAAb,OAAA,EAAAsE,UAAA,EAAAC,UAAA;EAAA,WAAAzD,aAAA,CAAAd,OAAA,EAAAsE,UAAA;IAAAvD,GAAA;IAAAC,KAAA,EAId,SAAAwD,MAAMA,CAAA,EAAG;MACP,IAAAlD,cAAA,CAAAtB,OAAA,EAAAsE,UAAA;MACA,IAAI,CAACG,UAAU,GAAG,IAAIC,0BAAe,CAAC,IAAI,CAACC,OAAO,CAAC;IACrD;EAAA;IAAA5D,GAAA;IAAAC,KAAA,EAEA,SAAAC,KAAKA,CAAA,EAAG;MACN,OAAO,IAAI2D,mBAAK,CAAC,CAAC,CAACC,MAAM,CAAC,IAAI,CAAC7D,KAAK,CAAC,CAAC,MAAA8D,cAAA,CAAA9E,OAAA,MAAA8E,cAAA,CAAA9E,OAAA,MACjC,IAAI,CAAC+E,OAAO,CAAC,CAAC,GACd,IAAI,CAACN,UAAU,CAACO,MAAM,CAAC,EAC3B,CAAC;IACJ;EAAA;IAAAjE,GAAA;IAAAC,KAAA,EAEA,SAAAa,MAAMA,CAACL,IAAY,EAAER,KAAc,EAAE;MACnC,IAAMiE,SAAS,GAAG,IAAI,CAACxD,MAAM,CAACC,KAAK,CAACF,IAAI,EAAEG,gBAAK,CAACuD,eAAe,CAAC;MAChE,IAAID,SAAS,IAAI,IAAI,EAAE;QACrB;QACA,IAAI,CAACR,UAAU,CAACQ,SAAS,CAACA,SAAS,EAAEjE,KAAK,CAAC;MAC7C;IACF;EAAA;IAAAD,GAAA;IAAAC,KAAA,EAEA,SAAAO,QAAQA,CAACF,KAAa,EAAEf,MAAc,EAAEkB,IAAY,EAAER,KAAc,EAAE;MACpE,IAAI,CAACa,MAAM,CAACL,IAAI,EAAER,KAAK,CAAC;IAC1B;EAAA;IAAAD,GAAA;IAAAC,KAAA,EAEA,SAAAgB,QAAQA,CAACX,KAAa,EAAEL,KAAa,EAAEiB,GAAa,EAAE;MAAA,IAAAkD,MAAA;MACpD,IAAIlD,GAAG,IAAI,IAAI,EAAE;QACf,IAAAX,cAAA,CAAAtB,OAAA,EAAAsE,UAAA,wBAAejD,KAAK,EAAEL,KAAK,EAAEiB,GAAG;QAChC;MACF;MACA,IAAMC,KAAK,GAAGlB,KAAK,CAACmB,KAAK,CAAC,IAAI,CAAC;MAC/B,IAAMC,IAAI,GAAGF,KAAK,CAACkD,GAAG,CAAC,CAAC;MACxB,IAAMC,MAAM,GAAGnD,KAAK,CAACoD,GAAG,CAAE,UAAA3C,IAAI,EAAK;QACjC,IAAMH,KAAK,GAAG2C,MAAI,CAAC1D,MAAM,CAAC8D,MAAM,CAACzF,KAAK,CAACiE,QAAQ,CAAC;QAChDvB,KAAK,CAACR,QAAQ,CAAC,CAAC,EAAEW,IAAI,CAAC;QACvB,OAAOH,KAAK;MACd,CAAC,CAAC;MACF,IAAMM,GAAG,GAAG,IAAI,CAACX,KAAK,CAACd,KAAK,CAAC;MAC7BgE,MAAM,CAACG,OAAO,CAAE,UAAAhD,KAAK,EAAK;QACxB2C,MAAI,CAACvB,MAAM,CAAChB,YAAY,CAACJ,KAAK,EAAEM,GAAG,CAAC;MACtC,CAAC,CAAC;MACF,IAAIV,IAAI,EAAE;QACR,IAAI,CAACwB,MAAM,CAAChB,YAAY,CAAC,IAAI,CAACnB,MAAM,CAAC8D,MAAM,CAAC,MAAM,EAAEnD,IAAI,CAAC,EAAEU,GAAG,CAAC;MACjE;IACF;EAAA;AAAA,EA/CuBsB,oBAAS;AAiDlCE,UAAU,CAACmB,KAAK,GAAG9D,gBAAK,CAAC+D,UAAU;AACnC;;AAEA,SAASvE,UAAUA,CAAC0B,IAAe,EAAiB;EAAA,IAAf8C,MAAM,GAAAtF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAqD,SAAA,GAAArD,SAAA,MAAG,IAAI;EAChD,OAAOwC,IAAI,CACR+C,WAAW,CAACC,mBAAQ,CAAC,CACrBpD,MAAM,CAAC,UAACxB,KAAK,EAAE6E,IAAI,EAAK;IACvB,IAAIA,IAAI,CAACxF,MAAM,CAAC,CAAC,KAAK,CAAC,EAAE;MACvB,OAAOW,KAAK;IACd;IACA,OAAOA,KAAK,CAAC4D,MAAM,CAACiB,IAAI,CAAC9E,KAAK,CAAC,CAAC,EAAE+E,aAAa,CAACD,IAAI,EAAE,CAAC,CAAC,EAAEH,MAAM,CAAC,CAAC;EACpE,CAAC,EAAE,IAAIf,mBAAK,CAAC,CAAC,CAAC,CACdC,MAAM,CAAC,IAAI,EAAEkB,aAAa,CAAClD,IAAI,CAAC,CAAC;AACtC;AAEA,SAASkD,aAAaA,CACpBlD,IAAiB,EAGQ;EAAA,IAFzBkC,OAAgC,GAAA1E,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAqD,SAAA,GAAArD,SAAA,MAAG,CAAC,CAAC;EAAA,IACrCsF,MAAM,GAAAtF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAqD,SAAA,GAAArD,SAAA,MAAG,IAAI;EAEb,IAAIwC,IAAI,IAAI,IAAI,EAAE,OAAOkC,OAAO;EAChC,IAAI,SAAS,IAAIlC,IAAI,IAAI,OAAOA,IAAI,CAACkC,OAAO,KAAK,UAAU,EAAE;IAC3DA,OAAO,OAAAD,cAAA,CAAA9E,OAAA,MAAA8E,cAAA,CAAA9E,OAAA,MACF+E,OAAO,GACPlC,IAAI,CAACkC,OAAO,CAAC,EACjB;IACD,IAAIY,MAAM,EAAE;MACV;MACA,OAAOZ,OAAO,CAAC,YAAY,CAAC;IAC9B;EACF;EACA,IACElC,IAAI,CAACe,MAAM,IAAI,IAAI,IACnBf,IAAI,CAACe,MAAM,CAACoC,OAAO,CAACjC,QAAQ,KAAK,QAAQ,IACzClB,IAAI,CAACe,MAAM,CAACoC,OAAO,CAACP,KAAK,KAAK5C,IAAI,CAACmD,OAAO,CAACP,KAAK,EAChD;IACA,OAAOV,OAAO;EAChB;EACA,OAAOgB,aAAa,CAAClD,IAAI,CAACe,MAAM,EAAEmB,OAAO,EAAEY,MAAM,CAAC;AACpD", "ignoreList": []}]}