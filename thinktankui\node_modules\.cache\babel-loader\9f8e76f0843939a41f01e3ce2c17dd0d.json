{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\store\\getters.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\store\\getters.js", "mtime": 1749104047629}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749104418875}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5mdW5jdGlvbi5uYW1lLmpzIik7CnZhciBnZXR0ZXJzID0gewogIHNpZGViYXI6IGZ1bmN0aW9uIHNpZGViYXIoc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS5hcHAuc2lkZWJhcjsKICB9LAogIHNpemU6IGZ1bmN0aW9uIHNpemUoc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS5hcHAuc2l6ZTsKICB9LAogIGRldmljZTogZnVuY3Rpb24gZGV2aWNlKHN0YXRlKSB7CiAgICByZXR1cm4gc3RhdGUuYXBwLmRldmljZTsKICB9LAogIGRpY3Q6IGZ1bmN0aW9uIGRpY3Qoc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS5kaWN0LmRpY3Q7CiAgfSwKICB2aXNpdGVkVmlld3M6IGZ1bmN0aW9uIHZpc2l0ZWRWaWV3cyhzdGF0ZSkgewogICAgcmV0dXJuIHN0YXRlLnRhZ3NWaWV3LnZpc2l0ZWRWaWV3czsKICB9LAogIGNhY2hlZFZpZXdzOiBmdW5jdGlvbiBjYWNoZWRWaWV3cyhzdGF0ZSkgewogICAgcmV0dXJuIHN0YXRlLnRhZ3NWaWV3LmNhY2hlZFZpZXdzOwogIH0sCiAgdG9rZW46IGZ1bmN0aW9uIHRva2VuKHN0YXRlKSB7CiAgICByZXR1cm4gc3RhdGUudXNlci50b2tlbjsKICB9LAogIGF2YXRhcjogZnVuY3Rpb24gYXZhdGFyKHN0YXRlKSB7CiAgICByZXR1cm4gc3RhdGUudXNlci5hdmF0YXI7CiAgfSwKICBuYW1lOiBmdW5jdGlvbiBuYW1lKHN0YXRlKSB7CiAgICByZXR1cm4gc3RhdGUudXNlci5uYW1lOwogIH0sCiAgaW50cm9kdWN0aW9uOiBmdW5jdGlvbiBpbnRyb2R1Y3Rpb24oc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS51c2VyLmludHJvZHVjdGlvbjsKICB9LAogIHJvbGVzOiBmdW5jdGlvbiByb2xlcyhzdGF0ZSkgewogICAgcmV0dXJuIHN0YXRlLnVzZXIucm9sZXM7CiAgfSwKICBwZXJtaXNzaW9uczogZnVuY3Rpb24gcGVybWlzc2lvbnMoc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS51c2VyLnBlcm1pc3Npb25zOwogIH0sCiAgcGVybWlzc2lvbl9yb3V0ZXM6IGZ1bmN0aW9uIHBlcm1pc3Npb25fcm91dGVzKHN0YXRlKSB7CiAgICByZXR1cm4gc3RhdGUucGVybWlzc2lvbi5yb3V0ZXM7CiAgfSwKICB0b3BiYXJSb3V0ZXJzOiBmdW5jdGlvbiB0b3BiYXJSb3V0ZXJzKHN0YXRlKSB7CiAgICByZXR1cm4gc3RhdGUucGVybWlzc2lvbi50b3BiYXJSb3V0ZXJzOwogIH0sCiAgZGVmYXVsdFJvdXRlczogZnVuY3Rpb24gZGVmYXVsdFJvdXRlcyhzdGF0ZSkgewogICAgcmV0dXJuIHN0YXRlLnBlcm1pc3Npb24uZGVmYXVsdFJvdXRlczsKICB9LAogIHNpZGViYXJSb3V0ZXJzOiBmdW5jdGlvbiBzaWRlYmFyUm91dGVycyhzdGF0ZSkgewogICAgcmV0dXJuIHN0YXRlLnBlcm1pc3Npb24uc2lkZWJhclJvdXRlcnM7CiAgfQp9Owp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSBnZXR0ZXJzOw=="}, {"version": 3, "names": ["getters", "sidebar", "state", "app", "size", "device", "dict", "visitedViews", "tagsView", "cachedViews", "token", "user", "avatar", "name", "introduction", "roles", "permissions", "permission_routes", "permission", "routes", "topbarRouters", "defaultRoutes", "sidebarRouters", "_default", "exports", "default"], "sources": ["D:/thinktank/thinktankui/src/store/getters.js"], "sourcesContent": ["const getters = {\r\n  sidebar: state => state.app.sidebar,\r\n  size: state => state.app.size,\r\n  device: state => state.app.device,\r\n  dict: state => state.dict.dict,\r\n  visitedViews: state => state.tagsView.visitedViews,\r\n  cachedViews: state => state.tagsView.cachedViews,\r\n  token: state => state.user.token,\r\n  avatar: state => state.user.avatar,\r\n  name: state => state.user.name,\r\n  introduction: state => state.user.introduction,\r\n  roles: state => state.user.roles,\r\n  permissions: state => state.user.permissions,\r\n  permission_routes: state => state.permission.routes,\r\n  topbarRouters:state => state.permission.topbarRouters,\r\n  defaultRoutes:state => state.permission.defaultRoutes,\r\n  sidebarRouters:state => state.permission.sidebarRouters,\r\n}\r\nexport default getters\r\n"], "mappings": ";;;;;;;AAAA,IAAMA,OAAO,GAAG;EACdC,OAAO,EAAE,SAATA,OAAOA,CAAEC,KAAK;IAAA,OAAIA,KAAK,CAACC,GAAG,CAACF,OAAO;EAAA;EACnCG,IAAI,EAAE,SAANA,IAAIA,CAAEF,KAAK;IAAA,OAAIA,KAAK,CAACC,GAAG,CAACC,IAAI;EAAA;EAC7BC,MAAM,EAAE,SAARA,MAAMA,CAAEH,KAAK;IAAA,OAAIA,KAAK,CAACC,GAAG,CAACE,MAAM;EAAA;EACjCC,IAAI,EAAE,SAANA,IAAIA,CAAEJ,KAAK;IAAA,OAAIA,KAAK,CAACI,IAAI,CAACA,IAAI;EAAA;EAC9BC,YAAY,EAAE,SAAdA,YAAYA,CAAEL,KAAK;IAAA,OAAIA,KAAK,CAACM,QAAQ,CAACD,YAAY;EAAA;EAClDE,WAAW,EAAE,SAAbA,WAAWA,CAAEP,KAAK;IAAA,OAAIA,KAAK,CAACM,QAAQ,CAACC,WAAW;EAAA;EAChDC,KAAK,EAAE,SAAPA,KAAKA,CAAER,KAAK;IAAA,OAAIA,KAAK,CAACS,IAAI,CAACD,KAAK;EAAA;EAChCE,MAAM,EAAE,SAARA,MAAMA,CAAEV,KAAK;IAAA,OAAIA,KAAK,CAACS,IAAI,CAACC,MAAM;EAAA;EAClCC,IAAI,EAAE,SAANA,IAAIA,CAAEX,KAAK;IAAA,OAAIA,KAAK,CAACS,IAAI,CAACE,IAAI;EAAA;EAC9BC,YAAY,EAAE,SAAdA,YAAYA,CAAEZ,KAAK;IAAA,OAAIA,KAAK,CAACS,IAAI,CAACG,YAAY;EAAA;EAC9CC,KAAK,EAAE,SAAPA,KAAKA,CAAEb,KAAK;IAAA,OAAIA,KAAK,CAACS,IAAI,CAACI,KAAK;EAAA;EAChCC,WAAW,EAAE,SAAbA,WAAWA,CAAEd,KAAK;IAAA,OAAIA,KAAK,CAACS,IAAI,CAACK,WAAW;EAAA;EAC5CC,iBAAiB,EAAE,SAAnBA,iBAAiBA,CAAEf,KAAK;IAAA,OAAIA,KAAK,CAACgB,UAAU,CAACC,MAAM;EAAA;EACnDC,aAAa,EAAC,SAAdA,aAAaA,CAAClB,KAAK;IAAA,OAAIA,KAAK,CAACgB,UAAU,CAACE,aAAa;EAAA;EACrDC,aAAa,EAAC,SAAdA,aAAaA,CAACnB,KAAK;IAAA,OAAIA,KAAK,CAACgB,UAAU,CAACG,aAAa;EAAA;EACrDC,cAAc,EAAC,SAAfA,cAAcA,CAACpB,KAAK;IAAA,OAAIA,KAAK,CAACgB,UAAU,CAACI,cAAc;EAAA;AACzD,CAAC;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GACczB,OAAO", "ignoreList": []}]}