{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\monitor\\druid\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\monitor\\druid\\index.vue", "mtime": 1749104047642}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfaW5kZXggPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvY29tcG9uZW50cy9pRnJhbWUvaW5kZXgiKSk7Ci8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAiRHJ1aWQiLAogIGNvbXBvbmVudHM6IHsKICAgIGlGcmFtZTogX2luZGV4LmRlZmF1bHQKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB1cmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL2RydWlkL2xvZ2luLmh0bWwiCiAgICB9OwogIH0KfTs="}, {"version": 3, "names": ["_index", "_interopRequireDefault", "require", "name", "components", "iFrame", "data", "url", "process", "env", "VUE_APP_BASE_API"], "sources": ["src/views/monitor/druid/index.vue"], "sourcesContent": ["<template>\r\n  <!-- <i-frame :src=\"url\" /> -->\r\n  <div>\r\n    <div>我是数据监控</div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport iFrame from \"@/components/iFrame/index\";\r\nexport default {\r\n  name: \"Druid\",\r\n  components: { iFrame },\r\n  data() {\r\n    return {\r\n      url: process.env.VUE_APP_BASE_API + \"/druid/login.html\"\r\n    };\r\n  },\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;AAOA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;iCACA;EACAC,IAAA;EACAC,UAAA;IAAAC,MAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,GAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;IACA;EACA;AACA", "ignoreList": []}]}