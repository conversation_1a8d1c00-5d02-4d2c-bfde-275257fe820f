{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\HeaderSearch\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\HeaderSearch\\index.vue", "mtime": 1749104047621}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_fuseMin", "_interopRequireDefault", "require", "_path", "_validate", "_default", "exports", "default", "name", "data", "search", "options", "searchPool", "show", "fuse", "undefined", "computed", "routes", "$store", "getters", "permission_routes", "watch", "generateRoutes", "list", "initFuse", "value", "document", "body", "addEventListener", "close", "removeEventListener", "mounted", "methods", "click", "$refs", "headerSearchSelect", "focus", "blur", "change", "val", "_this", "path", "query", "isHttp", "pindex", "indexOf", "window", "open", "substr", "length", "$router", "push", "JSON", "parse", "$nextTick", "<PERSON><PERSON>", "shouldSort", "threshold", "location", "distance", "minMatchChar<PERSON>ength", "keys", "weight", "basePath", "arguments", "prefixTitle", "res", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "n", "done", "router", "hidden", "resolve", "title", "_toConsumableArray2", "meta", "concat", "redirect", "children", "tempRoutes", "err", "e", "f", "querySearch"], "sources": ["src/components/HeaderSearch/index.vue"], "sourcesContent": ["<template>\r\n  <div :class=\"{'show':show}\" class=\"header-search\">\r\n    <svg-icon class-name=\"search-icon\" icon-class=\"search\" @click.stop=\"click\" />\r\n    <el-select\r\n      ref=\"headerSearchSelect\"\r\n      v-model=\"search\"\r\n      :remote-method=\"querySearch\"\r\n      filterable\r\n      default-first-option\r\n      remote\r\n      placeholder=\"Search\"\r\n      class=\"header-search-select\"\r\n      @change=\"change\"\r\n    >\r\n      <el-option v-for=\"option in options\" :key=\"option.item.path\" :value=\"option.item\" :label=\"option.item.title.join(' > ')\" />\r\n    </el-select>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// fuse is a lightweight fuzzy-search module\r\n// make search results more in line with expectations\r\nimport Fuse from 'fuse.js/dist/fuse.min.js'\r\nimport path from 'path'\r\nimport { isHttp } from '@/utils/validate'\r\n\r\nexport default {\r\n  name: 'HeaderSearch',\r\n  data() {\r\n    return {\r\n      search: '',\r\n      options: [],\r\n      searchPool: [],\r\n      show: false,\r\n      fuse: undefined\r\n    }\r\n  },\r\n  computed: {\r\n    routes() {\r\n      return this.$store.getters.permission_routes\r\n    }\r\n  },\r\n  watch: {\r\n    routes() {\r\n      this.searchPool = this.generateRoutes(this.routes)\r\n    },\r\n    searchPool(list) {\r\n      this.initFuse(list)\r\n    },\r\n    show(value) {\r\n      if (value) {\r\n        document.body.addEventListener('click', this.close)\r\n      } else {\r\n        document.body.removeEventListener('click', this.close)\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.searchPool = this.generateRoutes(this.routes)\r\n  },\r\n  methods: {\r\n    click() {\r\n      this.show = !this.show\r\n      if (this.show) {\r\n        this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.focus()\r\n      }\r\n    },\r\n    close() {\r\n      this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.blur()\r\n      this.options = []\r\n      this.show = false\r\n    },\r\n    change(val) {\r\n      const path = val.path;\r\n      const query = val.query;\r\n      if(isHttp(val.path)) {\r\n        // http(s):// 路径新窗口打开\r\n        const pindex = path.indexOf(\"http\");\r\n        window.open(path.substr(pindex, path.length), \"_blank\");\r\n      } else {\r\n        if (query) {\r\n          this.$router.push({ path: path, query: JSON.parse(query) });\r\n        } else {\r\n          this.$router.push(path)\r\n        }\r\n      }\r\n      this.search = ''\r\n      this.options = []\r\n      this.$nextTick(() => {\r\n        this.show = false\r\n      })\r\n    },\r\n    initFuse(list) {\r\n      this.fuse = new Fuse(list, {\r\n        shouldSort: true,\r\n        threshold: 0.4,\r\n        location: 0,\r\n        distance: 100,\r\n        minMatchCharLength: 1,\r\n        keys: [{\r\n          name: 'title',\r\n          weight: 0.7\r\n        }, {\r\n          name: 'path',\r\n          weight: 0.3\r\n        }]\r\n      })\r\n    },\r\n    // Filter out the routes that can be displayed in the sidebar\r\n    // And generate the internationalized title\r\n    generateRoutes(routes, basePath = '/', prefixTitle = []) {\r\n      let res = []\r\n\r\n      for (const router of routes) {\r\n        // skip hidden router\r\n        if (router.hidden) { continue }\r\n\r\n        const data = {\r\n          path: !isHttp(router.path) ? path.resolve(basePath, router.path) : router.path,\r\n          title: [...prefixTitle]\r\n        }\r\n\r\n        if (router.meta && router.meta.title) {\r\n          data.title = [...data.title, router.meta.title]\r\n\r\n          if (router.redirect !== 'noRedirect') {\r\n            // only push the routes with title\r\n            // special case: need to exclude parent router without redirect\r\n            res.push(data)\r\n          }\r\n        }\r\n\r\n        if (router.query) {\r\n          data.query = router.query\r\n        }\r\n\r\n        // recursive child routes\r\n        if (router.children) {\r\n          const tempRoutes = this.generateRoutes(router.children, data.path, data.title)\r\n          if (tempRoutes.length >= 1) {\r\n            res = [...res, ...tempRoutes]\r\n          }\r\n        }\r\n      }\r\n      return res\r\n    },\r\n    querySearch(query) {\r\n      if (query !== '') {\r\n        this.options = this.fuse.search(query)\r\n      } else {\r\n        this.options = []\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.header-search {\r\n  font-size: 0 !important;\r\n\r\n  .search-icon {\r\n    cursor: pointer;\r\n    font-size: 18px;\r\n    vertical-align: middle;\r\n  }\r\n\r\n  .header-search-select {\r\n    font-size: 18px;\r\n    transition: width 0.2s;\r\n    width: 0;\r\n    overflow: hidden;\r\n    background: transparent;\r\n    border-radius: 0;\r\n    display: inline-block;\r\n    vertical-align: middle;\r\n\r\n    ::v-deep .el-input__inner {\r\n      border-radius: 0;\r\n      border: 0;\r\n      padding-left: 0;\r\n      padding-right: 0;\r\n      box-shadow: none !important;\r\n      border-bottom: 1px solid #d9d9d9;\r\n      vertical-align: middle;\r\n    }\r\n  }\r\n\r\n  &.show {\r\n    .header-search-select {\r\n      width: 210px;\r\n      margin-left: 10px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;AAsBA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;AAJA;AACA;AAAA,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAKA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA;MACAC,OAAA;MACAC,UAAA;MACAC,IAAA;MACAC,IAAA,EAAAC;IACA;EACA;EACAC,QAAA;IACAC,MAAA,WAAAA,OAAA;MACA,YAAAC,MAAA,CAAAC,OAAA,CAAAC,iBAAA;IACA;EACA;EACAC,KAAA;IACAJ,MAAA,WAAAA,OAAA;MACA,KAAAL,UAAA,QAAAU,cAAA,MAAAL,MAAA;IACA;IACAL,UAAA,WAAAA,WAAAW,IAAA;MACA,KAAAC,QAAA,CAAAD,IAAA;IACA;IACAV,IAAA,WAAAA,KAAAY,KAAA;MACA,IAAAA,KAAA;QACAC,QAAA,CAAAC,IAAA,CAAAC,gBAAA,eAAAC,KAAA;MACA;QACAH,QAAA,CAAAC,IAAA,CAAAG,mBAAA,eAAAD,KAAA;MACA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAnB,UAAA,QAAAU,cAAA,MAAAL,MAAA;EACA;EACAe,OAAA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAApB,IAAA,SAAAA,IAAA;MACA,SAAAA,IAAA;QACA,KAAAqB,KAAA,CAAAC,kBAAA,SAAAD,KAAA,CAAAC,kBAAA,CAAAC,KAAA;MACA;IACA;IACAP,KAAA,WAAAA,MAAA;MACA,KAAAK,KAAA,CAAAC,kBAAA,SAAAD,KAAA,CAAAC,kBAAA,CAAAE,IAAA;MACA,KAAA1B,OAAA;MACA,KAAAE,IAAA;IACA;IACAyB,MAAA,WAAAA,OAAAC,GAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,IAAA,GAAAF,GAAA,CAAAE,IAAA;MACA,IAAAC,KAAA,GAAAH,GAAA,CAAAG,KAAA;MACA,QAAAC,gBAAA,EAAAJ,GAAA,CAAAE,IAAA;QACA;QACA,IAAAG,MAAA,GAAAH,IAAA,CAAAI,OAAA;QACAC,MAAA,CAAAC,IAAA,CAAAN,IAAA,CAAAO,MAAA,CAAAJ,MAAA,EAAAH,IAAA,CAAAQ,MAAA;MACA;QACA,IAAAP,KAAA;UACA,KAAAQ,OAAA,CAAAC,IAAA;YAAAV,IAAA,EAAAA,IAAA;YAAAC,KAAA,EAAAU,IAAA,CAAAC,KAAA,CAAAX,KAAA;UAAA;QACA;UACA,KAAAQ,OAAA,CAAAC,IAAA,CAAAV,IAAA;QACA;MACA;MACA,KAAA/B,MAAA;MACA,KAAAC,OAAA;MACA,KAAA2C,SAAA;QACAd,KAAA,CAAA3B,IAAA;MACA;IACA;IACAW,QAAA,WAAAA,SAAAD,IAAA;MACA,KAAAT,IAAA,OAAAyC,gBAAA,CAAAhC,IAAA;QACAiC,UAAA;QACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,kBAAA;QACAC,IAAA;UACArD,IAAA;UACAsD,MAAA;QACA;UACAtD,IAAA;UACAsD,MAAA;QACA;MACA;IACA;IACA;IACA;IACAxC,cAAA,WAAAA,eAAAL,MAAA;MAAA,IAAA8C,QAAA,GAAAC,SAAA,CAAAf,MAAA,QAAAe,SAAA,QAAAjD,SAAA,GAAAiD,SAAA;MAAA,IAAAC,WAAA,GAAAD,SAAA,CAAAf,MAAA,QAAAe,SAAA,QAAAjD,SAAA,GAAAiD,SAAA;MACA,IAAAE,GAAA;MAAA,IAAAC,SAAA,OAAAC,2BAAA,CAAA7D,OAAA,EAEAU,MAAA;QAAAoD,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;UAAA,IAAAC,MAAA,GAAAJ,KAAA,CAAA5C,KAAA;UACA;UACA,IAAAgD,MAAA,CAAAC,MAAA;YAAA;UAAA;UAEA,IAAAjE,IAAA;YACAgC,IAAA,OAAAE,gBAAA,EAAA8B,MAAA,CAAAhC,IAAA,IAAAA,aAAA,CAAAkC,OAAA,CAAAZ,QAAA,EAAAU,MAAA,CAAAhC,IAAA,IAAAgC,MAAA,CAAAhC,IAAA;YACAmC,KAAA,MAAAC,mBAAA,CAAAtE,OAAA,EAAA0D,WAAA;UACA;UAEA,IAAAQ,MAAA,CAAAK,IAAA,IAAAL,MAAA,CAAAK,IAAA,CAAAF,KAAA;YACAnE,IAAA,CAAAmE,KAAA,MAAAG,MAAA,KAAAF,mBAAA,CAAAtE,OAAA,EAAAE,IAAA,CAAAmE,KAAA,IAAAH,MAAA,CAAAK,IAAA,CAAAF,KAAA;YAEA,IAAAH,MAAA,CAAAO,QAAA;cACA;cACA;cACAd,GAAA,CAAAf,IAAA,CAAA1C,IAAA;YACA;UACA;UAEA,IAAAgE,MAAA,CAAA/B,KAAA;YACAjC,IAAA,CAAAiC,KAAA,GAAA+B,MAAA,CAAA/B,KAAA;UACA;;UAEA;UACA,IAAA+B,MAAA,CAAAQ,QAAA;YACA,IAAAC,UAAA,QAAA5D,cAAA,CAAAmD,MAAA,CAAAQ,QAAA,EAAAxE,IAAA,CAAAgC,IAAA,EAAAhC,IAAA,CAAAmE,KAAA;YACA,IAAAM,UAAA,CAAAjC,MAAA;cACAiB,GAAA,MAAAa,MAAA,KAAAF,mBAAA,CAAAtE,OAAA,EAAA2D,GAAA,OAAAW,mBAAA,CAAAtE,OAAA,EAAA2E,UAAA;YACA;UACA;QACA;MAAA,SAAAC,GAAA;QAAAhB,SAAA,CAAAiB,CAAA,CAAAD,GAAA;MAAA;QAAAhB,SAAA,CAAAkB,CAAA;MAAA;MACA,OAAAnB,GAAA;IACA;IACAoB,WAAA,WAAAA,YAAA5C,KAAA;MACA,IAAAA,KAAA;QACA,KAAA/B,OAAA,QAAAG,IAAA,CAAAJ,MAAA,CAAAgC,KAAA;MACA;QACA,KAAA/B,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}