{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\Crontab\\hour.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\Crontab\\hour.vue", "mtime": 1749104047618}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["hour.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "hour.vue", "sourceRoot": "src/components/Crontab", "sourcesContent": ["<template>\r\n\t<el-form size=\"small\">\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio v-model='radioValue' :label=\"1\">\r\n\t\t\t\t小时，允许的通配符[, - * /]\r\n\t\t\t</el-radio>\r\n\t\t</el-form-item>\r\n\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio v-model='radioValue' :label=\"2\">\r\n\t\t\t\t周期从\r\n\t\t\t\t<el-input-number v-model='cycle01' :min=\"0\" :max=\"22\" /> -\r\n\t\t\t\t<el-input-number v-model='cycle02' :min=\"cycle01 ? cycle01 + 1 : 1\" :max=\"23\" /> 小时\r\n\t\t\t</el-radio>\r\n\t\t</el-form-item>\r\n\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio v-model='radioValue' :label=\"3\">\r\n\t\t\t\t从\r\n\t\t\t\t<el-input-number v-model='average01' :min=\"0\" :max=\"22\" /> 小时开始，每\r\n\t\t\t\t<el-input-number v-model='average02' :min=\"1\" :max=\"23 - average01 || 0\" /> 小时执行一次\r\n\t\t\t</el-radio>\r\n\t\t</el-form-item>\r\n\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio v-model='radioValue' :label=\"4\">\r\n\t\t\t\t指定\r\n\t\t\t\t<el-select clearable v-model=\"checkboxList\" placeholder=\"可多选\" multiple style=\"width:100%\">\r\n\t\t\t\t\t<el-option v-for=\"item in 24\" :key=\"item\" :value=\"item-1\">{{item-1}}</el-option>\r\n\t\t\t\t</el-select>\r\n\t\t\t</el-radio>\r\n\t\t</el-form-item>\r\n\t</el-form>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tradioValue: 1,\r\n\t\t\tcycle01: 0,\r\n\t\t\tcycle02: 1,\r\n\t\t\taverage01: 0,\r\n\t\t\taverage02: 1,\r\n\t\t\tcheckboxList: [],\r\n\t\t\tcheckNum: this.$options.propsData.check\r\n\t\t}\r\n\t},\r\n\tname: 'crontab-hour',\r\n\tprops: ['check', 'cron'],\r\n\tmethods: {\r\n\t\t// 单选按钮值变化时\r\n\t\tradioChange() {\r\n\t\t\tif (this.cron.min === '*') {\r\n\t\t\t    this.$emit('update', 'min', '0', 'hour');\r\n\t\t\t}\r\n\t\t\tif (this.cron.second === '*') {\r\n\t\t\t    this.$emit('update', 'second', '0', 'hour');\r\n\t\t\t}\r\n\t\t\tswitch (this.radioValue) {\r\n\t\t\t\tcase 1:\r\n\t\t\t\t\tthis.$emit('update', 'hour', '*')\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 2:\r\n\t\t\t\t\tthis.$emit('update', 'hour', this.cycleTotal);\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 3:\r\n\t\t\t\t\tthis.$emit('update', 'hour', this.averageTotal);\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 4:\r\n\t\t\t\t\tthis.$emit('update', 'hour', this.checkboxString);\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 周期两个值变化时\r\n\t\tcycleChange() {\r\n\t\t\tif (this.radioValue == '2') {\r\n\t\t\t\tthis.$emit('update', 'hour', this.cycleTotal);\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 平均两个值变化时\r\n\t\taverageChange() {\r\n\t\t\tif (this.radioValue == '3') {\r\n\t\t\t\tthis.$emit('update', 'hour', this.averageTotal);\r\n\t\t\t}\r\n\t\t},\r\n\t\t// checkbox值变化时\r\n\t\tcheckboxChange() {\r\n\t\t\tif (this.radioValue == '4') {\r\n\t\t\t\tthis.$emit('update', 'hour', this.checkboxString);\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\twatch: {\r\n\t\t'radioValue': 'radioChange',\r\n\t\t'cycleTotal': 'cycleChange',\r\n\t\t'averageTotal': 'averageChange',\r\n\t\t'checkboxString': 'checkboxChange'\r\n\t},\r\n\tcomputed: {\r\n\t\t// 计算两个周期值\r\n\t\tcycleTotal: function () {\r\n\t\t\tconst cycle01 = this.checkNum(this.cycle01, 0, 22)\r\n\t\t\tconst cycle02 = this.checkNum(this.cycle02, cycle01 ? cycle01 + 1 : 1, 23)\r\n\t\t\treturn cycle01 + '-' + cycle02;\r\n\t\t},\r\n\t\t// 计算平均用到的值\r\n\t\taverageTotal: function () {\r\n\t\t\tconst average01 = this.checkNum(this.average01, 0, 22)\r\n\t\t\tconst average02 = this.checkNum(this.average02, 1, 23 - average01 || 0)\r\n\t\t\treturn average01 + '/' + average02;\r\n\t\t},\r\n\t\t// 计算勾选的checkbox值合集\r\n\t\tcheckboxString: function () {\r\n\t\t\tlet str = this.checkboxList.join();\r\n\t\t\treturn str == '' ? '*' : str;\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n"]}]}