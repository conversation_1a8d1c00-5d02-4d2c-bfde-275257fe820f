{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\info-summary\\index.vue?vue&type=style&index=0&id=7e184638&lang=scss&scoped=true", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\info-summary\\index.vue", "mtime": 1749104047640}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749104419119}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749104421224}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749104419872}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749104418479}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYXBwLWNvbnRhaW5lciB7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgaGVpZ2h0OiAxMDB2aDsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2YwZjJmNTsNCn0NCg0KLy8g5pON5L2c5oyJ6ZKu5Yy65Z+f5qC35byPDQouYWN0aW9uLWJ1dHRvbnMgew0KICBwb3NpdGlvbjogYWJzb2x1dGU7DQogIHRvcDogMTVweDsNCiAgbGVmdDogMTVweDsNCiAgei1pbmRleDogMTA7DQogIGRpc3BsYXk6IGZsZXg7DQogIGdhcDogMTBweDsNCn0NCg0KLmluZm8tc3VtbWFyeS1jb250YWluZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBoZWlnaHQ6IDEwMCU7DQogIHBhZGRpbmctdG9wOiA2MHB4OyAvLyDkuLrmlrDlu7rmlrnmoYjmjInpkq7nlZnlh7rnqbrpl7QNCn0NCg0KLy8g5bem5L6n5a+86Iiq5qCP5qC35byPDQoubGVmdC1zaWRlYmFyIHsNCiAgd2lkdGg6IDE4MHB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICBib3JkZXItcmlnaHQ6IDFweCBzb2xpZCAjZThlOGU4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBvdmVyZmxvdy15OiBhdXRvOw0KfQ0KDQouc2lkZWJhci1oZWFkZXIgew0KICBwYWRkaW5nOiAxNXB4Ow0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U4ZThlODsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KfQ0KDQouc2lkZWJhci1tZW51IHsNCiAgZmxleDogMTsNCn0NCg0KLmVsLW1lbnUtdmVydGljYWwgew0KICBib3JkZXItcmlnaHQ6IG5vbmU7DQp9DQoNCi8vIOWPs+S+p+WGheWuueWMuuWfn+agt+W8jw0KLnJpZ2h0LWNvbnRlbnQgew0KICBmbGV4OiAxOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KICBtYXJnaW46IDAgMTVweDsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBib3gtc2hhZG93OiAwIDFweCA0cHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KfQ0KDQouY29udGVudC1oZWFkZXIgew0KICBwYWRkaW5nOiAxNXB4Ow0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U4ZThlODsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KfQ0KDQouZW50aXR5LXRpdGxlIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBmb250LXdlaWdodDogYm9sZDsNCn0NCg0KLmVudGl0eS1uYW1lIHsNCiAgbWFyZ2luLXJpZ2h0OiA1cHg7DQp9DQoNCi8vIOagh+etvumhteagt+W8jw0KLnRhYnMtY29udGFpbmVyIHsNCiAgcGFkZGluZzogMTBweCAxNXB4Ow0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U4ZThlODsNCn0NCg0KLmZpbHRlci10YWJzIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLy8g562b6YCJ5Yy65Z+f5qC35byPDQouZmlsdGVyLXNlY3Rpb24gew0KICBwYWRkaW5nOiAxNXB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjlmOWY5Ow0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U4ZThlODsNCn0NCg0KLmZpbHRlci1yb3cgew0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsNCn0NCg0KLmZpbHRlci1yb3c6bGFzdC1jaGlsZCB7DQogIG1hcmdpbi1ib3R0b206IDA7DQp9DQoNCi5maWx0ZXItbGFiZWwgew0KICB3aWR0aDogODBweDsNCiAgY29sb3I6ICM2MDYyNjY7DQogIGxpbmUtaGVpZ2h0OiAyOHB4Ow0KfQ0KDQouZmlsdGVyLWFjdGlvbnMgew0KICBtYXJnaW4tdG9wOiAxMHB4Ow0KICBwYWRkaW5nLWxlZnQ6IDgwcHg7DQp9DQoNCi8vIOaTjeS9nOagj+agt+W8jw0KLmFjdGlvbi1iYXIgew0KICBwYWRkaW5nOiAxMHB4IDE1cHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOGU4ZTg7DQp9DQoNCi5sZWZ0LWFjdGlvbnMsIC5yaWdodC1hY3Rpb25zIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLnNlYXJjaC1pbnB1dCB7DQogIHdpZHRoOiAyMDBweDsNCiAgbWFyZ2luLXJpZ2h0OiAxMHB4Ow0KfQ0KDQovLyDkv6Hmga/liJfooajmoLflvI8NCi5pbmZvLWxpc3Qgew0KICBmbGV4OiAxOw0KICBvdmVyZmxvdy15OiBhdXRvOw0KICBwYWRkaW5nOiAwOw0KfQ0KDQouaW5mby1pdGVtIHsNCiAgcGFkZGluZzogMTVweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlYmVlZjU7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0Ow0KfQ0KDQouaXRlbS1jaGVja2JveCB7DQogIG1hcmdpbi1yaWdodDogMTBweDsNCiAgbWFyZ2luLXRvcDogM3B4Ow0KfQ0KDQouaW5mby1jb250ZW50IHsNCiAgZmxleDogMTsNCn0NCg0KLmluZm8taGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQp9DQoNCi5pbmZvLXRpdGxlIHsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBmb250LXdlaWdodDogNTAwOw0KICBjb2xvcjogIzMwMzEzMzsNCn0NCg0KLmluZm8tc3VtbWFyeSB7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQogIGxpbmUtaGVpZ2h0OiAxLjU7DQp9DQoNCi5pbmZvLWZvb3RlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KICBmb250LXNpemU6IDEzcHg7DQp9DQoNCi5pbmZvLXNvdXJjZSwgLmluZm8tdGltZSwgLmluZm8tc2VudGltZW50LCAuaW5mby12aWV3cywgLmluZm8tY29tbWVudHMgew0KICBtYXJnaW4tcmlnaHQ6IDE1cHg7DQp9DQoNCi5pbmZvLWluZGV4IHsNCiAgbWFyZ2luLWxlZnQ6IGF1dG87DQp9DQoNCi5zZW50aW1lbnQtcG9zaXRpdmUgew0KICBjb2xvcjogIzY3YzIzYTsNCn0NCg0KLnNlbnRpbWVudC1uZXV0cmFsIHsNCiAgY29sb3I6ICM5MDkzOTk7DQp9DQoNCi5zZW50aW1lbnQtbmVnYXRpdmUgew0KICBjb2xvcjogI2Y1NmM2YzsNCn0NCg0KLmluZm8taW1hZ2VzIHsNCiAgZGlzcGxheTogZmxleDsNCiAgbWFyZ2luLXRvcDogMTBweDsNCn0NCg0KLmluZm8taW1hZ2Ugew0KICB3aWR0aDogODBweDsNCiAgaGVpZ2h0OiA4MHB4Ow0KICBvYmplY3QtZml0OiBjb3ZlcjsNCiAgbWFyZ2luLXJpZ2h0OiAxMHB4Ow0KICBib3JkZXItcmFkaXVzOiA0cHg7DQp9DQoNCi8vIOmrmOS6ruagt+W8jw0KOmRlZXAoLmhpZ2hsaWdodCkgew0KICBjb2xvcjogIzQwOWVmZjsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQp9DQoNCi8vIOaWsOW7uuaWueahiOWvueivneahhuagt+W8jw0KLmNyZWF0ZS1wbGFuLWRpYWxvZyB7DQogIC5lbC1kaWFsb2dfX2hlYWRlciB7DQogICAgcGFkZGluZzogMTVweCAyMHB4Ow0KICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZThlOGU4Ow0KICB9DQoNCiAgLmVsLXRhYnNfX2hlYWRlciB7DQogICAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgfQ0KDQogIC5lbC10YWJzX19uYXYgew0KICAgIHdpZHRoOiAxMDAlOw0KICB9DQoNCiAgLmVsLXRhYnNfX2l0ZW0gew0KICAgIGZsZXg6IDE7DQogICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgIGhlaWdodDogNDBweDsNCiAgICBsaW5lLWhlaWdodDogNDBweDsNCiAgfQ0KDQogIC5lbC1mb3JtLWl0ZW0gew0KICAgIG1hcmdpbi1ib3R0b206IDE1cHg7DQogIH0NCn0NCg0KLm1vbml0b3Itb2JqZWN0LXNlbGVjdCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCg0KICAuZWwtZHJvcGRvd24gew0KICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICByaWdodDogMTBweDsNCiAgICB0b3A6IDA7DQogICAgaGVpZ2h0OiAxMDAlOw0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBjdXJzb3I6IHBvaW50ZXI7DQogIH0NCn0NCg0KLmxvY2F0aW9uLXNlbGVjdCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi5sb2NhdGlvbi1idG4gew0KICBtYXJnaW4tbGVmdDogMTBweDsNCn0NCg0KLmluZHVzdHJ5LXNlbGVjdC1idG4gew0KICB3aWR0aDogMTAwJTsNCiAgdGV4dC1hbGlnbjogbGVmdDsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7DQogIHdoaXRlLXNwYWNlOiBub3dyYXA7DQogIHBhZGRpbmc6IDhweCAxNXB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjZGNkZmU2Ow0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBjdXJzb3I6IHBvaW50ZXI7DQogIHRyYW5zaXRpb246IGFsbCAwLjNzOw0KDQogICY6aG92ZXIgew0KICAgIGJvcmRlci1jb2xvcjogI2MwYzRjYzsNCiAgfQ0KfQ0KDQoudGhlbWUtcm93LCAuaW5kdXN0cnktcm93IHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC13cmFwOiB3cmFwOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KfQ0KDQoudGhlbWUtaW5wdXQsIC5pbmR1c3RyeS1pbnB1dCB7DQogIHdpZHRoOiAxMDBweDsNCiAgbWFyZ2luLWxlZnQ6IDEwcHg7DQogIHZlcnRpY2FsLWFsaWduOiBib3R0b207DQp9DQoNCi50aGVtZS1idXR0b24sIC5pbmR1c3RyeS1idXR0b24gew0KICBtYXJnaW4tbGVmdDogMTBweDsNCiAgaGVpZ2h0OiAzMnB4Ow0KICBsaW5lLWhlaWdodDogMzBweDsNCiAgcGFkZGluZy10b3A6IDA7DQogIHBhZGRpbmctYm90dG9tOiAwOw0KfQ0KDQouZWwtdGFnIHsNCiAgbWFyZ2luLXJpZ2h0OiAxMHB4Ow0KICBtYXJnaW4tYm90dG9tOiA1cHg7DQp9DQoNCi5jaGFubmVscy1yb3cgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KDQogIC5lbC1jaGVja2JveCB7DQogICAgbWFyZ2luLXJpZ2h0OiAyMHB4Ow0KICB9DQoNCiAgJjpsYXN0LWNoaWxkIHsNCiAgICBtYXJnaW4tYm90dG9tOiAwOw0KICB9DQp9DQoNCi8vIOWIhumhteWuueWZqOagt+W8jw0KLnBhZ2luYXRpb24tY29udGFpbmVyIHsNCiAgcGFkZGluZzogMTVweDsNCiAgdGV4dC1hbGlnbjogcmlnaHQ7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZThlOGU4Ow0KfQ0KDQovLyDooYzkuJrliIbnsbvlvLnnqpfmoLflvI8NCi5pbmR1c3RyeS1kaWFsb2cgew0KICAuZWwtZGlhbG9nX19oZWFkZXIgew0KICAgIHBhZGRpbmc6IDE1cHggMjBweDsNCiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U4ZThlODsNCiAgfQ0KDQogIC5pbmR1c3RyeS1kaWFsb2ctY29udGVudCB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBoZWlnaHQ6IDQwMHB4Ow0KICB9DQoNCiAgLmluZHVzdHJ5LXRyZWUtY29udGFpbmVyIHsNCiAgICBmbGV4OiAxOw0KICAgIHBhZGRpbmc6IDE1cHg7DQogICAgYm9yZGVyLXJpZ2h0OiAxcHggc29saWQgI2U4ZThlODsNCiAgICBvdmVyZmxvdy15OiBhdXRvOw0KICB9DQoNCiAgLmluZHVzdHJ5LXNlbGVjdGVkLWNvbnRhaW5lciB7DQogICAgd2lkdGg6IDIwMHB4Ow0KICAgIHBhZGRpbmc6IDE1cHg7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICB9DQoNCiAgLmluZHVzdHJ5LXNlbGVjdGVkLXRpdGxlIHsNCiAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICB9DQp9DQoNCi8vIOWPkemAgemihOitpuW8ueeql+agt+W8jw0KLnNlbmQtYWxlcnQtZGlhbG9nIHsNCiAgLmVsLWRpYWxvZ19faGVhZGVyIHsNCiAgICBwYWRkaW5nOiAxNXB4IDIwcHg7DQogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOGU4ZTg7DQogIH0NCg0KICAuc2VuZC1hbGVydC1jb250ZW50IHsNCiAgICBwYWRkaW5nOiAyMHB4Ow0KICB9DQoNCiAgLnJlY2VpdmVyLWxpc3Qgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICBnYXA6IDEwcHg7DQogIH0NCg0KICAucmVjZWl2ZXItaXRlbSB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICB9DQoNCiAgLnJlY2VpdmVyLXR5cGUgew0KICAgIHdpZHRoOiA4MHB4Ow0KICAgIG1hcmdpbi1yaWdodDogMTBweDsNCiAgfQ0KDQogIC5yZWNlaXZlci1zZWxlY3Qgew0KICAgIHdpZHRoOiAxMDAlOw0KICB9DQp9DQoNCi8vIOato+mdouaDheaEn+W8ueeql+agt+W8jw0KLnBvc2l0aXZlLXNlbnRpbWVudC1kaWFsb2cgew0KICAuZWwtZGlhbG9nX19oZWFkZXIgew0KICAgIHBhZGRpbmc6IDE1cHggMjBweDsNCiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U4ZThlODsNCiAgfQ0KICAuZWwtZGlhbG9nX19ib2R5IHsNCiAgICBwYWRkaW5nOiAyMHB4Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA02BA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/info-summary", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 操作按钮区域 -->\r\n    <div class=\"action-buttons\">\r\n      <el-button type=\"primary\" icon=\"el-icon-message\" @click=\"showSendAlertDialog\">发送预警</el-button>\r\n      <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"showCreatePlanDialog\">新建方案</el-button>\r\n      <el-button type=\"primary\" icon=\"el-icon-folder-add\" @click=\"showAddToAlertMaterialDialog\">加入至报告素材</el-button>\r\n      <el-button type=\"primary\" icon=\"el-icon-share\" @click=\"showInfoGraphDialog\">信息图谱</el-button>\r\n      <el-button type=\"primary\" icon=\"el-icon-document-checked\" @click=\"showOriginalProofreadingDialog\">原稿校对</el-button>\r\n    </div>\r\n\r\n    <div class=\"info-summary-container\">\r\n      <!-- 左侧导航栏 -->\r\n      <div class=\"left-sidebar\">\r\n        <div class=\"sidebar-header\">\r\n          <span class=\"sidebar-title\">方太</span>\r\n          <i class=\"el-icon-arrow-right\"></i>\r\n        </div>\r\n        <div class=\"sidebar-menu\">\r\n          <el-menu\r\n            :default-active=\"activeMenu\"\r\n            class=\"el-menu-vertical\"\r\n          >\r\n            <el-menu-item index=\"1\" @click=\"handleMenuClick('总览')\">\r\n              <i class=\"el-icon-s-home\"></i>\r\n              <span slot=\"title\">总览</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"2\" @click=\"handleMenuClick('品牌')\">\r\n              <i class=\"el-icon-s-goods\"></i>\r\n              <span slot=\"title\">品牌(1)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"3\" @click=\"handleMenuClick('人物')\">\r\n              <i class=\"el-icon-user\"></i>\r\n              <span slot=\"title\">人物(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"4\" @click=\"handleMenuClick('机构')\">\r\n              <i class=\"el-icon-office-building\"></i>\r\n              <span slot=\"title\">机构(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"5\" @click=\"handleMenuClick('产品')\">\r\n              <i class=\"el-icon-shopping-bag-1\"></i>\r\n              <span slot=\"title\">产品(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"6\" @click=\"handleMenuClick('事件')\">\r\n              <i class=\"el-icon-bell\"></i>\r\n              <span slot=\"title\">事件(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"7\" @click=\"handleMenuClick('话题')\">\r\n              <i class=\"el-icon-chat-dot-square\"></i>\r\n              <span slot=\"title\">话题(0)</span>\r\n            </el-menu-item>\r\n          </el-menu>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧内容区域 -->\r\n      <div class=\"right-content\">\r\n        <!-- 标题和操作区域 -->\r\n        <div class=\"content-header\">\r\n          <div class=\"entity-title\">\r\n            <span class=\"entity-name\">方太</span>\r\n            <i class=\"el-icon-arrow-right\"></i>\r\n          </div>\r\n          <div class=\"view-actions\">\r\n            <el-button-group>\r\n              <el-button size=\"small\" type=\"primary\" icon=\"el-icon-s-grid\"></el-button>\r\n              <el-button size=\"small\" icon=\"el-icon-menu\"></el-button>\r\n              <el-button size=\"small\" icon=\"el-icon-s-unfold\"></el-button>\r\n            </el-button-group>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 标签页 -->\r\n        <div class=\"tabs-container\">\r\n          <div class=\"filter-tabs\">\r\n            <el-radio-group v-model=\"activeTab\" size=\"small\">\r\n              <el-radio-button label=\"today\">今天</el-radio-button>\r\n              <el-radio-button label=\"yesterday\">昨天</el-radio-button>\r\n              <el-radio-button label=\"before_yesterday\">前天</el-radio-button>\r\n              <el-radio-button label=\"earlier\">更早</el-radio-button>\r\n              <el-radio-button label=\"custom\">自定义</el-radio-button>\r\n            </el-radio-group>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 信息类型筛选 -->\r\n        <div class=\"filter-section\">\r\n          <div class=\"filter-row\">\r\n            <span class=\"filter-label\">平台分类:</span>\r\n            <el-checkbox-group v-model=\"platformTypes\" size=\"small\">\r\n              <el-checkbox label=\"news\">新闻 (46/217)</el-checkbox>\r\n              <el-checkbox label=\"weibo\">微博 (5/34)</el-checkbox>\r\n              <el-checkbox label=\"wechat\">微信 (14/54)</el-checkbox>\r\n              <el-checkbox label=\"video\">视频 (2/78)</el-checkbox>\r\n              <el-checkbox label=\"app\">APP (1/29)</el-checkbox>\r\n              <el-checkbox label=\"forum\">论坛 (0/9)</el-checkbox>\r\n              <el-checkbox label=\"ecommerce\">电商 (44/446)</el-checkbox>\r\n              <el-checkbox label=\"qa\">问答 (6/21)</el-checkbox>\r\n              <el-checkbox label=\"other\">其他 (1/2)</el-checkbox>\r\n            </el-checkbox-group>\r\n          </div>\r\n\r\n          <div class=\"filter-row\">\r\n            <span class=\"filter-label\">情感倾向:</span>\r\n            <el-checkbox-group v-model=\"sentimentTypes\" size=\"small\">\r\n              <el-checkbox label=\"positive\">正面 (46/217)</el-checkbox>\r\n              <el-checkbox label=\"neutral\">中性 (7/8)</el-checkbox>\r\n              <el-checkbox label=\"negative\">负面 (3/57)</el-checkbox>\r\n              <el-checkbox label=\"other\">其他 (1/3)</el-checkbox>\r\n            </el-checkbox-group>\r\n          </div>\r\n\r\n          <div class=\"filter-row\">\r\n            <span class=\"filter-label\">信息属性:</span>\r\n            <el-checkbox-group v-model=\"infoAttributes\" size=\"small\">\r\n              <el-checkbox label=\"official\">官方发布</el-checkbox>\r\n              <el-checkbox label=\"media\">媒体报道</el-checkbox>\r\n              <el-checkbox label=\"user\">用户评价</el-checkbox>\r\n              <el-checkbox label=\"competitor\">竞品信息</el-checkbox>\r\n              <el-checkbox label=\"industry\">行业动态</el-checkbox>\r\n              <el-checkbox label=\"policy\">政策法规</el-checkbox>\r\n            </el-checkbox-group>\r\n          </div>\r\n\r\n          <div class=\"filter-actions\">\r\n            <el-button size=\"small\" type=\"primary\">筛选</el-button>\r\n            <el-button size=\"small\">重置</el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 操作栏 -->\r\n        <div class=\"action-bar\">\r\n          <div class=\"left-actions\">\r\n            <el-button size=\"small\" type=\"primary\">全选</el-button>\r\n            <el-button size=\"small\">导出</el-button>\r\n          </div>\r\n          <div class=\"right-actions\">\r\n            <el-input\r\n              placeholder=\"搜索关键词\"\r\n              prefix-icon=\"el-icon-search\"\r\n              v-model=\"searchKeyword\"\r\n              size=\"small\"\r\n              clearable\r\n              class=\"search-input\"\r\n              @keyup.enter=\"handleSearchKeyword\"\r\n            >\r\n              <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"handleSearchKeyword\"></el-button>\r\n            </el-input>\r\n            <el-dropdown size=\"small\" split-button type=\"primary\" @command=\"handleCommand\">\r\n              排序\r\n              <el-dropdown-menu slot=\"dropdown\">\r\n                <el-dropdown-item command=\"time_desc\">时间降序</el-dropdown-item>\r\n                <el-dropdown-item command=\"time_asc\">时间升序</el-dropdown-item>\r\n                <el-dropdown-item command=\"relevance\">相关性</el-dropdown-item>\r\n              </el-dropdown-menu>\r\n            </el-dropdown>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 信息列表 -->\r\n        <div class=\"info-list\">\r\n          <div v-for=\"(item, index) in paginatedList\" :key=\"index\" class=\"info-item\">\r\n            <el-checkbox v-model=\"item.selected\" class=\"item-checkbox\"></el-checkbox>\r\n            <div class=\"info-content\">\r\n              <div class=\"info-header\">\r\n                <div class=\"info-title\" v-html=\"item.title\"></div>\r\n                <div class=\"info-actions\">\r\n                  <el-button type=\"text\" icon=\"el-icon-star-off\"></el-button>\r\n                  <el-button type=\"text\" icon=\"el-icon-share\"></el-button>\r\n                  <el-button type=\"text\" icon=\"el-icon-more\"></el-button>\r\n                </div>\r\n              </div>\r\n              <div class=\"info-summary\" v-html=\"item.content\"></div>\r\n              <div class=\"info-footer\">\r\n                <span class=\"info-source\">{{ item.source }}</span>\r\n                <span class=\"info-time\">{{ item.time }}</span>\r\n                <span class=\"info-sentiment\" :class=\"'sentiment-' + item.sentiment\">\r\n                  <el-button\r\n                    :type=\"item.sentiment === 'positive' ? 'success' : item.sentiment === 'negative' ? 'danger' : 'info'\"\r\n                    size=\"mini\"\r\n                    @click=\"handleSentimentClick(item)\"\r\n                  >\r\n                    <i :class=\"getSentimentIcon(item.sentiment)\"></i>\r\n                    {{ getSentimentText(item.sentiment) }}\r\n                  </el-button>\r\n                </span>\r\n                <span class=\"info-views\">\r\n                  <i class=\"el-icon-view\"></i> {{ item.views }}\r\n                </span>\r\n                <span class=\"info-comments\">\r\n                  <i class=\"el-icon-chat-line-square\"></i> {{ item.comments }}\r\n                </span>\r\n                <span class=\"info-index\">{{ (currentPage - 1) * pageSize + index + 1 }}</span>\r\n              </div>\r\n              <div class=\"info-images\" v-if=\"item.images && item.images.length > 0\">\r\n                <img v-for=\"(img, imgIndex) in item.images\" :key=\"imgIndex\" :src=\"img\" class=\"info-image\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-container\">\r\n          <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :current-page=\"currentPage\"\r\n            :page-sizes=\"[10, 20, 30, 50]\"\r\n            :page-size=\"pageSize\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"totalItems\"\r\n            background\r\n          ></el-pagination>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 新建方案对话框 -->\r\n    <el-dialog\r\n      title=\"新建方案\"\r\n      :visible.sync=\"createPlanDialogVisible\"\r\n      width=\"50%\"\r\n      :close-on-click-modal=\"false\"\r\n      :close-on-press-escape=\"false\"\r\n      append-to-body\r\n      custom-class=\"create-plan-dialog\"\r\n    >\r\n      <el-tabs v-model=\"planActiveTab\">\r\n        <el-tab-pane label=\"监测方式\" name=\"standard\">\r\n          <el-form :model=\"planForm\" label-width=\"70px\" size=\"small\">\r\n            <el-form-item label=\"方案名称\">\r\n              <el-input v-model=\"planForm.name\" placeholder=\"请输入方案名称\"></el-input>\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"作用范围\">\r\n              <el-select v-model=\"planForm.scope\" placeholder=\"请选择\" style=\"width: 100%\">\r\n                <el-option label=\"全部\" value=\"all\"></el-option>\r\n                <el-option label=\"选项1\" value=\"option1\"></el-option>\r\n                <el-option label=\"选项2\" value=\"option2\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"监测对象\">\r\n              <div class=\"monitor-object-select\">\r\n                <el-input v-model=\"planForm.monitorObject\" placeholder=\"请输入监测对象\"></el-input>\r\n                <el-dropdown trigger=\"click\" @command=\"handleMonitorObjectCommand\">\r\n                  <span class=\"el-dropdown-link\">\r\n                    <i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n                  </span>\r\n                  <el-dropdown-menu slot=\"dropdown\">\r\n                    <el-dropdown-item command=\"option1\">选项1</el-dropdown-item>\r\n                    <el-dropdown-item command=\"option2\">选项2</el-dropdown-item>\r\n                    <el-dropdown-item command=\"option3\">选项3</el-dropdown-item>\r\n                  </el-dropdown-menu>\r\n                </el-dropdown>\r\n              </div>\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"地域\">\r\n              <div class=\"location-select\">\r\n                <el-input v-model=\"planForm.location\" placeholder=\"请选择地域\" readonly></el-input>\r\n                <el-button type=\"text\" class=\"location-btn\" @click=\"showLocationMap\">\r\n                  <i class=\"el-icon-location\"></i>\r\n                </el-button>\r\n              </div>\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"主题\">\r\n              <div class=\"theme-row\">\r\n                <el-tag\r\n                  v-for=\"(tag, index) in planForm.themes\"\r\n                  :key=\"index\"\r\n                  closable\r\n                  @close=\"handleRemoveTheme(tag)\"\r\n                >\r\n                  {{ tag }}\r\n                </el-tag>\r\n                <el-input\r\n                  class=\"theme-input\"\r\n                  v-if=\"themeInputVisible\"\r\n                  v-model=\"themeInputValue\"\r\n                  ref=\"themeInput\"\r\n                  size=\"small\"\r\n                  @keyup.enter.native=\"handleAddTheme\"\r\n                  @blur=\"handleAddTheme\"\r\n                >\r\n                </el-input>\r\n                <el-button v-else class=\"theme-button\" size=\"small\" @click=\"showThemeInput\">+ 添加主题</el-button>\r\n              </div>\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"行业分类\">\r\n              <div class=\"industry-row\">\r\n                <el-tag\r\n                  v-if=\"planForm.industry\"\r\n                  closable\r\n                  @close=\"planForm.industry = ''\"\r\n                >\r\n                  {{ planForm.industry }}\r\n                </el-tag>\r\n                <el-button v-if=\"!planForm.industry\" class=\"industry-button\" size=\"small\" @click=\"showIndustrySelect\">+ 添加行业分类</el-button>\r\n              </div>\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"时间段\">\r\n              <el-date-picker\r\n                v-model=\"planForm.timeRange\"\r\n                type=\"daterange\"\r\n                range-separator=\"至\"\r\n                start-placeholder=\"开始日期\"\r\n                end-placeholder=\"结束日期\"\r\n                style=\"width: 100%\"\r\n              >\r\n              </el-date-picker>\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"渠道\">\r\n              <el-checkbox-group v-model=\"planForm.channels\">\r\n                <div class=\"channels-row\">\r\n                  <el-checkbox label=\"news\">新闻</el-checkbox>\r\n                  <el-checkbox label=\"weibo\">微博</el-checkbox>\r\n                  <el-checkbox label=\"wechat\">微信</el-checkbox>\r\n                </div>\r\n                <div class=\"channels-row\">\r\n                  <el-checkbox label=\"video\">视频</el-checkbox>\r\n                  <el-checkbox label=\"app\">APP</el-checkbox>\r\n                  <el-checkbox label=\"forum\">论坛</el-checkbox>\r\n                </div>\r\n              </el-checkbox-group>\r\n            </el-form-item>\r\n          </el-form>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"高级方式\" name=\"advanced\">\r\n          <el-form :model=\"advancedPlanForm\" label-width=\"70px\" size=\"small\">\r\n            <!-- 高级模式的表单内容 -->\r\n            <el-form-item label=\"方案名称\">\r\n              <el-input v-model=\"advancedPlanForm.name\" placeholder=\"请输入方案名称\"></el-input>\r\n            </el-form-item>\r\n\r\n            <!-- 其他高级选项 -->\r\n          </el-form>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"createPlanDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"savePlan\">保存</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 行业分类弹窗 -->\r\n    <el-dialog\r\n      title=\"行业分类\"\r\n      :visible.sync=\"industryDialogVisible\"\r\n      width=\"40%\"\r\n      :close-on-click-modal=\"true\"\r\n      :close-on-press-escape=\"true\"\r\n      append-to-body\r\n      custom-class=\"industry-dialog\"\r\n    >\r\n      <div class=\"industry-dialog-content\">\r\n        <div class=\"industry-tree-container\">\r\n          <el-tree\r\n            :data=\"industryTreeData\"\r\n            :props=\"industryTreeProps\"\r\n            node-key=\"id\"\r\n            default-expand-all\r\n            highlight-current\r\n            @node-click=\"handleIndustryNodeClick\"\r\n          />\r\n        </div>\r\n        <div class=\"industry-selected-container\">\r\n          <div class=\"industry-selected-title\">\r\n            {{ selectedIndustry ? selectedIndustry.label : '请选择行业分类' }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"industryDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmIndustrySelect\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 发送预警弹窗 -->\r\n    <el-dialog\r\n      title=\"发送预警\"\r\n      :visible.sync=\"sendAlertDialogVisible\"\r\n      width=\"40%\"\r\n      :close-on-click-modal=\"true\"\r\n      :close-on-press-escape=\"true\"\r\n      append-to-body\r\n      custom-class=\"send-alert-dialog\"\r\n    >\r\n      <div class=\"send-alert-content\">\r\n        <el-form :model=\"alertForm\" label-width=\"80px\" size=\"small\">\r\n          <el-form-item label=\"预警标题\">\r\n            <el-input v-model=\"alertForm.title\" placeholder=\"请输入预警标题\"></el-input>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"接收人\">\r\n            <div class=\"receiver-list\">\r\n              <div class=\"receiver-item\" v-for=\"(receiver, index) in receivers\" :key=\"index\">\r\n                <div class=\"receiver-type\">{{ receiver.type }}</div>\r\n                <el-select\r\n                  v-model=\"alertForm.selectedReceivers[index]\"\r\n                  :placeholder=\"'请选择' + receiver.type\"\r\n                  class=\"receiver-select\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"person in receiver.persons\"\r\n                    :key=\"person\"\r\n                    :label=\"person\"\r\n                    :value=\"person\"\r\n                  ></el-option>\r\n                </el-select>\r\n              </div>\r\n            </div>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelSendAlert\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmSendAlert\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 正面情感信息弹窗 -->\r\n    <el-dialog\r\n      title=\"情感属性纠错\"\r\n      :visible.sync=\"positiveSentimentDialogVisible\"\r\n      width=\"50%\"\r\n      append-to-body\r\n      custom-class=\"positive-sentiment-dialog\"\r\n    >\r\n      <el-radio-group v-model=\"selectedSentiment\" size=\"small\">\r\n        <el-radio-button label=\"positive\">正面</el-radio-button>\r\n        <el-radio-button label=\"neutral\">中性</el-radio-button>\r\n        <el-radio-button label=\"negative\">负面</el-radio-button>\r\n      </el-radio-group>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"positiveSentimentDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"handlePositiveDialogConfirm\">确定</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 加入至报告素材对话框 -->\r\n    <el-dialog\r\n      title=\"加入至报告素材\"\r\n      :visible.sync=\"addToAlertMaterialDialogVisible\"\r\n      width=\"30%\"\r\n      :close-on-click-modal=\"false\"\r\n      :close-on-press-escape=\"false\"\r\n      append-to-body\r\n      custom-class=\"add-to-alert-material-dialog\"\r\n    >\r\n      <div class=\"add-to-alert-material-content\">\r\n        <el-form label-width=\"80px\" size=\"small\">\r\n          <el-form-item label=\"选择素材库\">\r\n            <el-select v-model=\"selectedMaterialLibrary\" placeholder=\"请选择素材库\" style=\"width: 100%\">\r\n              <!-- 这里需要根据实际数据填充选项 -->\r\n              <el-option label=\"素材库1\" value=\"library1\"></el-option>\r\n              <el-option label=\"素材库2\" value=\"library2\"></el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelAddToAlertMaterial\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmAddToAlertMaterial\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'InfoSummary',\r\n  computed: {\r\n    // 根据当前页码和每页显示数量计算当前页的数据\r\n    paginatedList() {\r\n      const start = (this.currentPage - 1) * this.pageSize;\r\n      // 实际应用中，这里应该是从后端获取分页数据\r\n      // 这里为了演示，我们从本地数据中截取一部分\r\n      return this.infoList.slice(0, Math.min(this.infoList.length, this.pageSize));\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // 页面基础数据\r\n      originalTopNav: undefined, // 存储原始的topNav状态\r\n      searchKeyword: '',\r\n      positiveSentimentDialogVisible: false, // 控制正面情感弹窗的显示\r\n      selectedSentiment: '', // 用于情感属性纠错弹窗的选中值\r\n      editingItem: null, // 当前编辑的情感条目\r\n      activeMenu: '2', // 默认选中品牌\r\n      activeTab: 'today', // 默认选中今天\r\n      platformTypes: ['news', 'weibo', 'wechat', 'video', 'app'], // 默认选中的平台类型\r\n      sentimentTypes: ['positive', 'neutral'], // 默认选中的情感类型\r\n      infoAttributes: ['official', 'media'], // 默认选中的信息属性\r\n\r\n      // 分页相关数据\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      totalItems: 120, // 假设总共有120条数据\r\n\r\n      // 新建方案对话框相关数据\r\n      createPlanDialogVisible: false,\r\n      planActiveTab: 'standard',\r\n      themeInputVisible: false,\r\n      themeInputValue: '',\r\n      planForm: {\r\n        name: '',\r\n        scope: 'all',\r\n        monitorObject: '',\r\n        location: '',\r\n        themes: [],\r\n        industry: '',\r\n        timeRange: '',\r\n        channels: ['news', 'weibo', 'wechat']\r\n      },\r\n      advancedPlanForm: {\r\n        name: ''\r\n      },\r\n\r\n      // 行业分类弹窗相关数据\r\n      industryDialogVisible: false,\r\n      selectedIndustry: null,\r\n      industryTreeProps: {\r\n        label: 'label',\r\n        children: 'children'\r\n      },\r\n\r\n      // 发送预警弹窗相关数据\r\n      sendAlertDialogVisible: false,\r\n      addToAlertMaterialDialogVisible: false, // 新增：控制加入报告素材弹窗的显示\r\n      alertForm: {\r\n        title: '',\r\n        selectedReceivers: ['', '', '', '', '', '']\r\n      },\r\n      receivers: [\r\n        {\r\n          type: '总监',\r\n          persons: ['王总监', '李总监', '张总监']\r\n        },\r\n        {\r\n          type: '经理',\r\n          persons: ['王经理', '李经理', '张经理']\r\n        },\r\n        {\r\n          type: '主管',\r\n          persons: ['王主管', '李主管', '张主管']\r\n        },\r\n        {\r\n          type: '员工',\r\n          persons: ['王员工', '李员工', '张员工']\r\n        },\r\n        {\r\n          type: '外部人员',\r\n          persons: ['外部人员1', '外部人员2', '外部人员3']\r\n        },\r\n        {\r\n          type: '其他',\r\n          persons: ['其他人员1', '其他人员2', '其他人员3']\r\n        }\r\n      ],\r\n      industryTreeData: [\r\n        {\r\n          id: 1,\r\n          label: '制造',\r\n          children: []\r\n        },\r\n        {\r\n          id: 2,\r\n          label: '公共',\r\n          children: []\r\n        },\r\n        {\r\n          id: 3,\r\n          label: '教育',\r\n          children: []\r\n        },\r\n        {\r\n          id: 4,\r\n          label: '工业设备',\r\n          children: []\r\n        },\r\n        {\r\n          id: 5,\r\n          label: '环保设备',\r\n          children: []\r\n        },\r\n        {\r\n          id: 6,\r\n          label: '金融',\r\n          children: []\r\n        },\r\n        {\r\n          id: 7,\r\n          label: '商业',\r\n          children: []\r\n        },\r\n        {\r\n          id: 8,\r\n          label: '民用与商用',\r\n          children: []\r\n        },\r\n        {\r\n          id: 9,\r\n          label: '政府部门',\r\n          children: []\r\n        }\r\n      ],\r\n\r\n      // 信息列表数据\r\n      infoList: [\r\n        {\r\n          selected: false,\r\n          title: '新，<span class=\"highlight\">高价值联名</span>打造<span class=\"highlight\">方太</span>(Fotile)厨电新品牌',\r\n          content: '据<span class=\"highlight\">方太</span>(Fotile)官方消息，<span class=\"highlight\">方太</span>与设计师联名打造了一系列高端厨电产品，引领厨房新潮流...',\r\n          source: '新浪财经',\r\n          time: '2023-04-29 20:27:51',\r\n          sentiment: 'positive',\r\n          views: 6437,\r\n          comments: 89,\r\n          images: []\r\n        },\r\n        {\r\n          selected: false,\r\n          title: '招聘一位【<span class=\"highlight\">方太</span>集团(Fotile)-111 <span class=\"highlight\">方太</span>集团(Fotile)-400.6808.655，一键呼叫，服务响应一一】的<span class=\"highlight\">方太</span>集团(Fotile)维修师傅',\r\n          content: '招聘一位【<span class=\"highlight\">方太</span>集团(Fotile)官方认证的<span class=\"highlight\">方太</span>集团(Fotile)-111<span class=\"highlight\">方太</span>集团(Fotile)400.6808.655，一键呼叫，服务响应一一】的<span class=\"highlight\">方太</span>集团(Fotile)维修师傅...',\r\n          source: '头条新闻',\r\n          time: '2023-04-29 20:24:22',\r\n          sentiment: 'neutral',\r\n          views: 5321,\r\n          comments: 42,\r\n          images: ['/images/sample1.jpg', '/images/sample2.jpg']\r\n        },\r\n        {\r\n          selected: false,\r\n          title: '<span class=\"highlight\">方太</span>集团(Fotile)官方服务中心-<span class=\"highlight\">方太</span>集团(Fotile)-111 <span class=\"highlight\">方太</span>集团(Fotile) 400.6808.655，一键呼叫，服务响应一一的<span class=\"highlight\">方太</span>集团(Fotile)维修师傅',\r\n          content: '<span class=\"highlight\">方太</span>集团(Fotile)官方服务中心-<span class=\"highlight\">方太</span>集团(Fotile)-111<span class=\"highlight\">方太</span>集团(Fotile)400.6808.655，<span class=\"highlight\">方太</span>集团(Fotile)维修部。可预约上门，一站式解决<span class=\"highlight\">方太</span>厨电故障问题...',\r\n          source: '今日头条',\r\n          time: '2023-04-29 20:14:03',\r\n          sentiment: 'neutral',\r\n          views: 3642,\r\n          comments: 33,\r\n          images: ['/images/sample3.jpg', '/images/sample4.jpg']\r\n        }\r\n      ]\r\n    };\r\n  },\r\n  mounted() {\r\n    // 隐藏顶部导航栏\r\n    this.originalTopNav = this.$store.state.settings.topNav\r\n    this.$store.dispatch('settings/changeSetting', {\r\n      key: 'topNav',\r\n      value: false\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    // 恢复顶部导航栏设置\r\n    if (this.originalTopNav !== undefined) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'topNav',\r\n        value: this.originalTopNav\r\n      })\r\n    }\r\n  },\r\n  methods: {\r\n\r\n    // 菜单点击事件\r\n    handleMenuClick(menuName) {\r\n      console.log('选择菜单:', menuName);\r\n    },\r\n\r\n    // 获取情感图标\r\n    getSentimentIcon(sentiment) {\r\n      const icons = {\r\n        positive: 'el-icon-sunny',\r\n        neutral: 'el-icon-partly-cloudy',\r\n        negative: 'el-icon-cloudy'\r\n      };\r\n      return icons[sentiment] || 'el-icon-question';\r\n    },\r\n\r\n    // 获取情感文本\r\n    getSentimentText(sentiment) {\r\n      const texts = {\r\n        positive: '正面',\r\n        neutral: '中性',\r\n        negative: '负面'\r\n      };\r\n      return texts[sentiment] || '未知';\r\n    },\r\n\r\n    // 下拉菜单命令处理\r\n    handleCommand(command) {\r\n      console.log('执行命令:', command);\r\n    },\r\n\r\n    // 处理搜索关键词\r\n    handleSearchKeyword() {\r\n      if (this.searchKeyword.trim()) {\r\n        // 跳转到搜索结果页面，并传递搜索关键词\r\n        this.$router.push({\r\n          path: '/search-results',\r\n          query: {\r\n            q: this.searchKeyword.trim()\r\n          }\r\n        });\r\n      } else {\r\n        this.$message.warning('请输入搜索关键词');\r\n      }\r\n    },\r\n\r\n    // 分页相关方法\r\n    handleSizeChange(size) {\r\n      this.pageSize = size;\r\n      this.currentPage = 1; // 切换每页显示数量时，重置为第一页\r\n      console.log('每页显示数量:', size);\r\n    },\r\n\r\n    handleCurrentChange(page) {\r\n      this.currentPage = page;\r\n      console.log('当前页码:', page);\r\n    },\r\n\r\n    // 新建方案相关方法\r\n    showCreatePlanDialog() {\r\n      this.createPlanDialogVisible = true;\r\n    },\r\n\r\n    // 主题标签相关方法\r\n    handleRemoveTheme(tag) {\r\n      this.planForm.themes.splice(this.planForm.themes.indexOf(tag), 1);\r\n    },\r\n\r\n    showThemeInput() {\r\n      this.themeInputVisible = true;\r\n      this.$nextTick(_ => {\r\n        this.$refs.themeInput.$refs.input.focus();\r\n      });\r\n    },\r\n\r\n    handleAddTheme() {\r\n      let inputValue = this.themeInputValue;\r\n      if (inputValue) {\r\n        if (!this.planForm.themes.includes(inputValue)) {\r\n          this.planForm.themes.push(inputValue);\r\n        }\r\n      }\r\n      this.themeInputVisible = false;\r\n      this.themeInputValue = '';\r\n    },\r\n\r\n    // 监测对象下拉菜单命令处理\r\n    handleMonitorObjectCommand(command) {\r\n      this.planForm.monitorObject = command;\r\n    },\r\n\r\n    // 显示地域选择地图\r\n    showLocationMap() {\r\n      // 这里可以实现地图选择功能\r\n      this.$message.info('显示地域选择地图');\r\n    },\r\n\r\n    // 显示行业分类选择\r\n    showIndustrySelect() {\r\n      this.industryDialogVisible = true;\r\n    },\r\n\r\n    // 处理行业分类树节点点击\r\n    handleIndustryNodeClick(data) {\r\n      this.selectedIndustry = data;\r\n    },\r\n\r\n    // 确认行业分类选择\r\n    confirmIndustrySelect() {\r\n      if (this.selectedIndustry) {\r\n        this.planForm.industry = this.selectedIndustry.label;\r\n      }\r\n      this.industryDialogVisible = false;\r\n    },\r\n\r\n    // 保存方案\r\n    savePlan() {\r\n      // 根据当前激活的标签页选择不同的表单数据\r\n      const formData = this.planActiveTab === 'standard' ? this.planForm : this.advancedPlanForm;\r\n\r\n      console.log('保存方案:', formData);\r\n      // 这里可以添加表单验证和提交到后端的逻辑\r\n\r\n      // 关闭对话框\r\n      this.createPlanDialogVisible = false;\r\n\r\n      // 重置表单\r\n      if (this.planActiveTab === 'standard') {\r\n        this.planForm = {\r\n          name: '',\r\n          scope: 'all',\r\n          monitorObject: '',\r\n          location: '',\r\n          themes: [],\r\n          industry: '',\r\n          timeRange: '',\r\n          channels: ['news', 'weibo', 'wechat']\r\n        };\r\n      } else {\r\n        this.advancedPlanForm = {\r\n          name: ''\r\n        };\r\n      }\r\n    },\r\n\r\n    // 显示发送预警弹窗\r\n    showSendAlertDialog() {\r\n      this.sendAlertDialogVisible = true;\r\n    },\r\n\r\n    // 取消发送预警\r\n    cancelSendAlert() {\r\n      this.sendAlertDialogVisible = false;\r\n      // 重置表单\r\n      this.alertForm = {\r\n        title: '',\r\n        selectedReceivers: ['', '', '', '', '', '']\r\n      };\r\n    },\r\n    handleSentimentClick(item) {\r\n      this.editingItem = item;\r\n      this.selectedSentiment = item.sentiment;\r\n      this.positiveSentimentDialogVisible = true;\r\n    },\r\n    handlePositiveDialogConfirm() {\r\n      if (this.editingItem) {\r\n        this.editingItem.sentiment = this.selectedSentiment;\r\n        // 在实际应用中，这里可能需要调用API将更改保存到后端\r\n        // 例如: this.updateSentimentApi(this.editingItem.id, this.selectedSentiment);\r\n      }\r\n      this.positiveSentimentDialogVisible = false;\r\n    },\r\n\r\n    // 确认发送预警\r\n    confirmSendAlert() {\r\n      // 在这里处理发送预警的逻辑\r\n      console.log('发送预警:', this.alertForm);\r\n      this.sendAlertDialogVisible = false;\r\n      // 清空表单\r\n      this.alertForm = {\r\n        title: '',\r\n        selectedReceivers: ['', '', '', '', '', '']\r\n      };\r\n    },\r\n\r\n    // 加入至报告素材对话框相关方法\r\n    showAddToAlertMaterialDialog() {\r\n      this.addToAlertMaterialDialogVisible = true;\r\n    },\r\n    cancelAddToAlertMaterial() {\r\n      this.addToAlertMaterialDialogVisible = false;\r\n      this.selectedMaterialLibrary = ''; // 清空选中值\r\n    },\r\n    confirmAddToAlertMaterial() {\r\n      // 这里添加确认逻辑，例如提交选中的素材库\r\n      console.log('Selected Material Library:', this.selectedMaterialLibrary);\r\n      this.addToAlertMaterialDialogVisible = false;\r\n      this.selectedMaterialLibrary = ''; // 清空选中值\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  position: relative;\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  background-color: #f0f2f5;\r\n}\r\n\r\n// 操作按钮区域样式\r\n.action-buttons {\r\n  position: absolute;\r\n  top: 15px;\r\n  left: 15px;\r\n  z-index: 10;\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.info-summary-container {\r\n  display: flex;\r\n  height: 100%;\r\n  padding-top: 60px; // 为新建方案按钮留出空间\r\n}\r\n\r\n// 左侧导航栏样式\r\n.left-sidebar {\r\n  width: 180px;\r\n  background-color: #fff;\r\n  border-right: 1px solid #e8e8e8;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow-y: auto;\r\n}\r\n\r\n.sidebar-header {\r\n  padding: 15px;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.sidebar-menu {\r\n  flex: 1;\r\n}\r\n\r\n.el-menu-vertical {\r\n  border-right: none;\r\n}\r\n\r\n// 右侧内容区域样式\r\n.right-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #fff;\r\n  overflow: hidden;\r\n  margin: 0 15px;\r\n  border-radius: 4px;\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.content-header {\r\n  padding: 15px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.entity-title {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n}\r\n\r\n.entity-name {\r\n  margin-right: 5px;\r\n}\r\n\r\n// 标签页样式\r\n.tabs-container {\r\n  padding: 10px 15px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.filter-tabs {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n// 筛选区域样式\r\n.filter-section {\r\n  padding: 15px;\r\n  background-color: #f9f9f9;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.filter-row {\r\n  margin-bottom: 10px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.filter-row:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.filter-label {\r\n  width: 80px;\r\n  color: #606266;\r\n  line-height: 28px;\r\n}\r\n\r\n.filter-actions {\r\n  margin-top: 10px;\r\n  padding-left: 80px;\r\n}\r\n\r\n// 操作栏样式\r\n.action-bar {\r\n  padding: 10px 15px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.left-actions, .right-actions {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.search-input {\r\n  width: 200px;\r\n  margin-right: 10px;\r\n}\r\n\r\n// 信息列表样式\r\n.info-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 0;\r\n}\r\n\r\n.info-item {\r\n  padding: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.item-checkbox {\r\n  margin-right: 10px;\r\n  margin-top: 3px;\r\n}\r\n\r\n.info-content {\r\n  flex: 1;\r\n}\r\n\r\n.info-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.info-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n\r\n.info-summary {\r\n  color: #606266;\r\n  margin-bottom: 8px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.info-footer {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #909399;\r\n  font-size: 13px;\r\n}\r\n\r\n.info-source, .info-time, .info-sentiment, .info-views, .info-comments {\r\n  margin-right: 15px;\r\n}\r\n\r\n.info-index {\r\n  margin-left: auto;\r\n}\r\n\r\n.sentiment-positive {\r\n  color: #67c23a;\r\n}\r\n\r\n.sentiment-neutral {\r\n  color: #909399;\r\n}\r\n\r\n.sentiment-negative {\r\n  color: #f56c6c;\r\n}\r\n\r\n.info-images {\r\n  display: flex;\r\n  margin-top: 10px;\r\n}\r\n\r\n.info-image {\r\n  width: 80px;\r\n  height: 80px;\r\n  object-fit: cover;\r\n  margin-right: 10px;\r\n  border-radius: 4px;\r\n}\r\n\r\n// 高亮样式\r\n:deep(.highlight) {\r\n  color: #409eff;\r\n  font-weight: bold;\r\n}\r\n\r\n// 新建方案对话框样式\r\n.create-plan-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n\r\n  .el-tabs__header {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .el-tabs__nav {\r\n    width: 100%;\r\n  }\r\n\r\n  .el-tabs__item {\r\n    flex: 1;\r\n    text-align: center;\r\n    height: 40px;\r\n    line-height: 40px;\r\n  }\r\n\r\n  .el-form-item {\r\n    margin-bottom: 15px;\r\n  }\r\n}\r\n\r\n.monitor-object-select {\r\n  display: flex;\r\n  align-items: center;\r\n  position: relative;\r\n\r\n  .el-dropdown {\r\n    position: absolute;\r\n    right: 10px;\r\n    top: 0;\r\n    height: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n.location-select {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.location-btn {\r\n  margin-left: 10px;\r\n}\r\n\r\n.industry-select-btn {\r\n  width: 100%;\r\n  text-align: left;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  padding: 8px 15px;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  color: #606266;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n\r\n  &:hover {\r\n    border-color: #c0c4cc;\r\n  }\r\n}\r\n\r\n.theme-row, .industry-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n}\r\n\r\n.theme-input, .industry-input {\r\n  width: 100px;\r\n  margin-left: 10px;\r\n  vertical-align: bottom;\r\n}\r\n\r\n.theme-button, .industry-button {\r\n  margin-left: 10px;\r\n  height: 32px;\r\n  line-height: 30px;\r\n  padding-top: 0;\r\n  padding-bottom: 0;\r\n}\r\n\r\n.el-tag {\r\n  margin-right: 10px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.channels-row {\r\n  display: flex;\r\n  margin-bottom: 10px;\r\n\r\n  .el-checkbox {\r\n    margin-right: 20px;\r\n  }\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n// 分页容器样式\r\n.pagination-container {\r\n  padding: 15px;\r\n  text-align: right;\r\n  background-color: #fff;\r\n  border-top: 1px solid #e8e8e8;\r\n}\r\n\r\n// 行业分类弹窗样式\r\n.industry-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n\r\n  .industry-dialog-content {\r\n    display: flex;\r\n    height: 400px;\r\n  }\r\n\r\n  .industry-tree-container {\r\n    flex: 1;\r\n    padding: 15px;\r\n    border-right: 1px solid #e8e8e8;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .industry-selected-container {\r\n    width: 200px;\r\n    padding: 15px;\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n\r\n  .industry-selected-title {\r\n    font-weight: bold;\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n\r\n// 发送预警弹窗样式\r\n.send-alert-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n\r\n  .send-alert-content {\r\n    padding: 20px;\r\n  }\r\n\r\n  .receiver-list {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n\r\n  .receiver-item {\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n\r\n  .receiver-type {\r\n    width: 80px;\r\n    margin-right: 10px;\r\n  }\r\n\r\n  .receiver-select {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n// 正面情感弹窗样式\r\n.positive-sentiment-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n  .el-dialog__body {\r\n    padding: 20px;\r\n  }\r\n}\r\n</style>\r\n\r\n<el-button type=\"success\" size=\"mini\" @click=\"handlePositiveClick\">正面</el-button>,\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  position: relative;\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  background-color: #f0f2f5;\r\n}\r\n\r\n// 操作按钮区域样式\r\n.action-buttons {\r\n  position: absolute;\r\n  top: 15px;\r\n  left: 15px;\r\n  z-index: 10;\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.info-summary-container {\r\n  display: flex;\r\n  height: 100%;\r\n  padding-top: 60px; // 为新建方案按钮留出空间\r\n}\r\n\r\n// 左侧导航栏样式\r\n.left-sidebar {\r\n  width: 180px;\r\n  background-color: #fff;\r\n  border-right: 1px solid #e8e8e8;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow-y: auto;\r\n}\r\n\r\n.sidebar-header {\r\n  padding: 15px;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.sidebar-menu {\r\n  flex: 1;\r\n}\r\n\r\n.el-menu-vertical {\r\n  border-right: none;\r\n}\r\n\r\n// 右侧内容区域样式\r\n.right-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #fff;\r\n  overflow: hidden;\r\n  margin: 0 15px;\r\n  border-radius: 4px;\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.content-header {\r\n  padding: 15px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.entity-title {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n}\r\n\r\n.entity-name {\r\n  margin-right: 5px;\r\n}\r\n\r\n// 标签页样式\r\n.tabs-container {\r\n  padding: 10px 15px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.filter-tabs {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n// 筛选区域样式\r\n.filter-section {\r\n  padding: 15px;\r\n  background-color: #f9f9f9;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.filter-row {\r\n  margin-bottom: 10px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.filter-row:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.filter-label {\r\n  width: 80px;\r\n  color: #606266;\r\n  line-height: 28px;\r\n}\r\n\r\n.filter-actions {\r\n  margin-top: 10px;\r\n  padding-left: 80px;\r\n}\r\n\r\n// 操作栏样式\r\n.action-bar {\r\n  padding: 10px 15px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.left-actions, .right-actions {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.search-input {\r\n  width: 200px;\r\n  margin-right: 10px;\r\n}\r\n\r\n// 信息列表样式\r\n.info-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 0;\r\n}\r\n\r\n.info-item {\r\n  padding: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.item-checkbox {\r\n  margin-right: 10px;\r\n  margin-top: 3px;\r\n}\r\n\r\n.info-content {\r\n  flex: 1;\r\n}\r\n\r\n.info-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.info-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n\r\n.info-summary {\r\n  color: #606266;\r\n  margin-bottom: 8px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.info-footer {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #909399;\r\n  font-size: 13px;\r\n}\r\n\r\n.info-source, .info-time, .info-sentiment, .info-views, .info-comments {\r\n  margin-right: 15px;\r\n}\r\n\r\n.info-index {\r\n  margin-left: auto;\r\n}\r\n\r\n.sentiment-positive {\r\n  color: #67c23a;\r\n}\r\n\r\n.sentiment-neutral {\r\n  color: #909399;\r\n}\r\n\r\n.sentiment-negative {\r\n  color: #f56c6c;\r\n}\r\n\r\n.info-images {\r\n  display: flex;\r\n  margin-top: 10px;\r\n}\r\n\r\n.info-image {\r\n  width: 80px;\r\n  height: 80px;\r\n  object-fit: cover;\r\n  margin-right: 10px;\r\n  border-radius: 4px;\r\n}\r\n\r\n// 高亮样式\r\n:deep(.highlight) {\r\n  color: #409eff;\r\n  font-weight: bold;\r\n}\r\n\r\n// 新建方案对话框样式\r\n.create-plan-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n\r\n  .el-tabs__header {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .el-tabs__nav {\r\n    width: 100%;\r\n  }\r\n\r\n  .el-tabs__item {\r\n    flex: 1;\r\n    text-align: center;\r\n    height: 40px;\r\n    line-height: 40px;\r\n  }\r\n\r\n  .el-form-item {\r\n    margin-bottom: 15px;\r\n  }\r\n}\r\n\r\n.monitor-object-select {\r\n  display: flex;\r\n  align-items: center;\r\n  position: relative;\r\n\r\n  .el-dropdown {\r\n    position: absolute;\r\n    right: 10px;\r\n    top: 0;\r\n    height: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n.location-select {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.location-btn {\r\n  margin-left: 10px;\r\n}\r\n\r\n.industry-select-btn {\r\n  width: 100%;\r\n  text-align: left;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  padding: 8px 15px;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  color: #606266;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n\r\n  &:hover {\r\n    border-color: #c0c4cc;\r\n  }\r\n}\r\n\r\n.theme-row, .industry-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n}\r\n\r\n.theme-input, .industry-input {\r\n  width: 100px;\r\n  margin-left: 10px;\r\n  vertical-align: bottom;\r\n}\r\n\r\n.theme-button, .industry-button {\r\n  margin-left: 10px;\r\n  height: 32px;\r\n  line-height: 30px;\r\n  padding-top: 0;\r\n  padding-bottom: 0;\r\n}\r\n\r\n.el-tag {\r\n  margin-right: 10px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.channels-row {\r\n  display: flex;\r\n  margin-bottom: 10px;\r\n\r\n  .el-checkbox {\r\n    margin-right: 20px;\r\n  }\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n// 分页容器样式\r\n.pagination-container {\r\n  padding: 15px;\r\n  text-align: right;\r\n  background-color: #fff;\r\n  border-top: 1px solid #e8e8e8;\r\n}\r\n\r\n// 行业分类弹窗样式\r\n.industry-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n\r\n  .industry-dialog-content {\r\n    display: flex;\r\n    height: 400px;\r\n  }\r\n\r\n  .industry-tree-container {\r\n    flex: 1;\r\n    padding: 15px;\r\n    border-right: 1px solid #e8e8e8;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .industry-selected-container {\r\n    width: 200px;\r\n    padding: 15px;\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n\r\n  .industry-selected-title {\r\n    font-weight: bold;\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n\r\n// 发送预警弹窗样式\r\n.send-alert-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n\r\n  .send-alert-content {\r\n    padding: 20px;\r\n  }\r\n\r\n  .receiver-list {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n\r\n  .receiver-item {\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n\r\n  .receiver-type {\r\n    width: 80px;\r\n    margin-right: 10px;\r\n  }\r\n\r\n  .receiver-select {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n// 正面情感弹窗样式\r\n.positive-sentiment-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n  .el-dialog__body {\r\n    padding: 20px;\r\n  }\r\n}\r\n</style>\r\n\r\nel-dialog\r\ntitle=\"信息图谱\"\r\n:visible.sync=\"infoGraphDialogVisible\"\r\nwidth=\"50%\"\r\nappend-to-body\r\ncustom-class=\"info-graph-dialog\"\r\n>\r\n<div class=\"info-graph-content\">\r\n<!-- 根据提供的图示调整内容布局 -->\r\n<div class=\"graph-container\">\r\n<div class=\"graph-node\">东木头人</div>\r\n<div class=\"graph-node\">永兴队</div>\r\n<!-- 添加更多节点 -->\r\n</div>\r\n</div>\r\n<div slot=\"footer\" class=\"dialog-footer\">\r\n<el-button @click=\"infoGraphDialogVisible = false\">关闭</el-button>\r\n</div>\r\n</el-dialog>\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  position: relative;\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  background-color: #f0f2f5;\r\n}\r\n\r\n// 操作按钮区域样式\r\n.action-buttons {\r\n  position: absolute;\r\n  top: 15px;\r\n  left: 15px;\r\n  z-index: 10;\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.info-summary-container {\r\n  display: flex;\r\n  height: 100%;\r\n  padding-top: 60px; // 为新建方案按钮留出空间\r\n}\r\n\r\n// 左侧导航栏样式\r\n.left-sidebar {\r\n  width: 180px;\r\n  background-color: #fff;\r\n  border-right: 1px solid #e8e8e8;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow-y: auto;\r\n}\r\n\r\n.sidebar-header {\r\n  padding: 15px;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.sidebar-menu {\r\n  flex: 1;\r\n}\r\n\r\n.el-menu-vertical {\r\n  border-right: none;\r\n}\r\n\r\n// 右侧内容区域样式\r\n.right-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #fff;\r\n  overflow: hidden;\r\n  margin: 0 15px;\r\n  border-radius: 4px;\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.content-header {\r\n  padding: 15px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.entity-title {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n}\r\n\r\n.entity-name {\r\n  margin-right: 5px;\r\n}\r\n\r\n// 标签页样式\r\n.tabs-container {\r\n  padding: 10px 15px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.filter-tabs {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n// 筛选区域样式\r\n.filter-section {\r\n  padding: 15px;\r\n  background-color: #f9f9f9;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.filter-row {\r\n  margin-bottom: 10px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.filter-row:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.filter-label {\r\n  width: 80px;\r\n  color: #606266;\r\n  line-height: 28px;\r\n}\r\n\r\n.filter-actions {\r\n  margin-top: 10px;\r\n  padding-left: 80px;\r\n}\r\n\r\n// 操作栏样式\r\n.action-bar {\r\n  padding: 10px 15px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.left-actions, .right-actions {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.search-input {\r\n  width: 200px;\r\n  margin-right: 10px;\r\n}\r\n\r\n// 信息列表样式\r\n.info-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 0;\r\n}\r\n\r\n.info-item {\r\n  padding: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.item-checkbox {\r\n  margin-right: 10px;\r\n  margin-top: 3px;\r\n}\r\n\r\n.info-content {\r\n  flex: 1;\r\n}\r\n\r\n.info-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.info-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n\r\n.info-summary {\r\n  color: #606266;\r\n  margin-bottom: 8px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.info-footer {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #909399;\r\n  font-size: 13px;\r\n}\r\n\r\n.info-source, .info-time, .info-sentiment, .info-views, .info-comments {\r\n  margin-right: 15px;\r\n}\r\n\r\n.info-index {\r\n  margin-left: auto;\r\n}\r\n\r\n.sentiment-positive {\r\n  color: #67c23a;\r\n}\r\n\r\n.sentiment-neutral {\r\n  color: #909399;\r\n}\r\n\r\n.sentiment-negative {\r\n  color: #f56c6c;\r\n}\r\n\r\n.info-images {\r\n  display: flex;\r\n  margin-top: 10px;\r\n}\r\n\r\n.info-image {\r\n  width: 80px;\r\n  height: 80px;\r\n  object-fit: cover;\r\n  margin-right: 10px;\r\n  border-radius: 4px;\r\n}\r\n\r\n// 高亮样式\r\n:deep(.highlight) {\r\n  color: #409eff;\r\n  font-weight: bold;\r\n}\r\n\r\n// 新建方案对话框样式\r\n.create-plan-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n\r\n  .el-tabs__header {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .el-tabs__nav {\r\n    width: 100%;\r\n  }\r\n\r\n  .el-tabs__item {\r\n    flex: 1;\r\n    text-align: center;\r\n    height: 40px;\r\n    line-height: 40px;\r\n  }\r\n\r\n  .el-form-item {\r\n    margin-bottom: 15px;\r\n  }\r\n}\r\n\r\n.monitor-object-select {\r\n  display: flex;\r\n  align-items: center;\r\n  position: relative;\r\n\r\n  .el-dropdown {\r\n    position: absolute;\r\n    right: 10px;\r\n    top: 0;\r\n    height: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n.location-select {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.location-btn {\r\n  margin-left: 10px;\r\n}\r\n\r\n.industry-select-btn {\r\n  width: 100%;\r\n  text-align: left;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  padding: 8px 15px;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  color: #606266;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n\r\n  &:hover {\r\n    border-color: #c0c4cc;\r\n  }\r\n}\r\n\r\n.theme-row, .industry-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n}\r\n\r\n.theme-input, .industry-input {\r\n  width: 100px;\r\n  margin-left: 10px;\r\n  vertical-align: bottom;\r\n}\r\n\r\n.theme-button, .industry-button {\r\n  margin-left: 10px;\r\n  height: 32px;\r\n  line-height: 30px;\r\n  padding-top: 0;\r\n  padding-bottom: 0;\r\n}\r\n\r\n.el-tag {\r\n  margin-right: 10px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.channels-row {\r\n  display: flex;\r\n  margin-bottom: 10px;\r\n\r\n  .el-checkbox {\r\n    margin-right: 20px;\r\n  }\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n// 分页容器样式\r\n.pagination-container {\r\n  padding: 15px;\r\n  text-align: right;\r\n  background-color: #fff;\r\n  border-top: 1px solid #e8e8e8;\r\n}\r\n\r\n// 行业分类弹窗样式\r\n.industry-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n\r\n  .industry-dialog-content {\r\n    display: flex;\r\n    height: 400px;\r\n  }\r\n\r\n  .industry-tree-container {\r\n    flex: 1;\r\n    padding: 15px;\r\n    border-right: 1px solid #e8e8e8;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .industry-selected-container {\r\n    width: 200px;\r\n    padding: 15px;\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n\r\n  .industry-selected-title {\r\n    font-weight: bold;\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n\r\n// 发送预警弹窗样式\r\n.send-alert-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n\r\n  .send-alert-content {\r\n    padding: 20px;\r\n  }\r\n\r\n  .receiver-list {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n\r\n  .receiver-item {\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n\r\n  .receiver-type {\r\n    width: 80px;\r\n    margin-right: 10px;\r\n  }\r\n\r\n  .receiver-select {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n// 正面情感弹窗样式\r\n.positive-sentiment-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n  .el-dialog__body {\r\n    padding: 20px;\r\n  }\r\n}\r\n</style>\r\n\r\n<el-button type=\"success\" size=\"mini\" @click=\"handlePositiveClick\">正面</el-button>,\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  position: relative;\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  background-color: #f0f2f5;\r\n}\r\n\r\n// 操作按钮区域样式\r\n.action-buttons {\r\n  position: absolute;\r\n  top: 15px;\r\n  left: 15px;\r\n  z-index: 10;\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.info-summary-container {\r\n  display: flex;\r\n  height: 100%;\r\n  padding-top: 60px; // 为新建方案按钮留出空间\r\n}\r\n\r\n// 左侧导航栏样式\r\n.left-sidebar {\r\n  width: 180px;\r\n  background-color: #fff;\r\n  border-right: 1px solid #e8e8e8;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow-y: auto;\r\n}\r\n\r\n.sidebar-header {\r\n  padding: 15px;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.sidebar-menu {\r\n  flex: 1;\r\n}\r\n\r\n.el-menu-vertical {\r\n  border-right: none;\r\n}\r\n\r\n// 右侧内容区域样式\r\n.right-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #fff;\r\n  overflow: hidden;\r\n  margin: 0 15px;\r\n  border-radius: 4px;\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.content-header {\r\n  padding: 15px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.entity-title {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n}\r\n\r\n.entity-name {\r\n  margin-right: 5px;\r\n}\r\n\r\n// 标签页样式\r\n.tabs-container {\r\n  padding: 10px 15px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.filter-tabs {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n// 筛选区域样式\r\n.filter-section {\r\n  padding: 15px;\r\n  background-color: #f9f9f9;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.filter-row {\r\n  margin-bottom: 10px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.filter-row:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.filter-label {\r\n  width: 80px;\r\n  color: #606266;\r\n  line-height: 28px;\r\n}\r\n\r\n.filter-actions {\r\n  margin-top: 10px;\r\n  padding-left: 80px;\r\n}\r\n\r\n// 操作栏样式\r\n.action-bar {\r\n  padding: 10px 15px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.left-actions, .right-actions {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.search-input {\r\n  width: 200px;\r\n  margin-right: 10px;\r\n}\r\n\r\n// 信息列表样式\r\n.info-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 0;\r\n}\r\n\r\n.info-item {\r\n  padding: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.item-checkbox {\r\n  margin-right: 10px;\r\n  margin-top: 3px;\r\n}\r\n\r\n.info-content {\r\n  flex: 1;\r\n}\r\n\r\n.info-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.info-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n\r\n.info-summary {\r\n  color: #606266;\r\n  margin-bottom: 8px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.info-footer {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #909399;\r\n  font-size: 13px;\r\n}\r\n\r\n.info-source, .info-time, .info-sentiment, .info-views, .info-comments {\r\n  margin-right: 15px;\r\n}\r\n\r\n.info-index {\r\n  margin-left: auto;\r\n}\r\n\r\n.sentiment-positive {\r\n  color: #67c23a;\r\n}\r\n\r\n.sentiment-neutral {\r\n  color: #909399;\r\n}\r\n\r\n.sentiment-negative {\r\n  color: #f56c6c;\r\n}\r\n\r\n.info-images {\r\n  display: flex;\r\n  margin-top: 10px;\r\n}\r\n\r\n.info-image {\r\n  width: 80px;\r\n  height: 80px;\r\n  object-fit: cover;\r\n  margin-right: 10px;\r\n  border-radius: 4px;\r\n}\r\n\r\n// 高亮样式\r\n:deep(.highlight) {\r\n  color: #409eff;\r\n  font-weight: bold;\r\n}\r\n\r\n// 新建方案对话框样式\r\n.create-plan-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n"]}]}