{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\utils\\dict\\index.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\utils\\dict\\index.js", "mtime": 1749104047634}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749104418875}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSBfZGVmYXVsdDsKdmFyIF9EaWN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL0RpY3QiKSk7CnZhciBfRGljdE9wdGlvbnMgPSByZXF1aXJlKCIuL0RpY3RPcHRpb25zIik7CmZ1bmN0aW9uIF9kZWZhdWx0KFZ1ZSwgb3B0aW9ucykgewogICgwLCBfRGljdE9wdGlvbnMubWVyZ2VPcHRpb25zKShvcHRpb25zKTsKICBWdWUubWl4aW4oewogICAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgICAgaWYgKHRoaXMuJG9wdGlvbnMgPT09IHVuZGVmaW5lZCB8fCB0aGlzLiRvcHRpb25zLmRpY3RzID09PSB1bmRlZmluZWQgfHwgdGhpcy4kb3B0aW9ucy5kaWN0cyA9PT0gbnVsbCkgewogICAgICAgIHJldHVybiB7fTsKICAgICAgfQogICAgICB2YXIgZGljdCA9IG5ldyBfRGljdC5kZWZhdWx0KCk7CiAgICAgIGRpY3Qub3duZXIgPSB0aGlzOwogICAgICByZXR1cm4gewogICAgICAgIGRpY3Q6IGRpY3QKICAgICAgfTsKICAgIH0sCiAgICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICBpZiAoISh0aGlzLmRpY3QgaW5zdGFuY2VvZiBfRGljdC5kZWZhdWx0KSkgewogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBvcHRpb25zLm9uQ3JlYXRlZCAmJiBvcHRpb25zLm9uQ3JlYXRlZCh0aGlzLmRpY3QpOwogICAgICB0aGlzLmRpY3QuaW5pdCh0aGlzLiRvcHRpb25zLmRpY3RzKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBvcHRpb25zLm9uUmVhZHkgJiYgb3B0aW9ucy5vblJlYWR5KF90aGlzLmRpY3QpOwogICAgICAgIF90aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgICBfdGhpcy4kZW1pdCgnZGljdFJlYWR5JywgX3RoaXMuZGljdCk7CiAgICAgICAgICBpZiAoX3RoaXMuJG9wdGlvbnMubWV0aG9kcyAmJiBfdGhpcy4kb3B0aW9ucy5tZXRob2RzLm9uRGljdFJlYWR5IGluc3RhbmNlb2YgRnVuY3Rpb24pIHsKICAgICAgICAgICAgX3RoaXMuJG9wdGlvbnMubWV0aG9kcy5vbkRpY3RSZWFkeS5jYWxsKF90aGlzLCBfdGhpcy5kaWN0KTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9CiAgfSk7Cn0="}, {"version": 3, "names": ["_Dict", "_interopRequireDefault", "require", "_DictOptions", "_default", "<PERSON><PERSON>", "options", "mergeOptions", "mixin", "data", "$options", "undefined", "dicts", "dict", "Dict", "owner", "created", "_this", "onCreated", "init", "then", "onReady", "$nextTick", "$emit", "methods", "onDictReady", "Function", "call"], "sources": ["D:/thinktank/thinktankui/src/utils/dict/index.js"], "sourcesContent": ["import Dict from './Dict'\r\nimport { mergeOptions } from './DictOptions'\r\n\r\nexport default function(Vue, options) {\r\n  mergeOptions(options)\r\n  Vue.mixin({\r\n    data() {\r\n      if (this.$options === undefined || this.$options.dicts === undefined || this.$options.dicts === null) {\r\n        return {}\r\n      }\r\n      const dict = new Dict()\r\n      dict.owner = this\r\n      return {\r\n        dict\r\n      }\r\n    },\r\n    created() {\r\n      if (!(this.dict instanceof Dict)) {\r\n        return\r\n      }\r\n      options.onCreated && options.onCreated(this.dict)\r\n      this.dict.init(this.$options.dicts).then(() => {\r\n        options.onReady && options.onReady(this.dict)\r\n        this.$nextTick(() => {\r\n          this.$emit('dictReady', this.dict)\r\n          if (this.$options.methods && this.$options.methods.onDictReady instanceof Function) {\r\n            this.$options.methods.onDictReady.call(this, this.dict)\r\n          }\r\n        })\r\n      })\r\n    },\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;AAAA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEe,SAAAE,SAASC,GAAG,EAAEC,OAAO,EAAE;EACpC,IAAAC,yBAAY,EAACD,OAAO,CAAC;EACrBD,GAAG,CAACG,KAAK,CAAC;IACRC,IAAI,WAAJA,IAAIA,CAAA,EAAG;MACL,IAAI,IAAI,CAACC,QAAQ,KAAKC,SAAS,IAAI,IAAI,CAACD,QAAQ,CAACE,KAAK,KAAKD,SAAS,IAAI,IAAI,CAACD,QAAQ,CAACE,KAAK,KAAK,IAAI,EAAE;QACpG,OAAO,CAAC,CAAC;MACX;MACA,IAAMC,IAAI,GAAG,IAAIC,aAAI,CAAC,CAAC;MACvBD,IAAI,CAACE,KAAK,GAAG,IAAI;MACjB,OAAO;QACLF,IAAI,EAAJA;MACF,CAAC;IACH,CAAC;IACDG,OAAO,WAAPA,OAAOA,CAAA,EAAG;MAAA,IAAAC,KAAA;MACR,IAAI,EAAE,IAAI,CAACJ,IAAI,YAAYC,aAAI,CAAC,EAAE;QAChC;MACF;MACAR,OAAO,CAACY,SAAS,IAAIZ,OAAO,CAACY,SAAS,CAAC,IAAI,CAACL,IAAI,CAAC;MACjD,IAAI,CAACA,IAAI,CAACM,IAAI,CAAC,IAAI,CAACT,QAAQ,CAACE,KAAK,CAAC,CAACQ,IAAI,CAAC,YAAM;QAC7Cd,OAAO,CAACe,OAAO,IAAIf,OAAO,CAACe,OAAO,CAACJ,KAAI,CAACJ,IAAI,CAAC;QAC7CI,KAAI,CAACK,SAAS,CAAC,YAAM;UACnBL,KAAI,CAACM,KAAK,CAAC,WAAW,EAAEN,KAAI,CAACJ,IAAI,CAAC;UAClC,IAAII,KAAI,CAACP,QAAQ,CAACc,OAAO,IAAIP,KAAI,CAACP,QAAQ,CAACc,OAAO,CAACC,WAAW,YAAYC,QAAQ,EAAE;YAClFT,KAAI,CAACP,QAAQ,CAACc,OAAO,CAACC,WAAW,CAACE,IAAI,CAACV,KAAI,EAAEA,KAAI,CAACJ,IAAI,CAAC;UACzD;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;AACJ", "ignoreList": []}]}