{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\ImageUpload\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\ImageUpload\\index.vue", "mtime": 1749104047621}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_auth", "require", "_validate", "props", "value", "String", "Object", "Array", "limit", "type", "Number", "default", "fileSize", "fileType", "isShowTip", "Boolean", "data", "number", "uploadList", "dialogImageUrl", "dialogVisible", "hideUpload", "baseUrl", "process", "env", "VUE_APP_BASE_API", "uploadImgUrl", "headers", "Authorization", "getToken", "fileList", "watch", "handler", "val", "_this", "list", "isArray", "split", "map", "item", "indexOf", "isExternal", "name", "url", "deep", "immediate", "computed", "showTip", "methods", "handleBeforeUpload", "file", "isImg", "length", "fileExtension", "lastIndexOf", "slice", "some", "$modal", "msgError", "concat", "join", "includes", "isLt", "size", "loading", "handleExceed", "handleUploadSuccess", "res", "code", "push", "fileName", "uploadedSuccessfully", "closeLoading", "msg", "$refs", "imageUpload", "handleRemove", "handleDelete", "findex", "f", "splice", "$emit", "listToString", "handleUploadError", "handlePictureCardPreview", "separator", "strs", "i", "replace", "substr"], "sources": ["src/components/ImageUpload/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"component-upload-image\">\r\n    <el-upload\r\n      multiple\r\n      :action=\"uploadImgUrl\"\r\n      list-type=\"picture-card\"\r\n      :on-success=\"handleUploadSuccess\"\r\n      :before-upload=\"handleBeforeUpload\"\r\n      :limit=\"limit\"\r\n      :on-error=\"handleUploadError\"\r\n      :on-exceed=\"handleExceed\"\r\n      ref=\"imageUpload\"\r\n      :on-remove=\"handleDelete\"\r\n      :show-file-list=\"true\"\r\n      :headers=\"headers\"\r\n      :file-list=\"fileList\"\r\n      :on-preview=\"handlePictureCardPreview\"\r\n      :class=\"{hide: this.fileList.length >= this.limit}\"\r\n    >\r\n      <i class=\"el-icon-plus\"></i>\r\n    </el-upload>\r\n\r\n    <!-- 上传提示 -->\r\n    <div class=\"el-upload__tip\" slot=\"tip\" v-if=\"showTip\">\r\n      请上传\r\n      <template v-if=\"fileSize\"> 大小不超过 <b style=\"color: #f56c6c\">{{ fileSize }}MB</b> </template>\r\n      <template v-if=\"fileType\"> 格式为 <b style=\"color: #f56c6c\">{{ fileType.join(\"/\") }}</b> </template>\r\n      的文件\r\n    </div>\r\n\r\n    <el-dialog\r\n      :visible.sync=\"dialogVisible\"\r\n      title=\"预览\"\r\n      width=\"800\"\r\n      append-to-body\r\n    >\r\n      <img\r\n        :src=\"dialogImageUrl\"\r\n        style=\"display: block; max-width: 100%; margin: 0 auto\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getToken } from \"@/utils/auth\";\r\nimport { isExternal } from \"@/utils/validate\";\r\n\r\nexport default {\r\n  props: {\r\n    value: [String, Object, Array],\r\n    // 图片数量限制\r\n    limit: {\r\n      type: Number,\r\n      default: 5,\r\n    },\r\n    // 大小限制(MB)\r\n    fileSize: {\r\n       type: Number,\r\n      default: 5,\r\n    },\r\n    // 文件类型, 例如['png', 'jpg', 'jpeg']\r\n    fileType: {\r\n      type: Array,\r\n      default: () => [\"png\", \"jpg\", \"jpeg\"],\r\n    },\r\n    // 是否显示提示\r\n    isShowTip: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      number: 0,\r\n      uploadList: [],\r\n      dialogImageUrl: \"\",\r\n      dialogVisible: false,\r\n      hideUpload: false,\r\n      baseUrl: process.env.VUE_APP_BASE_API,\r\n      uploadImgUrl: process.env.VUE_APP_BASE_API + \"/common/upload\", // 上传的图片服务器地址\r\n      headers: {\r\n        Authorization: \"Bearer \" + getToken(),\r\n      },\r\n      fileList: []\r\n    };\r\n  },\r\n  watch: {\r\n    value: {\r\n      handler(val) {\r\n        if (val) {\r\n          // 首先将值转为数组\r\n          const list = Array.isArray(val) ? val : this.value.split(',');\r\n          // 然后将数组转为对象数组\r\n          this.fileList = list.map(item => {\r\n            if (typeof item === \"string\") {\r\n              if (item.indexOf(this.baseUrl) === -1 && !isExternal(item)) {\r\n                  item = { name: this.baseUrl + item, url: this.baseUrl + item };\r\n              } else {\r\n                  item = { name: item, url: item };\r\n              }\r\n            }\r\n            return item;\r\n          });\r\n        } else {\r\n          this.fileList = [];\r\n          return [];\r\n        }\r\n      },\r\n      deep: true,\r\n      immediate: true\r\n    }\r\n  },\r\n  computed: {\r\n    // 是否显示提示\r\n    showTip() {\r\n      return this.isShowTip && (this.fileType || this.fileSize);\r\n    },\r\n  },\r\n  methods: {\r\n    // 上传前loading加载\r\n    handleBeforeUpload(file) {\r\n      let isImg = false;\r\n      if (this.fileType.length) {\r\n        let fileExtension = \"\";\r\n        if (file.name.lastIndexOf(\".\") > -1) {\r\n          fileExtension = file.name.slice(file.name.lastIndexOf(\".\") + 1);\r\n        }\r\n        isImg = this.fileType.some(type => {\r\n          if (file.type.indexOf(type) > -1) return true;\r\n          if (fileExtension && fileExtension.indexOf(type) > -1) return true;\r\n          return false;\r\n        });\r\n      } else {\r\n        isImg = file.type.indexOf(\"image\") > -1;\r\n      }\r\n\r\n      if (!isImg) {\r\n        this.$modal.msgError(`文件格式不正确，请上传${this.fileType.join(\"/\")}图片格式文件!`);\r\n        return false;\r\n      }\r\n      if (file.name.includes(',')) {\r\n        this.$modal.msgError('文件名不正确，不能包含英文逗号!');\r\n        return false;\r\n      }\r\n      if (this.fileSize) {\r\n        const isLt = file.size / 1024 / 1024 < this.fileSize;\r\n        if (!isLt) {\r\n          this.$modal.msgError(`上传头像图片大小不能超过 ${this.fileSize} MB!`);\r\n          return false;\r\n        }\r\n      }\r\n      this.$modal.loading(\"正在上传图片，请稍候...\");\r\n      this.number++;\r\n    },\r\n    // 文件个数超出\r\n    handleExceed() {\r\n      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`);\r\n    },\r\n    // 上传成功回调\r\n    handleUploadSuccess(res, file) {\r\n      if (res.code === 200) {\r\n        this.uploadList.push({ name: res.fileName, url: res.fileName });\r\n        this.uploadedSuccessfully();\r\n      } else {\r\n        this.number--;\r\n        this.$modal.closeLoading();\r\n        this.$modal.msgError(res.msg);\r\n        this.$refs.imageUpload.handleRemove(file);\r\n        this.uploadedSuccessfully();\r\n      }\r\n    },\r\n    // 删除图片\r\n    handleDelete(file) {\r\n      const findex = this.fileList.map(f => f.name).indexOf(file.name);\r\n      if (findex > -1) {\r\n        this.fileList.splice(findex, 1);\r\n        this.$emit(\"input\", this.listToString(this.fileList));\r\n      }\r\n    },\r\n    // 上传失败\r\n    handleUploadError() {\r\n      this.$modal.msgError(\"上传图片失败，请重试\");\r\n      this.$modal.closeLoading();\r\n    },\r\n    // 上传结束处理\r\n    uploadedSuccessfully() {\r\n      if (this.number > 0 && this.uploadList.length === this.number) {\r\n        this.fileList = this.fileList.concat(this.uploadList);\r\n        this.uploadList = [];\r\n        this.number = 0;\r\n        this.$emit(\"input\", this.listToString(this.fileList));\r\n        this.$modal.closeLoading();\r\n      }\r\n    },\r\n    // 预览\r\n    handlePictureCardPreview(file) {\r\n      this.dialogImageUrl = file.url;\r\n      this.dialogVisible = true;\r\n    },\r\n    // 对象转成指定字符串分隔\r\n    listToString(list, separator) {\r\n      let strs = \"\";\r\n      separator = separator || \",\";\r\n      for (let i in list) {\r\n        if (list[i].url) {\r\n          strs += list[i].url.replace(this.baseUrl, \"\") + separator;\r\n        }\r\n      }\r\n      return strs != '' ? strs.substr(0, strs.length - 1) : '';\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n// .el-upload--picture-card 控制加号部分\r\n::v-deep.hide .el-upload--picture-card {\r\n    display: none;\r\n}\r\n// 去掉动画效果\r\n::v-deep .el-list-enter-active,\r\n::v-deep .el-list-leave-active {\r\n    transition: all 0s;\r\n}\r\n\r\n::v-deep .el-list-enter, .el-list-leave-active {\r\n  opacity: 0;\r\n  transform: translateY(0);\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AA6CA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAE,KAAA;IACAC,KAAA,GAAAC,MAAA,EAAAC,MAAA,EAAAC,KAAA;IACA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACA;IACAC,QAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACA;IACAE,QAAA;MACAJ,IAAA,EAAAF,KAAA;MACAI,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACA;IACAG,SAAA;MACAL,IAAA,EAAAM,OAAA;MACAJ,OAAA;IACA;EACA;EACAK,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA;MACAC,UAAA;MACAC,cAAA;MACAC,aAAA;MACAC,UAAA;MACAC,OAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACAC,YAAA,EAAAH,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MAAA;MACAE,OAAA;QACAC,aAAA,kBAAAC,cAAA;MACA;MACAC,QAAA;IACA;EACA;EACAC,KAAA;IACA3B,KAAA;MACA4B,OAAA,WAAAA,QAAAC,GAAA;QAAA,IAAAC,KAAA;QACA,IAAAD,GAAA;UACA;UACA,IAAAE,IAAA,GAAA5B,KAAA,CAAA6B,OAAA,CAAAH,GAAA,IAAAA,GAAA,QAAA7B,KAAA,CAAAiC,KAAA;UACA;UACA,KAAAP,QAAA,GAAAK,IAAA,CAAAG,GAAA,WAAAC,IAAA;YACA,WAAAA,IAAA;cACA,IAAAA,IAAA,CAAAC,OAAA,CAAAN,KAAA,CAAAZ,OAAA,iBAAAmB,oBAAA,EAAAF,IAAA;gBACAA,IAAA;kBAAAG,IAAA,EAAAR,KAAA,CAAAZ,OAAA,GAAAiB,IAAA;kBAAAI,GAAA,EAAAT,KAAA,CAAAZ,OAAA,GAAAiB;gBAAA;cACA;gBACAA,IAAA;kBAAAG,IAAA,EAAAH,IAAA;kBAAAI,GAAA,EAAAJ;gBAAA;cACA;YACA;YACA,OAAAA,IAAA;UACA;QACA;UACA,KAAAT,QAAA;UACA;QACA;MACA;MACAc,IAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,OAAA,WAAAA,QAAA;MACA,YAAAjC,SAAA,UAAAD,QAAA,SAAAD,QAAA;IACA;EACA;EACAoC,OAAA;IACA;IACAC,kBAAA,WAAAA,mBAAAC,IAAA;MACA,IAAAC,KAAA;MACA,SAAAtC,QAAA,CAAAuC,MAAA;QACA,IAAAC,aAAA;QACA,IAAAH,IAAA,CAAAR,IAAA,CAAAY,WAAA;UACAD,aAAA,GAAAH,IAAA,CAAAR,IAAA,CAAAa,KAAA,CAAAL,IAAA,CAAAR,IAAA,CAAAY,WAAA;QACA;QACAH,KAAA,QAAAtC,QAAA,CAAA2C,IAAA,WAAA/C,IAAA;UACA,IAAAyC,IAAA,CAAAzC,IAAA,CAAA+B,OAAA,CAAA/B,IAAA;UACA,IAAA4C,aAAA,IAAAA,aAAA,CAAAb,OAAA,CAAA/B,IAAA;UACA;QACA;MACA;QACA0C,KAAA,GAAAD,IAAA,CAAAzC,IAAA,CAAA+B,OAAA;MACA;MAEA,KAAAW,KAAA;QACA,KAAAM,MAAA,CAAAC,QAAA,sEAAAC,MAAA,MAAA9C,QAAA,CAAA+C,IAAA;QACA;MACA;MACA,IAAAV,IAAA,CAAAR,IAAA,CAAAmB,QAAA;QACA,KAAAJ,MAAA,CAAAC,QAAA;QACA;MACA;MACA,SAAA9C,QAAA;QACA,IAAAkD,IAAA,GAAAZ,IAAA,CAAAa,IAAA,sBAAAnD,QAAA;QACA,KAAAkD,IAAA;UACA,KAAAL,MAAA,CAAAC,QAAA,6EAAAC,MAAA,MAAA/C,QAAA;UACA;QACA;MACA;MACA,KAAA6C,MAAA,CAAAO,OAAA;MACA,KAAA/C,MAAA;IACA;IACA;IACAgD,YAAA,WAAAA,aAAA;MACA,KAAAR,MAAA,CAAAC,QAAA,iEAAAC,MAAA,MAAAnD,KAAA;IACA;IACA;IACA0D,mBAAA,WAAAA,oBAAAC,GAAA,EAAAjB,IAAA;MACA,IAAAiB,GAAA,CAAAC,IAAA;QACA,KAAAlD,UAAA,CAAAmD,IAAA;UAAA3B,IAAA,EAAAyB,GAAA,CAAAG,QAAA;UAAA3B,GAAA,EAAAwB,GAAA,CAAAG;QAAA;QACA,KAAAC,oBAAA;MACA;QACA,KAAAtD,MAAA;QACA,KAAAwC,MAAA,CAAAe,YAAA;QACA,KAAAf,MAAA,CAAAC,QAAA,CAAAS,GAAA,CAAAM,GAAA;QACA,KAAAC,KAAA,CAAAC,WAAA,CAAAC,YAAA,CAAA1B,IAAA;QACA,KAAAqB,oBAAA;MACA;IACA;IACA;IACAM,YAAA,WAAAA,aAAA3B,IAAA;MACA,IAAA4B,MAAA,QAAAhD,QAAA,CAAAQ,GAAA,WAAAyC,CAAA;QAAA,OAAAA,CAAA,CAAArC,IAAA;MAAA,GAAAF,OAAA,CAAAU,IAAA,CAAAR,IAAA;MACA,IAAAoC,MAAA;QACA,KAAAhD,QAAA,CAAAkD,MAAA,CAAAF,MAAA;QACA,KAAAG,KAAA,eAAAC,YAAA,MAAApD,QAAA;MACA;IACA;IACA;IACAqD,iBAAA,WAAAA,kBAAA;MACA,KAAA1B,MAAA,CAAAC,QAAA;MACA,KAAAD,MAAA,CAAAe,YAAA;IACA;IACA;IACAD,oBAAA,WAAAA,qBAAA;MACA,SAAAtD,MAAA,aAAAC,UAAA,CAAAkC,MAAA,UAAAnC,MAAA;QACA,KAAAa,QAAA,QAAAA,QAAA,CAAA6B,MAAA,MAAAzC,UAAA;QACA,KAAAA,UAAA;QACA,KAAAD,MAAA;QACA,KAAAgE,KAAA,eAAAC,YAAA,MAAApD,QAAA;QACA,KAAA2B,MAAA,CAAAe,YAAA;MACA;IACA;IACA;IACAY,wBAAA,WAAAA,yBAAAlC,IAAA;MACA,KAAA/B,cAAA,GAAA+B,IAAA,CAAAP,GAAA;MACA,KAAAvB,aAAA;IACA;IACA;IACA8D,YAAA,WAAAA,aAAA/C,IAAA,EAAAkD,SAAA;MACA,IAAAC,IAAA;MACAD,SAAA,GAAAA,SAAA;MACA,SAAAE,CAAA,IAAApD,IAAA;QACA,IAAAA,IAAA,CAAAoD,CAAA,EAAA5C,GAAA;UACA2C,IAAA,IAAAnD,IAAA,CAAAoD,CAAA,EAAA5C,GAAA,CAAA6C,OAAA,MAAAlE,OAAA,QAAA+D,SAAA;QACA;MACA;MACA,OAAAC,IAAA,SAAAA,IAAA,CAAAG,MAAA,IAAAH,IAAA,CAAAlC,MAAA;IACA;EACA;AACA", "ignoreList": []}]}