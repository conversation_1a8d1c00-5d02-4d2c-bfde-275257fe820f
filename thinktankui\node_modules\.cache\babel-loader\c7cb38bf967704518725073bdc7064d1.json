{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\RightToolbar\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\RightToolbar\\index.vue", "mtime": 1749104047621}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "value", "title", "open", "props", "showSearch", "type", "Boolean", "default", "columns", "Array", "search", "showColumnsType", "String", "gutter", "Number", "computed", "style", "ret", "marginRight", "concat", "created", "item", "visible", "push", "parseInt", "methods", "toggleSearch", "$emit", "refresh", "dataChange", "key", "includes", "showColumn", "checkboxChange", "event", "label", "filter"], "sources": ["src/components/RightToolbar/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"top-right-btn\" :style=\"style\">\r\n    <el-row>\r\n      <el-tooltip class=\"item\" effect=\"dark\" :content=\"showSearch ? '隐藏搜索' : '显示搜索'\" placement=\"top\" v-if=\"search\">\r\n        <el-button size=\"mini\" circle icon=\"el-icon-search\" @click=\"toggleSearch()\" />\r\n      </el-tooltip>\r\n      <el-tooltip class=\"item\" effect=\"dark\" content=\"刷新\" placement=\"top\">\r\n        <el-button size=\"mini\" circle icon=\"el-icon-refresh\" @click=\"refresh()\" />\r\n      </el-tooltip>\r\n      <el-tooltip class=\"item\" effect=\"dark\" content=\"显隐列\" placement=\"top\" v-if=\"columns\">\r\n        <el-button size=\"mini\" circle icon=\"el-icon-menu\" @click=\"showColumn()\" v-if=\"showColumnsType == 'transfer'\"/>\r\n        <el-dropdown trigger=\"click\" :hide-on-click=\"false\" style=\"padding-left: 12px\" v-if=\"showColumnsType == 'checkbox'\">\r\n          <el-button size=\"mini\" circle icon=\"el-icon-menu\" />\r\n          <el-dropdown-menu slot=\"dropdown\">\r\n            <template v-for=\"item in columns\">\r\n              <el-dropdown-item :key=\"item.key\">\r\n                <el-checkbox :checked=\"item.visible\" @change=\"checkboxChange($event, item.label)\" :label=\"item.label\" />\r\n              </el-dropdown-item>\r\n            </template>\r\n          </el-dropdown-menu>\r\n        </el-dropdown>\r\n      </el-tooltip>\r\n    </el-row>\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" append-to-body>\r\n      <el-transfer\r\n        :titles=\"['显示', '隐藏']\"\r\n        v-model=\"value\"\r\n        :data=\"columns\"\r\n        @change=\"dataChange\"\r\n      ></el-transfer>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: \"RightToolbar\",\r\n  data() {\r\n    return {\r\n      // 显隐数据\r\n      value: [],\r\n      // 弹出层标题\r\n      title: \"显示/隐藏\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n    };\r\n  },\r\n  props: {\r\n    /* 是否显示检索条件 */\r\n    showSearch: {\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    /* 显隐列信息 */\r\n    columns: {\r\n      type: Array,\r\n    },\r\n    /* 是否显示检索图标 */\r\n    search: {\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    /* 显隐列类型（transfer穿梭框、checkbox复选框） */\r\n    showColumnsType: {\r\n      type: String,\r\n      default: \"checkbox\",\r\n    },\r\n    /* 右外边距 */\r\n    gutter: {\r\n      type: Number,\r\n      default: 10,\r\n    },\r\n  },\r\n  computed: {\r\n    style() {\r\n      const ret = {};\r\n      if (this.gutter) {\r\n        ret.marginRight = `${this.gutter / 2}px`;\r\n      }\r\n      return ret;\r\n    }\r\n  },\r\n  created() {\r\n    if (this.showColumnsType == 'transfer') {\r\n      // 显隐列初始默认隐藏列\r\n      for (let item in this.columns) {\r\n        if (this.columns[item].visible === false) {\r\n          this.value.push(parseInt(item));\r\n        }\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    // 搜索\r\n    toggleSearch() {\r\n      this.$emit(\"update:showSearch\", !this.showSearch);\r\n    },\r\n    // 刷新\r\n    refresh() {\r\n      this.$emit(\"queryTable\");\r\n    },\r\n    // 右侧列表元素变化\r\n    dataChange(data) {\r\n      for (let item in this.columns) {\r\n        const key = this.columns[item].key;\r\n        this.columns[item].visible = !data.includes(key);\r\n      }\r\n    },\r\n    // 打开显隐列dialog\r\n    showColumn() {\r\n      this.open = true;\r\n    },\r\n    // 勾选\r\n    checkboxChange(event, label) {\r\n      this.columns.filter(item => item.label == label)[0].visible = event;\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n::v-deep .el-transfer__button {\r\n  border-radius: 50%;\r\n  padding: 12px;\r\n  display: block;\r\n  margin-left: 0px;\r\n}\r\n::v-deep .el-transfer__button:first-child {\r\n  margin-bottom: 10px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAkCA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,KAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;IACA;EACA;EACAC,KAAA;IACA;IACAC,UAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACA;IACAC,OAAA;MACAH,IAAA,EAAAI;IACA;IACA;IACAC,MAAA;MACAL,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACA;IACAI,eAAA;MACAN,IAAA,EAAAO,MAAA;MACAL,OAAA;IACA;IACA;IACAM,MAAA;MACAR,IAAA,EAAAS,MAAA;MACAP,OAAA;IACA;EACA;EACAQ,QAAA;IACAC,KAAA,WAAAA,MAAA;MACA,IAAAC,GAAA;MACA,SAAAJ,MAAA;QACAI,GAAA,CAAAC,WAAA,MAAAC,MAAA,MAAAN,MAAA;MACA;MACA,OAAAI,GAAA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA,SAAAT,eAAA;MACA;MACA,SAAAU,IAAA,SAAAb,OAAA;QACA,SAAAA,OAAA,CAAAa,IAAA,EAAAC,OAAA;UACA,KAAAtB,KAAA,CAAAuB,IAAA,CAAAC,QAAA,CAAAH,IAAA;QACA;MACA;IACA;EACA;EACAI,OAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,KAAA,4BAAAvB,UAAA;IACA;IACA;IACAwB,OAAA,WAAAA,QAAA;MACA,KAAAD,KAAA;IACA;IACA;IACAE,UAAA,WAAAA,WAAA9B,IAAA;MACA,SAAAsB,IAAA,SAAAb,OAAA;QACA,IAAAsB,GAAA,QAAAtB,OAAA,CAAAa,IAAA,EAAAS,GAAA;QACA,KAAAtB,OAAA,CAAAa,IAAA,EAAAC,OAAA,IAAAvB,IAAA,CAAAgC,QAAA,CAAAD,GAAA;MACA;IACA;IACA;IACAE,UAAA,WAAAA,WAAA;MACA,KAAA9B,IAAA;IACA;IACA;IACA+B,cAAA,WAAAA,eAAAC,KAAA,EAAAC,KAAA;MACA,KAAA3B,OAAA,CAAA4B,MAAA,WAAAf,IAAA;QAAA,OAAAA,IAAA,CAAAc,KAAA,IAAAA,KAAA;MAAA,MAAAb,OAAA,GAAAY,KAAA;IACA;EACA;AACA", "ignoreList": []}]}