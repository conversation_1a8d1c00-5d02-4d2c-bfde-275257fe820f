{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\SvgIcon\\index.vue?vue&type=style&index=0&id=c8a70580&scoped=true&lang=css", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\SvgIcon\\index.vue", "mtime": 1749104047621}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749104419119}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749104421224}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749104419872}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLnN2Zy1pY29uIHsNCiAgd2lkdGg6IDFlbTsNCiAgaGVpZ2h0OiAxZW07DQogIHZlcnRpY2FsLWFsaWduOiAtMC4xNWVtOw0KICBmaWxsOiBjdXJyZW50Q29sb3I7DQogIG92ZXJmbG93OiBoaWRkZW47DQp9DQoNCi5zdmctZXh0ZXJuYWwtaWNvbiB7DQogIGJhY2tncm91bmQtY29sb3I6IGN1cnJlbnRDb2xvcjsNCiAgbWFzay1zaXplOiBjb3ZlciFpbXBvcnRhbnQ7DQogIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/SvgIcon", "sourcesContent": ["<template>\r\n  <div v-if=\"isExternal\" :style=\"styleExternalIcon\" class=\"svg-external-icon svg-icon\" v-on=\"$listeners\" />\r\n  <svg v-else :class=\"svgClass\" aria-hidden=\"true\" v-on=\"$listeners\">\r\n    <use :xlink:href=\"iconName\" />\r\n  </svg>\r\n</template>\r\n\r\n<script>\r\nimport { isExternal } from '@/utils/validate'\r\n\r\nexport default {\r\n  name: 'SvgIcon',\r\n  props: {\r\n    iconClass: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    className: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  computed: {\r\n    isExternal() {\r\n      return isExternal(this.iconClass)\r\n    },\r\n    iconName() {\r\n      return `#icon-${this.iconClass}`\r\n    },\r\n    svgClass() {\r\n      if (this.className) {\r\n        return 'svg-icon ' + this.className\r\n      } else {\r\n        return 'svg-icon'\r\n      }\r\n    },\r\n    styleExternalIcon() {\r\n      return {\r\n        mask: `url(${this.iconClass}) no-repeat 50% 50%`,\r\n        '-webkit-mask': `url(${this.iconClass}) no-repeat 50% 50%`\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.svg-icon {\r\n  width: 1em;\r\n  height: 1em;\r\n  vertical-align: -0.15em;\r\n  fill: currentColor;\r\n  overflow: hidden;\r\n}\r\n\r\n.svg-external-icon {\r\n  background-color: currentColor;\r\n  mask-size: cover!important;\r\n  display: inline-block;\r\n}\r\n</style>\r\n"]}]}