{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\api\\system\\user.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\api\\system\\user.js", "mtime": 1749104047591}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749104418875}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmFkZFVzZXIgPSBhZGRVc2VyOwpleHBvcnRzLmNoYW5nZVVzZXJTdGF0dXMgPSBjaGFuZ2VVc2VyU3RhdHVzOwpleHBvcnRzLmRlbFVzZXIgPSBkZWxVc2VyOwpleHBvcnRzLmRlcHRUcmVlU2VsZWN0ID0gZGVwdFRyZWVTZWxlY3Q7CmV4cG9ydHMuZ2V0QXV0aFJvbGUgPSBnZXRBdXRoUm9sZTsKZXhwb3J0cy5nZXRVc2VyID0gZ2V0VXNlcjsKZXhwb3J0cy5nZXRVc2VyUHJvZmlsZSA9IGdldFVzZXJQcm9maWxlOwpleHBvcnRzLmxpc3RVc2VyID0gbGlzdFVzZXI7CmV4cG9ydHMucmVzZXRVc2VyUHdkID0gcmVzZXRVc2VyUHdkOwpleHBvcnRzLnVwZGF0ZUF1dGhSb2xlID0gdXBkYXRlQXV0aFJvbGU7CmV4cG9ydHMudXBkYXRlVXNlciA9IHVwZGF0ZVVzZXI7CmV4cG9ydHMudXBkYXRlVXNlclByb2ZpbGUgPSB1cGRhdGVVc2VyUHJvZmlsZTsKZXhwb3J0cy51cGRhdGVVc2VyUHdkID0gdXBkYXRlVXNlclB3ZDsKZXhwb3J0cy51cGxvYWRBdmF0YXIgPSB1cGxvYWRBdmF0YXI7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwp2YXIgX3J1b3lpID0gcmVxdWlyZSgiQC91dGlscy9ydW95aSIpOwovLyDmn6Xor6LnlKjmiLfliJfooagKZnVuY3Rpb24gbGlzdFVzZXIocXVlcnkpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9zeXN0ZW0vdXNlci9saXN0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOafpeivoueUqOaIt+ivpue7hgpmdW5jdGlvbiBnZXRVc2VyKHVzZXJJZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS91c2VyLycgKyAoMCwgX3J1b3lpLnBhcnNlU3RyRW1wdHkpKHVzZXJJZCksCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOaWsOWinueUqOaItwpmdW5jdGlvbiBhZGRVc2VyKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9zeXN0ZW0vdXNlcicsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5L+u5pS555So5oi3CmZ1bmN0aW9uIHVwZGF0ZVVzZXIoZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS91c2VyJywKICAgIG1ldGhvZDogJ3B1dCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOWIoOmZpOeUqOaItwpmdW5jdGlvbiBkZWxVc2VyKHVzZXJJZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS91c2VyLycgKyB1c2VySWQsCiAgICBtZXRob2Q6ICdkZWxldGUnCiAgfSk7Cn0KCi8vIOeUqOaIt+WvhueggemHjee9rgpmdW5jdGlvbiByZXNldFVzZXJQd2QodXNlcklkLCBwYXNzd29yZCkgewogIHZhciBkYXRhID0gewogICAgdXNlcklkOiB1c2VySWQsCiAgICBwYXNzd29yZDogcGFzc3dvcmQKICB9OwogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS91c2VyL3Jlc2V0UHdkJywKICAgIG1ldGhvZDogJ3B1dCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOeUqOaIt+eKtuaAgeS/ruaUuQpmdW5jdGlvbiBjaGFuZ2VVc2VyU3RhdHVzKHVzZXJJZCwgc3RhdHVzKSB7CiAgdmFyIGRhdGEgPSB7CiAgICB1c2VySWQ6IHVzZXJJZCwKICAgIHN0YXR1czogc3RhdHVzCiAgfTsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9zeXN0ZW0vdXNlci9jaGFuZ2VTdGF0dXMnLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5p+l6K+i55So5oi35Liq5Lq65L+h5oGvCmZ1bmN0aW9uIGdldFVzZXJQcm9maWxlKCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS91c2VyL3Byb2ZpbGUnLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDkv67mlLnnlKjmiLfkuKrkurrkv6Hmga8KZnVuY3Rpb24gdXBkYXRlVXNlclByb2ZpbGUoZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS91c2VyL3Byb2ZpbGUnLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g55So5oi35a+G56CB6YeN572uCmZ1bmN0aW9uIHVwZGF0ZVVzZXJQd2Qob2xkUGFzc3dvcmQsIG5ld1Bhc3N3b3JkKSB7CiAgdmFyIGRhdGEgPSB7CiAgICBvbGRQYXNzd29yZDogb2xkUGFzc3dvcmQsCiAgICBuZXdQYXNzd29yZDogbmV3UGFzc3dvcmQKICB9OwogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS91c2VyL3Byb2ZpbGUvdXBkYXRlUHdkJywKICAgIG1ldGhvZDogJ3B1dCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOeUqOaIt+WktOWDj+S4iuS8oApmdW5jdGlvbiB1cGxvYWRBdmF0YXIoZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS91c2VyL3Byb2ZpbGUvYXZhdGFyJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgaGVhZGVyczogewogICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL3gtd3d3LWZvcm0tdXJsZW5jb2RlZCcKICAgIH0sCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOafpeivouaOiOadg+inkuiJsgpmdW5jdGlvbiBnZXRBdXRoUm9sZSh1c2VySWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9zeXN0ZW0vdXNlci9hdXRoUm9sZS8nICsgdXNlcklkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDkv53lrZjmjojmnYPop5LoibIKZnVuY3Rpb24gdXBkYXRlQXV0aFJvbGUoZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS91c2VyL2F1dGhSb2xlJywKICAgIG1ldGhvZDogJ3B1dCcsCiAgICBwYXJhbXM6IGRhdGEKICB9KTsKfQoKLy8g5p+l6K+i6YOo6Zeo5LiL5ouJ5qCR57uT5p6ECmZ1bmN0aW9uIGRlcHRUcmVlU2VsZWN0KCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS91c2VyL2RlcHRUcmVlJywKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "_ruoyi", "listUser", "query", "request", "url", "method", "params", "getUser", "userId", "parseStrEmpty", "addUser", "data", "updateUser", "<PERSON><PERSON><PERSON>", "resetUserPwd", "password", "changeUserStatus", "status", "getUserProfile", "updateUserProfile", "updateUserPwd", "oldPassword", "newPassword", "uploadAvatar", "headers", "getAuthRole", "updateAuthRole", "deptTreeSelect"], "sources": ["D:/thinktank/thinktankui/src/api/system/user.js"], "sourcesContent": ["import request from '@/utils/request'\r\nimport { parseStrEmpty } from \"@/utils/ruoyi\";\r\n\r\n// 查询用户列表\r\nexport function listUser(query) {\r\n  return request({\r\n    url: '/system/user/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询用户详细\r\nexport function getUser(userId) {\r\n  return request({\r\n    url: '/system/user/' + parseStrEmpty(userId),\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增用户\r\nexport function addUser(data) {\r\n  return request({\r\n    url: '/system/user',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改用户\r\nexport function updateUser(data) {\r\n  return request({\r\n    url: '/system/user',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除用户\r\nexport function delUser(userId) {\r\n  return request({\r\n    url: '/system/user/' + userId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 用户密码重置\r\nexport function resetUserPwd(userId, password) {\r\n  const data = {\r\n    userId,\r\n    password\r\n  }\r\n  return request({\r\n    url: '/system/user/resetPwd',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 用户状态修改\r\nexport function changeUserStatus(userId, status) {\r\n  const data = {\r\n    userId,\r\n    status\r\n  }\r\n  return request({\r\n    url: '/system/user/changeStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 查询用户个人信息\r\nexport function getUserProfile() {\r\n  return request({\r\n    url: '/system/user/profile',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 修改用户个人信息\r\nexport function updateUserProfile(data) {\r\n  return request({\r\n    url: '/system/user/profile',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 用户密码重置\r\nexport function updateUserPwd(oldPassword, newPassword) {\r\n  const data = {\r\n    oldPassword,\r\n    newPassword\r\n  }\r\n  return request({\r\n    url: '/system/user/profile/updatePwd',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 用户头像上传\r\nexport function uploadAvatar(data) {\r\n  return request({\r\n    url: '/system/user/profile/avatar',\r\n    method: 'post',\r\n    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },\r\n    data: data\r\n  })\r\n}\r\n\r\n// 查询授权角色\r\nexport function getAuthRole(userId) {\r\n  return request({\r\n    url: '/system/user/authRole/' + userId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 保存授权角色\r\nexport function updateAuthRole(data) {\r\n  return request({\r\n    url: '/system/user/authRole',\r\n    method: 'put',\r\n    params: data\r\n  })\r\n}\r\n\r\n// 查询部门下拉树结构\r\nexport function deptTreeSelect() {\r\n  return request({\r\n    url: '/system/user/deptTree',\r\n    method: 'get'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAEA;AACO,SAASE,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAG,IAAAK,oBAAa,EAACD,MAAM,CAAC;IAC5CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,OAAOA,CAACL,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,YAAYA,CAACN,MAAM,EAAEO,QAAQ,EAAE;EAC7C,IAAMJ,IAAI,GAAG;IACXH,MAAM,EAANA,MAAM;IACNO,QAAQ,EAARA;EACF,CAAC;EACD,OAAO,IAAAZ,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,gBAAgBA,CAACR,MAAM,EAAES,MAAM,EAAE;EAC/C,IAAMN,IAAI,GAAG;IACXH,MAAM,EAANA,MAAM;IACNS,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAd,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,cAAcA,CAAA,EAAG;EAC/B,OAAO,IAAAf,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASc,iBAAiBA,CAACR,IAAI,EAAE;EACtC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,aAAaA,CAACC,WAAW,EAAEC,WAAW,EAAE;EACtD,IAAMX,IAAI,GAAG;IACXU,WAAW,EAAXA,WAAW;IACXC,WAAW,EAAXA;EACF,CAAC;EACD,OAAO,IAAAnB,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,YAAYA,CAACZ,IAAI,EAAE;EACjC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,MAAM;IACdmB,OAAO,EAAE;MAAE,cAAc,EAAE;IAAoC,CAAC;IAChEb,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASc,WAAWA,CAACjB,MAAM,EAAE;EAClC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB,GAAGI,MAAM;IACtCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASqB,cAAcA,CAACf,IAAI,EAAE;EACnC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEK;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASgB,cAAcA,CAAA,EAAG;EAC/B,OAAO,IAAAxB,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}