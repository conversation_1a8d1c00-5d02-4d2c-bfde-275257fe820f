{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\layout\\components\\Sidebar\\SidebarItem.vue?vue&type=template&id=2d2bbdc2", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\layout\\components\\Sidebar\\SidebarItem.vue", "mtime": 1749104047626}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749104421286}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}