{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\monitor\\cache\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\monitor\\cache\\list.vue", "mtime": 1749104047642}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0Q2FjaGVOYW1lLCBsaXN0Q2FjaGVLZXksIGdldENhY2hlVmFsdWUsIGNsZWFyQ2FjaGVOYW1lLCBjbGVhckNhY2hlS2V5LCBjbGVhckNhY2hlQWxsIH0gZnJvbSAiQC9hcGkvbW9uaXRvci9jYWNoZSI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkNhY2hlTGlzdCIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGNhY2hlTmFtZXM6IFtdLA0KICAgICAgY2FjaGVLZXlzOiBbXSwNCiAgICAgIGNhY2hlRm9ybToge30sDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgc3ViTG9hZGluZzogZmFsc2UsDQogICAgICBub3dDYWNoZU5hbWU6ICIiLA0KICAgICAgdGFibGVIZWlnaHQ6IHdpbmRvdy5pbm5lckhlaWdodCAtIDIwMA0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRDYWNoZU5hbWVzKCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKiog5p+l6K+i57yT5a2Y5ZCN56ew5YiX6KGoICovDQogICAgZ2V0Q2FjaGVOYW1lcygpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0Q2FjaGVOYW1lKCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuY2FjaGVOYW1lcyA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Yi35paw57yT5a2Y5ZCN56ew5YiX6KGoICovDQogICAgcmVmcmVzaENhY2hlTmFtZXMoKSB7DQogICAgICB0aGlzLmdldENhY2hlTmFtZXMoKTsNCiAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIt+aWsOe8k+WtmOWIl+ihqOaIkOWKnyIpOw0KICAgIH0sDQogICAgLyoqIOa4heeQhuaMh+WumuWQjeensOe8k+WtmCAqLw0KICAgIGhhbmRsZUNsZWFyQ2FjaGVOYW1lKHJvdykgew0KICAgICAgY2xlYXJDYWNoZU5hbWUocm93LmNhY2hlTmFtZSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIua4heeQhue8k+WtmOWQjeensFsiICsgcm93LmNhY2hlTmFtZSArICJd5oiQ5YqfIik7DQogICAgICAgIHRoaXMuZ2V0Q2FjaGVLZXlzKCk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDmn6Xor6LnvJPlrZjplK7lkI3liJfooaggKi8NCiAgICBnZXRDYWNoZUtleXMocm93KSB7DQogICAgICBjb25zdCBjYWNoZU5hbWUgPSByb3cgIT09IHVuZGVmaW5lZCA/IHJvdy5jYWNoZU5hbWUgOiB0aGlzLm5vd0NhY2hlTmFtZTsNCiAgICAgIGlmIChjYWNoZU5hbWUgPT09ICIiKSB7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIHRoaXMuc3ViTG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0Q2FjaGVLZXkoY2FjaGVOYW1lKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5jYWNoZUtleXMgPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB0aGlzLnN1YkxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgdGhpcy5ub3dDYWNoZU5hbWUgPSBjYWNoZU5hbWU7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDliLfmlrDnvJPlrZjplK7lkI3liJfooaggKi8NCiAgICByZWZyZXNoQ2FjaGVLZXlzKCkgew0KICAgICAgdGhpcy5nZXRDYWNoZUtleXMoKTsNCiAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIt+aWsOmUruWQjeWIl+ihqOaIkOWKnyIpOw0KICAgIH0sDQogICAgLyoqIOa4heeQhuaMh+WumumUruWQjee8k+WtmCAqLw0KICAgIGhhbmRsZUNsZWFyQ2FjaGVLZXkoY2FjaGVLZXkpIHsNCiAgICAgIGNsZWFyQ2FjaGVLZXkoY2FjaGVLZXkpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmuIXnkIbnvJPlrZjplK7lkI1bIiArIGNhY2hlS2V5ICsgIl3miJDlip8iKTsNCiAgICAgICAgdGhpcy5nZXRDYWNoZUtleXMoKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWIl+ihqOWJjee8gOWOu+mZpCAqLw0KICAgIG5hbWVGb3JtYXR0ZXIocm93KSB7DQogICAgICByZXR1cm4gcm93LmNhY2hlTmFtZS5yZXBsYWNlKCI6IiwgIiIpOw0KICAgIH0sDQogICAgLyoqIOmUruWQjeWJjee8gOWOu+mZpCAqLw0KICAgIGtleUZvcm1hdHRlcihjYWNoZUtleSkgew0KICAgICAgcmV0dXJuIGNhY2hlS2V5LnJlcGxhY2UodGhpcy5ub3dDYWNoZU5hbWUsICIiKTsNCiAgICB9LA0KICAgIC8qKiDmn6Xor6LnvJPlrZjlhoXlrrnor6bnu4YgKi8NCiAgICBoYW5kbGVDYWNoZVZhbHVlKGNhY2hlS2V5KSB7DQogICAgICBnZXRDYWNoZVZhbHVlKHRoaXMubm93Q2FjaGVOYW1lLCBjYWNoZUtleSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuY2FjaGVGb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOa4heeQhuWFqOmDqOe8k+WtmCAqLw0KICAgIGhhbmRsZUNsZWFyQ2FjaGVBbGwoKSB7DQogICAgICBjbGVhckNhY2hlQWxsKCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIua4heeQhuWFqOmDqOe8k+WtmOaIkOWKnyIpOw0KICAgICAgfSk7DQogICAgfQ0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["list.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0JA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "list.vue", "sourceRoot": "src/views/monitor/cache", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"10\">\r\n      <el-col :span=\"8\">\r\n        <el-card style=\"height: calc(100vh - 125px)\">\r\n          <div slot=\"header\">\r\n            <span><i class=\"el-icon-collection\"></i> 缓存列表</span>\r\n            <el-button\r\n              style=\"float: right; padding: 3px 0\"\r\n              type=\"text\"\r\n              icon=\"el-icon-refresh-right\"\r\n              @click=\"refreshCacheNames()\"\r\n            ></el-button>\r\n          </div>\r\n          <el-table\r\n            v-loading=\"loading\"\r\n            :data=\"cacheNames\"\r\n            :height=\"tableHeight\"\r\n            highlight-current-row\r\n            @row-click=\"getCacheKeys\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-table-column\r\n              label=\"序号\"\r\n              width=\"60\"\r\n              type=\"index\"\r\n            ></el-table-column>\r\n\r\n            <el-table-column\r\n              label=\"缓存名称\"\r\n              align=\"center\"\r\n              prop=\"cacheName\"\r\n              :show-overflow-tooltip=\"true\"\r\n              :formatter=\"nameFormatter\"\r\n            ></el-table-column>\r\n\r\n            <el-table-column\r\n              label=\"备注\"\r\n              align=\"center\"\r\n              prop=\"remark\"\r\n              :show-overflow-tooltip=\"true\"\r\n            />\r\n            <el-table-column\r\n              label=\"操作\"\r\n              width=\"60\"\r\n              align=\"center\"\r\n              class-name=\"small-padding fixed-width\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  size=\"mini\"\r\n                  type=\"text\"\r\n                  icon=\"el-icon-delete\"\r\n                  @click=\"handleClearCacheName(scope.row)\"\r\n                ></el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <el-col :span=\"8\">\r\n        <el-card style=\"height: calc(100vh - 125px)\">\r\n          <div slot=\"header\">\r\n            <span><i class=\"el-icon-key\"></i> 键名列表</span>\r\n            <el-button\r\n              style=\"float: right; padding: 3px 0\"\r\n              type=\"text\"\r\n              icon=\"el-icon-refresh-right\"\r\n              @click=\"refreshCacheKeys()\"\r\n            ></el-button>\r\n          </div>\r\n          <el-table\r\n            v-loading=\"subLoading\"\r\n            :data=\"cacheKeys\"\r\n            :height=\"tableHeight\"\r\n            highlight-current-row\r\n            @row-click=\"handleCacheValue\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-table-column\r\n              label=\"序号\"\r\n              width=\"60\"\r\n              type=\"index\"\r\n            ></el-table-column>\r\n            <el-table-column\r\n              label=\"缓存键名\"\r\n              align=\"center\"\r\n              :show-overflow-tooltip=\"true\"\r\n              :formatter=\"keyFormatter\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"操作\"\r\n              width=\"60\"\r\n              align=\"center\"\r\n              class-name=\"small-padding fixed-width\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  size=\"mini\"\r\n                  type=\"text\"\r\n                  icon=\"el-icon-delete\"\r\n                  @click=\"handleClearCacheKey(scope.row)\"\r\n                ></el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <el-col :span=\"8\">\r\n        <el-card :bordered=\"false\" style=\"height: calc(100vh - 125px)\">\r\n          <div slot=\"header\">\r\n            <span><i class=\"el-icon-document\"></i> 缓存内容</span>\r\n            <el-button\r\n              style=\"float: right; padding: 3px 0\"\r\n              type=\"text\"\r\n              icon=\"el-icon-refresh-right\"\r\n              @click=\"handleClearCacheAll()\"\r\n              >清理全部</el-button\r\n            >\r\n          </div>\r\n          <el-form :model=\"cacheForm\">\r\n            <el-row :gutter=\"32\">\r\n              <el-col :offset=\"1\" :span=\"22\">\r\n                <el-form-item label=\"缓存名称:\" prop=\"cacheName\">\r\n                  <el-input v-model=\"cacheForm.cacheName\" :readOnly=\"true\" />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :offset=\"1\" :span=\"22\">\r\n                <el-form-item label=\"缓存键名:\" prop=\"cacheKey\">\r\n                  <el-input v-model=\"cacheForm.cacheKey\" :readOnly=\"true\" />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :offset=\"1\" :span=\"22\">\r\n                <el-form-item label=\"缓存内容:\" prop=\"cacheValue\">\r\n                  <el-input\r\n                    v-model=\"cacheForm.cacheValue\"\r\n                    type=\"textarea\"\r\n                    :rows=\"8\"\r\n                    :readOnly=\"true\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n          </el-form>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listCacheName, listCacheKey, getCacheValue, clearCacheName, clearCacheKey, clearCacheAll } from \"@/api/monitor/cache\";\r\n\r\nexport default {\r\n  name: \"CacheList\",\r\n  data() {\r\n    return {\r\n      cacheNames: [],\r\n      cacheKeys: [],\r\n      cacheForm: {},\r\n      loading: true,\r\n      subLoading: false,\r\n      nowCacheName: \"\",\r\n      tableHeight: window.innerHeight - 200\r\n    };\r\n  },\r\n  created() {\r\n    this.getCacheNames();\r\n  },\r\n  methods: {\r\n    /** 查询缓存名称列表 */\r\n    getCacheNames() {\r\n      this.loading = true;\r\n      listCacheName().then(response => {\r\n        this.cacheNames = response.data;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 刷新缓存名称列表 */\r\n    refreshCacheNames() {\r\n      this.getCacheNames();\r\n      this.$modal.msgSuccess(\"刷新缓存列表成功\");\r\n    },\r\n    /** 清理指定名称缓存 */\r\n    handleClearCacheName(row) {\r\n      clearCacheName(row.cacheName).then(response => {\r\n        this.$modal.msgSuccess(\"清理缓存名称[\" + row.cacheName + \"]成功\");\r\n        this.getCacheKeys();\r\n      });\r\n    },\r\n    /** 查询缓存键名列表 */\r\n    getCacheKeys(row) {\r\n      const cacheName = row !== undefined ? row.cacheName : this.nowCacheName;\r\n      if (cacheName === \"\") {\r\n        return;\r\n      }\r\n      this.subLoading = true;\r\n      listCacheKey(cacheName).then(response => {\r\n        this.cacheKeys = response.data;\r\n        this.subLoading = false;\r\n        this.nowCacheName = cacheName;\r\n      });\r\n    },\r\n    /** 刷新缓存键名列表 */\r\n    refreshCacheKeys() {\r\n      this.getCacheKeys();\r\n      this.$modal.msgSuccess(\"刷新键名列表成功\");\r\n    },\r\n    /** 清理指定键名缓存 */\r\n    handleClearCacheKey(cacheKey) {\r\n      clearCacheKey(cacheKey).then(response => {\r\n        this.$modal.msgSuccess(\"清理缓存键名[\" + cacheKey + \"]成功\");\r\n        this.getCacheKeys();\r\n      });\r\n    },\r\n    /** 列表前缀去除 */\r\n    nameFormatter(row) {\r\n      return row.cacheName.replace(\":\", \"\");\r\n    },\r\n    /** 键名前缀去除 */\r\n    keyFormatter(cacheKey) {\r\n      return cacheKey.replace(this.nowCacheName, \"\");\r\n    },\r\n    /** 查询缓存内容详细 */\r\n    handleCacheValue(cacheKey) {\r\n      getCacheValue(this.nowCacheName, cacheKey).then(response => {\r\n        this.cacheForm = response.data;\r\n      });\r\n    },\r\n    /** 清理全部缓存 */\r\n    handleClearCacheAll() {\r\n      clearCacheAll().then(response => {\r\n        this.$modal.msgSuccess(\"清理全部缓存成功\");\r\n      });\r\n    }\r\n  },\r\n};\r\n</script>\r\n"]}]}