{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\dashboard\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\dashboard\\index.vue", "mtime": 1749104047639}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5jb25jYXQuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnB1c2guanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmZ1bmN0aW9uLm5hbWUuanMiKTsKcmVxdWlyZSgiYW50LWRlc2lnbi12dWUvZXMvYmFkZ2Uvc3R5bGUiKTsKdmFyIF9iYWRnZSA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiYW50LWRlc2lnbi12dWUvZXMvYmFkZ2UiKSk7CnJlcXVpcmUoImFudC1kZXNpZ24tdnVlL2VzL3N3aXRjaC9zdHlsZSIpOwp2YXIgX3N3aXRjaCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiYW50LWRlc2lnbi12dWUvZXMvc3dpdGNoIikpOwpyZXF1aXJlKCJhbnQtZGVzaWduLXZ1ZS9lcy9kaXZpZGVyL3N0eWxlIik7CnZhciBfZGl2aWRlciA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiYW50LWRlc2lnbi12dWUvZXMvZGl2aWRlciIpKTsKcmVxdWlyZSgiYW50LWRlc2lnbi12dWUvZXMvdGFnL3N0eWxlIik7CnZhciBfdGFnID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJhbnQtZGVzaWduLXZ1ZS9lcy90YWciKSk7CnJlcXVpcmUoImFudC1kZXNpZ24tdnVlL2VzL3N0YXRpc3RpYy9zdHlsZSIpOwp2YXIgX3N0YXRpc3RpYyA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiYW50LWRlc2lnbi12dWUvZXMvc3RhdGlzdGljIikpOwpyZXF1aXJlKCJhbnQtZGVzaWduLXZ1ZS9lcy9yb3cvc3R5bGUiKTsKdmFyIF9yb3cgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoImFudC1kZXNpZ24tdnVlL2VzL3JvdyIpKTsKcmVxdWlyZSgiYW50LWRlc2lnbi12dWUvZXMvbGlzdC9zdHlsZSIpOwp2YXIgX2xpc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoImFudC1kZXNpZ24tdnVlL2VzL2xpc3QiKSk7CnJlcXVpcmUoImFudC1kZXNpZ24tdnVlL2VzL2NvbC9zdHlsZSIpOwp2YXIgX2NvbCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiYW50LWRlc2lnbi12dWUvZXMvY29sIikpOwpyZXF1aXJlKCJhbnQtZGVzaWduLXZ1ZS9lcy9jYXJkL3N0eWxlIik7CnZhciBfY2FyZCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiYW50LWRlc2lnbi12dWUvZXMvY2FyZCIpKTsKcmVxdWlyZSgiYW50LWRlc2lnbi12dWUvZXMvYnV0dG9uL3N0eWxlIik7CnZhciBfYnV0dG9uID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJhbnQtZGVzaWduLXZ1ZS9lcy9idXR0b24iKSk7CnJlcXVpcmUoImFudC1kZXNpZ24tdnVlL2VzL2F2YXRhci9zdHlsZSIpOwp2YXIgX2F2YXRhciA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiYW50LWRlc2lnbi12dWUvZXMvYXZhdGFyIikpOwp2YXIgX3Zpc2VyVnVlID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJ2aXNlci12dWUiKSk7CnJlcXVpcmUoImFudC1kZXNpZ24tdnVlL2Rpc3QvYW50ZC5jc3MiKTsKdmFyIF92dWUgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoInZ1ZSIpKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCl92dWUuZGVmYXVsdC51c2UoX3Zpc2VyVnVlLmRlZmF1bHQpOwpfdnVlLmRlZmF1bHQuY29tcG9uZW50KF9hdmF0YXIuZGVmYXVsdC5uYW1lLCBfYXZhdGFyLmRlZmF1bHQpOwpfdnVlLmRlZmF1bHQuY29tcG9uZW50KF9idXR0b24uZGVmYXVsdC5uYW1lLCBfYnV0dG9uLmRlZmF1bHQpOwpfdnVlLmRlZmF1bHQuY29tcG9uZW50KF9jYXJkLmRlZmF1bHQubmFtZSwgX2NhcmQuZGVmYXVsdCk7Cl92dWUuZGVmYXVsdC5jb21wb25lbnQoX2NhcmQuZGVmYXVsdC5HcmlkLm5hbWUsIF9jYXJkLmRlZmF1bHQuR3JpZCk7Cl92dWUuZGVmYXVsdC5jb21wb25lbnQoX2NhcmQuZGVmYXVsdC5NZXRhLm5hbWUsIF9jYXJkLmRlZmF1bHQuTWV0YSk7Cl92dWUuZGVmYXVsdC5jb21wb25lbnQoX2NvbC5kZWZhdWx0Lm5hbWUsIF9jb2wuZGVmYXVsdCk7Cl92dWUuZGVmYXVsdC5jb21wb25lbnQoX2xpc3QuZGVmYXVsdC5uYW1lLCBfbGlzdC5kZWZhdWx0KTsKX3Z1ZS5kZWZhdWx0LmNvbXBvbmVudChfbGlzdC5kZWZhdWx0Lkl0ZW0ubmFtZSwgX2xpc3QuZGVmYXVsdC5JdGVtKTsKX3Z1ZS5kZWZhdWx0LmNvbXBvbmVudChfbGlzdC5kZWZhdWx0Lkl0ZW0uTWV0YS5uYW1lLCBfbGlzdC5kZWZhdWx0Lkl0ZW0uTWV0YSk7Cl92dWUuZGVmYXVsdC5jb21wb25lbnQoX3Jvdy5kZWZhdWx0Lm5hbWUsIF9yb3cuZGVmYXVsdCk7Cl92dWUuZGVmYXVsdC5jb21wb25lbnQoX3N0YXRpc3RpYy5kZWZhdWx0Lm5hbWUsIF9zdGF0aXN0aWMuZGVmYXVsdCk7Cl92dWUuZGVmYXVsdC5jb21wb25lbnQoX3RhZy5kZWZhdWx0Lm5hbWUsIF90YWcuZGVmYXVsdCk7Cl92dWUuZGVmYXVsdC5jb21wb25lbnQoX2RpdmlkZXIuZGVmYXVsdC5uYW1lLCBfZGl2aWRlci5kZWZhdWx0KTsKX3Z1ZS5kZWZhdWx0LmNvbXBvbmVudChfc3dpdGNoLmRlZmF1bHQubmFtZSwgX3N3aXRjaC5kZWZhdWx0KTsKX3Z1ZS5kZWZhdWx0LmNvbXBvbmVudChfYmFkZ2UuZGVmYXVsdC5uYW1lLCBfYmFkZ2UuZGVmYXVsdCk7CnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAiRGFzaEJvYXJkIiwKICBtZXRob2RzOiB7CiAgICBnb1RvU2VhcmNoOiBmdW5jdGlvbiBnb1RvU2VhcmNoKCkgewogICAgICAvLyDot7PovazliLDmkJzntKLnu5PmnpzpobXpnaLvvIzlubbkvKDpgJLmtYvor5XmkJzntKLlhbPplK7or40KICAgICAgdGhpcy4kcm91dGVyLnB1c2goewogICAgICAgIHBhdGg6ICcvc2VhcmNoLXJlc3VsdHMnLAogICAgICAgIHF1ZXJ5OiB7CiAgICAgICAgICBxOiAn5pa55aSqIOWOqOeUtScKICAgICAgICB9CiAgICAgIH0pOwogICAgfQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIG9yaWdpbmFsVG9wTmF2OiB1bmRlZmluZWQsCiAgICAgIC8vIOWtmOWCqOWOn+Wni+eahHRvcE5hdueKtuaAgQogICAgICBjdXJyZW50VXNlcjogewogICAgICAgIGF2YXRhcjogImh0dHBzOi8vZ3cuYWxpcGF5b2JqZWN0cy5jb20vem9zL3Jtc3BvcnRhbC9CaWF6ZmFueG1hbU5Sb3h4VnhrYS5wbmciLAogICAgICAgIG5hbWU6ICLmlrnlpKoiLAogICAgICAgIHVzZXJpZDogIjAwMDAwMDAxIiwKICAgICAgICBlbWFpbDogImZhbmd0YUBleGFtcGxlLmNvbSIsCiAgICAgICAgc2lnbmF0dXJlOiAi6IiG5oOF55uR5o6n77yM5a6e5pe25oqK5o6nIiwKICAgICAgICB0aXRsZTogIuiIhuaDheWIhuaekOW4iCIsCiAgICAgICAgZ3JvdXA6ICLoiIbmg4Xnm5HmjqfkuK3lv4MiCiAgICAgIH0sCiAgICAgIC8vIOe7n+iuoeaVsOaNrgogICAgICBob3RUb3BpY3NDb3VudDogMjQsCiAgICAgIG5lZ2F0aXZlQ291bnQ6IDgsCiAgICAgIHRvdGFsTW9uaXRvckNvdW50OiAxNDQ2LAogICAgICAvLyAyNOWwj+aXtuS8oOaSrei2i+WKv+aVsOaNrgogICAgICB0cmVuZERhdGE6IFt7CiAgICAgICAgdGltZTogIjAwOjAwIiwKICAgICAgICB2YWx1ZTogMzAwLAogICAgICAgIHR5cGU6ICLmgLvph48iCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAiMDE6MDAiLAogICAgICAgIHZhbHVlOiAyNTAsCiAgICAgICAgdHlwZTogIuaAu+mHjyIKICAgICAgfSwgewogICAgICAgIHRpbWU6ICIwMjowMCIsCiAgICAgICAgdmFsdWU6IDIwMCwKICAgICAgICB0eXBlOiAi5oC76YePIgogICAgICB9LCB7CiAgICAgICAgdGltZTogIjAzOjAwIiwKICAgICAgICB2YWx1ZTogMTgwLAogICAgICAgIHR5cGU6ICLmgLvph48iCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAiMDQ6MDAiLAogICAgICAgIHZhbHVlOiAxNTAsCiAgICAgICAgdHlwZTogIuaAu+mHjyIKICAgICAgfSwgewogICAgICAgIHRpbWU6ICIwNTowMCIsCiAgICAgICAgdmFsdWU6IDE3MCwKICAgICAgICB0eXBlOiAi5oC76YePIgogICAgICB9LCB7CiAgICAgICAgdGltZTogIjA2OjAwIiwKICAgICAgICB2YWx1ZTogMjIwLAogICAgICAgIHR5cGU6ICLmgLvph48iCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAiMDc6MDAiLAogICAgICAgIHZhbHVlOiAzNTAsCiAgICAgICAgdHlwZTogIuaAu+mHjyIKICAgICAgfSwgewogICAgICAgIHRpbWU6ICIwODowMCIsCiAgICAgICAgdmFsdWU6IDUwMCwKICAgICAgICB0eXBlOiAi5oC76YePIgogICAgICB9LCB7CiAgICAgICAgdGltZTogIjA5OjAwIiwKICAgICAgICB2YWx1ZTogNjIwLAogICAgICAgIHR5cGU6ICLmgLvph48iCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAiMTA6MDAiLAogICAgICAgIHZhbHVlOiA1NTAsCiAgICAgICAgdHlwZTogIuaAu+mHjyIKICAgICAgfSwgewogICAgICAgIHRpbWU6ICIxMTowMCIsCiAgICAgICAgdmFsdWU6IDQ4MCwKICAgICAgICB0eXBlOiAi5oC76YePIgogICAgICB9LCB7CiAgICAgICAgdGltZTogIjEyOjAwIiwKICAgICAgICB2YWx1ZTogNDAwLAogICAgICAgIHR5cGU6ICLmgLvph48iCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAiMTM6MDAiLAogICAgICAgIHZhbHVlOiA0NTAsCiAgICAgICAgdHlwZTogIuaAu+mHjyIKICAgICAgfSwgewogICAgICAgIHRpbWU6ICIxNDowMCIsCiAgICAgICAgdmFsdWU6IDUwMCwKICAgICAgICB0eXBlOiAi5oC76YePIgogICAgICB9LCB7CiAgICAgICAgdGltZTogIjE1OjAwIiwKICAgICAgICB2YWx1ZTogNDcwLAogICAgICAgIHR5cGU6ICLmgLvph48iCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAiMTY6MDAiLAogICAgICAgIHZhbHVlOiA0NjAsCiAgICAgICAgdHlwZTogIuaAu+mHjyIKICAgICAgfSwgewogICAgICAgIHRpbWU6ICIxNzowMCIsCiAgICAgICAgdmFsdWU6IDUyMCwKICAgICAgICB0eXBlOiAi5oC76YePIgogICAgICB9LCB7CiAgICAgICAgdGltZTogIjE4OjAwIiwKICAgICAgICB2YWx1ZTogNTgwLAogICAgICAgIHR5cGU6ICLmgLvph48iCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAiMTk6MDAiLAogICAgICAgIHZhbHVlOiA1NTAsCiAgICAgICAgdHlwZTogIuaAu+mHjyIKICAgICAgfSwgewogICAgICAgIHRpbWU6ICIyMDowMCIsCiAgICAgICAgdmFsdWU6IDUwMCwKICAgICAgICB0eXBlOiAi5oC76YePIgogICAgICB9LCB7CiAgICAgICAgdGltZTogIjIxOjAwIiwKICAgICAgICB2YWx1ZTogNDUwLAogICAgICAgIHR5cGU6ICLmgLvph48iCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAiMjI6MDAiLAogICAgICAgIHZhbHVlOiA0MDAsCiAgICAgICAgdHlwZTogIuaAu+mHjyIKICAgICAgfSwgewogICAgICAgIHRpbWU6ICIyMzowMCIsCiAgICAgICAgdmFsdWU6IDM1MCwKICAgICAgICB0eXBlOiAi5oC76YePIgogICAgICB9LCB7CiAgICAgICAgdGltZTogIjAwOjAwIiwKICAgICAgICB2YWx1ZTogMTAwLAogICAgICAgIHR5cGU6ICLlvq7ljZoiCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAiMDE6MDAiLAogICAgICAgIHZhbHVlOiA4MCwKICAgICAgICB0eXBlOiAi5b6u5Y2aIgogICAgICB9LCB7CiAgICAgICAgdGltZTogIjAyOjAwIiwKICAgICAgICB2YWx1ZTogNjAsCiAgICAgICAgdHlwZTogIuW+ruWNmiIKICAgICAgfSwgewogICAgICAgIHRpbWU6ICIwMzowMCIsCiAgICAgICAgdmFsdWU6IDUwLAogICAgICAgIHR5cGU6ICLlvq7ljZoiCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAiMDQ6MDAiLAogICAgICAgIHZhbHVlOiA0MCwKICAgICAgICB0eXBlOiAi5b6u5Y2aIgogICAgICB9LCB7CiAgICAgICAgdGltZTogIjA1OjAwIiwKICAgICAgICB2YWx1ZTogNTAsCiAgICAgICAgdHlwZTogIuW+ruWNmiIKICAgICAgfSwgewogICAgICAgIHRpbWU6ICIwNjowMCIsCiAgICAgICAgdmFsdWU6IDcwLAogICAgICAgIHR5cGU6ICLlvq7ljZoiCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAiMDc6MDAiLAogICAgICAgIHZhbHVlOiAxMDAsCiAgICAgICAgdHlwZTogIuW+ruWNmiIKICAgICAgfSwgewogICAgICAgIHRpbWU6ICIwODowMCIsCiAgICAgICAgdmFsdWU6IDE1MCwKICAgICAgICB0eXBlOiAi5b6u5Y2aIgogICAgICB9LCB7CiAgICAgICAgdGltZTogIjA5OjAwIiwKICAgICAgICB2YWx1ZTogMTgwLAogICAgICAgIHR5cGU6ICLlvq7ljZoiCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAiMTA6MDAiLAogICAgICAgIHZhbHVlOiAxNjAsCiAgICAgICAgdHlwZTogIuW+ruWNmiIKICAgICAgfSwgewogICAgICAgIHRpbWU6ICIxMTowMCIsCiAgICAgICAgdmFsdWU6IDE0MCwKICAgICAgICB0eXBlOiAi5b6u5Y2aIgogICAgICB9LCB7CiAgICAgICAgdGltZTogIjEyOjAwIiwKICAgICAgICB2YWx1ZTogMTIwLAogICAgICAgIHR5cGU6ICLlvq7ljZoiCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAiMTM6MDAiLAogICAgICAgIHZhbHVlOiAxMzAsCiAgICAgICAgdHlwZTogIuW+ruWNmiIKICAgICAgfSwgewogICAgICAgIHRpbWU6ICIxNDowMCIsCiAgICAgICAgdmFsdWU6IDE1MCwKICAgICAgICB0eXBlOiAi5b6u5Y2aIgogICAgICB9LCB7CiAgICAgICAgdGltZTogIjE1OjAwIiwKICAgICAgICB2YWx1ZTogMTQwLAogICAgICAgIHR5cGU6ICLlvq7ljZoiCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAiMTY6MDAiLAogICAgICAgIHZhbHVlOiAxMzAsCiAgICAgICAgdHlwZTogIuW+ruWNmiIKICAgICAgfSwgewogICAgICAgIHRpbWU6ICIxNzowMCIsCiAgICAgICAgdmFsdWU6IDE1MCwKICAgICAgICB0eXBlOiAi5b6u5Y2aIgogICAgICB9LCB7CiAgICAgICAgdGltZTogIjE4OjAwIiwKICAgICAgICB2YWx1ZTogMTcwLAogICAgICAgIHR5cGU6ICLlvq7ljZoiCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAiMTk6MDAiLAogICAgICAgIHZhbHVlOiAxNjAsCiAgICAgICAgdHlwZTogIuW+ruWNmiIKICAgICAgfSwgewogICAgICAgIHRpbWU6ICIyMDowMCIsCiAgICAgICAgdmFsdWU6IDE1MCwKICAgICAgICB0eXBlOiAi5b6u5Y2aIgogICAgICB9LCB7CiAgICAgICAgdGltZTogIjIxOjAwIiwKICAgICAgICB2YWx1ZTogMTMwLAogICAgICAgIHR5cGU6ICLlvq7ljZoiCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAiMjI6MDAiLAogICAgICAgIHZhbHVlOiAxMjAsCiAgICAgICAgdHlwZTogIuW+ruWNmiIKICAgICAgfSwgewogICAgICAgIHRpbWU6ICIyMzowMCIsCiAgICAgICAgdmFsdWU6IDEwMCwKICAgICAgICB0eXBlOiAi5b6u5Y2aIgogICAgICB9LCB7CiAgICAgICAgdGltZTogIjAwOjAwIiwKICAgICAgICB2YWx1ZTogODAsCiAgICAgICAgdHlwZTogIuinhumikSIKICAgICAgfSwgewogICAgICAgIHRpbWU6ICIwMTowMCIsCiAgICAgICAgdmFsdWU6IDcwLAogICAgICAgIHR5cGU6ICLop4bpopEiCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAiMDI6MDAiLAogICAgICAgIHZhbHVlOiA2MCwKICAgICAgICB0eXBlOiAi6KeG6aKRIgogICAgICB9LCB7CiAgICAgICAgdGltZTogIjAzOjAwIiwKICAgICAgICB2YWx1ZTogNTAsCiAgICAgICAgdHlwZTogIuinhumikSIKICAgICAgfSwgewogICAgICAgIHRpbWU6ICIwNDowMCIsCiAgICAgICAgdmFsdWU6IDQwLAogICAgICAgIHR5cGU6ICLop4bpopEiCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAiMDU6MDAiLAogICAgICAgIHZhbHVlOiA1MCwKICAgICAgICB0eXBlOiAi6KeG6aKRIgogICAgICB9LCB7CiAgICAgICAgdGltZTogIjA2OjAwIiwKICAgICAgICB2YWx1ZTogNjAsCiAgICAgICAgdHlwZTogIuinhumikSIKICAgICAgfSwgewogICAgICAgIHRpbWU6ICIwNzowMCIsCiAgICAgICAgdmFsdWU6IDgwLAogICAgICAgIHR5cGU6ICLop4bpopEiCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAiMDg6MDAiLAogICAgICAgIHZhbHVlOiAxMDAsCiAgICAgICAgdHlwZTogIuinhumikSIKICAgICAgfSwgewogICAgICAgIHRpbWU6ICIwOTowMCIsCiAgICAgICAgdmFsdWU6IDEyMCwKICAgICAgICB0eXBlOiAi6KeG6aKRIgogICAgICB9LCB7CiAgICAgICAgdGltZTogIjEwOjAwIiwKICAgICAgICB2YWx1ZTogMTEwLAogICAgICAgIHR5cGU6ICLop4bpopEiCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAiMTE6MDAiLAogICAgICAgIHZhbHVlOiAxMDAsCiAgICAgICAgdHlwZTogIuinhumikSIKICAgICAgfSwgewogICAgICAgIHRpbWU6ICIxMjowMCIsCiAgICAgICAgdmFsdWU6IDkwLAogICAgICAgIHR5cGU6ICLop4bpopEiCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAiMTM6MDAiLAogICAgICAgIHZhbHVlOiAxMDAsCiAgICAgICAgdHlwZTogIuinhumikSIKICAgICAgfSwgewogICAgICAgIHRpbWU6ICIxNDowMCIsCiAgICAgICAgdmFsdWU6IDExMCwKICAgICAgICB0eXBlOiAi6KeG6aKRIgogICAgICB9LCB7CiAgICAgICAgdGltZTogIjE1OjAwIiwKICAgICAgICB2YWx1ZTogMTAwLAogICAgICAgIHR5cGU6ICLop4bpopEiCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAiMTY6MDAiLAogICAgICAgIHZhbHVlOiA5MCwKICAgICAgICB0eXBlOiAi6KeG6aKRIgogICAgICB9LCB7CiAgICAgICAgdGltZTogIjE3OjAwIiwKICAgICAgICB2YWx1ZTogMTAwLAogICAgICAgIHR5cGU6ICLop4bpopEiCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAiMTg6MDAiLAogICAgICAgIHZhbHVlOiAxMTAsCiAgICAgICAgdHlwZTogIuinhumikSIKICAgICAgfSwgewogICAgICAgIHRpbWU6ICIxOTowMCIsCiAgICAgICAgdmFsdWU6IDEwMCwKICAgICAgICB0eXBlOiAi6KeG6aKRIgogICAgICB9LCB7CiAgICAgICAgdGltZTogIjIwOjAwIiwKICAgICAgICB2YWx1ZTogOTAsCiAgICAgICAgdHlwZTogIuinhumikSIKICAgICAgfSwgewogICAgICAgIHRpbWU6ICIyMTowMCIsCiAgICAgICAgdmFsdWU6IDgwLAogICAgICAgIHR5cGU6ICLop4bpopEiCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAiMjI6MDAiLAogICAgICAgIHZhbHVlOiA3MCwKICAgICAgICB0eXBlOiAi6KeG6aKRIgogICAgICB9LCB7CiAgICAgICAgdGltZTogIjIzOjAwIiwKICAgICAgICB2YWx1ZTogNjAsCiAgICAgICAgdHlwZTogIuinhumikSIKICAgICAgfV0sCiAgICAgIHRyZW5kU2NhbGU6IFt7CiAgICAgICAgZGF0YUtleTogJ3ZhbHVlJywKICAgICAgICBtaW46IDAKICAgICAgfV0sCiAgICAgIC8vIOW5s+WPsOadpea6kOWNoOavlOaVsOaNrgogICAgICBwbGF0Zm9ybURhdGE6IFt7CiAgICAgICAgdHlwZTogJ+W+ruWNmicsCiAgICAgICAgcGVyY2VudDogMzEuMgogICAgICB9LCB7CiAgICAgICAgdHlwZTogJ+inhumikScsCiAgICAgICAgcGVyY2VudDogMTcuOQogICAgICB9LCB7CiAgICAgICAgdHlwZTogJ+WktOadoeWPtycsCiAgICAgICAgcGVyY2VudDogMTUuMwogICAgICB9LCB7CiAgICAgICAgdHlwZTogJ0FQUCcsCiAgICAgICAgcGVyY2VudDogMTIuNwogICAgICB9LCB7CiAgICAgICAgdHlwZTogJ+W+ruS/oScsCiAgICAgICAgcGVyY2VudDogOS44CiAgICAgIH0sIHsKICAgICAgICB0eXBlOiAn5YW25LuWJywKICAgICAgICBwZXJjZW50OiAxMy4xCiAgICAgIH1dLAogICAgICBwbGF0Zm9ybVNjYWxlOiBbewogICAgICAgIGRhdGFLZXk6ICdwZXJjZW50JywKICAgICAgICBtaW46IDAsCiAgICAgICAgZm9ybWF0dGVyOiAnLjAlJwogICAgICB9XSwKICAgICAgbGFiZWxDb25maWc6IHsKICAgICAgICBvZmZzZXQ6IC0yMCwKICAgICAgICB0ZXh0U3R5bGU6IHsKICAgICAgICAgIGZpbGw6ICcjMDAwJywKICAgICAgICAgIGZvbnRTaXplOiAxMiwKICAgICAgICAgIGZvbnRXZWlnaHQ6ICdib2xkJwogICAgICAgIH0sCiAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbiBmb3JtYXR0ZXIodGV4dCwgaXRlbSkgewogICAgICAgICAgcmV0dXJuICIiLmNvbmNhdChpdGVtLnBvaW50LnR5cGUsICI6ICIpLmNvbmNhdChpdGVtLnBvaW50LnBlcmNlbnQsICIlIik7CiAgICAgICAgfQogICAgICB9LAogICAgICAvLyDmg4XmhJ/liIbluIPljaDmr5TmlbDmja4KICAgICAgc2VudGltZW50RGF0YTogW3sKICAgICAgICB0aW1lOiAnMDA6MDAnLAogICAgICAgIHZhbHVlOiAzMDAsCiAgICAgICAgdHlwZTogJ+S4reaApycKICAgICAgfSwgewogICAgICAgIHRpbWU6ICcwMjowMCcsCiAgICAgICAgdmFsdWU6IDI4MCwKICAgICAgICB0eXBlOiAn5Lit5oCnJwogICAgICB9LCB7CiAgICAgICAgdGltZTogJzA0OjAwJywKICAgICAgICB2YWx1ZTogMjUwLAogICAgICAgIHR5cGU6ICfkuK3mgKcnCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAnMDY6MDAnLAogICAgICAgIHZhbHVlOiAyNjAsCiAgICAgICAgdHlwZTogJ+S4reaApycKICAgICAgfSwgewogICAgICAgIHRpbWU6ICcwODowMCcsCiAgICAgICAgdmFsdWU6IDI4MCwKICAgICAgICB0eXBlOiAn5Lit5oCnJwogICAgICB9LCB7CiAgICAgICAgdGltZTogJzEwOjAwJywKICAgICAgICB2YWx1ZTogMzAwLAogICAgICAgIHR5cGU6ICfkuK3mgKcnCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAnMTI6MDAnLAogICAgICAgIHZhbHVlOiAyODAsCiAgICAgICAgdHlwZTogJ+S4reaApycKICAgICAgfSwgewogICAgICAgIHRpbWU6ICcxNDowMCcsCiAgICAgICAgdmFsdWU6IDI1MCwKICAgICAgICB0eXBlOiAn5Lit5oCnJwogICAgICB9LCB7CiAgICAgICAgdGltZTogJzE2OjAwJywKICAgICAgICB2YWx1ZTogMjYwLAogICAgICAgIHR5cGU6ICfkuK3mgKcnCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAnMTg6MDAnLAogICAgICAgIHZhbHVlOiAyODAsCiAgICAgICAgdHlwZTogJ+S4reaApycKICAgICAgfSwgewogICAgICAgIHRpbWU6ICcyMDowMCcsCiAgICAgICAgdmFsdWU6IDMwMCwKICAgICAgICB0eXBlOiAn5Lit5oCnJwogICAgICB9LCB7CiAgICAgICAgdGltZTogJzIyOjAwJywKICAgICAgICB2YWx1ZTogMjgwLAogICAgICAgIHR5cGU6ICfkuK3mgKcnCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAnMDA6MDAnLAogICAgICAgIHZhbHVlOiAxMDAsCiAgICAgICAgdHlwZTogJ+ato+mdoicKICAgICAgfSwgewogICAgICAgIHRpbWU6ICcwMjowMCcsCiAgICAgICAgdmFsdWU6IDEyMCwKICAgICAgICB0eXBlOiAn5q2j6Z2iJwogICAgICB9LCB7CiAgICAgICAgdGltZTogJzA0OjAwJywKICAgICAgICB2YWx1ZTogMTQwLAogICAgICAgIHR5cGU6ICfmraPpnaInCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAnMDY6MDAnLAogICAgICAgIHZhbHVlOiAxMzAsCiAgICAgICAgdHlwZTogJ+ato+mdoicKICAgICAgfSwgewogICAgICAgIHRpbWU6ICcwODowMCcsCiAgICAgICAgdmFsdWU6IDEyMCwKICAgICAgICB0eXBlOiAn5q2j6Z2iJwogICAgICB9LCB7CiAgICAgICAgdGltZTogJzEwOjAwJywKICAgICAgICB2YWx1ZTogMTAwLAogICAgICAgIHR5cGU6ICfmraPpnaInCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAnMTI6MDAnLAogICAgICAgIHZhbHVlOiAxMjAsCiAgICAgICAgdHlwZTogJ+ato+mdoicKICAgICAgfSwgewogICAgICAgIHRpbWU6ICcxNDowMCcsCiAgICAgICAgdmFsdWU6IDE0MCwKICAgICAgICB0eXBlOiAn5q2j6Z2iJwogICAgICB9LCB7CiAgICAgICAgdGltZTogJzE2OjAwJywKICAgICAgICB2YWx1ZTogMTMwLAogICAgICAgIHR5cGU6ICfmraPpnaInCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAnMTg6MDAnLAogICAgICAgIHZhbHVlOiAxMjAsCiAgICAgICAgdHlwZTogJ+ato+mdoicKICAgICAgfSwgewogICAgICAgIHRpbWU6ICcyMDowMCcsCiAgICAgICAgdmFsdWU6IDEwMCwKICAgICAgICB0eXBlOiAn5q2j6Z2iJwogICAgICB9LCB7CiAgICAgICAgdGltZTogJzIyOjAwJywKICAgICAgICB2YWx1ZTogMTIwLAogICAgICAgIHR5cGU6ICfmraPpnaInCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAnMDA6MDAnLAogICAgICAgIHZhbHVlOiA1MCwKICAgICAgICB0eXBlOiAn6LSf6Z2iJwogICAgICB9LCB7CiAgICAgICAgdGltZTogJzAyOjAwJywKICAgICAgICB2YWx1ZTogNjAsCiAgICAgICAgdHlwZTogJ+i0n+mdoicKICAgICAgfSwgewogICAgICAgIHRpbWU6ICcwNDowMCcsCiAgICAgICAgdmFsdWU6IDcwLAogICAgICAgIHR5cGU6ICfotJ/pnaInCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAnMDY6MDAnLAogICAgICAgIHZhbHVlOiA2NSwKICAgICAgICB0eXBlOiAn6LSf6Z2iJwogICAgICB9LCB7CiAgICAgICAgdGltZTogJzA4OjAwJywKICAgICAgICB2YWx1ZTogNjAsCiAgICAgICAgdHlwZTogJ+i0n+mdoicKICAgICAgfSwgewogICAgICAgIHRpbWU6ICcxMDowMCcsCiAgICAgICAgdmFsdWU6IDUwLAogICAgICAgIHR5cGU6ICfotJ/pnaInCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAnMTI6MDAnLAogICAgICAgIHZhbHVlOiA2MCwKICAgICAgICB0eXBlOiAn6LSf6Z2iJwogICAgICB9LCB7CiAgICAgICAgdGltZTogJzE0OjAwJywKICAgICAgICB2YWx1ZTogNzAsCiAgICAgICAgdHlwZTogJ+i0n+mdoicKICAgICAgfSwgewogICAgICAgIHRpbWU6ICcxNjowMCcsCiAgICAgICAgdmFsdWU6IDY1LAogICAgICAgIHR5cGU6ICfotJ/pnaInCiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAnMTg6MDAnLAogICAgICAgIHZhbHVlOiA2MCwKICAgICAgICB0eXBlOiAn6LSf6Z2iJwogICAgICB9LCB7CiAgICAgICAgdGltZTogJzIwOjAwJywKICAgICAgICB2YWx1ZTogNTAsCiAgICAgICAgdHlwZTogJ+i0n+mdoicKICAgICAgfSwgewogICAgICAgIHRpbWU6ICcyMjowMCcsCiAgICAgICAgdmFsdWU6IDYwLAogICAgICAgIHR5cGU6ICfotJ/pnaInCiAgICAgIH1dLAogICAgICBzZW50aW1lbnRTY2FsZTogW3sKICAgICAgICBkYXRhS2V5OiAndmFsdWUnLAogICAgICAgIG1pbjogMAogICAgICB9XSwKICAgICAgLy8g54Ot6Zeo5paH56ug5YiX6KGoCiAgICAgIGhvdEFydGljbGVzOiBbewogICAgICAgIGlkOiAxLAogICAgICAgIHRpdGxlOiAn5pa55aSq54Ot5rC05ZmoKEZvdGlsZSnlrpjmlrnml5foiLDlupcg5pa55aSq54Ot5rC05ZmoKEZvdGlsZSktMTExIOaWueWkqueDreawtOWZqChGb3RpbGUpIDQwMC42ODA4LjY1NS4uLicsCiAgICAgICAgdGFnOiAn54Ot6ZeoJywKICAgICAgICB0YWdDb2xvcjogJyNmNTAnLAogICAgICAgIHNvdXJjZTogJ+eUteWVhuW5s+WPsCcsCiAgICAgICAgcHVibGlzaFRpbWU6ICcyMDI1LTA0LTI5IDIwOjI0OjAwJywKICAgICAgICBsaW5rOiAnaHR0cHM6Ly9leGFtcGxlLmNvbS9hcnRpY2xlLzEnCiAgICAgIH0sIHsKICAgICAgICBpZDogMiwKICAgICAgICB0aXRsZTogJ+i/keWcsOmTujMw5bm05Zyw5pqW5Y+j56KR77yM6Z2g6ICB5a2X5LqM5YWo5paw6K6+6K6h5L2P5Lq644CCI+aIkOmDveS6jOaJi+aIvyAj5Zyw5pqW55S15rqQJywKICAgICAgICB0YWc6ICfng63pl6gnLAogICAgICAgIHRhZ0NvbG9yOiAnI2Y1MCcsCiAgICAgICAgc291cmNlOiAn6KeG6aKR5bmz5Y+wJywKICAgICAgICBwdWJsaXNoVGltZTogJzIwMjUtMDQtMjkgMTU6MTU6MTYnLAogICAgICAgIGxpbms6ICdodHRwczovL2V4YW1wbGUuY29tL2FydGljbGUvMicKICAgICAgfSwgewogICAgICAgIGlkOiAzLAogICAgICAgIHRpdGxlOiAn546w5Zyo6L+Z5Liq56S+5Lya6KaB6Z2g6ISR6KKL5ZCD6aWt77yM5Y+v6IO95piv5LiA6L6I5a2Q55qE5LqL77yM5byA546p56yRIOaIkeS4gOS4quWunuS9k+S6uiDmm7TlupTor6XmnInlk4HotKjjgIInLAogICAgICAgIHRhZzogJ+eDremXqCcsCiAgICAgICAgdGFnQ29sb3I6ICcjZjUwJywKICAgICAgICBzb3VyY2U6ICfop4bpopHlubPlj7AnLAogICAgICAgIHB1Ymxpc2hUaW1lOiAnMjAyNS0wNC0yOSAxNDoyOTowOScsCiAgICAgICAgbGluazogJ2h0dHBzOi8vZXhhbXBsZS5jb20vYXJ0aWNsZS8zJwogICAgICB9LCB7CiAgICAgICAgaWQ6IDQsCiAgICAgICAgdGl0bGU6ICfvvIjml6DmoIfpopjvvIlTMiBWIGIgIyMgYiAjIyMg6ZmQ5a6aMuS6uuaWueWkquWFqOeQg+mmluWIm+S4gOS7o+eHg+awlOeBtuWFqOeQg+mmluWPkSAwMyBBTE9RQVYgMCAxMyDlm75BLTU5ODYgNTkuLi4nLAogICAgICAgIHRhZzogJ+eDremXqCcsCiAgICAgICAgdGFnQ29sb3I6ICcjZjUwJywKICAgICAgICBzb3VyY2U6ICfop4bpopHlubPlj7AnLAogICAgICAgIHB1Ymxpc2hUaW1lOiAnMjAyNS0wNC0yOSAxNDoxOToyMycsCiAgICAgICAgbGluazogJ2h0dHBzOi8vZXhhbXBsZS5jb20vYXJ0aWNsZS80JwogICAgICB9LCB7CiAgICAgICAgaWQ6IDUsCiAgICAgICAgdGl0bGU6ICfkvaDkuo7ov4fov5nkuovlkJc/IOadpeeci+WQiOS6hiPmlrnlpKrlrpjnvZEgI+WNl+eypCAj5paw5qGl5Y+j5aSn5Y6o55qE5a2X5YaZ5b6XICPlkIzln47lp5DlprnnmoTmnIvlj4vnnIvov4fmnaUgI+eUteW9sScsCiAgICAgICAgdGFnOiAn54Ot6ZeoJywKICAgICAgICB0YWdDb2xvcjogJyNmNTAnLAogICAgICAgIHNvdXJjZTogJ+inhumikeW5s+WPsCcsCiAgICAgICAgcHVibGlzaFRpbWU6ICcyMDI1LTA0LTI5IDEyOjQ4OjA0JywKICAgICAgICBsaW5rOiAnaHR0cHM6Ly9leGFtcGxlLmNvbS9hcnRpY2xlLzUnCiAgICAgIH1dLAogICAgICAvLyDmiqXlkYrmqKHmnb/liJfooagKICAgICAgcmVwb3J0VGVtcGxhdGVzOiBbewogICAgICAgIGlkOiAxLAogICAgICAgIHRpdGxlOiAn6IiG5oOFLeWRqOaKpeihqCcsCiAgICAgICAgY3JlYXRlVGltZTogJzIwMTktMTEtMTYgMTg6MDI6MDAnCiAgICAgIH0sIHsKICAgICAgICBpZDogMiwKICAgICAgICB0aXRsZTogJ+iIhuaDhS3mnIjmiqXooagnLAogICAgICAgIGNyZWF0ZVRpbWU6ICcyMDE5LTExLTE4IDE4OjA2OjUyJwogICAgICB9LCB7CiAgICAgICAgaWQ6IDMsCiAgICAgICAgdGl0bGU6ICfoiIbmg4Ut5a2j5bqm6KGoJywKICAgICAgICBjcmVhdGVUaW1lOiAnMjAyMS0wOS0xMiAxMDoxNTowMCcKICAgICAgfV0sCiAgICAgIC8vIOacgOaWsOi0n+mdouS/oeaBrwogICAgICBuZWdhdGl2ZU5ld3M6IFt7CiAgICAgICAgaWQ6IDEsCiAgICAgICAgdGl0bGU6ICfmlrnlpKrng63msLTlmajlh7rnjrDotKjph4/pl67popjvvIzlpJrlkI3nlKjmiLfmipXor4nml6DkurrlpITnkIYnLAogICAgICAgIHB1Ymxpc2hUaW1lOiAnMjAyNS0wNC0yOSAxOTowMzowMCcsCiAgICAgICAgbGluazogJ2h0dHBzOi8vZXhhbXBsZS5jb20vbmVnYXRpdmUvMScKICAgICAgfSwgewogICAgICAgIGlkOiAyLAogICAgICAgIHRpdGxlOiAn5pa55aSq5Y6o55S15a6J6KOF55S16K+d5peg5Lq65o6l5ZCs77yM5a6i5oi35oqV6K+J5pyN5Yqh5oCB5bqm5beuJywKICAgICAgICBwdWJsaXNoVGltZTogJzIwMjUtMDQtMjkgMTg6MjI6NDMnLAogICAgICAgIGxpbms6ICdodHRwczovL2V4YW1wbGUuY29tL25lZ2F0aXZlLzInCiAgICAgIH0sIHsKICAgICAgICBpZDogMywKICAgICAgICB0aXRsZTogJ+iIquepuuWls+WPi+WBt+aLjeaXpeiusOiiq+ekvO+8jOe9keWPi+WQju+8jOaIkeWcqOW6iuS4iueci+WIsOS6hueUteinhuS4gOW5leeahOWluScsCiAgICAgICAgcHVibGlzaFRpbWU6ICcyMDI1LTA0LTI5IDE3OjQ4OjQ1JywKICAgICAgICBsaW5rOiAnaHR0cHM6Ly9leGFtcGxlLmNvbS9uZWdhdGl2ZS8zJwogICAgICB9LCB7CiAgICAgICAgaWQ6IDQsCiAgICAgICAgdGl0bGU6ICfmn5Dlk4HniYzlpKflnovmir3msrnng5/mnLrlmarpn7Ppl67popjlvJXlj5HnlKjmiLfkuI3mu6EnLAogICAgICAgIHB1Ymxpc2hUaW1lOiAnMjAyNS0wNC0yOSAxNToxNToxNicsCiAgICAgICAgbGluazogJ2h0dHBzOi8vZXhhbXBsZS5jb20vbmVnYXRpdmUvNCcKICAgICAgfSwgewogICAgICAgIGlkOiA1LAogICAgICAgIHRpdGxlOiAn5a6255S15ZSu5ZCO5pyN5Yqh6LCD5p+l77ya5aSa5ZOB54mM5pyN5Yqh6LSo6YeP5Y+C5beu5LiN6b2QJywKICAgICAgICBwdWJsaXNoVGltZTogJzIwMjUtMDQtMjkgMTI6MjY6MDQnLAogICAgICAgIGxpbms6ICdodHRwczovL2V4YW1wbGUuY29tL25lZ2F0aXZlLzUnCiAgICAgIH1dLAogICAgICBsb2FkaW5nOiB0cnVlCiAgICB9OwogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAvLyDpmpDol4/pobbpg6jlr7zoiKrmoI8KICAgIHRoaXMub3JpZ2luYWxUb3BOYXYgPSB0aGlzLiRzdG9yZS5zdGF0ZS5zZXR0aW5ncy50b3BOYXY7CiAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnc2V0dGluZ3MvY2hhbmdlU2V0dGluZycsIHsKICAgICAga2V5OiAndG9wTmF2JywKICAgICAgdmFsdWU6IGZhbHNlCiAgICB9KTsKICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICB9LCAxMDAwKTsKICB9LAogIGJlZm9yZURlc3Ryb3k6IGZ1bmN0aW9uIGJlZm9yZURlc3Ryb3koKSB7CiAgICAvLyDmgaLlpI3pobbpg6jlr7zoiKrmoI/orr7nva4KICAgIGlmICh0aGlzLm9yaWdpbmFsVG9wTmF2ICE9PSB1bmRlZmluZWQpIHsKICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ3NldHRpbmdzL2NoYW5nZVNldHRpbmcnLCB7CiAgICAgICAga2V5OiAndG9wTmF2JywKICAgICAgICB2YWx1ZTogdGhpcy5vcmlnaW5hbFRvcE5hdgogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_viserVue", "_interopRequireDefault", "require", "_vue", "<PERSON><PERSON>", "use", "Viser", "component", "_avatar", "default", "name", "_button", "_card", "Grid", "Meta", "_col", "_list", "<PERSON><PERSON>", "_row", "_statistic", "_tag", "_divider", "_switch", "_badge", "_default", "exports", "methods", "goToSearch", "$router", "push", "path", "query", "q", "data", "originalTopNav", "undefined", "currentUser", "avatar", "userid", "email", "signature", "title", "group", "hotTopicsCount", "negativeCount", "totalMonitorCount", "trendData", "time", "value", "type", "trendScale", "dataKey", "min", "platformData", "percent", "platformScale", "formatter", "labelConfig", "offset", "textStyle", "fill", "fontSize", "fontWeight", "text", "item", "concat", "point", "sentimentData", "sentimentScale", "hotArticles", "id", "tag", "tagColor", "source", "publishTime", "link", "reportTemplates", "createTime", "negativeNews", "loading", "mounted", "_this", "$store", "state", "settings", "topNav", "dispatch", "key", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/views/dashboard/index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"page-header-content\">\r\n      <div class=\"avatar\">\r\n        <a-avatar size=\"large\" :src=\"currentUser.avatar\" />\r\n      </div>\r\n      <div class=\"content\">\r\n        <div class=\"content-title\">\r\n          {{ currentUser.name }}<span class=\"welcome-text\">，欢迎使用舆情监控系统</span>\r\n        </div>\r\n        <div>{{ currentUser.title }} | {{ currentUser.group }}</div>\r\n      </div>\r\n      <div class=\"extra-content\">\r\n        <div class=\"stat-item\">\r\n          <a-statistic title=\"今日热点\" :value=\"hotTopicsCount\" />\r\n        </div>\r\n        <div class=\"stat-item\">\r\n          <a-statistic title=\"负面信息\" :value=\"negativeCount\" />\r\n        </div>\r\n        <div class=\"stat-item\">\r\n          <a-statistic title=\"总监控量\" :value=\"totalMonitorCount\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div>\r\n      <a-row :gutter=\"24\">\r\n        <a-col :xl=\"16\" :lg=\"24\" :md=\"24\" :sm=\"24\" :xs=\"24\">\r\n          <a-card\r\n            :loading=\"loading\"\r\n            style=\"margin-bottom: 24px\"\r\n            :bordered=\"false\"\r\n            title=\"24小时传播趋势\"\r\n          >\r\n            <div style=\"height: 400px; position: relative;\">\r\n              <v-chart :forceFit=\"true\" height=\"400\" :data=\"trendData\" :scale=\"trendScale\">\r\n                <v-tooltip crosshairs></v-tooltip>\r\n                <v-axis dataKey=\"time\"></v-axis>\r\n                <v-axis dataKey=\"value\"></v-axis>\r\n                <v-legend></v-legend>\r\n                <v-line position=\"time*value\" color=\"type\" :size=\"2\"></v-line>\r\n                <v-point position=\"time*value\" color=\"type\" :size=\"4\" shape=\"circle\"></v-point>\r\n              </v-chart>\r\n            </div>\r\n          </a-card>\r\n\r\n          <a-row :gutter=\"24\">\r\n            <a-col :span=\"12\">\r\n              <a-card\r\n                :loading=\"loading\"\r\n                style=\"margin-bottom: 24px\"\r\n                :bordered=\"false\"\r\n                title=\"平台来源占比\"\r\n              >\r\n                <div style=\"height: 300px;\">\r\n                  <v-chart :forceFit=\"true\" height=\"300\" :data=\"platformData\" :scale=\"platformScale\">\r\n                    <v-tooltip></v-tooltip>\r\n                    <v-legend dataKey=\"type\"></v-legend>\r\n                    <v-coord type=\"theta\" radius=\"0.75\" innerRadius=\"0.6\"></v-coord>\r\n                    <v-pie position=\"percent\" color=\"type\" :label=\"labelConfig\"></v-pie>\r\n                  </v-chart>\r\n                </div>\r\n              </a-card>\r\n            </a-col>\r\n            <a-col :span=\"12\">\r\n              <a-card\r\n                :loading=\"loading\"\r\n                style=\"margin-bottom: 24px\"\r\n                :bordered=\"false\"\r\n                title=\"情感分布占比\"\r\n              >\r\n                <div style=\"height: 300px;\">\r\n                  <v-chart :forceFit=\"true\" height=\"300\" :data=\"sentimentData\" :scale=\"sentimentScale\">\r\n                    <v-tooltip></v-tooltip>\r\n                    <v-axis dataKey=\"time\"></v-axis>\r\n                    <v-axis dataKey=\"value\"></v-axis>\r\n                    <v-legend dataKey=\"type\"></v-legend>\r\n                    <v-area position=\"time*value\" color=\"type\" :opacity=\"0.6\"></v-area>\r\n                  </v-chart>\r\n                </div>\r\n              </a-card>\r\n            </a-col>\r\n          </a-row>\r\n\r\n          <a-card :loading=\"loading\" :bordered=\"false\">\r\n            <template slot=\"title\">\r\n              <i class=\"el-icon-hot\" style=\"color: #F5222D; margin-right: 8px;\"></i>\r\n              热门文章\r\n            </template>\r\n            <a-list>\r\n              <a-list-item :key=\"index\" v-for=\"(item, index) in hotArticles\">\r\n                <a-list-item-meta>\r\n                  <template slot=\"avatar\">\r\n                    <a-tag :color=\"item.tagColor\">{{ item.tag }}</a-tag>\r\n                  </template>\r\n                  <div slot=\"title\">\r\n                    <a :href=\"item.link\">{{ item.title }}</a>\r\n                  </div>\r\n                  <div slot=\"description\">\r\n                    <span>{{ item.source }}</span>\r\n                    <span style=\"float: right;\">{{ item.publishTime }}</span>\r\n                  </div>\r\n                </a-list-item-meta>\r\n              </a-list-item>\r\n            </a-list>\r\n          </a-card>\r\n        </a-col>\r\n        <a-col\r\n          style=\"padding: 0 12px\"\r\n          :xl=\"8\"\r\n          :lg=\"24\"\r\n          :md=\"24\"\r\n          :sm=\"24\"\r\n          :xs=\"24\"\r\n        >\r\n          <a-card\r\n            style=\"margin-bottom: 24px\"\r\n            :bordered=\"false\"\r\n          >\r\n            <template slot=\"title\">\r\n              <i class=\"el-icon-warning-outline\" style=\"color: #FA8C16; margin-right: 8px;\"></i>\r\n              预警方案设置\r\n            </template>\r\n            <div class=\"setting-buttons\">\r\n              <a-button type=\"primary\" icon=\"user\" style=\"margin-right: 8px;\">接收人设置</a-button>\r\n              <a-button type=\"primary\" icon=\"setting\" style=\"margin-right: 8px;\">预警设置</a-button>\r\n              <a-button type=\"primary\" icon=\"bell\" style=\"margin-right: 8px;\">关键词设置</a-button>\r\n              <a-button type=\"primary\" icon=\"search\" @click=\"goToSearch\">搜索测试</a-button>\r\n            </div>\r\n            <a-divider />\r\n            <div class=\"switch-buttons\">\r\n              <div style=\"margin-bottom: 16px; display: flex; justify-content: space-between; align-items: center;\">\r\n                <span>预警开关</span>\r\n                <a-switch checked></a-switch>\r\n              </div>\r\n              <div style=\"display: flex; justify-content: space-between;\">\r\n                <a-button>自动预警</a-button>\r\n                <a-button type=\"primary\">人工预警</a-button>\r\n              </div>\r\n            </div>\r\n          </a-card>\r\n          <a-card\r\n            style=\"margin-bottom: 24px\"\r\n            :bordered=\"false\"\r\n          >\r\n            <template slot=\"title\">\r\n              <i class=\"el-icon-document\" style=\"color: #52C41A; margin-right: 8px;\"></i>\r\n              报告模板\r\n            </template>\r\n            <a-list>\r\n              <a-list-item v-for=\"(item, index) in reportTemplates\" :key=\"index\">\r\n                <a-list-item-meta>\r\n                  <div slot=\"title\">{{ item.title }}</div>\r\n                  <div slot=\"description\">{{ item.createTime }}</div>\r\n                </a-list-item-meta>\r\n                <div>\r\n                  <a-button type=\"link\" icon=\"edit\"></a-button>\r\n                  <a-button type=\"link\" icon=\"copy\"></a-button>\r\n                  <a-button type=\"link\" icon=\"delete\"></a-button>\r\n                </div>\r\n              </a-list-item>\r\n            </a-list>\r\n          </a-card>\r\n          <a-card :loading=\"loading\" :bordered=\"false\">\r\n            <template slot=\"title\">\r\n              <i class=\"el-icon-warning\" style=\"color: #FF4D4F; margin-right: 8px;\"></i>\r\n              最新负面\r\n            </template>\r\n            <a-list>\r\n              <a-list-item v-for=\"(item, index) in negativeNews\" :key=\"index\">\r\n                <a-list-item-meta>\r\n                  <template slot=\"avatar\">\r\n                    <a-badge status=\"error\" />\r\n                  </template>\r\n                  <div slot=\"title\">\r\n                    <a :href=\"item.link\">{{ item.title }}</a>\r\n                  </div>\r\n                  <div slot=\"description\">{{ item.publishTime }}</div>\r\n                </a-list-item-meta>\r\n              </a-list-item>\r\n            </a-list>\r\n          </a-card>\r\n        </a-col>\r\n      </a-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Viser from 'viser-vue';\r\nimport {\r\n  Avatar,\r\n  Button,\r\n  Card,\r\n  Col,\r\n  List,\r\n  Row,\r\n  Statistic,\r\n  Tag,\r\n  Divider,\r\n  Switch,\r\n  Badge,\r\n} from \"ant-design-vue\";\r\nimport 'ant-design-vue/dist/antd.css';\r\nimport Vue from \"vue\";\r\n\r\nVue.use(Viser);\r\nVue.component(Avatar.name, Avatar);\r\nVue.component(Button.name, Button);\r\nVue.component(Card.name, Card);\r\nVue.component(Card.Grid.name, Card.Grid);\r\nVue.component(Card.Meta.name, Card.Meta);\r\nVue.component(Col.name, Col);\r\nVue.component(List.name, List);\r\nVue.component(List.Item.name, List.Item);\r\nVue.component(List.Item.Meta.name, List.Item.Meta);\r\nVue.component(Row.name, Row);\r\nVue.component(Statistic.name, Statistic);\r\nVue.component(Tag.name, Tag);\r\nVue.component(Divider.name, Divider);\r\nVue.component(Switch.name, Switch);\r\nVue.component(Badge.name, Badge);\r\n\r\nexport default {\r\n  name: \"DashBoard\",\r\n  methods: {\r\n    goToSearch() {\r\n      // 跳转到搜索结果页面，并传递测试搜索关键词\r\n      this.$router.push({\r\n        path: '/search-results',\r\n        query: {\r\n          q: '方太 厨电'\r\n        }\r\n      });\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      originalTopNav: undefined, // 存储原始的topNav状态\r\n      currentUser: {\r\n        avatar:\r\n          \"https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png\",\r\n        name: \"方太\",\r\n        userid: \"00000001\",\r\n        email: \"<EMAIL>\",\r\n        signature: \"舆情监控，实时把控\",\r\n        title: \"舆情分析师\",\r\n        group: \"舆情监控中心\",\r\n      },\r\n      // 统计数据\r\n      hotTopicsCount: 24,\r\n      negativeCount: 8,\r\n      totalMonitorCount: 1446,\r\n\r\n      // 24小时传播趋势数据\r\n      trendData: [\r\n        { time: \"00:00\", value: 300, type: \"总量\" },\r\n        { time: \"01:00\", value: 250, type: \"总量\" },\r\n        { time: \"02:00\", value: 200, type: \"总量\" },\r\n        { time: \"03:00\", value: 180, type: \"总量\" },\r\n        { time: \"04:00\", value: 150, type: \"总量\" },\r\n        { time: \"05:00\", value: 170, type: \"总量\" },\r\n        { time: \"06:00\", value: 220, type: \"总量\" },\r\n        { time: \"07:00\", value: 350, type: \"总量\" },\r\n        { time: \"08:00\", value: 500, type: \"总量\" },\r\n        { time: \"09:00\", value: 620, type: \"总量\" },\r\n        { time: \"10:00\", value: 550, type: \"总量\" },\r\n        { time: \"11:00\", value: 480, type: \"总量\" },\r\n        { time: \"12:00\", value: 400, type: \"总量\" },\r\n        { time: \"13:00\", value: 450, type: \"总量\" },\r\n        { time: \"14:00\", value: 500, type: \"总量\" },\r\n        { time: \"15:00\", value: 470, type: \"总量\" },\r\n        { time: \"16:00\", value: 460, type: \"总量\" },\r\n        { time: \"17:00\", value: 520, type: \"总量\" },\r\n        { time: \"18:00\", value: 580, type: \"总量\" },\r\n        { time: \"19:00\", value: 550, type: \"总量\" },\r\n        { time: \"20:00\", value: 500, type: \"总量\" },\r\n        { time: \"21:00\", value: 450, type: \"总量\" },\r\n        { time: \"22:00\", value: 400, type: \"总量\" },\r\n        { time: \"23:00\", value: 350, type: \"总量\" },\r\n\r\n        { time: \"00:00\", value: 100, type: \"微博\" },\r\n        { time: \"01:00\", value: 80, type: \"微博\" },\r\n        { time: \"02:00\", value: 60, type: \"微博\" },\r\n        { time: \"03:00\", value: 50, type: \"微博\" },\r\n        { time: \"04:00\", value: 40, type: \"微博\" },\r\n        { time: \"05:00\", value: 50, type: \"微博\" },\r\n        { time: \"06:00\", value: 70, type: \"微博\" },\r\n        { time: \"07:00\", value: 100, type: \"微博\" },\r\n        { time: \"08:00\", value: 150, type: \"微博\" },\r\n        { time: \"09:00\", value: 180, type: \"微博\" },\r\n        { time: \"10:00\", value: 160, type: \"微博\" },\r\n        { time: \"11:00\", value: 140, type: \"微博\" },\r\n        { time: \"12:00\", value: 120, type: \"微博\" },\r\n        { time: \"13:00\", value: 130, type: \"微博\" },\r\n        { time: \"14:00\", value: 150, type: \"微博\" },\r\n        { time: \"15:00\", value: 140, type: \"微博\" },\r\n        { time: \"16:00\", value: 130, type: \"微博\" },\r\n        { time: \"17:00\", value: 150, type: \"微博\" },\r\n        { time: \"18:00\", value: 170, type: \"微博\" },\r\n        { time: \"19:00\", value: 160, type: \"微博\" },\r\n        { time: \"20:00\", value: 150, type: \"微博\" },\r\n        { time: \"21:00\", value: 130, type: \"微博\" },\r\n        { time: \"22:00\", value: 120, type: \"微博\" },\r\n        { time: \"23:00\", value: 100, type: \"微博\" },\r\n\r\n        { time: \"00:00\", value: 80, type: \"视频\" },\r\n        { time: \"01:00\", value: 70, type: \"视频\" },\r\n        { time: \"02:00\", value: 60, type: \"视频\" },\r\n        { time: \"03:00\", value: 50, type: \"视频\" },\r\n        { time: \"04:00\", value: 40, type: \"视频\" },\r\n        { time: \"05:00\", value: 50, type: \"视频\" },\r\n        { time: \"06:00\", value: 60, type: \"视频\" },\r\n        { time: \"07:00\", value: 80, type: \"视频\" },\r\n        { time: \"08:00\", value: 100, type: \"视频\" },\r\n        { time: \"09:00\", value: 120, type: \"视频\" },\r\n        { time: \"10:00\", value: 110, type: \"视频\" },\r\n        { time: \"11:00\", value: 100, type: \"视频\" },\r\n        { time: \"12:00\", value: 90, type: \"视频\" },\r\n        { time: \"13:00\", value: 100, type: \"视频\" },\r\n        { time: \"14:00\", value: 110, type: \"视频\" },\r\n        { time: \"15:00\", value: 100, type: \"视频\" },\r\n        { time: \"16:00\", value: 90, type: \"视频\" },\r\n        { time: \"17:00\", value: 100, type: \"视频\" },\r\n        { time: \"18:00\", value: 110, type: \"视频\" },\r\n        { time: \"19:00\", value: 100, type: \"视频\" },\r\n        { time: \"20:00\", value: 90, type: \"视频\" },\r\n        { time: \"21:00\", value: 80, type: \"视频\" },\r\n        { time: \"22:00\", value: 70, type: \"视频\" },\r\n        { time: \"23:00\", value: 60, type: \"视频\" },\r\n      ],\r\n      trendScale: [\r\n        {\r\n          dataKey: 'value',\r\n          min: 0,\r\n        },\r\n      ],\r\n\r\n      // 平台来源占比数据\r\n      platformData: [\r\n        { type: '微博', percent: 31.2 },\r\n        { type: '视频', percent: 17.9 },\r\n        { type: '头条号', percent: 15.3 },\r\n        { type: 'APP', percent: 12.7 },\r\n        { type: '微信', percent: 9.8 },\r\n        { type: '其他', percent: 13.1 },\r\n      ],\r\n      platformScale: [\r\n        {\r\n          dataKey: 'percent',\r\n          min: 0,\r\n          formatter: '.0%',\r\n        },\r\n      ],\r\n      labelConfig: {\r\n        offset: -20,\r\n        textStyle: {\r\n          fill: '#000',\r\n          fontSize: 12,\r\n          fontWeight: 'bold',\r\n        },\r\n        formatter: (text, item) => {\r\n          return `${item.point.type}: ${item.point.percent}%`;\r\n        },\r\n      },\r\n\r\n      // 情感分布占比数据\r\n      sentimentData: [\r\n        { time: '00:00', value: 300, type: '中性' },\r\n        { time: '02:00', value: 280, type: '中性' },\r\n        { time: '04:00', value: 250, type: '中性' },\r\n        { time: '06:00', value: 260, type: '中性' },\r\n        { time: '08:00', value: 280, type: '中性' },\r\n        { time: '10:00', value: 300, type: '中性' },\r\n        { time: '12:00', value: 280, type: '中性' },\r\n        { time: '14:00', value: 250, type: '中性' },\r\n        { time: '16:00', value: 260, type: '中性' },\r\n        { time: '18:00', value: 280, type: '中性' },\r\n        { time: '20:00', value: 300, type: '中性' },\r\n        { time: '22:00', value: 280, type: '中性' },\r\n\r\n        { time: '00:00', value: 100, type: '正面' },\r\n        { time: '02:00', value: 120, type: '正面' },\r\n        { time: '04:00', value: 140, type: '正面' },\r\n        { time: '06:00', value: 130, type: '正面' },\r\n        { time: '08:00', value: 120, type: '正面' },\r\n        { time: '10:00', value: 100, type: '正面' },\r\n        { time: '12:00', value: 120, type: '正面' },\r\n        { time: '14:00', value: 140, type: '正面' },\r\n        { time: '16:00', value: 130, type: '正面' },\r\n        { time: '18:00', value: 120, type: '正面' },\r\n        { time: '20:00', value: 100, type: '正面' },\r\n        { time: '22:00', value: 120, type: '正面' },\r\n\r\n        { time: '00:00', value: 50, type: '负面' },\r\n        { time: '02:00', value: 60, type: '负面' },\r\n        { time: '04:00', value: 70, type: '负面' },\r\n        { time: '06:00', value: 65, type: '负面' },\r\n        { time: '08:00', value: 60, type: '负面' },\r\n        { time: '10:00', value: 50, type: '负面' },\r\n        { time: '12:00', value: 60, type: '负面' },\r\n        { time: '14:00', value: 70, type: '负面' },\r\n        { time: '16:00', value: 65, type: '负面' },\r\n        { time: '18:00', value: 60, type: '负面' },\r\n        { time: '20:00', value: 50, type: '负面' },\r\n        { time: '22:00', value: 60, type: '负面' },\r\n      ],\r\n      sentimentScale: [\r\n        {\r\n          dataKey: 'value',\r\n          min: 0,\r\n        },\r\n      ],\r\n\r\n      // 热门文章列表\r\n      hotArticles: [\r\n        {\r\n          id: 1,\r\n          title: '方太热水器(Fotile)官方旗舰店 方太热水器(Fotile)-111 方太热水器(Fotile) 400.6808.655...',\r\n          tag: '热门',\r\n          tagColor: '#f50',\r\n          source: '电商平台',\r\n          publishTime: '2025-04-29 20:24:00',\r\n          link: 'https://example.com/article/1',\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '近地铺30年地暖口碑，靠老字二全新设计住人。#成都二手房 #地暖电源',\r\n          tag: '热门',\r\n          tagColor: '#f50',\r\n          source: '视频平台',\r\n          publishTime: '2025-04-29 15:15:16',\r\n          link: 'https://example.com/article/2',\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '现在这个社会要靠脑袋吃饭，可能是一辈子的事，开玩笑 我一个实体人 更应该有品质。',\r\n          tag: '热门',\r\n          tagColor: '#f50',\r\n          source: '视频平台',\r\n          publishTime: '2025-04-29 14:29:09',\r\n          link: 'https://example.com/article/3',\r\n        },\r\n        {\r\n          id: 4,\r\n          title: '（无标题）S2 V b ## b ### 限定2人方太全球首创一代燃气灶全球首发 03 ALOQAV 0 13 图A-5986 59...',\r\n          tag: '热门',\r\n          tagColor: '#f50',\r\n          source: '视频平台',\r\n          publishTime: '2025-04-29 14:19:23',\r\n          link: 'https://example.com/article/4',\r\n        },\r\n        {\r\n          id: 5,\r\n          title: '你于过这事吗? 来看合了#方太官网 #南粤 #新桥口大厨的字写得 #同城姐妹的朋友看过来 #电影',\r\n          tag: '热门',\r\n          tagColor: '#f50',\r\n          source: '视频平台',\r\n          publishTime: '2025-04-29 12:48:04',\r\n          link: 'https://example.com/article/5',\r\n        },\r\n      ],\r\n\r\n      // 报告模板列表\r\n      reportTemplates: [\r\n        {\r\n          id: 1,\r\n          title: '舆情-周报表',\r\n          createTime: '2019-11-16 18:02:00',\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '舆情-月报表',\r\n          createTime: '2019-11-18 18:06:52',\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '舆情-季度表',\r\n          createTime: '2021-09-12 10:15:00',\r\n        },\r\n      ],\r\n\r\n      // 最新负面信息\r\n      negativeNews: [\r\n        {\r\n          id: 1,\r\n          title: '方太热水器出现质量问题，多名用户投诉无人处理',\r\n          publishTime: '2025-04-29 19:03:00',\r\n          link: 'https://example.com/negative/1',\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '方太厨电安装电话无人接听，客户投诉服务态度差',\r\n          publishTime: '2025-04-29 18:22:43',\r\n          link: 'https://example.com/negative/2',\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '航空女友偷拍日记被礼，网友后，我在床上看到了电视一幕的她',\r\n          publishTime: '2025-04-29 17:48:45',\r\n          link: 'https://example.com/negative/3',\r\n        },\r\n        {\r\n          id: 4,\r\n          title: '某品牌大型抽油烟机噪音问题引发用户不满',\r\n          publishTime: '2025-04-29 15:15:16',\r\n          link: 'https://example.com/negative/4',\r\n        },\r\n        {\r\n          id: 5,\r\n          title: '家电售后服务调查：多品牌服务质量参差不齐',\r\n          publishTime: '2025-04-29 12:26:04',\r\n          link: 'https://example.com/negative/5',\r\n        },\r\n      ],\r\n\r\n      loading: true\r\n    };\r\n  },\r\n\r\n  mounted() {\r\n    // 隐藏顶部导航栏\r\n    this.originalTopNav = this.$store.state.settings.topNav\r\n    this.$store.dispatch('settings/changeSetting', {\r\n      key: 'topNav',\r\n      value: false\r\n    })\r\n\r\n    setTimeout(() => {\r\n      this.loading = false;\r\n    }, 1000);\r\n  },\r\n  beforeDestroy() {\r\n    // 恢复顶部导航栏设置\r\n    if (this.originalTopNav !== undefined) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'topNav',\r\n        value: this.originalTopNav\r\n      })\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n  <style lang=\"less\" scoped>\r\n@import \"./Workplace.less\";\r\n\r\n\r\n\r\n.page-header-content {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.setting-buttons {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.switch-buttons {\r\n  margin-top: 16px;\r\n}\r\n\r\n.ant-list-item {\r\n  transition: all 0.3s;\r\n\r\n  &:hover {\r\n    background-color: rgba(24, 144, 255, 0.05);\r\n  }\r\n}\r\n\r\n.ant-card {\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  transition: all 0.3s;\r\n\r\n  &:hover {\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  }\r\n}\r\n\r\n.ant-tag {\r\n  margin-right: 0;\r\n}\r\n\r\n.ant-list-item-meta-title {\r\n  margin-bottom: 4px;\r\n\r\n  a {\r\n    color: rgba(0, 0, 0, 0.85);\r\n\r\n    &:hover {\r\n      color: #1890ff;\r\n    }\r\n  }\r\n}\r\n\r\n.ant-list-item-meta-description {\r\n  color: rgba(0, 0, 0, 0.45);\r\n}\r\n\r\n.mobile {\r\n  .more-info {\r\n    border: 0;\r\n    padding-top: 16px;\r\n    margin: 16px 0 16px;\r\n  }\r\n\r\n  .headerContent .title .welcome-text {\r\n    display: none;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6LA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AAcAA,OAAA;AACA,IAAAC,IAAA,GAAAF,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEAE,YAAA,CAAAC,GAAA,CAAAC,iBAAA;AACAF,YAAA,CAAAG,SAAA,CAAAC,OAAA,CAAAC,OAAA,CAAAC,IAAA,EAAAF,OAAA,CAAAC,OAAA;AACAL,YAAA,CAAAG,SAAA,CAAAI,OAAA,CAAAF,OAAA,CAAAC,IAAA,EAAAC,OAAA,CAAAF,OAAA;AACAL,YAAA,CAAAG,SAAA,CAAAK,KAAA,CAAAH,OAAA,CAAAC,IAAA,EAAAE,KAAA,CAAAH,OAAA;AACAL,YAAA,CAAAG,SAAA,CAAAK,KAAA,CAAAH,OAAA,CAAAI,IAAA,CAAAH,IAAA,EAAAE,KAAA,CAAAH,OAAA,CAAAI,IAAA;AACAT,YAAA,CAAAG,SAAA,CAAAK,KAAA,CAAAH,OAAA,CAAAK,IAAA,CAAAJ,IAAA,EAAAE,KAAA,CAAAH,OAAA,CAAAK,IAAA;AACAV,YAAA,CAAAG,SAAA,CAAAQ,IAAA,CAAAN,OAAA,CAAAC,IAAA,EAAAK,IAAA,CAAAN,OAAA;AACAL,YAAA,CAAAG,SAAA,CAAAS,KAAA,CAAAP,OAAA,CAAAC,IAAA,EAAAM,KAAA,CAAAP,OAAA;AACAL,YAAA,CAAAG,SAAA,CAAAS,KAAA,CAAAP,OAAA,CAAAQ,IAAA,CAAAP,IAAA,EAAAM,KAAA,CAAAP,OAAA,CAAAQ,IAAA;AACAb,YAAA,CAAAG,SAAA,CAAAS,KAAA,CAAAP,OAAA,CAAAQ,IAAA,CAAAH,IAAA,CAAAJ,IAAA,EAAAM,KAAA,CAAAP,OAAA,CAAAQ,IAAA,CAAAH,IAAA;AACAV,YAAA,CAAAG,SAAA,CAAAW,IAAA,CAAAT,OAAA,CAAAC,IAAA,EAAAQ,IAAA,CAAAT,OAAA;AACAL,YAAA,CAAAG,SAAA,CAAAY,UAAA,CAAAV,OAAA,CAAAC,IAAA,EAAAS,UAAA,CAAAV,OAAA;AACAL,YAAA,CAAAG,SAAA,CAAAa,IAAA,CAAAX,OAAA,CAAAC,IAAA,EAAAU,IAAA,CAAAX,OAAA;AACAL,YAAA,CAAAG,SAAA,CAAAc,QAAA,CAAAZ,OAAA,CAAAC,IAAA,EAAAW,QAAA,CAAAZ,OAAA;AACAL,YAAA,CAAAG,SAAA,CAAAe,OAAA,CAAAb,OAAA,CAAAC,IAAA,EAAAY,OAAA,CAAAb,OAAA;AACAL,YAAA,CAAAG,SAAA,CAAAgB,MAAA,CAAAd,OAAA,CAAAC,IAAA,EAAAa,MAAA,CAAAd,OAAA;AAAA,IAAAe,QAAA,GAAAC,OAAA,CAAAhB,OAAA,GAEA;EACAC,IAAA;EACAgB,OAAA;IACAC,UAAA,WAAAA,WAAA;MACA;MACA,KAAAC,OAAA,CAAAC,IAAA;QACAC,IAAA;QACAC,KAAA;UACAC,CAAA;QACA;MACA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,cAAA,EAAAC,SAAA;MAAA;MACAC,WAAA;QACAC,MAAA,EACA;QACA3B,IAAA;QACA4B,MAAA;QACAC,KAAA;QACAC,SAAA;QACAC,KAAA;QACAC,KAAA;MACA;MACA;MACAC,cAAA;MACAC,aAAA;MACAC,iBAAA;MAEA;MACAC,SAAA,GACA;QAAAC,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GAEA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GAEA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,EACA;MACAC,UAAA,GACA;QACAC,OAAA;QACAC,GAAA;MACA,EACA;MAEA;MACAC,YAAA,GACA;QAAAJ,IAAA;QAAAK,OAAA;MAAA,GACA;QAAAL,IAAA;QAAAK,OAAA;MAAA,GACA;QAAAL,IAAA;QAAAK,OAAA;MAAA,GACA;QAAAL,IAAA;QAAAK,OAAA;MAAA,GACA;QAAAL,IAAA;QAAAK,OAAA;MAAA,GACA;QAAAL,IAAA;QAAAK,OAAA;MAAA,EACA;MACAC,aAAA,GACA;QACAJ,OAAA;QACAC,GAAA;QACAI,SAAA;MACA,EACA;MACAC,WAAA;QACAC,MAAA;QACAC,SAAA;UACAC,IAAA;UACAC,QAAA;UACAC,UAAA;QACA;QACAN,SAAA,WAAAA,UAAAO,IAAA,EAAAC,IAAA;UACA,UAAAC,MAAA,CAAAD,IAAA,CAAAE,KAAA,CAAAjB,IAAA,QAAAgB,MAAA,CAAAD,IAAA,CAAAE,KAAA,CAAAZ,OAAA;QACA;MACA;MAEA;MACAa,aAAA,GACA;QAAApB,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GAEA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GAEA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,EACA;MACAmB,cAAA,GACA;QACAjB,OAAA;QACAC,GAAA;MACA,EACA;MAEA;MACAiB,WAAA,GACA;QACAC,EAAA;QACA7B,KAAA;QACA8B,GAAA;QACAC,QAAA;QACAC,MAAA;QACAC,WAAA;QACAC,IAAA;MACA,GACA;QACAL,EAAA;QACA7B,KAAA;QACA8B,GAAA;QACAC,QAAA;QACAC,MAAA;QACAC,WAAA;QACAC,IAAA;MACA,GACA;QACAL,EAAA;QACA7B,KAAA;QACA8B,GAAA;QACAC,QAAA;QACAC,MAAA;QACAC,WAAA;QACAC,IAAA;MACA,GACA;QACAL,EAAA;QACA7B,KAAA;QACA8B,GAAA;QACAC,QAAA;QACAC,MAAA;QACAC,WAAA;QACAC,IAAA;MACA,GACA;QACAL,EAAA;QACA7B,KAAA;QACA8B,GAAA;QACAC,QAAA;QACAC,MAAA;QACAC,WAAA;QACAC,IAAA;MACA,EACA;MAEA;MACAC,eAAA,GACA;QACAN,EAAA;QACA7B,KAAA;QACAoC,UAAA;MACA,GACA;QACAP,EAAA;QACA7B,KAAA;QACAoC,UAAA;MACA,GACA;QACAP,EAAA;QACA7B,KAAA;QACAoC,UAAA;MACA,EACA;MAEA;MACAC,YAAA,GACA;QACAR,EAAA;QACA7B,KAAA;QACAiC,WAAA;QACAC,IAAA;MACA,GACA;QACAL,EAAA;QACA7B,KAAA;QACAiC,WAAA;QACAC,IAAA;MACA,GACA;QACAL,EAAA;QACA7B,KAAA;QACAiC,WAAA;QACAC,IAAA;MACA,GACA;QACAL,EAAA;QACA7B,KAAA;QACAiC,WAAA;QACAC,IAAA;MACA,GACA;QACAL,EAAA;QACA7B,KAAA;QACAiC,WAAA;QACAC,IAAA;MACA,EACA;MAEAI,OAAA;IACA;EACA;EAEAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA;IACA,KAAA/C,cAAA,QAAAgD,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,MAAA;IACA,KAAAH,MAAA,CAAAI,QAAA;MACAC,GAAA;MACAvC,KAAA;IACA;IAEAwC,UAAA;MACAP,KAAA,CAAAF,OAAA;IACA;EACA;EACAU,aAAA,WAAAA,cAAA;IACA;IACA,SAAAvD,cAAA,KAAAC,SAAA;MACA,KAAA+C,MAAA,CAAAI,QAAA;QACAC,GAAA;QACAvC,KAAA,OAAAd;MACA;IACA;EACA;AACA", "ignoreList": []}]}