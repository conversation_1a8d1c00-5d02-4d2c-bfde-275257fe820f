{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\dashboard\\RaddarChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\dashboard\\RaddarChart.vue", "mtime": 1749104047639}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KDQppbXBvcnQgKiBhcyBlY2hhcnRzIGZyb20gJ2VjaGFydHMnDQpyZXF1aXJlKCdlY2hhcnRzL3RoZW1lL21hY2Fyb25zJykgLy8gZWNoYXJ0cyB0aGVtZQ0KaW1wb3J0IHJlc2l6ZSBmcm9tICcuL21peGlucy9yZXNpemUnDQoNCmNvbnN0IGFuaW1hdGlvbkR1cmF0aW9uID0gMzAwMA0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG1peGluczogW3Jlc2l6ZV0sDQogIHByb3BzOiB7DQogICAgY2xhc3NOYW1lOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnY2hhcnQnDQogICAgfSwNCiAgICB3aWR0aDogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJzEwMCUnDQogICAgfSwNCiAgICBoZWlnaHQ6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICczMDBweCcNCiAgICB9DQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGNoYXJ0OiBudWxsDQogICAgfQ0KICB9LA0KICBtb3VudGVkKCkgew0KICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgIHRoaXMuaW5pdENoYXJ0KCkNCiAgICB9KQ0KICB9LA0KICBiZWZvcmVEZXN0cm95KCkgew0KICAgIGlmICghdGhpcy5jaGFydCkgew0KICAgICAgcmV0dXJuDQogICAgfQ0KICAgIHRoaXMuY2hhcnQuZGlzcG9zZSgpDQogICAgdGhpcy5jaGFydCA9IG51bGwNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGluaXRDaGFydCgpIHsNCiAgICAgIHRoaXMuY2hhcnQgPSBlY2hhcnRzLmluaXQodGhpcy4kZWwsICdtYWNhcm9ucycpDQoNCiAgICAgIHRoaXMuY2hhcnQuc2V0T3B0aW9uKHsNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywNCiAgICAgICAgICBheGlzUG9pbnRlcjogeyAvLyDlnZDmoIfovbTmjIfnpLrlmajvvIzlnZDmoIfovbTop6blj5HmnInmlYgNCiAgICAgICAgICAgIHR5cGU6ICdzaGFkb3cnIC8vIOm7mOiupOS4uuebtOe6v++8jOWPr+mAieS4uu+8midsaW5lJyB8ICdzaGFkb3cnDQogICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICByYWRhcjogew0KICAgICAgICAgIHJhZGl1czogJzY2JScsDQogICAgICAgICAgY2VudGVyOiBbJzUwJScsICc0MiUnXSwNCiAgICAgICAgICBzcGxpdE51bWJlcjogOCwNCiAgICAgICAgICBzcGxpdEFyZWE6IHsNCiAgICAgICAgICAgIGFyZWFTdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogJ3JnYmEoMTI3LDk1LDEzMiwuMyknLA0KICAgICAgICAgICAgICBvcGFjaXR5OiAxLA0KICAgICAgICAgICAgICBzaGFkb3dCbHVyOiA0NSwNCiAgICAgICAgICAgICAgc2hhZG93Q29sb3I6ICdyZ2JhKDAsMCwwLC41KScsDQogICAgICAgICAgICAgIHNoYWRvd09mZnNldFg6IDAsDQogICAgICAgICAgICAgIHNoYWRvd09mZnNldFk6IDE1DQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICBpbmRpY2F0b3I6IFsNCiAgICAgICAgICAgIHsgbmFtZTogJ1NhbGVzJywgbWF4OiAxMDAwMCB9LA0KICAgICAgICAgICAgeyBuYW1lOiAnQWRtaW5pc3RyYXRpb24nLCBtYXg6IDIwMDAwIH0sDQogICAgICAgICAgICB7IG5hbWU6ICdJbmZvcm1hdGlvbiBUZWNob2xvZ3knLCBtYXg6IDIwMDAwIH0sDQogICAgICAgICAgICB7IG5hbWU6ICdDdXN0b21lciBTdXBwb3J0JywgbWF4OiAyMDAwMCB9LA0KICAgICAgICAgICAgeyBuYW1lOiAnRGV2ZWxvcG1lbnQnLCBtYXg6IDIwMDAwIH0sDQogICAgICAgICAgICB7IG5hbWU6ICdNYXJrZXRpbmcnLCBtYXg6IDIwMDAwIH0NCiAgICAgICAgICBdDQogICAgICAgIH0sDQogICAgICAgIGxlZ2VuZDogew0KICAgICAgICAgIGxlZnQ6ICdjZW50ZXInLA0KICAgICAgICAgIGJvdHRvbTogJzEwJywNCiAgICAgICAgICBkYXRhOiBbJ0FsbG9jYXRlZCBCdWRnZXQnLCAnRXhwZWN0ZWQgU3BlbmRpbmcnLCAnQWN0dWFsIFNwZW5kaW5nJ10NCiAgICAgICAgfSwNCiAgICAgICAgc2VyaWVzOiBbew0KICAgICAgICAgIHR5cGU6ICdyYWRhcicsDQogICAgICAgICAgc3ltYm9sU2l6ZTogMCwNCiAgICAgICAgICBhcmVhU3R5bGU6IHsNCiAgICAgICAgICAgIG5vcm1hbDogew0KICAgICAgICAgICAgICBzaGFkb3dCbHVyOiAxMywNCiAgICAgICAgICAgICAgc2hhZG93Q29sb3I6ICdyZ2JhKDAsMCwwLC4yKScsDQogICAgICAgICAgICAgIHNoYWRvd09mZnNldFg6IDAsDQogICAgICAgICAgICAgIHNoYWRvd09mZnNldFk6IDEwLA0KICAgICAgICAgICAgICBvcGFjaXR5OiAxDQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICBkYXRhOiBbDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIHZhbHVlOiBbNTAwMCwgNzAwMCwgMTIwMDAsIDExMDAwLCAxNTAwMCwgMTQwMDBdLA0KICAgICAgICAgICAgICBuYW1lOiAnQWxsb2NhdGVkIEJ1ZGdldCcNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIHZhbHVlOiBbNDAwMCwgOTAwMCwgMTUwMDAsIDE1MDAwLCAxMzAwMCwgMTEwMDBdLA0KICAgICAgICAgICAgICBuYW1lOiAnRXhwZWN0ZWQgU3BlbmRpbmcnDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICB2YWx1ZTogWzU1MDAsIDExMDAwLCAxMjAwMCwgMTUwMDAsIDEyMDAwLCAxMjAwMF0sDQogICAgICAgICAgICAgIG5hbWU6ICdBY3R1YWwgU3BlbmRpbmcnDQogICAgICAgICAgICB9DQogICAgICAgICAgXSwNCiAgICAgICAgICBhbmltYXRpb25EdXJhdGlvbjogYW5pbWF0aW9uRHVyYXRpb24NCiAgICAgICAgfV0NCiAgICAgIH0pDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["RaddarChart.vue"], "names": [], "mappings": ";;;;;AAKA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RaddarChart.vue", "sourceRoot": "src/views/dashboard", "sourcesContent": ["<template>\r\n  <div :class=\"className\" :style=\"{height:height,width:width}\" />\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nrequire('echarts/theme/macarons') // echarts theme\r\nimport resize from './mixins/resize'\r\n\r\nconst animationDuration = 3000\r\n\r\nexport default {\r\n  mixins: [resize],\r\n  props: {\r\n    className: {\r\n      type: String,\r\n      default: 'chart'\r\n    },\r\n    width: {\r\n      type: String,\r\n      default: '100%'\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '300px'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      chart: null\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.initChart()\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    if (!this.chart) {\r\n      return\r\n    }\r\n    this.chart.dispose()\r\n    this.chart = null\r\n  },\r\n  methods: {\r\n    initChart() {\r\n      this.chart = echarts.init(this.$el, 'macarons')\r\n\r\n      this.chart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { // 坐标轴指示器，坐标轴触发有效\r\n            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'\r\n          }\r\n        },\r\n        radar: {\r\n          radius: '66%',\r\n          center: ['50%', '42%'],\r\n          splitNumber: 8,\r\n          splitArea: {\r\n            areaStyle: {\r\n              color: 'rgba(127,95,132,.3)',\r\n              opacity: 1,\r\n              shadowBlur: 45,\r\n              shadowColor: 'rgba(0,0,0,.5)',\r\n              shadowOffsetX: 0,\r\n              shadowOffsetY: 15\r\n            }\r\n          },\r\n          indicator: [\r\n            { name: 'Sales', max: 10000 },\r\n            { name: 'Administration', max: 20000 },\r\n            { name: 'Information Techology', max: 20000 },\r\n            { name: 'Customer Support', max: 20000 },\r\n            { name: 'Development', max: 20000 },\r\n            { name: 'Marketing', max: 20000 }\r\n          ]\r\n        },\r\n        legend: {\r\n          left: 'center',\r\n          bottom: '10',\r\n          data: ['Allocated Budget', 'Expected Spending', 'Actual Spending']\r\n        },\r\n        series: [{\r\n          type: 'radar',\r\n          symbolSize: 0,\r\n          areaStyle: {\r\n            normal: {\r\n              shadowBlur: 13,\r\n              shadowColor: 'rgba(0,0,0,.2)',\r\n              shadowOffsetX: 0,\r\n              shadowOffsetY: 10,\r\n              opacity: 1\r\n            }\r\n          },\r\n          data: [\r\n            {\r\n              value: [5000, 7000, 12000, 11000, 15000, 14000],\r\n              name: 'Allocated Budget'\r\n            },\r\n            {\r\n              value: [4000, 9000, 15000, 15000, 13000, 11000],\r\n              name: 'Expected Spending'\r\n            },\r\n            {\r\n              value: [5500, 11000, 12000, 15000, 12000, 12000],\r\n              name: 'Actual Spending'\r\n            }\r\n          ],\r\n          animationDuration: animationDuration\r\n        }]\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}