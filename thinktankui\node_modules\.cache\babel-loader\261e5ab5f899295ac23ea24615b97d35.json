{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\spread-analysis\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\spread-analysis\\index.vue", "mtime": 1749104047642}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "_interopRequireWildcard", "require", "_default", "exports", "default", "name", "data", "originalTopNav", "undefined", "fangtaiTimeRange", "fangtaiCustomTimeRange", "fangtaiSelectedPlatforms", "fangtaiSelectedSentiments", "summaryCards", "title", "value", "color", "activeTab", "searchType", "brandName", "searchQuery", "charts", "trendChart", "sourceChart", "<PERSON><PERSON><PERSON>", "eventChart", "sourceDistChart", "keywordCloudChart", "mounted", "$store", "state", "settings", "topNav", "dispatch", "key", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "watch", "newVal", "_this", "$nextTick", "resi<PERSON><PERSON><PERSON><PERSON>", "init", "$refs", "renderKeywordCloudChart", "methods", "_defineProperty2", "handleTabSelect", "handleFangtaiFilterChange", "console", "log", "timeRange", "customTime", "platforms", "sentiments", "applyFangtaiFilters", "renderTrendChart", "renderSourceChart", "<PERSON><PERSON><PERSON><PERSON>hart", "renderEventChart", "renderSourceDistChart", "resetFangtaiFilters", "_this2", "window", "addEventListener", "option", "tooltip", "show", "series", "type", "gridSize", "sizeRange", "rotation<PERSON>ange", "shape", "width", "height", "textStyle", "fontFamily", "fontWeight", "Math", "round", "random", "join", "emphasis", "focus", "textShadowBlur", "textShadowColor", "setOption", "Object", "values", "for<PERSON>ach", "chart", "resize", "trigger", "grid", "left", "right", "bottom", "containLabel", "xAxis", "boundaryGap", "yAxis", "smooth", "lineStyle", "areaStyle", "x", "y", "x2", "y2", "colorStops", "offset", "formatter", "radius", "avoidLabelOverlap", "label", "position", "fontSize", "labelLine", "itemStyle", "legend", "graphic", "LinearGradient", "<PERSON><PERSON><PERSON><PERSON>", "axisPointer", "backgroundColor", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "dispose"], "sources": ["src/views/spread-analysis/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 内容区域 -->\r\n    <div class=\"content-container\">\r\n      <!-- 左侧搜索方案区域 -->\r\n      <div class=\"sidebar\">\r\n        <div class=\"search-plan\">\r\n          <div class=\"plan-actions\">\r\n            <el-button type=\"primary\" class=\"new-plan-btn\">\r\n              <i class=\"el-icon-plus\"></i> 新建方案\r\n            </el-button>\r\n            <el-button class=\"folder-btn\">\r\n              <i class=\"el-icon-folder-opened\"></i>\r\n            </el-button>\r\n          </div>\r\n          <div class=\"plan-search\">\r\n            <el-input\r\n              placeholder=\"方案搜索\"\r\n              prefix-icon=\"el-icon-search\"\r\n              v-model=\"searchQuery\"\r\n              clearable\r\n              size=\"small\"\r\n            ></el-input>\r\n          </div>\r\n          <div class=\"plan-divider\"></div>\r\n          <div class=\"plan-list\">\r\n            <div class=\"plan-item\" :class=\"{ 'active': searchType === 'product' }\">\r\n              <el-radio v-model=\"searchType\" label=\"product\">竞品(1)</el-radio>\r\n              <i class=\"el-icon-arrow-down\"></i>\r\n            </div>\r\n            <div class=\"plan-item\" :class=\"{ 'active': searchType === 'brand' }\">\r\n              <el-radio v-model=\"searchType\" label=\"brand\">品牌(1)</el-radio>\r\n              <i class=\"el-icon-arrow-up\"></i>\r\n            </div>\r\n            <div class=\"brand-detail\" v-if=\"searchType === 'brand'\">\r\n              <div class=\"brand-name\">方太</div>\r\n            </div>\r\n            <div class=\"plan-item\">\r\n              <el-radio v-model=\"searchType\" label=\"person\">人物(0)</el-radio>\r\n              <i class=\"el-icon-arrow-down\"></i>\r\n            </div>\r\n            <div class=\"plan-item\">\r\n              <el-radio v-model=\"searchType\" label=\"organization\">机构(0)</el-radio>\r\n              <i class=\"el-icon-arrow-down\"></i>\r\n            </div>\r\n            <div class=\"plan-item\">\r\n              <el-radio v-model=\"searchType\" label=\"product-detail\">产品(0)</el-radio>\r\n              <i class=\"el-icon-arrow-down\"></i>\r\n            </div>\r\n            <div class=\"plan-item\">\r\n              <el-radio v-model=\"searchType\" label=\"event\">事件(0)</el-radio>\r\n              <i class=\"el-icon-arrow-down\"></i>\r\n            </div>\r\n            <div class=\"plan-item\">\r\n              <el-radio v-model=\"searchType\" label=\"other\">其他(0)</el-radio>\r\n              <i class=\"el-icon-arrow-down\"></i>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧图表区域 -->\r\n      <div class=\"main-content\">\r\n        <!-- 筛选条件区域 - 方太 -->\r\n        <div class=\"fangtai-filter-container\" v-if=\"searchType === 'brand' && brandName === '方太'\">\r\n          <div class=\"filter-header\" style=\"display: flex; justify-content: space-between; align-items: center; padding-bottom: 15px; border-bottom: 1px solid #ebeef5;\">\r\n            <span class=\"filter-title\" style=\"font-size: 18px; font-weight: bold;\">\r\n              {{ brandName }}\r\n              <i class=\"el-icon-edit\" style=\"margin-left: 8px; cursor: pointer; font-size: 16px;\"></i>\r\n              <i class=\"el-icon-s-tools\" style=\"margin-left: 8px; cursor: pointer; font-size: 16px;\"></i>\r\n            </span>\r\n            <!-- Add any top-right actions for this header if needed -->\r\n          </div>\r\n\r\n          <div class=\"filter-content\" style=\"padding-top: 15px;\">\r\n            <!-- 时间范围筛选 -->\r\n            <el-form size=\"small\" label-width=\"80px\">\r\n              <el-form-item label=\"时间范围:\">\r\n                <el-radio-group v-model=\"fangtaiTimeRange\" @change=\"handleFangtaiFilterChange\">\r\n                  <el-radio-button label=\"today\">今天</el-radio-button>\r\n                  <el-radio-button label=\"yesterday\">昨天</el-radio-button>\r\n                  <el-radio-button label=\"7d\">近七天</el-radio-button>\r\n                  <el-radio-button label=\"30d\">近30天</el-radio-button>\r\n                  <el-radio-button label=\"custom\">自定义</el-radio-button>\r\n                </el-radio-group>\r\n                <el-date-picker\r\n                  v-if=\"fangtaiTimeRange === 'custom'\"\r\n                  v-model=\"fangtaiCustomTimeRange\"\r\n                  type=\"daterange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  style=\"margin-left: 10px; width: 240px;\"\r\n                  @change=\"handleFangtaiFilterChange\"\r\n                ></el-date-picker>\r\n              </el-form-item>\r\n\r\n              <!-- 平台类型筛选 -->\r\n              <el-form-item label=\"平台类型:\">\r\n                <el-checkbox-group v-model=\"fangtaiSelectedPlatforms\" @change=\"handleFangtaiFilterChange\">\r\n                  <el-checkbox label=\"all_platform\">全部</el-checkbox>\r\n                  <el-checkbox label=\"web\">网页</el-checkbox>\r\n                  <el-checkbox label=\"weibo\">微博</el-checkbox>\r\n                  <el-checkbox label=\"toutiao\">头条号</el-checkbox>\r\n                  <el-checkbox label=\"app\">APP</el-checkbox>\r\n                  <el-checkbox label=\"video\">视频</el-checkbox>\r\n                  <el-checkbox label=\"sms\">短信</el-checkbox>\r\n                  <el-checkbox label=\"newspaper\">报刊</el-checkbox>\r\n                  <el-checkbox label=\"sohu\">搜狐</el-checkbox>\r\n                </el-checkbox-group>\r\n              </el-form-item>\r\n\r\n              <!-- 情感倾向筛选 -->\r\n              <el-form-item label=\"情感倾向:\">\r\n                <el-checkbox-group v-model=\"fangtaiSelectedSentiments\" @change=\"handleFangtaiFilterChange\">\r\n                  <el-checkbox label=\"all_sentiment\">全部</el-checkbox>\r\n                  <el-checkbox label=\"positive\">正面</el-checkbox>\r\n                  <el-checkbox label=\"neutral\">中性</el-checkbox>\r\n                  <el-checkbox label=\"negative\">负面</el-checkbox>\r\n                  <el-checkbox label=\"warning_target\">预警对象</el-checkbox>\r\n                  <el-checkbox label=\"sensitive_info\">敏感信息</el-checkbox>\r\n                  <el-checkbox label=\"official_letter\">正式发函</el-checkbox>\r\n                  <el-checkbox label=\"delete_complaint\">删帖投诉</el-checkbox>\r\n                </el-checkbox-group>\r\n              </el-form-item>\r\n\r\n              <el-form-item>\r\n                <el-button type=\"primary\" @click=\"applyFangtaiFilters\">应用</el-button>\r\n                <el-button @click=\"resetFangtaiFilters\">重置</el-button>\r\n              </el-form-item>\r\n            </el-form>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 信息总览卡片区域 -->\r\n        <el-row :gutter=\"20\" class=\"summary-cards\" style=\"margin-top: 20px; margin-bottom: 20px;\">\r\n          <el-col :xl=\"4\" :lg=\"4\" :md=\"8\" :sm=\"12\" :xs=\"24\" v-for=\"card in summaryCards\" :key=\"card.title\">\r\n            <el-card shadow=\"hover\" class=\"summary-card\" :body-style=\"{ padding: '15px' }\">\r\n              <div style=\"font-size: 14px; color: #606266;\">{{ card.title }}</div>\r\n              <div style=\"font-size: 24px; font-weight: bold; margin-top: 5px;\" :style=\"{ color: card.color }\">{{ card.value }}</div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 第一行图表 - 数据汇总图 -->\r\n        <el-card class=\"chart-card\">\r\n          <div class=\"chart-header\">\r\n            <div class=\"chart-title\">\r\n              <i class=\"el-icon-data-line blue-line\"></i>\r\n              <span>数据汇总</span>\r\n            </div>\r\n            <div class=\"chart-actions\">\r\n              <el-button-group size=\"mini\">\r\n                <el-button type=\"primary\" plain>数据趋势</el-button>\r\n                <el-button plain>7天监测图</el-button>\r\n              </el-button-group>\r\n              <i class=\"el-icon-refresh\" style=\"margin-left:10px; cursor:pointer;\"></i>\r\n              <i class=\"el-icon-download\" style=\"margin-left:10px; cursor:pointer;\"></i>\r\n            </div>\r\n          </div>\r\n          <div class=\"chart-content\" style=\"height: 300px;\">\r\n            <!-- 这里放置传播热度趋势图表 -->\r\n            <div ref=\"trendChart\" class=\"chart\"></div>\r\n            <!-- 热点标记 -->\r\n            <div class=\"hotspot-markers\">\r\n              <!-- 热点标记内容将由JS动态生成 -->\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n\r\n        <!-- 关键热搜词云图 -->\r\n        <el-card class=\"chart-card\" style=\"margin-top: 20px;\" v-if=\"searchType === 'brand' && brandName === '方太'\">\r\n          <div class=\"chart-header\">\r\n            <div class=\"chart-title\">\r\n              <i class=\"el-icon-magic-stick blue-line\"></i> <!-- Consider a more relevant icon like el-icon-collection-tag or el-icon-price-tag -->\r\n              <span>关键热搜词云</span>\r\n            </div>\r\n            <div class=\"chart-actions\">\r\n              <i class=\"el-icon-refresh\" style=\"cursor:pointer;\"></i>\r\n              <i class=\"el-icon-download\" style=\"margin-left:10px; cursor:pointer;\"></i>\r\n            </div>\r\n          </div>\r\n          <div class=\"chart-content\" style=\"height: 300px;\">\r\n            <div ref=\"keywordCloudChart\" class=\"chart\"></div>\r\n          </div>\r\n        </el-card>\r\n\r\n        <!-- 第二行图表 -->\r\n        <el-row :gutter=\"20\" class=\"chart-row\">\r\n          <!-- 平台来源占比 -->\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"chart-card\">\r\n              <div class=\"chart-header\">\r\n                <div class=\"chart-title\">\r\n                  <i class=\"el-icon-pie-chart blue-line\"></i>\r\n                  <span>平台声量占比</span>\r\n                </div>\r\n                <div class=\"chart-actions\">\r\n                  <i class=\"el-icon-refresh\" style=\"cursor:pointer;\"></i>\r\n                  <i class=\"el-icon-download\" style=\"margin-left:10px; cursor:pointer;\"></i>\r\n                </div>\r\n              </div>\r\n              <div class=\"chart-content\" style=\"height: 250px;\">\r\n                <!-- 这里放置平台来源占比图表 -->\r\n                <div ref=\"sourceChart\" class=\"chart\"></div>\r\n                <!-- 图例 -->\r\n                <div class=\"chart-legend\">\r\n                  <div class=\"legend-item\">\r\n                    <span class=\"legend-color\" style=\"background-color: #4CD384;\"></span>\r\n                    <span class=\"legend-text\">微博</span>\r\n                  </div>\r\n                  <div class=\"legend-item\">\r\n                    <span class=\"legend-color\" style=\"background-color: #36A3F7;\"></span>\r\n                    <span class=\"legend-text\">微信</span>\r\n                  </div>\r\n                  <div class=\"legend-item\">\r\n                    <span class=\"legend-color\" style=\"background-color: #F56C6C;\"></span>\r\n                    <span class=\"legend-text\">APP</span>\r\n                  </div>\r\n                  <!-- 更多图例项 -->\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n\r\n          <!-- 传播热度 -->\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"chart-card\">\r\n              <div class=\"chart-header\">\r\n                <div class=\"chart-title\">\r\n                  <i class=\"el-icon-data-analysis blue-line\"></i>\r\n                  <span>情感画像</span>\r\n                </div>\r\n                <div class=\"chart-actions\">\r\n                  <i class=\"el-icon-refresh\"></i>\r\n                  <i class=\"el-icon-more\"></i>\r\n                </div>\r\n              </div>\r\n              <div class=\"chart-content\" style=\"height: 250px;\">\r\n                <!-- 这里放置传播热度图表 -->\r\n                <div ref=\"heatChart\" class=\"chart\"></div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 第三行图表 -->\r\n        <el-row :gutter=\"20\" class=\"chart-row\">\r\n          <!-- 舆情事件分布 -->\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"chart-card\">\r\n              <div class=\"chart-header\">\r\n                <div class=\"chart-title\">\r\n                  <i class=\"el-icon-s-data blue-line\"></i>\r\n                  <span>媒体类型分布</span>\r\n                </div>\r\n                <div class=\"chart-actions\">\r\n                  <i class=\"el-icon-refresh\"></i>\r\n                  <i class=\"el-icon-more\"></i>\r\n                </div>\r\n              </div>\r\n              <div class=\"chart-content\" style=\"height: 250px;\">\r\n                <!-- 这里放置舆情事件分布图表 -->\r\n                <div ref=\"eventChart\" class=\"chart\"></div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n\r\n          <!-- 舆情来源占比 -->\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"chart-card\">\r\n              <div class=\"chart-header\">\r\n                <div class=\"chart-title\">\r\n                  <i class=\"el-icon-pie-chart blue-line\"></i>\r\n                  <span>媒体声量占比</span>\r\n                </div>\r\n                <div class=\"chart-actions\">\r\n                  <i class=\"el-icon-refresh\"></i>\r\n                  <i class=\"el-icon-more\"></i>\r\n                </div>\r\n              </div>\r\n              <div class=\"chart-content\" style=\"height: 250px;\">\r\n                <!-- 这里放置舆情来源占比图表 -->\r\n                <div ref=\"sourceDistChart\" class=\"chart\"></div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nimport 'echarts-wordcloud'; // 引入词云图\r\n\r\nexport default {\r\n  name: 'SpreadAnalysis',\r\n  data() {\r\n    return {\r\n      originalTopNav: undefined, // 存储原始的topNav状态\r\n      // 方太筛选相关数据\r\n      fangtaiTimeRange: 'today',\r\n      fangtaiCustomTimeRange: [],\r\n      fangtaiSelectedPlatforms: ['all_platform'],\r\n      fangtaiSelectedSentiments: ['all_sentiment'],\r\n      summaryCards: [\r\n        { title: '信息总量', value: '4653', color: '#409EFF' },\r\n        { title: '正面声量', value: '58', color: '#67C23A' },\r\n        { title: '中性声量', value: '4583', color: '#E6A23C' },\r\n        { title: '负面声量', value: '12', color: '#F56C6C' },\r\n        { title: '媒体总数', value: '1853', color: '#909399' }\r\n      ],\r\n      activeTab: 'spread',\r\n      searchType: 'brand',\r\n      brandName: '方太',\r\n      searchQuery: '',\r\n      charts: {\r\n        trendChart: null,\r\n        sourceChart: null,\r\n        heatChart: null,\r\n        eventChart: null,\r\n        sourceDistChart: null,\r\n        keywordCloudChart: null\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    // 隐藏顶部导航栏\r\n    this.originalTopNav = this.$store.state.settings.topNav\r\n    this.$store.dispatch('settings/changeSetting', {\r\n      key: 'topNav',\r\n      value: false\r\n    })\r\n\r\n    this.initCharts()\r\n  },\r\n  watch: {\r\n    searchType(newVal) {\r\n      this.$nextTick(() => {\r\n        this.resizeCharts();\r\n        if (newVal === 'brand' && this.brandName === '方太') {\r\n          // Potentially re-render or update charts specific to '方太' view if needed\r\n          if (!this.charts.keywordCloudChart) {\r\n             this.charts.keywordCloudChart = echarts.init(this.$refs.keywordCloudChart);\r\n             this.renderKeywordCloudChart();\r\n          } else {\r\n            this.renderKeywordCloudChart(); // Re-render if already initialized\r\n          }\r\n        }\r\n      });\r\n    }\r\n  },\r\n  methods: {\r\n    handleTabSelect(key) {\r\n      this.activeTab = key\r\n    },\r\n    handleFangtaiFilterChange() {\r\n      // 处理方太筛选条件变化，可以触发数据重新加载或图表更新\r\n      console.log('方太筛选条件变化:', {\r\n        timeRange: this.fangtaiTimeRange,\r\n        customTime: this.fangtaiCustomTimeRange,\r\n        platforms: this.fangtaiSelectedPlatforms,\r\n        sentiments: this.fangtaiSelectedSentiments\r\n      });\r\n      // 触发图表更新逻辑\r\n      this.applyFangtaiFilters();\r\n    },\r\n    applyFangtaiFilters() {\r\n      // 应用方太筛选条件，实际应用中会调用API获取数据并更新图表\r\n      console.log('应用方太筛选');\r\n      // 示例：重新渲染所有图表\r\n      this.renderTrendChart(); // 注意：图一中此图表为“数据汇总”\r\n      this.renderSourceChart(); // 注意：图一中此图表为“平台声量占比”\r\n      this.renderHeatChart(); // 注意：图一中此图表为“情感画像”\r\n      this.renderEventChart(); // 注意：图一中此图表为“媒体类型分布”\r\n      this.renderSourceDistChart(); // 注意：图一中此图表为“媒体声量占比”\r\n      if (this.searchType === 'brand' && this.brandName === '方太' && this.$refs.keywordCloudChart) {\r\n        if (!this.charts.keywordCloudChart) {\r\n            this.charts.keywordCloudChart = echarts.init(this.$refs.keywordCloudChart);\r\n        }\r\n        this.renderKeywordCloudChart();\r\n      }\r\n    },\r\n    resetFangtaiFilters() {\r\n      this.fangtaiTimeRange = 'today';\r\n      this.fangtaiCustomTimeRange = [];\r\n      this.fangtaiSelectedPlatforms = ['all_platform'];\r\n      this.fangtaiSelectedSentiments = ['all_sentiment'];\r\n      console.log('重置方太筛选');\r\n      this.applyFangtaiFilters();\r\n    },\r\n    initCharts() {\r\n      this.$nextTick(() => {\r\n        // 初始化传播热度趋势图\r\n        this.charts.trendChart = echarts.init(this.$refs.trendChart)\r\n        this.renderTrendChart()\r\n\r\n        // 初始化平台来源占比图\r\n        this.charts.sourceChart = echarts.init(this.$refs.sourceChart)\r\n        this.renderSourceChart()\r\n\r\n        // 初始化传播热度图\r\n        this.charts.heatChart = echarts.init(this.$refs.heatChart)\r\n        this.renderHeatChart()\r\n\r\n        // 初始化舆情事件分布图\r\n        this.charts.eventChart = echarts.init(this.$refs.eventChart)\r\n        this.renderEventChart()\r\n\r\n        // 初始化舆情来源占比图\r\n        this.charts.sourceDistChart = echarts.init(this.$refs.sourceDistChart)\r\n        this.renderSourceDistChart()\r\n\r\n        // 如果初始加载时就是方太品牌，则初始化词云图\r\n        if (this.searchType === 'brand' && this.brandName === '方太' && this.$refs.keywordCloudChart) {\r\n          this.charts.keywordCloudChart = echarts.init(this.$refs.keywordCloudChart)\r\n          this.renderKeywordCloudChart()\r\n        }\r\n\r\n        // 监听窗口大小变化，调整图表大小\r\n        window.addEventListener('resize', this.resizeCharts)\r\n      })\r\n    },\r\n    renderKeywordCloudChart() {\r\n      if (!this.charts.keywordCloudChart) return;\r\n      const option = {\r\n        tooltip: {\r\n          show: true\r\n        },\r\n        series: [{\r\n          type: 'wordCloud',\r\n          gridSize: 2,\r\n          sizeRange: [12, 50],\r\n          rotationRange: [-90, 90],\r\n          shape: 'pentagon',\r\n          width: '90%',\r\n          height: '90%',\r\n          textStyle: {\r\n            fontFamily: 'sans-serif',\r\n            fontWeight: 'bold',\r\n            color: function () {\r\n              return 'rgb(' + [\r\n                Math.round(Math.random() * 160),\r\n                Math.round(Math.random() * 160),\r\n                Math.round(Math.random() * 160)\r\n              ].join(',') + ')';\r\n            }\r\n          },\r\n          emphasis: {\r\n            focus: 'self',\r\n            textStyle: {\r\n              textShadowBlur: 10,\r\n              textShadowColor: '#333'\r\n            }\r\n          },\r\n          data: [\r\n            { name: '方太产品', value: 10000 },\r\n            { name: '厨房电器', value: 6181 },\r\n            { name: '油烟机', value: 4386 },\r\n            { name: '洗碗机', value: 4055 },\r\n            { name: '集成灶', value: 2467 },\r\n            { name: '售后服务', value: 2244 },\r\n            { name: '智能厨电', value: 1898 },\r\n            { name: '高端厨电', value: 1484 },\r\n            { name: '用户体验', value: 1112 },\r\n            { name: '新品发布', value: 965 },\r\n            { name: '质量问题', value: 847 },\r\n            { name: '价格优惠', value: 582 },\r\n            { name: '安装服务', value: 555 },\r\n            { name: '品牌活动', value: 550 },\r\n            { name: '线上购买', value: 462 },\r\n            { name: '线下门店', value: 366 },\r\n            { name: '厨电科技', value: 360 },\r\n            { name: '环保理念', value: 282 },\r\n            { name: '设计美学', value: 273 },\r\n            { name: '客户评价', value: 265 }\r\n          ]\r\n        }]\r\n      };\r\n      this.charts.keywordCloudChart.setOption(option);\r\n    },\r\n    resizeCharts() {\r\n      Object.values(this.charts).forEach(chart => {\r\n        chart && chart.resize()\r\n      })\r\n    },\r\n    renderKeywordCloudChart() {\r\n      if (!this.charts.keywordCloudChart) return;\r\n      const option = {\r\n        tooltip: {\r\n          show: true\r\n        },\r\n        series: [{\r\n          type: 'wordCloud',\r\n          gridSize: 2,\r\n          sizeRange: [12, 50],\r\n          rotationRange: [-90, 90],\r\n          shape: 'pentagon',\r\n          width: '90%',\r\n          height: '90%',\r\n          textStyle: {\r\n            fontFamily: 'sans-serif',\r\n            fontWeight: 'bold',\r\n            color: function () {\r\n              return 'rgb(' + [\r\n                Math.round(Math.random() * 160),\r\n                Math.round(Math.random() * 160),\r\n                Math.round(Math.random() * 160)\r\n              ].join(',') + ')';\r\n            }\r\n          },\r\n          emphasis: {\r\n            focus: 'self',\r\n            textStyle: {\r\n              textShadowBlur: 10,\r\n              textShadowColor: '#333'\r\n            }\r\n          },\r\n          data: [\r\n            { name: '方太产品', value: 10000 },\r\n            { name: '厨房电器', value: 6181 },\r\n            { name: '油烟机', value: 4386 },\r\n            { name: '洗碗机', value: 4055 },\r\n            { name: '集成灶', value: 2467 },\r\n            { name: '售后服务', value: 2244 },\r\n            { name: '智能厨电', value: 1898 },\r\n            { name: '高端厨电', value: 1484 },\r\n            { name: '用户体验', value: 1112 },\r\n            { name: '新品发布', value: 965 },\r\n            { name: '质量问题', value: 847 },\r\n            { name: '价格优惠', value: 582 },\r\n            { name: '安装服务', value: 555 },\r\n            { name: '品牌活动', value: 550 },\r\n            { name: '线上购买', value: 462 },\r\n            { name: '线下门店', value: 366 },\r\n            { name: '厨电科技', value: 360 },\r\n            { name: '环保理念', value: 282 },\r\n            { name: '设计美学', value: 273 },\r\n            { name: '客户评价', value: 265 }\r\n          ]\r\n        }]\r\n      };\r\n      this.charts.keywordCloudChart.setOption(option);\r\n    },\r\n    renderTrendChart() {\r\n      // 传播热度趋势图配置\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis'\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: ['00:00', '01:00', '02:00', '03:00', '04:00', '05:00', '06:00', '07:00', '08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00']\r\n        },\r\n        yAxis: {\r\n          type: 'value'\r\n        },\r\n        series: [{\r\n          name: '传播热度',\r\n          type: 'line',\r\n          smooth: true,\r\n          lineStyle: {\r\n            color: '#67C23A',\r\n            width: 2\r\n          },\r\n          areaStyle: {\r\n            color: {\r\n              type: 'linear',\r\n              x: 0,\r\n              y: 0,\r\n              x2: 0,\r\n              y2: 1,\r\n              colorStops: [{\r\n                offset: 0,\r\n                color: 'rgba(103, 194, 58, 0.3)'\r\n              }, {\r\n                offset: 1,\r\n                color: 'rgba(103, 194, 58, 0.1)'\r\n              }]\r\n            }\r\n          },\r\n          data: [100, 120, 110, 125, 130, 150, 160, 170, 180, 190, 210, 230, 210, 200, 190, 180, 200, 210, 220, 210, 200]\r\n        }]\r\n      }\r\n      this.charts.trendChart.setOption(option)\r\n    },\r\n    renderSourceChart() {\r\n      // 平台来源占比图配置\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\r\n        },\r\n        series: [{\r\n          name: '平台来源',\r\n          type: 'pie',\r\n          radius: ['40%', '70%'],\r\n          avoidLabelOverlap: false,\r\n          label: {\r\n            show: true,\r\n            position: 'outside',\r\n            formatter: '{b}: {d}%'\r\n          },\r\n          emphasis: {\r\n            label: {\r\n              show: true,\r\n              fontSize: '16',\r\n              fontWeight: 'bold'\r\n            }\r\n          },\r\n          labelLine: {\r\n            show: true\r\n          },\r\n          data: [\r\n            { value: 31.26, name: '微博', itemStyle: { color: '#4CD384' } },\r\n            { value: 31.11, name: '微信', itemStyle: { color: '#36A3F7' } },\r\n            { value: 16.32, name: 'APP', itemStyle: { color: '#F56C6C' } },\r\n            { value: 10.47, name: '网站', itemStyle: { color: '#E6A23C' } },\r\n            { value: 5.47, name: '论坛', itemStyle: { color: '#909399' } },\r\n            { value: 3.29, name: '报刊', itemStyle: { color: '#9B55FF' } },\r\n            { value: 2.08, name: '其他', itemStyle: { color: '#FF9A2F' } }\r\n          ]\r\n        }]\r\n      }\r\n      this.charts.sourceChart.setOption(option)\r\n    },\r\n    renderHeatChart() {\r\n      // 传播热度图配置\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis'\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: ['00:00', '02:00', '04:00', '06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00']\r\n        },\r\n        yAxis: {\r\n          type: 'value'\r\n        },\r\n        legend: {\r\n          data: ['正面声量', '中性声量', '负面声量'],\r\n          bottom: 10\r\n        },\r\n        series: [\r\n          {\r\n            name: '正面声量',\r\n            type: 'line',\r\n            smooth: true,\r\n            lineStyle: { color: '#67C23A' },\r\n            areaStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: 'rgba(103, 194, 58, 0.3)' }, { offset: 1, color: 'rgba(103, 194, 58, 0.1)' }]) },\r\n            data: [10, 15, 12, 18, 22, 25, 30, 28, 35, 40, 38, 42, 45, 50, 48, 52, 55, 58, 60, 56, 50]\r\n          },\r\n          {\r\n            name: '中性声量',\r\n            type: 'line',\r\n            smooth: true,\r\n            lineStyle: { color: '#E6A23C' },\r\n            areaStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: 'rgba(230, 162, 60, 0.3)' }, { offset: 1, color: 'rgba(230, 162, 60, 0.1)' }]) },\r\n            data: [100, 120, 110, 125, 130, 150, 160, 170, 180, 190, 210, 230, 210, 200, 190, 180, 200, 210, 220, 210, 200]\r\n          },\r\n          {\r\n            name: '负面声量',\r\n            type: 'line',\r\n            smooth: true,\r\n            lineStyle: { color: '#F56C6C' },\r\n            areaStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: 'rgba(245, 108, 108, 0.3)' }, { offset: 1, color: 'rgba(245, 108, 108, 0.1)' }]) },\r\n            data: [5, 8, 6, 10, 12, 9, 11, 14, 10, 13, 15, 12, 16, 18, 15, 17, 20, 16, 19, 22, 18]\r\n          }\r\n        ]\r\n      }\r\n      this.charts.heatChart.setOption(option)\r\n    },\r\n    renderEventChart() {\r\n      // 舆情事件分布图配置\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis'\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: ['新闻', 'APP', '论坛', '博客', '微博', '视频', '微信']\r\n        },\r\n        yAxis: {\r\n          type: 'value'\r\n        },\r\n        series: [{\r\n          name: '事件数量',\r\n          type: 'bar',\r\n          barWidth: '60%',\r\n          data: [320, 280, 220, 180, 150, 120, 100]\r\n        }]\r\n      }\r\n      this.charts.eventChart.setOption(option)\r\n    },\r\n    renderSourceDistChart() {\r\n      // 舆情来源占比图配置\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross',\r\n            label: {\r\n              backgroundColor: '#6a7985'\r\n            }\r\n          }\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: ['00:00', '02:00', '04:00', '06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00']\r\n        },\r\n        yAxis: {\r\n          type: 'value'\r\n        },\r\n        series: [{\r\n          name: '媒体声量占比',\r\n          type: 'pie',\r\n          radius: ['40%', '70%'],\r\n          avoidLabelOverlap: false,\r\n          label: {\r\n            show: true,\r\n            position: 'outside',\r\n            formatter: '{b}: {d}%'\r\n          },\r\n          emphasis: {\r\n            label: {\r\n              show: true,\r\n              fontSize: '16',\r\n              fontWeight: 'bold'\r\n            }\r\n          },\r\n          labelLine: {\r\n            show: true\r\n          },\r\n          data: [\r\n            { value: 35, name: '新闻客户端', itemStyle: { color: '#5470C6'} },\r\n            { value: 25, name: '社交媒体', itemStyle: { color: '#91CC75'} },\r\n            { value: 15, name: '视频平台', itemStyle: { color: '#FAC858'} },\r\n            { value: 10, name: '传统媒体网站', itemStyle: { color: '#EE6666'} },\r\n            { value: 8, name: '论坛博客', itemStyle: { color: '#73C0DE'} },\r\n            { value: 7, name: '其他', itemStyle: { color: '#3BA272'} }\r\n          ]\r\n        }]\r\n      }\r\n      this.charts.sourceDistChart.setOption(option)\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    // 恢复顶部导航栏设置\r\n    if (this.originalTopNav !== undefined) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'topNav',\r\n        value: this.originalTopNav\r\n      })\r\n    }\r\n\r\n    // 移除窗口大小变化监听\r\n    window.removeEventListener('resize', this.resizeCharts)\r\n    // 销毁图表实例\r\n    Object.values(this.charts).forEach(chart => {\r\n      chart && chart.dispose()\r\n    })\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background-color: #f0f2f5;\r\n  min-height: 100vh;\r\n  padding: 0;\r\n}\r\n\r\n.top-nav-container {\r\n  background-color: #fff;\r\n  border-bottom: 1px solid #e6e6e6;\r\n\r\n  .top-menu {\r\n    border-bottom: none;\r\n\r\n    .el-menu-item {\r\n      height: 50px;\r\n      line-height: 50px;\r\n      font-size: 14px;\r\n\r\n      &.is-active, &.active-menu {\r\n        color: #1890ff;\r\n        border-bottom: 2px solid #1890ff;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.content-container {\r\n  padding: 20px;\r\n  display: flex;\r\n}\r\n\r\n.chart-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 4px;\r\n\r\n  .chart-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 12px 20px;\r\n    border-bottom: 1px solid #ebeef5;\r\n\r\n    .chart-title {\r\n      display: flex;\r\n      align-items: center;\r\n      font-size: 16px;\r\n      font-weight: 500;\r\n\r\n      .blue-line {\r\n        display: inline-block;\r\n        width: 3px;\r\n        height: 16px;\r\n        background-color: #1890ff;\r\n        margin-right: 8px;\r\n      }\r\n    }\r\n\r\n    .chart-tabs {\r\n      display: flex;\r\n\r\n      .tab-button {\r\n        margin-right: 10px;\r\n\r\n        &.active {\r\n          background-color: #1890ff;\r\n          border-color: #1890ff;\r\n        }\r\n      }\r\n    }\r\n\r\n    .chart-actions {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      i {\r\n        font-size: 16px;\r\n        color: #909399;\r\n        margin-left: 15px;\r\n        cursor: pointer;\r\n\r\n        &:hover {\r\n          color: #1890ff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .chart-content {\r\n    padding: 20px;\r\n\r\n    .chart {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n}\r\n\r\n.chart-row {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.chart-legend {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin-top: 15px;\r\n\r\n  .legend-item {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-right: 15px;\r\n    margin-bottom: 5px;\r\n\r\n    .legend-color {\r\n      display: inline-block;\r\n      width: 10px;\r\n      height: 10px;\r\n      border-radius: 2px;\r\n      margin-right: 5px;\r\n    }\r\n\r\n    .legend-text {\r\n      font-size: 12px;\r\n      color: #606266;\r\n    }\r\n  }\r\n}\r\n\r\n.hotspot-markers {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  pointer-events: none;\r\n}\r\n\r\n/* 左侧搜索方案区域样式 */\r\n.sidebar {\r\n  width: 240px;\r\n  margin-right: 20px;\r\n}\r\n\r\n.search-plan {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.plan-actions {\r\n  padding: 16px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n\r\n  .new-plan-btn {\r\n    flex: 1;\r\n    background-color: #FF9A2F;\r\n    border-color: #FF9A2F;\r\n\r\n    &:hover, &:focus {\r\n      background-color: #F08C1E;\r\n      border-color: #F08C1E;\r\n    }\r\n  }\r\n\r\n  .folder-btn {\r\n    margin-left: 10px;\r\n    padding: 10px;\r\n    color: #FF9A2F;\r\n    border-color: #DCDFE6;\r\n  }\r\n}\r\n\r\n.plan-search {\r\n  padding: 0 16px 16px;\r\n\r\n  .el-input ::v-deep .el-input__inner {\r\n    border-radius: 4px;\r\n    height: 32px;\r\n  }\r\n}\r\n\r\n.plan-divider {\r\n  height: 1px;\r\n  background-color: #EBEEF5;\r\n  margin: 0 16px;\r\n}\r\n\r\n.plan-list {\r\n  padding: 16px;\r\n}\r\n\r\n.plan-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n\r\n  &.active {\r\n    ::v-deep .el-radio__input.is-checked .el-radio__inner {\r\n      background-color: #409EFF;\r\n      border-color: #409EFF;\r\n    }\r\n\r\n    ::v-deep .el-radio__input.is-checked + .el-radio__label {\r\n      color: #303133;\r\n    }\r\n  }\r\n\r\n  i {\r\n    color: #909399;\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n.brand-detail {\r\n  margin: -10px 0 16px 24px;\r\n\r\n  .brand-name {\r\n    background-color: #E6F1FC;\r\n    color: #303133;\r\n    padding: 8px 12px;\r\n    border-radius: 4px;\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n/* 右侧内容区域样式 */\r\n.main-content {\r\n  flex: 1;\r\n}\r\n/* 样式可以根据需要进一步细化 */\r\n\r\n/* 方太筛选器专属样式 */\r\n.fangtai-filter-container .filter-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-bottom: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.fangtai-filter-container .filter-title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.fangtai-filter-container .filter-content {\r\n  padding-top: 15px;\r\n}\r\n\r\n.fangtai-filter-container .el-form-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.fangtai-filter-container .el-checkbox-group .el-checkbox {\r\n  margin-right: 20px;\r\n}\r\n\r\n/* 信息总览卡片样式 */\r\n.summary-cards {\r\n  margin-top: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.summary-card .el-card__body {\r\n  padding: 15px !important; /* 强制修改element-ui默认padding */\r\n}\r\n\r\n.summary-card div:first-child {\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n.summary-card div:last-child {\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  margin-top: 5px;\r\n}\r\n\r\n/* 通用图表卡片样式调整 */\r\n.chart-card .chart-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #f0f0f0; /* 统一边框颜色 */\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.chart-card .chart-title {\r\n  font-size: 16px; /* 统一标题字号 */\r\n  font-weight: 500;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.chart-card .chart-title .blue-line {\r\n  color: #409EFF; /* 统一图标颜色 */\r\n  margin-right: 8px;\r\n  font-size: 18px;\r\n}\r\n\r\n.chart-card .chart-actions .el-button-group .el-button {\r\n  padding: 5px 10px; /* 调整按钮组内按钮padding */\r\n}\r\n\r\n.chart-card .chart-actions .el-icon-refresh,\r\n.chart-card .chart-actions .el-icon-download,\r\n.chart-card .chart-actions .el-icon-more {\r\n  margin-left: 10px;\r\n  cursor: pointer;\r\n  font-size: 16px; /* 统一操作图标大小 */\r\n  color: #606266; /* 统一操作图标颜色 */\r\n}\r\n\r\n.chart-card .chart-actions .el-icon-refresh:hover,\r\n.chart-card .chart-actions .el-icon-download:hover,\r\n.chart-card .chart-actions .el-icon-more:hover {\r\n  color: #409EFF;\r\n}\r\n\r\n/* 平台来源占比图的图例样式 */\r\n.chart-legend {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 10px;\r\n  flex-wrap: wrap; /* 允许图例换行 */\r\n}\r\n\r\n.legend-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-right: 15px;\r\n  margin-bottom: 5px; /* 增加底部间距以便换行 */\r\n  font-size: 12px; /* 调整图例文字大小 */\r\n}\r\n\r\n.legend-color {\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 50%;\r\n  margin-right: 5px;\r\n  display: inline-block;\r\n}\r\n\r\n/* 左侧边栏激活状态 */\r\n.sidebar .plan-item.active {\r\n  background-color: #ecf5ff; /* Element UI 主题蓝的浅色 */\r\n  color: #409EFF;\r\n  border-right: 3px solid #409EFF;\r\n}\r\n\r\n.sidebar .plan-item.active .el-radio__label {\r\n color: #409EFF !important;\r\n}\r\n\r\n/* 顶部导航激活状态 */\r\n.top-menu .el-menu-item.is-active {\r\n  border-bottom-color: #409EFF !important;\r\n  color: #409EFF !important;\r\n}\r\n\r\n.top-menu .active-menu {\r\n   border-bottom-color: #409EFF !important;\r\n   color: #409EFF !important;\r\n   font-weight: bold;\r\n}\r\n\r\n/* 调整图表容器确保其撑满父容器 */\r\n.chart {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n/* 确保内容区域在小屏幕上也能良好显示 */\r\n.content-container {\r\n  display: flex;\r\n  flex-direction: row; /* 保持左右布局 */\r\n}\r\n\r\n.sidebar {\r\n  width: 220px; /* 固定左侧栏宽度 */\r\n  min-width: 220px;\r\n  border-right: 1px solid #e6e6e6;\r\n  padding-right: 15px;\r\n  margin-right: 15px;\r\n}\r\n\r\n.main-content {\r\n  flex-grow: 1; /* 右侧内容区域占据剩余空间 */\r\n  overflow-x: auto; /* 如果内容过宽，允许水平滚动 */\r\n}\r\n\r\n/* 响应式调整：当屏幕宽度小于768px时，可以考虑将侧边栏隐藏或变为抽屉式 */\r\n@media (max-width: 768px) {\r\n  .content-container {\r\n    flex-direction: column;\r\n  }\r\n  .sidebar {\r\n    width: 100%;\r\n    border-right: none;\r\n    border-bottom: 1px solid #e6e6e6;\r\n    margin-right: 0;\r\n    margin-bottom: 15px;\r\n    padding-right: 0;\r\n  }\r\n  .fangtai-filter-container .el-form-item .el-checkbox-group,\r\n  .fangtai-filter-container .el-form-item .el-radio-group {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n  }\r\n  .fangtai-filter-container .el-form-item .el-checkbox,\r\n  .fangtai-filter-container .el-form-item .el-radio-button {\r\n    margin-bottom: 5px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;AAsSA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AACAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,cAAA,EAAAC,SAAA;MAAA;MACA;MACAC,gBAAA;MACAC,sBAAA;MACAC,wBAAA;MACAC,yBAAA;MACAC,YAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,SAAA;MACAC,UAAA;MACAC,SAAA;MACAC,WAAA;MACAC,MAAA;QACAC,UAAA;QACAC,WAAA;QACAC,SAAA;QACAC,UAAA;QACAC,eAAA;QACAC,iBAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAArB,cAAA,QAAAsB,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,MAAA;IACA,KAAAH,MAAA,CAAAI,QAAA;MACAC,GAAA;MACAnB,KAAA;IACA;IAEA,KAAAoB,UAAA;EACA;EACAC,KAAA;IACAlB,UAAA,WAAAA,WAAAmB,MAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,SAAA;QACAD,KAAA,CAAAE,YAAA;QACA,IAAAH,MAAA,gBAAAC,KAAA,CAAAnB,SAAA;UACA;UACA,KAAAmB,KAAA,CAAAjB,MAAA,CAAAM,iBAAA;YACAW,KAAA,CAAAjB,MAAA,CAAAM,iBAAA,GAAA5B,OAAA,CAAA0C,IAAA,CAAAH,KAAA,CAAAI,KAAA,CAAAf,iBAAA;YACAW,KAAA,CAAAK,uBAAA;UACA;YACAL,KAAA,CAAAK,uBAAA;UACA;QACA;MACA;IACA;EACA;EACAC,OAAA,MAAAC,gBAAA,CAAAzC,OAAA,MAAAyC,gBAAA,CAAAzC,OAAA,MAAAyC,gBAAA,CAAAzC,OAAA,MAAAyC,gBAAA,CAAAzC,OAAA,MAAAyC,gBAAA,CAAAzC,OAAA,MAAAyC,gBAAA,CAAAzC,OAAA;IACA0C,eAAA,WAAAA,gBAAAZ,GAAA;MACA,KAAAjB,SAAA,GAAAiB,GAAA;IACA;IACAa,yBAAA,WAAAA,0BAAA;MACA;MACAC,OAAA,CAAAC,GAAA;QACAC,SAAA,OAAAzC,gBAAA;QACA0C,UAAA,OAAAzC,sBAAA;QACA0C,SAAA,OAAAzC,wBAAA;QACA0C,UAAA,OAAAzC;MACA;MACA;MACA,KAAA0C,mBAAA;IACA;IACAA,mBAAA,WAAAA,oBAAA;MACA;MACAN,OAAA,CAAAC,GAAA;MACA;MACA,KAAAM,gBAAA;MACA,KAAAC,iBAAA;MACA,KAAAC,eAAA;MACA,KAAAC,gBAAA;MACA,KAAAC,qBAAA;MACA,SAAAzC,UAAA,qBAAAC,SAAA,kBAAAuB,KAAA,CAAAf,iBAAA;QACA,UAAAN,MAAA,CAAAM,iBAAA;UACA,KAAAN,MAAA,CAAAM,iBAAA,GAAA5B,OAAA,CAAA0C,IAAA,MAAAC,KAAA,CAAAf,iBAAA;QACA;QACA,KAAAgB,uBAAA;MACA;IACA;IACAiB,mBAAA,WAAAA,oBAAA;MACA,KAAAnD,gBAAA;MACA,KAAAC,sBAAA;MACA,KAAAC,wBAAA;MACA,KAAAC,yBAAA;MACAoC,OAAA,CAAAC,GAAA;MACA,KAAAK,mBAAA;IACA;IACAnB,UAAA,WAAAA,WAAA;MAAA,IAAA0B,MAAA;MACA,KAAAtB,SAAA;QACA;QACAsB,MAAA,CAAAxC,MAAA,CAAAC,UAAA,GAAAvB,OAAA,CAAA0C,IAAA,CAAAoB,MAAA,CAAAnB,KAAA,CAAApB,UAAA;QACAuC,MAAA,CAAAN,gBAAA;;QAEA;QACAM,MAAA,CAAAxC,MAAA,CAAAE,WAAA,GAAAxB,OAAA,CAAA0C,IAAA,CAAAoB,MAAA,CAAAnB,KAAA,CAAAnB,WAAA;QACAsC,MAAA,CAAAL,iBAAA;;QAEA;QACAK,MAAA,CAAAxC,MAAA,CAAAG,SAAA,GAAAzB,OAAA,CAAA0C,IAAA,CAAAoB,MAAA,CAAAnB,KAAA,CAAAlB,SAAA;QACAqC,MAAA,CAAAJ,eAAA;;QAEA;QACAI,MAAA,CAAAxC,MAAA,CAAAI,UAAA,GAAA1B,OAAA,CAAA0C,IAAA,CAAAoB,MAAA,CAAAnB,KAAA,CAAAjB,UAAA;QACAoC,MAAA,CAAAH,gBAAA;;QAEA;QACAG,MAAA,CAAAxC,MAAA,CAAAK,eAAA,GAAA3B,OAAA,CAAA0C,IAAA,CAAAoB,MAAA,CAAAnB,KAAA,CAAAhB,eAAA;QACAmC,MAAA,CAAAF,qBAAA;;QAEA;QACA,IAAAE,MAAA,CAAA3C,UAAA,gBAAA2C,MAAA,CAAA1C,SAAA,aAAA0C,MAAA,CAAAnB,KAAA,CAAAf,iBAAA;UACAkC,MAAA,CAAAxC,MAAA,CAAAM,iBAAA,GAAA5B,OAAA,CAAA0C,IAAA,CAAAoB,MAAA,CAAAnB,KAAA,CAAAf,iBAAA;UACAkC,MAAA,CAAAlB,uBAAA;QACA;;QAEA;QACAmB,MAAA,CAAAC,gBAAA,WAAAF,MAAA,CAAArB,YAAA;MACA;IACA;IACAG,uBAAA,WAAAA,wBAAA;MACA,UAAAtB,MAAA,CAAAM,iBAAA;MACA,IAAAqC,MAAA;QACAC,OAAA;UACAC,IAAA;QACA;QACAC,MAAA;UACAC,IAAA;UACAC,QAAA;UACAC,SAAA;UACAC,aAAA;UACAC,KAAA;UACAC,KAAA;UACAC,MAAA;UACAC,SAAA;YACAC,UAAA;YACAC,UAAA;YACA7D,KAAA,WAAAA,MAAA;cACA,iBACA8D,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,WACAF,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,WACAF,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,UACA,CAAAC,IAAA;YACA;UACA;UACAC,QAAA;YACAC,KAAA;YACAR,SAAA;cACAS,cAAA;cACAC,eAAA;YACA;UACA;UACA/E,IAAA,GACA;YAAAD,IAAA;YAAAU,KAAA;UAAA,GACA;YAAAV,IAAA;YAAAU,KAAA;UAAA,GACA;YAAAV,IAAA;YAAAU,KAAA;UAAA,GACA;YAAAV,IAAA;YAAAU,KAAA;UAAA,GACA;YAAAV,IAAA;YAAAU,KAAA;UAAA,GACA;YAAAV,IAAA;YAAAU,KAAA;UAAA,GACA;YAAAV,IAAA;YAAAU,KAAA;UAAA,GACA;YAAAV,IAAA;YAAAU,KAAA;UAAA,GACA;YAAAV,IAAA;YAAAU,KAAA;UAAA,GACA;YAAAV,IAAA;YAAAU,KAAA;UAAA,GACA;YAAAV,IAAA;YAAAU,KAAA;UAAA,GACA;YAAAV,IAAA;YAAAU,KAAA;UAAA,GACA;YAAAV,IAAA;YAAAU,KAAA;UAAA,GACA;YAAAV,IAAA;YAAAU,KAAA;UAAA,GACA;YAAAV,IAAA;YAAAU,KAAA;UAAA,GACA;YAAAV,IAAA;YAAAU,KAAA;UAAA,GACA;YAAAV,IAAA;YAAAU,KAAA;UAAA,GACA;YAAAV,IAAA;YAAAU,KAAA;UAAA,GACA;YAAAV,IAAA;YAAAU,KAAA;UAAA,GACA;YAAAV,IAAA;YAAAU,KAAA;UAAA;QAEA;MACA;MACA,KAAAM,MAAA,CAAAM,iBAAA,CAAA2D,SAAA,CAAAtB,MAAA;IACA;IACAxB,YAAA,WAAAA,aAAA;MACA+C,MAAA,CAAAC,MAAA,MAAAnE,MAAA,EAAAoE,OAAA,WAAAC,KAAA;QACAA,KAAA,IAAAA,KAAA,CAAAC,MAAA;MACA;IACA;EAAA,uCAAAhD,wBAAA,EACA;IACA,UAAAtB,MAAA,CAAAM,iBAAA;IACA,IAAAqC,MAAA;MACAC,OAAA;QACAC,IAAA;MACA;MACAC,MAAA;QACAC,IAAA;QACAC,QAAA;QACAC,SAAA;QACAC,aAAA;QACAC,KAAA;QACAC,KAAA;QACAC,MAAA;QACAC,SAAA;UACAC,UAAA;UACAC,UAAA;UACA7D,KAAA,WAAAA,MAAA;YACA,iBACA8D,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,WACAF,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,WACAF,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,UACA,CAAAC,IAAA;UACA;QACA;QACAC,QAAA;UACAC,KAAA;UACAR,SAAA;YACAS,cAAA;YACAC,eAAA;UACA;QACA;QACA/E,IAAA,GACA;UAAAD,IAAA;UAAAU,KAAA;QAAA,GACA;UAAAV,IAAA;UAAAU,KAAA;QAAA,GACA;UAAAV,IAAA;UAAAU,KAAA;QAAA,GACA;UAAAV,IAAA;UAAAU,KAAA;QAAA,GACA;UAAAV,IAAA;UAAAU,KAAA;QAAA,GACA;UAAAV,IAAA;UAAAU,KAAA;QAAA,GACA;UAAAV,IAAA;UAAAU,KAAA;QAAA,GACA;UAAAV,IAAA;UAAAU,KAAA;QAAA,GACA;UAAAV,IAAA;UAAAU,KAAA;QAAA,GACA;UAAAV,IAAA;UAAAU,KAAA;QAAA,GACA;UAAAV,IAAA;UAAAU,KAAA;QAAA,GACA;UAAAV,IAAA;UAAAU,KAAA;QAAA,GACA;UAAAV,IAAA;UAAAU,KAAA;QAAA,GACA;UAAAV,IAAA;UAAAU,KAAA;QAAA,GACA;UAAAV,IAAA;UAAAU,KAAA;QAAA,GACA;UAAAV,IAAA;UAAAU,KAAA;QAAA,GACA;UAAAV,IAAA;UAAAU,KAAA;QAAA,GACA;UAAAV,IAAA;UAAAU,KAAA;QAAA,GACA;UAAAV,IAAA;UAAAU,KAAA;QAAA,GACA;UAAAV,IAAA;UAAAU,KAAA;QAAA;MAEA;IACA;IACA,KAAAM,MAAA,CAAAM,iBAAA,CAAA2D,SAAA,CAAAtB,MAAA;EACA,iCACAT,iBAAA;IACA;IACA,IAAAS,MAAA;MACAC,OAAA;QACA2B,OAAA;MACA;MACAC,IAAA;QACAC,IAAA;QACAC,KAAA;QACAC,MAAA;QACAC,YAAA;MACA;MACAC,KAAA;QACA9B,IAAA;QACA+B,WAAA;QACA7F,IAAA;MACA;MACA8F,KAAA;QACAhC,IAAA;MACA;MACAD,MAAA;QACA9D,IAAA;QACA+D,IAAA;QACAiC,MAAA;QACAC,SAAA;UACAtF,KAAA;UACAyD,KAAA;QACA;QACA8B,SAAA;UACAvF,KAAA;YACAoD,IAAA;YACAoC,CAAA;YACAC,CAAA;YACAC,EAAA;YACAC,EAAA;YACAC,UAAA;cACAC,MAAA;cACA7F,KAAA;YACA;cACA6F,MAAA;cACA7F,KAAA;YACA;UACA;QACA;QACAV,IAAA;MACA;IACA;IACA,KAAAe,MAAA,CAAAC,UAAA,CAAAgE,SAAA,CAAAtB,MAAA;EACA,kCACAR,kBAAA;IACA;IACA,IAAAQ,MAAA;MACAC,OAAA;QACA2B,OAAA;QACAkB,SAAA;MACA;MACA3C,MAAA;QACA9D,IAAA;QACA+D,IAAA;QACA2C,MAAA;QACAC,iBAAA;QACAC,KAAA;UACA/C,IAAA;UACAgD,QAAA;UACAJ,SAAA;QACA;QACA5B,QAAA;UACA+B,KAAA;YACA/C,IAAA;YACAiD,QAAA;YACAtC,UAAA;UACA;QACA;QACAuC,SAAA;UACAlD,IAAA;QACA;QACA5D,IAAA,GACA;UAAAS,KAAA;UAAAV,IAAA;UAAAgH,SAAA;YAAArG,KAAA;UAAA;QAAA,GACA;UAAAD,KAAA;UAAAV,IAAA;UAAAgH,SAAA;YAAArG,KAAA;UAAA;QAAA,GACA;UAAAD,KAAA;UAAAV,IAAA;UAAAgH,SAAA;YAAArG,KAAA;UAAA;QAAA,GACA;UAAAD,KAAA;UAAAV,IAAA;UAAAgH,SAAA;YAAArG,KAAA;UAAA;QAAA,GACA;UAAAD,KAAA;UAAAV,IAAA;UAAAgH,SAAA;YAAArG,KAAA;UAAA;QAAA,GACA;UAAAD,KAAA;UAAAV,IAAA;UAAAgH,SAAA;YAAArG,KAAA;UAAA;QAAA,GACA;UAAAD,KAAA;UAAAV,IAAA;UAAAgH,SAAA;YAAArG,KAAA;UAAA;QAAA;MAEA;IACA;IACA,KAAAK,MAAA,CAAAE,WAAA,CAAA+D,SAAA,CAAAtB,MAAA;EACA,gCACAP,gBAAA;IACA;IACA,IAAAO,MAAA;MACAC,OAAA;QACA2B,OAAA;MACA;MACAC,IAAA;QACAC,IAAA;QACAC,KAAA;QACAC,MAAA;QACAC,YAAA;MACA;MACAC,KAAA;QACA9B,IAAA;QACA+B,WAAA;QACA7F,IAAA;MACA;MACA8F,KAAA;QACAhC,IAAA;MACA;MACAkD,MAAA;QACAhH,IAAA;QACA0F,MAAA;MACA;MACA7B,MAAA,GACA;QACA9D,IAAA;QACA+D,IAAA;QACAiC,MAAA;QACAC,SAAA;UAAAtF,KAAA;QAAA;QACAuF,SAAA;UAAAvF,KAAA,MAAAjB,OAAA,CAAAwH,OAAA,CAAAC,cAAA;YAAAX,MAAA;YAAA7F,KAAA;UAAA;YAAA6F,MAAA;YAAA7F,KAAA;UAAA;QAAA;QACAV,IAAA;MACA,GACA;QACAD,IAAA;QACA+D,IAAA;QACAiC,MAAA;QACAC,SAAA;UAAAtF,KAAA;QAAA;QACAuF,SAAA;UAAAvF,KAAA,MAAAjB,OAAA,CAAAwH,OAAA,CAAAC,cAAA;YAAAX,MAAA;YAAA7F,KAAA;UAAA;YAAA6F,MAAA;YAAA7F,KAAA;UAAA;QAAA;QACAV,IAAA;MACA,GACA;QACAD,IAAA;QACA+D,IAAA;QACAiC,MAAA;QACAC,SAAA;UAAAtF,KAAA;QAAA;QACAuF,SAAA;UAAAvF,KAAA,MAAAjB,OAAA,CAAAwH,OAAA,CAAAC,cAAA;YAAAX,MAAA;YAAA7F,KAAA;UAAA;YAAA6F,MAAA;YAAA7F,KAAA;UAAA;QAAA;QACAV,IAAA;MACA;IAEA;IACA,KAAAe,MAAA,CAAAG,SAAA,CAAA8D,SAAA,CAAAtB,MAAA;EACA,iCACAN,iBAAA;IACA;IACA,IAAAM,MAAA;MACAC,OAAA;QACA2B,OAAA;MACA;MACAC,IAAA;QACAC,IAAA;QACAC,KAAA;QACAC,MAAA;QACAC,YAAA;MACA;MACAC,KAAA;QACA9B,IAAA;QACA9D,IAAA;MACA;MACA8F,KAAA;QACAhC,IAAA;MACA;MACAD,MAAA;QACA9D,IAAA;QACA+D,IAAA;QACAqD,QAAA;QACAnH,IAAA;MACA;IACA;IACA,KAAAe,MAAA,CAAAI,UAAA,CAAA6D,SAAA,CAAAtB,MAAA;EACA,sCACAL,sBAAA;IACA;IACA,IAAAK,MAAA;MACAC,OAAA;QACA2B,OAAA;QACA8B,WAAA;UACAtD,IAAA;UACA6C,KAAA;YACAU,eAAA;UACA;QACA;MACA;MACA9B,IAAA;QACAC,IAAA;QACAC,KAAA;QACAC,MAAA;QACAC,YAAA;MACA;MACAC,KAAA;QACA9B,IAAA;QACA+B,WAAA;QACA7F,IAAA;MACA;MACA8F,KAAA;QACAhC,IAAA;MACA;MACAD,MAAA;QACA9D,IAAA;QACA+D,IAAA;QACA2C,MAAA;QACAC,iBAAA;QACAC,KAAA;UACA/C,IAAA;UACAgD,QAAA;UACAJ,SAAA;QACA;QACA5B,QAAA;UACA+B,KAAA;YACA/C,IAAA;YACAiD,QAAA;YACAtC,UAAA;UACA;QACA;QACAuC,SAAA;UACAlD,IAAA;QACA;QACA5D,IAAA,GACA;UAAAS,KAAA;UAAAV,IAAA;UAAAgH,SAAA;YAAArG,KAAA;UAAA;QAAA,GACA;UAAAD,KAAA;UAAAV,IAAA;UAAAgH,SAAA;YAAArG,KAAA;UAAA;QAAA,GACA;UAAAD,KAAA;UAAAV,IAAA;UAAAgH,SAAA;YAAArG,KAAA;UAAA;QAAA,GACA;UAAAD,KAAA;UAAAV,IAAA;UAAAgH,SAAA;YAAArG,KAAA;UAAA;QAAA,GACA;UAAAD,KAAA;UAAAV,IAAA;UAAAgH,SAAA;YAAArG,KAAA;UAAA;QAAA,GACA;UAAAD,KAAA;UAAAV,IAAA;UAAAgH,SAAA;YAAArG,KAAA;UAAA;QAAA;MAEA;IACA;IACA,KAAAK,MAAA,CAAAK,eAAA,CAAA4D,SAAA,CAAAtB,MAAA;EACA,EACA;EACA4D,aAAA,WAAAA,cAAA;IACA;IACA,SAAArH,cAAA,KAAAC,SAAA;MACA,KAAAqB,MAAA,CAAAI,QAAA;QACAC,GAAA;QACAnB,KAAA,OAAAR;MACA;IACA;;IAEA;IACAuD,MAAA,CAAA+D,mBAAA,gBAAArF,YAAA;IACA;IACA+C,MAAA,CAAAC,MAAA,MAAAnE,MAAA,EAAAoE,OAAA,WAAAC,KAAA;MACAA,KAAA,IAAAA,KAAA,CAAAoC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}