{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\my-issues\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\my-issues\\index.vue", "mtime": 1749104047642}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "activeMenuItem", "searchText", "originalTopNav", "undefined", "issuesList", "type", "title", "description", "mounted", "$store", "state", "settings", "topNav", "dispatch", "key", "value", "<PERSON><PERSON><PERSON><PERSON>", "methods", "handleMenuSelect", "index", "console", "log", "$router", "push"], "sources": ["src/views/my-issues/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"my-issues\">\r\n    <!-- 左侧导航栏 -->\r\n    <div class=\"left-sidebar\">\r\n      <div class=\"sidebar-header\">\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\">新建方案</el-button>\r\n      </div>\r\n\r\n      <div class=\"sidebar-search\">\r\n        <el-input\r\n          v-model=\"searchText\"\r\n          placeholder=\"搜索方案\"\r\n          size=\"small\"\r\n          prefix-icon=\"el-icon-search\">\r\n        </el-input>\r\n      </div>\r\n\r\n      <div class=\"sidebar-menu\">\r\n        <div class=\"menu-section\">\r\n          <div class=\"section-title\">已有方案</div>\r\n          <el-menu\r\n            :default-active=\"activeMenuItem\"\r\n            class=\"sidebar-menu-list\"\r\n            @select=\"handleMenuSelect\">\r\n            <el-menu-item index=\"首页\">\r\n              <i class=\"el-icon-s-home\"></i>\r\n              <span>首页</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"总监(1)\">\r\n              <i class=\"el-icon-s-custom\"></i>\r\n              <span>总监(1)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"品牌(1)\">\r\n              <i class=\"el-icon-s-goods\"></i>\r\n              <span>品牌(1)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"方太\" class=\"active-item\">\r\n              <i class=\"el-icon-star-off\"></i>\r\n              <span>方太</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"人物(0)\">\r\n              <i class=\"el-icon-user\"></i>\r\n              <span>人物(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"机构(0)\">\r\n              <i class=\"el-icon-office-building\"></i>\r\n              <span>机构(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"产品(0)\">\r\n              <i class=\"el-icon-goods\"></i>\r\n              <span>产品(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"事件(0)\">\r\n              <i class=\"el-icon-warning\"></i>\r\n              <span>事件(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"话题(0)\">\r\n              <i class=\"el-icon-chat-dot-round\"></i>\r\n              <span>话题(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"舆情总览\">\r\n              <i class=\"el-icon-monitor\"></i>\r\n              <span>舆情总览</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"发送预警\">\r\n              <i class=\"el-icon-message\"></i>\r\n              <span>发送预警</span>\r\n            </el-menu-item>\r\n          </el-menu>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 右侧内容区 -->\r\n    <div class=\"right-content\">\r\n      <!-- 主要内容区域 -->\r\n      <div class=\"main-content\">\r\n        <div class=\"issues-header\">\r\n          <h2>我的问题</h2>\r\n        </div>\r\n\r\n        <div class=\"issues-list\">\r\n          <div\r\n            v-for=\"(issue, index) in issuesList\"\r\n            :key=\"index\"\r\n            class=\"issue-item\"\r\n            :class=\"issue.type\">\r\n            <div class=\"issue-indicator\"></div>\r\n            <div class=\"issue-content\">\r\n              <div class=\"issue-title\">{{ issue.title }}</div>\r\n              <div class=\"issue-description\" v-if=\"issue.description\">{{ issue.description }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'MyIssues',\r\n  data() {\r\n    return {\r\n      activeMenuItem: '方太',\r\n      searchText: '',\r\n      originalTopNav: undefined, // 存储原始的topNav状态\r\n      issuesList: [\r\n        {\r\n          type: 'negative',\r\n          title: '为什么会被误解为不好？',\r\n          description: '网民对某些产品或服务产生负面印象，可能是由于信息传播不当或误解造成的。'\r\n        },\r\n        {\r\n          type: 'neutral',\r\n          title: '在网络上的口碑怎么样？',\r\n          description: ''\r\n        },\r\n        {\r\n          type: 'positive',\r\n          title: '古今中外都有哪些，比如：今天的网络舆情中，哪些是正面的？哪些是负面的？哪些是中性的？如：古代、现代、国内、国外等。',\r\n          description: ''\r\n        },\r\n        {\r\n          type: 'negative',\r\n          title: '网络舆情的影响因素有哪些？',\r\n          description: ''\r\n        },\r\n        {\r\n          type: 'positive',\r\n          title: '什么是网络舆情？',\r\n          description: ''\r\n        },\r\n        {\r\n          type: 'negative',\r\n          title: '什么是网络舆情？',\r\n          description: ''\r\n        },\r\n        {\r\n          type: 'positive',\r\n          title: '古今中外都有哪些，比如：今天的网络舆情中，哪些是正面的？哪些是负面的？哪些是中性的？如：古代、现代、国内、国外等。',\r\n          description: ''\r\n        },\r\n        {\r\n          type: 'negative',\r\n          title: '古今中外都有哪些，比如：今天的网络舆情中，哪些是正面的？哪些是负面的？哪些是中性的？如：古代、现代、国内、国外等。',\r\n          description: ''\r\n        },\r\n        {\r\n          type: 'positive',\r\n          title: '网络舆情的影响因素有哪些？',\r\n          description: ''\r\n        },\r\n        {\r\n          type: 'negative',\r\n          title: '为什么网络舆情监测很重要？',\r\n          description: '网络舆情监测对企业品牌管理和危机预警具有重要意义。'\r\n        },\r\n        {\r\n          type: 'positive',\r\n          title: '为什么网络舆情监测很重要？',\r\n          description: '网络舆情监测对企业品牌管理和危机预警具有重要意义。'\r\n        },\r\n        {\r\n          type: 'negative',\r\n          title: '中国网络舆情监测的发展历程是什么？',\r\n          description: '从早期的人工监测到现在的智能化监测系统，经历了技术革新的过程。'\r\n        },\r\n        {\r\n          type: 'positive',\r\n          title: '网络舆情监测的技术手段有哪些？',\r\n          description: '包括数据采集、情感分析、关键词监测、趋势预测等多种技术手段。'\r\n        },\r\n        {\r\n          type: 'negative',\r\n          title: '为什么会被误解为\"全球干旱\"，而不是下雨或者其他气象变化？',\r\n          description: '媒体报道的角度和用词选择可能会影响公众对气象事件的理解。'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  mounted() {\r\n    // 隐藏顶部导航栏\r\n    this.originalTopNav = this.$store.state.settings.topNav\r\n    this.$store.dispatch('settings/changeSetting', {\r\n      key: 'topNav',\r\n      value: false\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    // 恢复顶部导航栏设置\r\n    if (this.originalTopNav !== undefined) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'topNav',\r\n        value: this.originalTopNav\r\n      })\r\n    }\r\n  },\r\n  methods: {\r\n    handleMenuSelect(index) {\r\n      this.activeMenuItem = index\r\n      console.log('Menu selected:', index)\r\n\r\n      // 根据选择的菜单项进行路由跳转\r\n      if (index === '首页') {\r\n        this.$router.push('/index')\r\n      } else if (index === '舆情总览') {\r\n        this.$router.push('/opinion-overview')\r\n      } else if (index === '发送预警') {\r\n        // 这里可以触发发送预警的功能\r\n        console.log('发送预警功能')\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.my-issues {\r\n  display: flex;\r\n  height: 100vh;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.left-sidebar {\r\n  width: 280px;\r\n  background-color: #fff;\r\n  border-right: 1px solid #e8e8e8;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .sidebar-header {\r\n    padding: 16px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n\r\n  .sidebar-search {\r\n    padding: 16px;\r\n  }\r\n\r\n  .sidebar-menu {\r\n    flex: 1;\r\n    padding: 0 16px;\r\n\r\n    .section-title {\r\n      font-size: 14px;\r\n      color: #666;\r\n      margin-bottom: 8px;\r\n    }\r\n\r\n    .sidebar-menu-list {\r\n      border: none;\r\n\r\n      .el-menu-item {\r\n        height: 40px;\r\n        line-height: 40px;\r\n        margin-bottom: 4px;\r\n        border-radius: 4px;\r\n\r\n        &.active-item {\r\n          background-color: #e6f7ff;\r\n          color: #1890ff;\r\n        }\r\n\r\n        &:hover {\r\n          background-color: #f5f5f5;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.right-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #fff;\r\n\r\n  .main-content {\r\n    flex: 1;\r\n    padding: 20px 24px;\r\n    overflow-y: auto;\r\n  }\r\n}\r\n\r\n.issues-header {\r\n  margin-bottom: 16px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n\r\n  h2 {\r\n    margin: 0;\r\n    font-size: 18px;\r\n    font-weight: 500;\r\n    color: #333;\r\n  }\r\n}\r\n\r\n.issues-list {\r\n  .issue-item {\r\n    display: flex;\r\n    align-items: flex-start;\r\n    padding: 12px 0;\r\n    border-bottom: 1px solid #f0f0f0;\r\n    cursor: pointer;\r\n    transition: background-color 0.2s;\r\n\r\n    &:hover {\r\n      background-color: #f9f9f9;\r\n    }\r\n\r\n    &:last-child {\r\n      border-bottom: none;\r\n    }\r\n\r\n    .issue-indicator {\r\n      width: 6px;\r\n      height: 6px;\r\n      border-radius: 50%;\r\n      margin-right: 8px;\r\n      margin-top: 8px;\r\n      flex-shrink: 0;\r\n    }\r\n\r\n    &.positive .issue-indicator {\r\n      background-color: #52c41a;\r\n    }\r\n\r\n    &.negative .issue-indicator {\r\n      background-color: #ff4d4f;\r\n    }\r\n\r\n    &.neutral .issue-indicator {\r\n      background-color: #faad14;\r\n    }\r\n\r\n    .issue-content {\r\n      flex: 1;\r\n\r\n      .issue-title {\r\n        font-size: 14px;\r\n        color: #333;\r\n        line-height: 1.6;\r\n        margin-bottom: 4px;\r\n        font-weight: normal;\r\n        word-wrap: break-word;\r\n        word-break: break-all;\r\n      }\r\n\r\n      .issue-description {\r\n        font-size: 12px;\r\n        color: #999;\r\n        line-height: 1.5;\r\n        margin-top: 4px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAoGA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,cAAA;MACAC,UAAA;MACAC,cAAA,EAAAC,SAAA;MAAA;MACAC,UAAA,GACA;QACAC,IAAA;QACAC,KAAA;QACAC,WAAA;MACA,GACA;QACAF,IAAA;QACAC,KAAA;QACAC,WAAA;MACA,GACA;QACAF,IAAA;QACAC,KAAA;QACAC,WAAA;MACA,GACA;QACAF,IAAA;QACAC,KAAA;QACAC,WAAA;MACA,GACA;QACAF,IAAA;QACAC,KAAA;QACAC,WAAA;MACA,GACA;QACAF,IAAA;QACAC,KAAA;QACAC,WAAA;MACA,GACA;QACAF,IAAA;QACAC,KAAA;QACAC,WAAA;MACA,GACA;QACAF,IAAA;QACAC,KAAA;QACAC,WAAA;MACA,GACA;QACAF,IAAA;QACAC,KAAA;QACAC,WAAA;MACA,GACA;QACAF,IAAA;QACAC,KAAA;QACAC,WAAA;MACA,GACA;QACAF,IAAA;QACAC,KAAA;QACAC,WAAA;MACA,GACA;QACAF,IAAA;QACAC,KAAA;QACAC,WAAA;MACA,GACA;QACAF,IAAA;QACAC,KAAA;QACAC,WAAA;MACA,GACA;QACAF,IAAA;QACAC,KAAA;QACAC,WAAA;MACA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAN,cAAA,QAAAO,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,MAAA;IACA,KAAAH,MAAA,CAAAI,QAAA;MACAC,GAAA;MACAC,KAAA;IACA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA;IACA,SAAAd,cAAA,KAAAC,SAAA;MACA,KAAAM,MAAA,CAAAI,QAAA;QACAC,GAAA;QACAC,KAAA,OAAAb;MACA;IACA;EACA;EACAe,OAAA;IACAC,gBAAA,WAAAA,iBAAAC,KAAA;MACA,KAAAnB,cAAA,GAAAmB,KAAA;MACAC,OAAA,CAAAC,GAAA,mBAAAF,KAAA;;MAEA;MACA,IAAAA,KAAA;QACA,KAAAG,OAAA,CAAAC,IAAA;MACA,WAAAJ,KAAA;QACA,KAAAG,OAAA,CAAAC,IAAA;MACA,WAAAJ,KAAA;QACA;QACAC,OAAA,CAAAC,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}