{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\FileUpload\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\FileUpload\\index.vue", "mtime": 1749104047618}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRUb2tlbiB9IGZyb20gIkAvdXRpbHMvYXV0aCI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkZpbGVVcGxvYWQiLA0KICBwcm9wczogew0KICAgIC8vIOWAvA0KICAgIHZhbHVlOiBbU3RyaW5nLCBPYmplY3QsIEFycmF5XSwNCiAgICAvLyDmlbDph4/pmZDliLYNCiAgICBsaW1pdDogew0KICAgICAgdHlwZTogTnVtYmVyLA0KICAgICAgZGVmYXVsdDogNQ0KICAgIH0sDQogICAgLy8g5aSn5bCP6ZmQ5Yi2KE1CKQ0KICAgIGZpbGVTaXplOiB7DQogICAgICB0eXBlOiBOdW1iZXIsDQogICAgICBkZWZhdWx0OiA1DQogICAgfSwNCiAgICAvLyDmlofku7bnsbvlnossIOS+i+WmglsncG5nJywgJ2pwZycsICdqcGVnJ10NCiAgICBmaWxlVHlwZTogew0KICAgICAgdHlwZTogQXJyYXksDQogICAgICBkZWZhdWx0OiAoKSA9PiBbImRvYyIsICJkb2N4IiwgInhscyIsICJ4bHN4IiwgInBwdCIsICJwcHR4IiwgInR4dCIsICJwZGYiXQ0KICAgIH0sDQogICAgLy8g5piv5ZCm5pi+56S65o+Q56S6DQogICAgaXNTaG93VGlwOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogdHJ1ZQ0KICAgIH0sDQogICAgLy8g56aB55So57uE5Lu277yI5LuF5p+l55yL5paH5Lu277yJDQogICAgZGlzYWJsZWQ6IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiBmYWxzZQ0KICAgIH0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgbnVtYmVyOiAwLA0KICAgICAgdXBsb2FkTGlzdDogW10sDQogICAgICBiYXNlVXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJLA0KICAgICAgdXBsb2FkRmlsZVVybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICIvY29tbW9uL3VwbG9hZCIsIC8vIOS4iuS8oOaWh+S7tuacjeWKoeWZqOWcsOWdgA0KICAgICAgaGVhZGVyczogew0KICAgICAgICBBdXRob3JpemF0aW9uOiAiQmVhcmVyICIgKyBnZXRUb2tlbigpLA0KICAgICAgfSwNCiAgICAgIGZpbGVMaXN0OiBbXSwNCiAgICB9Ow0KICB9LA0KICB3YXRjaDogew0KICAgIHZhbHVlOiB7DQogICAgICBoYW5kbGVyKHZhbCkgew0KICAgICAgICBpZiAodmFsKSB7DQogICAgICAgICAgbGV0IHRlbXAgPSAxOw0KICAgICAgICAgIC8vIOmmluWFiOWwhuWAvOi9rOS4uuaVsOe7hA0KICAgICAgICAgIGNvbnN0IGxpc3QgPSBBcnJheS5pc0FycmF5KHZhbCkgPyB2YWwgOiB0aGlzLnZhbHVlLnNwbGl0KCcsJyk7DQogICAgICAgICAgLy8g54S25ZCO5bCG5pWw57uE6L2s5Li65a+56LGh5pWw57uEDQogICAgICAgICAgdGhpcy5maWxlTGlzdCA9IGxpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICAgICAgaWYgKHR5cGVvZiBpdGVtID09PSAic3RyaW5nIikgew0KICAgICAgICAgICAgICBpdGVtID0geyBuYW1lOiBpdGVtLCB1cmw6IGl0ZW0gfTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGl0ZW0udWlkID0gaXRlbS51aWQgfHwgbmV3IERhdGUoKS5nZXRUaW1lKCkgKyB0ZW1wKys7DQogICAgICAgICAgICByZXR1cm4gaXRlbTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmZpbGVMaXN0ID0gW107DQogICAgICAgICAgcmV0dXJuIFtdOw0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgZGVlcDogdHJ1ZSwNCiAgICAgIGltbWVkaWF0ZTogdHJ1ZQ0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICAvLyDmmK/lkKbmmL7npLrmj5DnpLoNCiAgICBzaG93VGlwKCkgew0KICAgICAgcmV0dXJuIHRoaXMuaXNTaG93VGlwICYmICh0aGlzLmZpbGVUeXBlIHx8IHRoaXMuZmlsZVNpemUpOw0KICAgIH0sDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDkuIrkvKDliY3moKHmo4DmoLzlvI/lkozlpKflsI8NCiAgICBoYW5kbGVCZWZvcmVVcGxvYWQoZmlsZSkgew0KICAgICAgLy8g5qCh5qOA5paH5Lu257G75Z6LDQogICAgICBpZiAodGhpcy5maWxlVHlwZSkgew0KICAgICAgICBjb25zdCBmaWxlTmFtZSA9IGZpbGUubmFtZS5zcGxpdCgnLicpOw0KICAgICAgICBjb25zdCBmaWxlRXh0ID0gZmlsZU5hbWVbZmlsZU5hbWUubGVuZ3RoIC0gMV07DQogICAgICAgIGNvbnN0IGlzVHlwZU9rID0gdGhpcy5maWxlVHlwZS5pbmRleE9mKGZpbGVFeHQpID49IDA7DQogICAgICAgIGlmICghaXNUeXBlT2spIHsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcihg5paH5Lu25qC85byP5LiN5q2j56Gu77yM6K+35LiK5LygJHt0aGlzLmZpbGVUeXBlLmpvaW4oIi8iKX3moLzlvI/mlofku7YhYCk7DQogICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICB9DQogICAgICB9DQogICAgICAvLyDmoKHmo4Dmlofku7blkI3mmK/lkKbljIXlkKvnibnmrorlrZfnrKYNCiAgICAgIGlmIChmaWxlLm5hbWUuaW5jbHVkZXMoJywnKSkgew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign5paH5Lu25ZCN5LiN5q2j56Gu77yM5LiN6IO95YyF5ZCr6Iux5paH6YCX5Y+3IScpOw0KICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICB9DQogICAgICAvLyDmoKHmo4Dmlofku7blpKflsI8NCiAgICAgIGlmICh0aGlzLmZpbGVTaXplKSB7DQogICAgICAgIGNvbnN0IGlzTHQgPSBmaWxlLnNpemUgLyAxMDI0IC8gMTAyNCA8IHRoaXMuZmlsZVNpemU7DQogICAgICAgIGlmICghaXNMdCkgew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKGDkuIrkvKDmlofku7blpKflsI/kuI3og73otoXov4cgJHt0aGlzLmZpbGVTaXplfSBNQiFgKTsNCiAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHRoaXMuJG1vZGFsLmxvYWRpbmcoIuato+WcqOS4iuS8oOaWh+S7tu+8jOivt+eojeWAmS4uLiIpOw0KICAgICAgdGhpcy5udW1iZXIrKzsNCiAgICAgIHJldHVybiB0cnVlOw0KICAgIH0sDQogICAgLy8g5paH5Lu25Liq5pWw6LaF5Ye6DQogICAgaGFuZGxlRXhjZWVkKCkgew0KICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoYOS4iuS8oOaWh+S7tuaVsOmHj+S4jeiDvei2hei/hyAke3RoaXMubGltaXR9IOS4qiFgKTsNCiAgICB9LA0KICAgIC8vIOS4iuS8oOWksei0pQ0KICAgIGhhbmRsZVVwbG9hZEVycm9yKGVycikgew0KICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuS4iuS8oOaWh+S7tuWksei0pe+8jOivt+mHjeivlSIpOw0KICAgICAgdGhpcy4kbW9kYWwuY2xvc2VMb2FkaW5nKCk7DQogICAgfSwNCiAgICAvLyDkuIrkvKDmiJDlip/lm57osIMNCiAgICBoYW5kbGVVcGxvYWRTdWNjZXNzKHJlcywgZmlsZSkgew0KICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgdGhpcy51cGxvYWRMaXN0LnB1c2goeyBuYW1lOiByZXMuZmlsZU5hbWUsIHVybDogcmVzLmZpbGVOYW1lIH0pOw0KICAgICAgICB0aGlzLnVwbG9hZGVkU3VjY2Vzc2Z1bGx5KCk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLm51bWJlci0tOw0KICAgICAgICB0aGlzLiRtb2RhbC5jbG9zZUxvYWRpbmcoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IocmVzLm1zZyk7DQogICAgICAgIHRoaXMuJHJlZnMuZmlsZVVwbG9hZC5oYW5kbGVSZW1vdmUoZmlsZSk7DQogICAgICAgIHRoaXMudXBsb2FkZWRTdWNjZXNzZnVsbHkoKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOWIoOmZpOaWh+S7tg0KICAgIGhhbmRsZURlbGV0ZShpbmRleCkgew0KICAgICAgdGhpcy5maWxlTGlzdC5zcGxpY2UoaW5kZXgsIDEpOw0KICAgICAgdGhpcy4kZW1pdCgiaW5wdXQiLCB0aGlzLmxpc3RUb1N0cmluZyh0aGlzLmZpbGVMaXN0KSk7DQogICAgfSwNCiAgICAvLyDkuIrkvKDnu5PmnZ/lpITnkIYNCiAgICB1cGxvYWRlZFN1Y2Nlc3NmdWxseSgpIHsNCiAgICAgIGlmICh0aGlzLm51bWJlciA+IDAgJiYgdGhpcy51cGxvYWRMaXN0Lmxlbmd0aCA9PT0gdGhpcy5udW1iZXIpIHsNCiAgICAgICAgdGhpcy5maWxlTGlzdCA9IHRoaXMuZmlsZUxpc3QuY29uY2F0KHRoaXMudXBsb2FkTGlzdCk7DQogICAgICAgIHRoaXMudXBsb2FkTGlzdCA9IFtdOw0KICAgICAgICB0aGlzLm51bWJlciA9IDA7DQogICAgICAgIHRoaXMuJGVtaXQoImlucHV0IiwgdGhpcy5saXN0VG9TdHJpbmcodGhpcy5maWxlTGlzdCkpOw0KICAgICAgICB0aGlzLiRtb2RhbC5jbG9zZUxvYWRpbmcoKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOiOt+WPluaWh+S7tuWQjeensA0KICAgIGdldEZpbGVOYW1lKG5hbWUpIHsNCiAgICAgIC8vIOWmguaenOaYr3VybOmCo+S5iOWPluacgOWQjueahOWQjeWtlyDlpoLmnpzkuI3mmK/nm7TmjqXov5Tlm54NCiAgICAgIGlmIChuYW1lLmxhc3RJbmRleE9mKCIvIikgPiAtMSkgew0KICAgICAgICByZXR1cm4gbmFtZS5zbGljZShuYW1lLmxhc3RJbmRleE9mKCIvIikgKyAxKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiBuYW1lOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5a+56LGh6L2s5oiQ5oyH5a6a5a2X56ym5Liy5YiG6ZqUDQogICAgbGlzdFRvU3RyaW5nKGxpc3QsIHNlcGFyYXRvcikgew0KICAgICAgbGV0IHN0cnMgPSAiIjsNCiAgICAgIHNlcGFyYXRvciA9IHNlcGFyYXRvciB8fCAiLCI7DQogICAgICBmb3IgKGxldCBpIGluIGxpc3QpIHsNCiAgICAgICAgc3RycyArPSBsaXN0W2ldLnVybCArIHNlcGFyYXRvcjsNCiAgICAgIH0NCiAgICAgIHJldHVybiBzdHJzICE9ICcnID8gc3Rycy5zdWJzdHIoMCwgc3Rycy5sZW5ndGggLSAxKSA6ICcnOw0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2CA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/FileUpload", "sourcesContent": ["<template>\r\n  <div class=\"upload-file\">\r\n    <el-upload\r\n      multiple\r\n      :action=\"uploadFileUrl\"\r\n      :before-upload=\"handleBeforeUpload\"\r\n      :file-list=\"fileList\"\r\n      :limit=\"limit\"\r\n      :on-error=\"handleUploadError\"\r\n      :on-exceed=\"handleExceed\"\r\n      :on-success=\"handleUploadSuccess\"\r\n      :show-file-list=\"false\"\r\n      :headers=\"headers\"\r\n      class=\"upload-file-uploader\"\r\n      ref=\"fileUpload\"\r\n      v-if=\"!disabled\"\r\n    >\r\n      <!-- 上传按钮 -->\r\n      <el-button size=\"mini\" type=\"primary\">选取文件</el-button>\r\n      <!-- 上传提示 -->\r\n      <div class=\"el-upload__tip\" slot=\"tip\" v-if=\"showTip\">\r\n        请上传\r\n        <template v-if=\"fileSize\"> 大小不超过 <b style=\"color: #f56c6c\">{{ fileSize }}MB</b> </template>\r\n        <template v-if=\"fileType\"> 格式为 <b style=\"color: #f56c6c\">{{ fileType.join(\"/\") }}</b> </template>\r\n        的文件\r\n      </div>\r\n    </el-upload>\r\n\r\n    <!-- 文件列表 -->\r\n    <transition-group class=\"upload-file-list el-upload-list el-upload-list--text\" name=\"el-fade-in-linear\" tag=\"ul\">\r\n      <li :key=\"file.url\" class=\"el-upload-list__item ele-upload-list__item-content\" v-for=\"(file, index) in fileList\">\r\n        <el-link :href=\"`${baseUrl}${file.url}`\" :underline=\"false\" target=\"_blank\">\r\n          <span class=\"el-icon-document\"> {{ getFileName(file.name) }} </span>\r\n        </el-link>\r\n        <div class=\"ele-upload-list__item-content-action\">\r\n          <el-link :underline=\"false\" @click=\"handleDelete(index)\" type=\"danger\" v-if=\"!disabled\">删除</el-link>\r\n        </div>\r\n      </li>\r\n    </transition-group>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"FileUpload\",\r\n  props: {\r\n    // 值\r\n    value: [String, Object, Array],\r\n    // 数量限制\r\n    limit: {\r\n      type: Number,\r\n      default: 5\r\n    },\r\n    // 大小限制(MB)\r\n    fileSize: {\r\n      type: Number,\r\n      default: 5\r\n    },\r\n    // 文件类型, 例如['png', 'jpg', 'jpeg']\r\n    fileType: {\r\n      type: Array,\r\n      default: () => [\"doc\", \"docx\", \"xls\", \"xlsx\", \"ppt\", \"pptx\", \"txt\", \"pdf\"]\r\n    },\r\n    // 是否显示提示\r\n    isShowTip: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    // 禁用组件（仅查看文件）\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      number: 0,\r\n      uploadList: [],\r\n      baseUrl: process.env.VUE_APP_BASE_API,\r\n      uploadFileUrl: process.env.VUE_APP_BASE_API + \"/common/upload\", // 上传文件服务器地址\r\n      headers: {\r\n        Authorization: \"Bearer \" + getToken(),\r\n      },\r\n      fileList: [],\r\n    };\r\n  },\r\n  watch: {\r\n    value: {\r\n      handler(val) {\r\n        if (val) {\r\n          let temp = 1;\r\n          // 首先将值转为数组\r\n          const list = Array.isArray(val) ? val : this.value.split(',');\r\n          // 然后将数组转为对象数组\r\n          this.fileList = list.map(item => {\r\n            if (typeof item === \"string\") {\r\n              item = { name: item, url: item };\r\n            }\r\n            item.uid = item.uid || new Date().getTime() + temp++;\r\n            return item;\r\n          });\r\n        } else {\r\n          this.fileList = [];\r\n          return [];\r\n        }\r\n      },\r\n      deep: true,\r\n      immediate: true\r\n    }\r\n  },\r\n  computed: {\r\n    // 是否显示提示\r\n    showTip() {\r\n      return this.isShowTip && (this.fileType || this.fileSize);\r\n    },\r\n  },\r\n  methods: {\r\n    // 上传前校检格式和大小\r\n    handleBeforeUpload(file) {\r\n      // 校检文件类型\r\n      if (this.fileType) {\r\n        const fileName = file.name.split('.');\r\n        const fileExt = fileName[fileName.length - 1];\r\n        const isTypeOk = this.fileType.indexOf(fileExt) >= 0;\r\n        if (!isTypeOk) {\r\n          this.$modal.msgError(`文件格式不正确，请上传${this.fileType.join(\"/\")}格式文件!`);\r\n          return false;\r\n        }\r\n      }\r\n      // 校检文件名是否包含特殊字符\r\n      if (file.name.includes(',')) {\r\n        this.$modal.msgError('文件名不正确，不能包含英文逗号!');\r\n        return false;\r\n      }\r\n      // 校检文件大小\r\n      if (this.fileSize) {\r\n        const isLt = file.size / 1024 / 1024 < this.fileSize;\r\n        if (!isLt) {\r\n          this.$modal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`);\r\n          return false;\r\n        }\r\n      }\r\n      this.$modal.loading(\"正在上传文件，请稍候...\");\r\n      this.number++;\r\n      return true;\r\n    },\r\n    // 文件个数超出\r\n    handleExceed() {\r\n      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`);\r\n    },\r\n    // 上传失败\r\n    handleUploadError(err) {\r\n      this.$modal.msgError(\"上传文件失败，请重试\");\r\n      this.$modal.closeLoading();\r\n    },\r\n    // 上传成功回调\r\n    handleUploadSuccess(res, file) {\r\n      if (res.code === 200) {\r\n        this.uploadList.push({ name: res.fileName, url: res.fileName });\r\n        this.uploadedSuccessfully();\r\n      } else {\r\n        this.number--;\r\n        this.$modal.closeLoading();\r\n        this.$modal.msgError(res.msg);\r\n        this.$refs.fileUpload.handleRemove(file);\r\n        this.uploadedSuccessfully();\r\n      }\r\n    },\r\n    // 删除文件\r\n    handleDelete(index) {\r\n      this.fileList.splice(index, 1);\r\n      this.$emit(\"input\", this.listToString(this.fileList));\r\n    },\r\n    // 上传结束处理\r\n    uploadedSuccessfully() {\r\n      if (this.number > 0 && this.uploadList.length === this.number) {\r\n        this.fileList = this.fileList.concat(this.uploadList);\r\n        this.uploadList = [];\r\n        this.number = 0;\r\n        this.$emit(\"input\", this.listToString(this.fileList));\r\n        this.$modal.closeLoading();\r\n      }\r\n    },\r\n    // 获取文件名称\r\n    getFileName(name) {\r\n      // 如果是url那么取最后的名字 如果不是直接返回\r\n      if (name.lastIndexOf(\"/\") > -1) {\r\n        return name.slice(name.lastIndexOf(\"/\") + 1);\r\n      } else {\r\n        return name;\r\n      }\r\n    },\r\n    // 对象转成指定字符串分隔\r\n    listToString(list, separator) {\r\n      let strs = \"\";\r\n      separator = separator || \",\";\r\n      for (let i in list) {\r\n        strs += list[i].url + separator;\r\n      }\r\n      return strs != '' ? strs.substr(0, strs.length - 1) : '';\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.upload-file-uploader {\r\n  margin-bottom: 5px;\r\n}\r\n.upload-file-list .el-upload-list__item {\r\n  border: 1px solid #e4e7ed;\r\n  line-height: 2;\r\n  margin-bottom: 10px;\r\n  position: relative;\r\n}\r\n.upload-file-list .ele-upload-list__item-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  color: inherit;\r\n}\r\n.ele-upload-list__item-content-action .el-link {\r\n  margin-right: 10px;\r\n}\r\n</style>\r\n"]}]}