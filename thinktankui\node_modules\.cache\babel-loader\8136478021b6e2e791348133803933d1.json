{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\utils\\dict\\DictMeta.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\utils\\dict\\DictMeta.js", "mtime": 1749104047634}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749104418875}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_ruoyi", "require", "_DictOptions", "_interopRequireDefault", "DictMeta", "exports", "default", "_createClass2", "options", "_classCallCheck2", "type", "request", "responseConverter", "labelField", "valueField", "lazy", "parse", "opts", "DictOptions", "metas", "_typeof2", "mergeRecursive"], "sources": ["D:/thinktank/thinktankui/src/utils/dict/DictMeta.js"], "sourcesContent": ["import { mergeRecursive } from \"@/utils/ruoyi\";\r\nimport DictOptions from './DictOptions'\r\n\r\n/**\r\n * @classdesc 字典元数据\r\n * @property {String} type 类型\r\n * @property {Function} request 请求\r\n * @property {String} label 标签字段\r\n * @property {String} value 值字段\r\n */\r\nexport default class DictMeta {\r\n  constructor(options) {\r\n    this.type = options.type\r\n    this.request = options.request\r\n    this.responseConverter = options.responseConverter\r\n    this.labelField = options.labelField\r\n    this.valueField = options.valueField\r\n    this.lazy = options.lazy === true\r\n  }\r\n}\r\n\r\n\r\n/**\r\n * 解析字典元数据\r\n * @param {Object} options\r\n * @returns {DictMeta}\r\n */\r\nDictMeta.parse= function(options) {\r\n  let opts = null\r\n  if (typeof options === 'string') {\r\n    opts = DictOptions.metas[options] || {}\r\n    opts.type = options\r\n  } else if (typeof options === 'object') {\r\n    opts = options\r\n  }\r\n  opts = mergeRecursive(DictOptions.metas['*'], opts)\r\n  return new DictMeta(opts)\r\n}\r\n"], "mappings": ";;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AANA,IAOqBG,QAAQ,GAAAC,OAAA,CAAAC,OAAA,oBAAAC,aAAA,CAAAD,OAAA,EAC3B,SAAAF,SAAYI,OAAO,EAAE;EAAA,IAAAC,gBAAA,CAAAH,OAAA,QAAAF,QAAA;EACnB,IAAI,CAACM,IAAI,GAAGF,OAAO,CAACE,IAAI;EACxB,IAAI,CAACC,OAAO,GAAGH,OAAO,CAACG,OAAO;EAC9B,IAAI,CAACC,iBAAiB,GAAGJ,OAAO,CAACI,iBAAiB;EAClD,IAAI,CAACC,UAAU,GAAGL,OAAO,CAACK,UAAU;EACpC,IAAI,CAACC,UAAU,GAAGN,OAAO,CAACM,UAAU;EACpC,IAAI,CAACC,IAAI,GAAGP,OAAO,CAACO,IAAI,KAAK,IAAI;AACnC,CAAC;AAIH;AACA;AACA;AACA;AACA;AACAX,QAAQ,CAACY,KAAK,GAAE,UAASR,OAAO,EAAE;EAChC,IAAIS,IAAI,GAAG,IAAI;EACf,IAAI,OAAOT,OAAO,KAAK,QAAQ,EAAE;IAC/BS,IAAI,GAAGC,oBAAW,CAACC,KAAK,CAACX,OAAO,CAAC,IAAI,CAAC,CAAC;IACvCS,IAAI,CAACP,IAAI,GAAGF,OAAO;EACrB,CAAC,MAAM,IAAI,IAAAY,QAAA,CAAAd,OAAA,EAAOE,OAAO,MAAK,QAAQ,EAAE;IACtCS,IAAI,GAAGT,OAAO;EAChB;EACAS,IAAI,GAAG,IAAAI,qBAAc,EAACH,oBAAW,CAACC,KAAK,CAAC,GAAG,CAAC,EAAEF,IAAI,CAAC;EACnD,OAAO,IAAIb,QAAQ,CAACa,IAAI,CAAC;AAC3B,CAAC", "ignoreList": []}]}