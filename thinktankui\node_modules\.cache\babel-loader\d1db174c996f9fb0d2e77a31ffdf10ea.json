{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\Crontab\\year.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\Crontab\\year.vue", "mtime": 1749104047618}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["data", "fullYear", "radioValue", "cycle01", "cycle02", "average01", "average02", "checkboxList", "checkNum", "$options", "propsData", "check", "name", "props", "methods", "radioChange", "$emit", "cycleTotal", "averageTotal", "checkboxString", "cycleChange", "averageChange", "checkboxChange", "watch", "computed", "str", "join", "mounted", "Number", "Date", "getFullYear"], "sources": ["src/components/Crontab/year.vue"], "sourcesContent": ["<template>\r\n\t<el-form size=\"small\">\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio :label=\"1\" v-model='radioValue'>\r\n\t\t\t\t不填，允许的通配符[, - * /]\r\n\t\t\t</el-radio>\r\n\t\t</el-form-item>\r\n\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio :label=\"2\" v-model='radioValue'>\r\n\t\t\t\t每年\r\n\t\t\t</el-radio>\r\n\t\t</el-form-item>\r\n\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio :label=\"3\" v-model='radioValue'>\r\n\t\t\t\t周期从\r\n\t\t\t\t<el-input-number v-model='cycle01' :min='fullYear' :max=\"2098\" /> -\r\n\t\t\t\t<el-input-number v-model='cycle02' :min=\"cycle01 ? cycle01 + 1 : fullYear + 1\" :max=\"2099\" />\r\n\t\t\t</el-radio>\r\n\t\t</el-form-item>\r\n\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio :label=\"4\" v-model='radioValue'>\r\n\t\t\t\t从\r\n\t\t\t\t<el-input-number v-model='average01' :min='fullYear' :max=\"2098\"/> 年开始，每\r\n\t\t\t\t<el-input-number v-model='average02' :min=\"1\" :max=\"2099 - average01 || fullYear\" /> 年执行一次\r\n\t\t\t</el-radio>\r\n\r\n\t\t</el-form-item>\r\n\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio :label=\"5\" v-model='radioValue'>\r\n\t\t\t\t指定\r\n\t\t\t\t<el-select clearable v-model=\"checkboxList\" placeholder=\"可多选\" multiple>\r\n\t\t\t\t\t<el-option v-for=\"item in 9\" :key=\"item\" :value=\"item - 1 + fullYear\" :label=\"item -1 + fullYear\" />\r\n\t\t\t\t</el-select>\r\n\t\t\t</el-radio>\r\n\t\t</el-form-item>\r\n\t</el-form>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tfullYear: 0,\r\n\t\t\tradioValue: 1,\r\n\t\t\tcycle01: 0,\r\n\t\t\tcycle02: 0,\r\n\t\t\taverage01: 0,\r\n\t\t\taverage02: 1,\r\n\t\t\tcheckboxList: [],\r\n\t\t\tcheckNum: this.$options.propsData.check\r\n\t\t}\r\n\t},\r\n\tname: 'crontab-year',\r\n\tprops: ['check', 'month', 'cron'],\r\n\tmethods: {\r\n\t\t// 单选按钮值变化时\r\n\t\tradioChange() {\r\n\t\t\tswitch (this.radioValue) {\r\n\t\t\t\tcase 1:\r\n\t\t\t\t\tthis.$emit('update', 'year', '');\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 2:\r\n\t\t\t\t\tthis.$emit('update', 'year', '*');\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 3:\r\n\t\t\t\t\tthis.$emit('update', 'year', this.cycleTotal);\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 4:\r\n\t\t\t\t\tthis.$emit('update', 'year', this.averageTotal);\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 5:\r\n\t\t\t\t\tthis.$emit('update', 'year', this.checkboxString);\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 周期两个值变化时\r\n\t\tcycleChange() {\r\n\t\t\tif (this.radioValue == '3') {\r\n\t\t\t\tthis.$emit('update', 'year', this.cycleTotal);\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 平均两个值变化时\r\n\t\taverageChange() {\r\n\t\t\tif (this.radioValue == '4') {\r\n\t\t\t\tthis.$emit('update', 'year', this.averageTotal);\r\n\t\t\t}\r\n\t\t},\r\n\t\t// checkbox值变化时\r\n\t\tcheckboxChange() {\r\n\t\t\tif (this.radioValue == '5') {\r\n\t\t\t\tthis.$emit('update', 'year', this.checkboxString);\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\twatch: {\r\n\t\t'radioValue': 'radioChange',\r\n\t\t'cycleTotal': 'cycleChange',\r\n\t\t'averageTotal': 'averageChange',\r\n\t\t'checkboxString': 'checkboxChange'\r\n\t},\r\n\tcomputed: {\r\n\t\t// 计算两个周期值\r\n\t\tcycleTotal: function () {\r\n\t\t\tconst cycle01 = this.checkNum(this.cycle01, this.fullYear, 2098)\r\n\t\t\tconst cycle02 = this.checkNum(this.cycle02, cycle01 ? cycle01 + 1 : this.fullYear + 1, 2099)\r\n\t\t\treturn cycle01 + '-' + cycle02;\r\n\t\t},\r\n\t\t// 计算平均用到的值\r\n\t\taverageTotal: function () {\r\n\t\t\tconst average01 = this.checkNum(this.average01, this.fullYear, 2098)\r\n\t\t\tconst average02 = this.checkNum(this.average02, 1, 2099 - average01 || this.fullYear)\r\n\t\t\treturn average01 + '/' + average02;\r\n\t\t},\r\n\t\t// 计算勾选的checkbox值合集\r\n\t\tcheckboxString: function () {\r\n\t\t\tlet str = this.checkboxList.join();\r\n\t\t\treturn str;\r\n\t\t}\r\n\t},\r\n\tmounted: function () {\r\n\t\t// 仅获取当前年份\r\n\t\tthis.fullYear = Number(new Date().getFullYear());\r\n\t\tthis.cycle01 = this.fullYear\r\n\t\tthis.average01 = this.fullYear\r\n\t}\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCA2CA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,UAAA;MACAC,OAAA;MACAC,OAAA;MACAC,SAAA;MACAC,SAAA;MACAC,YAAA;MACAC,QAAA,OAAAC,QAAA,CAAAC,SAAA,CAAAC;IACA;EACA;EACAC,IAAA;EACAC,KAAA;EACAC,OAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,aAAAb,UAAA;QACA;UACA,KAAAc,KAAA;UACA;QACA;UACA,KAAAA,KAAA;UACA;QACA;UACA,KAAAA,KAAA,wBAAAC,UAAA;UACA;QACA;UACA,KAAAD,KAAA,wBAAAE,YAAA;UACA;QACA;UACA,KAAAF,KAAA,wBAAAG,cAAA;UACA;MACA;IACA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,SAAAlB,UAAA;QACA,KAAAc,KAAA,wBAAAC,UAAA;MACA;IACA;IACA;IACAI,aAAA,WAAAA,cAAA;MACA,SAAAnB,UAAA;QACA,KAAAc,KAAA,wBAAAE,YAAA;MACA;IACA;IACA;IACAI,cAAA,WAAAA,eAAA;MACA,SAAApB,UAAA;QACA,KAAAc,KAAA,wBAAAG,cAAA;MACA;IACA;EACA;EACAI,KAAA;IACA;IACA;IACA;IACA;EACA;EACAC,QAAA;IACA;IACAP,UAAA,WAAAA,WAAA;MACA,IAAAd,OAAA,QAAAK,QAAA,MAAAL,OAAA,OAAAF,QAAA;MACA,IAAAG,OAAA,QAAAI,QAAA,MAAAJ,OAAA,EAAAD,OAAA,GAAAA,OAAA,YAAAF,QAAA;MACA,OAAAE,OAAA,SAAAC,OAAA;IACA;IACA;IACAc,YAAA,WAAAA,aAAA;MACA,IAAAb,SAAA,QAAAG,QAAA,MAAAH,SAAA,OAAAJ,QAAA;MACA,IAAAK,SAAA,QAAAE,QAAA,MAAAF,SAAA,YAAAD,SAAA,SAAAJ,QAAA;MACA,OAAAI,SAAA,SAAAC,SAAA;IACA;IACA;IACAa,cAAA,WAAAA,eAAA;MACA,IAAAM,GAAA,QAAAlB,YAAA,CAAAmB,IAAA;MACA,OAAAD,GAAA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA;IACA,KAAA1B,QAAA,GAAA2B,MAAA,KAAAC,IAAA,GAAAC,WAAA;IACA,KAAA3B,OAAA,QAAAF,QAAA;IACA,KAAAI,SAAA,QAAAJ,QAAA;EACA;AACA", "ignoreList": []}]}