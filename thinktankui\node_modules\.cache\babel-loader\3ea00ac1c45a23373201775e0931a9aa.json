{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\modules\\normalizeExternalHTML\\normalizers\\msWord.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\modules\\normalizeExternalHTML\\normalizers\\msWord.js", "mtime": 1749104422255}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749104047587}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["ignoreRegexp", "idRegexp", "indentRegexp", "parseListItem", "element", "html", "style", "getAttribute", "idMatch", "match", "id", "Number", "indentMatch", "indent", "typeRegexp", "RegExp", "concat", "typeMatch", "type", "normalizeListItem", "doc", "msoList", "Array", "from", "querySelectorAll", "ignored", "others", "for<PERSON>ach", "node", "shouldIgnore", "push", "_node$parentNode", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "documentElement", "innerHTML", "listItems", "map", "filter", "parsed", "_loop", "_childListItems$", "childListItems", "current", "shift", "_listItems$", "length", "nextElement<PERSON><PERSON>ling", "ul", "document", "createElement", "listItem", "li", "setAttribute", "append<PERSON><PERSON><PERSON>", "_ref2", "<PERSON><PERSON><PERSON><PERSON>", "slice", "_ref", "e", "normalize"], "sources": ["../../../../src/modules/normalizeExternalHTML/normalizers/msWord.ts"], "sourcesContent": ["const ignoreRegexp = /\\bmso-list:[^;]*ignore/i;\nconst idRegexp = /\\bmso-list:[^;]*\\bl(\\d+)/i;\nconst indentRegexp = /\\bmso-list:[^;]*\\blevel(\\d+)/i;\n\nconst parseListItem = (element: Element, html: string) => {\n  const style = element.getAttribute('style');\n  const idMatch = style?.match(idRegexp);\n  if (!idMatch) {\n    return null;\n  }\n  const id = Number(idMatch[1]);\n\n  const indentMatch = style?.match(indentRegexp);\n  const indent = indentMatch ? Number(indentMatch[1]) : 1;\n\n  const typeRegexp = new RegExp(\n    `@list l${id}:level${indent}\\\\s*\\\\{[^\\\\}]*mso-level-number-format:\\\\s*([\\\\w-]+)`,\n    'i',\n  );\n  const typeMatch = html.match(typeRegexp);\n  const type = typeMatch && typeMatch[1] === 'bullet' ? 'bullet' : 'ordered';\n\n  return { id, indent, type, element };\n};\n\n// list items are represented as `p` tags with styles like `mso-list: l0 level1` where:\n// 1. \"0\" in \"l0\" means the list item id;\n// 2. \"1\" in \"level1\" means the indent level, starting from 1.\nconst normalizeListItem = (doc: Document) => {\n  const msoList = Array.from(doc.querySelectorAll('[style*=mso-list]'));\n  const ignored: Element[] = [];\n  const others: Element[] = [];\n  msoList.forEach((node) => {\n    const shouldIgnore = (node.getAttribute('style') || '').match(ignoreRegexp);\n    if (shouldIgnore) {\n      ignored.push(node);\n    } else {\n      others.push(node);\n    }\n  });\n\n  // Each list item contains a marker wrapped with \"mso-list: Ignore\".\n  ignored.forEach((node) => node.parentNode?.removeChild(node));\n\n  // The list stype is not defined inline with the tag, instead, it's in the\n  // style tag so we need to pass the html as a string.\n  const html = doc.documentElement.innerHTML;\n  const listItems = others\n    .map((element) => parseListItem(element, html))\n    .filter((parsed) => parsed);\n\n  while (listItems.length) {\n    const childListItems = [];\n\n    let current = listItems.shift();\n    // Group continuous items into the same group (aka \"ul\")\n    while (current) {\n      childListItems.push(current);\n      current =\n        listItems.length &&\n        listItems[0]?.element === current.element.nextElementSibling &&\n        // Different id means the next item doesn't belong to this group.\n        listItems[0].id === current.id\n          ? listItems.shift()\n          : null;\n    }\n\n    const ul = document.createElement('ul');\n    childListItems.forEach((listItem) => {\n      const li = document.createElement('li');\n      li.setAttribute('data-list', listItem.type);\n      if (listItem.indent > 1) {\n        li.setAttribute('class', `ql-indent-${listItem.indent - 1}`);\n      }\n      li.innerHTML = listItem.element.innerHTML;\n      ul.appendChild(li);\n    });\n\n    const element = childListItems[0]?.element;\n    const { parentNode } = element ?? {};\n    if (element) {\n      parentNode?.replaceChild(ul, element);\n    }\n    childListItems.slice(1).forEach(({ element: e }) => {\n      parentNode?.removeChild(e);\n    });\n  }\n};\n\nexport default function normalize(doc: Document) {\n  if (\n    doc.documentElement.getAttribute('xmlns:w') ===\n    'urn:schemas-microsoft-com:office:word'\n  ) {\n    normalizeListItem(doc);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAMA,YAAY,GAAG,yBAAyB;AAC9C,IAAMC,QAAQ,GAAG,2BAA2B;AAC5C,IAAMC,YAAY,GAAG,+BAA+B;AAEpD,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,OAAgB,EAAEC,IAAY,EAAK;EACxD,IAAMC,KAAK,GAAGF,OAAO,CAACG,YAAY,CAAC,OAAO,CAAC;EAC3C,IAAMC,OAAO,GAAGF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEG,KAAK,CAACR,QAAQ,CAAC;EACtC,IAAI,CAACO,OAAO,EAAE;IACZ,OAAO,IAAI;EACb;EACA,IAAME,EAAE,GAAGC,MAAM,CAACH,OAAO,CAAC,CAAC,CAAC,CAAC;EAE7B,IAAMI,WAAW,GAAGN,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEG,KAAK,CAACP,YAAY,CAAC;EAC9C,IAAMW,MAAM,GAAGD,WAAW,GAAGD,MAAM,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;EAEvD,IAAME,UAAU,GAAG,IAAIC,MAAM,WAAAC,MAAA,CACjBN,EAAG,YAAAM,MAAA,CAAQH,MAAO,0DAC5B,GACF,CAAC;EACD,IAAMI,SAAS,GAAGZ,IAAI,CAACI,KAAK,CAACK,UAAU,CAAC;EACxC,IAAMI,IAAI,GAAGD,SAAS,IAAIA,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,GAAG,QAAQ,GAAG,SAAS;EAE1E,OAAO;IAAEP,EAAE,EAAFA,EAAE;IAAEG,MAAM,EAANA,MAAM;IAAEK,IAAI,EAAJA,IAAI;IAAEd,OAAA,EAAAA;EAAQ,CAAC;AACtC,CAAC;;AAED;AACA;AACA;AACA,IAAMe,iBAAiB,GAAI,SAArBA,iBAAiBA,CAAIC,GAAa,EAAK;EAC3C,IAAMC,OAAO,GAAGC,KAAK,CAACC,IAAI,CAACH,GAAG,CAACI,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;EACrE,IAAMC,OAAkB,GAAG,EAAE;EAC7B,IAAMC,MAAiB,GAAG,EAAE;EAC5BL,OAAO,CAACM,OAAO,CAAE,UAAAC,IAAI,EAAK;IACxB,IAAMC,YAAY,GAAG,CAACD,IAAI,CAACrB,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,EAAEE,KAAK,CAACT,YAAY,CAAC;IAC3E,IAAI6B,YAAY,EAAE;MAChBJ,OAAO,CAACK,IAAI,CAACF,IAAI,CAAC;IACpB,CAAC,MAAM;MACLF,MAAM,CAACI,IAAI,CAACF,IAAI,CAAC;IACnB;EACF,CAAC,CAAC;;EAEF;EACAH,OAAO,CAACE,OAAO,CAAE,UAAAC,IAAI;IAAA,IAAAG,gBAAA;IAAA,QAAAA,gBAAA,GAAKH,IAAI,CAACI,UAAU,cAAAD,gBAAA,uBAAfA,gBAAA,CAAiBE,WAAW,CAACL,IAAI,CAAC;EAAA,EAAC;;EAE7D;EACA;EACA,IAAMvB,IAAI,GAAGe,GAAG,CAACc,eAAe,CAACC,SAAS;EAC1C,IAAMC,SAAS,GAAGV,MAAM,CACrBW,GAAG,CAAE,UAAAjC,OAAO;IAAA,OAAKD,aAAa,CAACC,OAAO,EAAEC,IAAI,CAAC;EAAA,EAAC,CAC9CiC,MAAM,CAAE,UAAAC,MAAM;IAAA,OAAKA,MAAM;EAAA,EAAC;EAAA,IAAAC,KAAA,YAAAA,MAAA,EAEJ;IAAA,IAAAC,gBAAA;IACvB,IAAMC,cAAc,GAAG,EAAE;IAEzB,IAAIC,OAAO,GAAGP,SAAS,CAACQ,KAAK,CAAC,CAAC;IAC/B;IACA,OAAOD,OAAO,EAAE;MAAA,IAAAE,WAAA;MACdH,cAAc,CAACZ,IAAI,CAACa,OAAO,CAAC;MAC5BA,OAAO,GACLP,SAAS,CAACU,MAAM,IAChB,EAAAD,WAAA,GAAAT,SAAS,CAAC,CAAC,CAAC,cAAAS,WAAA,uBAAZA,WAAA,CAAczC,OAAO,MAAKuC,OAAO,CAACvC,OAAO,CAAC2C,kBAAkB;MAC5D;MACAX,SAAS,CAAC,CAAC,CAAC,CAAC1B,EAAE,KAAKiC,OAAO,CAACjC,EAAE,GAC1B0B,SAAS,CAACQ,KAAK,CAAC,CAAC,GACjB,IAAI;IACZ;IAEA,IAAMI,EAAE,GAAGC,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;IACvCR,cAAc,CAACf,OAAO,CAAE,UAAAwB,QAAQ,EAAK;MACnC,IAAMC,EAAE,GAAGH,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;MACvCE,EAAE,CAACC,YAAY,CAAC,WAAW,EAAEF,QAAQ,CAACjC,IAAI,CAAC;MAC3C,IAAIiC,QAAQ,CAACtC,MAAM,GAAG,CAAC,EAAE;QACvBuC,EAAE,CAACC,YAAY,CAAC,OAAO,eAAArC,MAAA,CAAemC,QAAQ,CAACtC,MAAM,GAAG,CAAE,CAAC,CAAC;MAC9D;MACAuC,EAAE,CAACjB,SAAS,GAAGgB,QAAQ,CAAC/C,OAAO,CAAC+B,SAAS;MACzCa,EAAE,CAACM,WAAW,CAACF,EAAE,CAAC;IACpB,CAAC,CAAC;IAEF,IAAMhD,OAAO,IAAAqC,gBAAA,GAAGC,cAAc,CAAC,CAAC,CAAC,cAAAD,gBAAA,uBAAjBA,gBAAA,CAAmBrC,OAAO;IAC1C,IAAAmD,KAAA,GAAuBnD,OAAO,aAAPA,OAAO,cAAPA,OAAO,GAAI,CAAC,CAAC;MAA5B4B,UAAA,GAAAuB,KAAA,CAAAvB,UAAA;IACR,IAAI5B,OAAO,EAAE;MACX4B,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEwB,YAAY,CAACR,EAAE,EAAE5C,OAAO,CAAC;IACvC;IACAsC,cAAc,CAACe,KAAK,CAAC,CAAC,CAAC,CAAC9B,OAAO,CAAC,UAAA+B,IAAA,EAAoB;MAAA,IAARC,CAAA,GAAGD,IAAA,CAAZtD,OAAO;MACxC4B,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEC,WAAW,CAAC0B,CAAC,CAAC;IAC5B,CAAC,CAAC;EACJ;EAnCA,OAAOvB,SAAS,CAACU,MAAM;IAAAN,KAAA;EAAA;AAoCzB,CAAC;AAEc,SAASoB,SAASA,CAACxC,GAAa,EAAE;EAC/C,IACEA,GAAG,CAACc,eAAe,CAAC3B,YAAY,CAAC,SAAS,CAAC,KAC3C,uCAAuC,EACvC;IACAY,iBAAiB,CAACC,GAAG,CAAC;EACxB;AACF", "ignoreList": []}]}