{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\search-results\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\search-results\\index.vue", "mtime": 1749104047642}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJTZWFyY2hSZXN1bHRzIiwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgb3JpZ2luYWxUb3BOYXY6IHVuZGVmaW5lZCwNCiAgICAgIHNlYXJjaFF1ZXJ5OiAi5LuK5pelIOS4h+WFgyIsDQogICAgICBzZWxlY3RlZFRpbWU6ICIyNGgiLA0KICAgICAgc2VsZWN0ZWRQbGF0Zm9ybTogImFsbCIsDQogICAgICBzZWxlY3RlZEVtb3Rpb246ICJhbGwiLA0KICAgICAgY3VycmVudFBhZ2U6IDEsDQogICAgICBwYWdlU2l6ZTogMTAsDQogICAgICB0b3RhbFJlc3VsdHM6IDEwMDAwLA0KDQogICAgICB0aW1lT3B0aW9uczogWw0KICAgICAgICB7IGxhYmVsOiAiMjTlsI/ml7YiLCB2YWx1ZTogIjI0aCIgfSwNCiAgICAgICAgeyBsYWJlbDogIuS4gOWRqCIsIHZhbHVlOiAiMXciIH0sDQogICAgICAgIHsgbGFiZWw6ICLljYrlubQiLCB2YWx1ZTogIjZtIiB9LA0KICAgICAgICB7IGxhYmVsOiAi5LiA5bm0IiwgdmFsdWU6ICIxeSIgfSwNCiAgICAgICAgeyBsYWJlbDogIuiHquWumuS5iSIsIHZhbHVlOiAiY3VzdG9tIiB9DQogICAgICBdLA0KDQogICAgICBwbGF0Zm9ybU9wdGlvbnM6IFsNCiAgICAgICAgeyBsYWJlbDogIuWFqOmDqCIsIHZhbHVlOiAiYWxsIiwgY291bnQ6IDEwNTQwIH0sDQogICAgICAgIHsgbGFiZWw6ICLlvq7kv6EiLCB2YWx1ZTogIndlY2hhdCIsIGNvdW50OiAxODQ3IH0sDQogICAgICAgIHsgbGFiZWw6ICLlvq7ljZoiLCB2YWx1ZTogIndlaWJvIiwgY291bnQ6IDIwMDggfSwNCiAgICAgICAgeyBsYWJlbDogIuWuouaIt+erryIsIHZhbHVlOiAiYXBwIiwgY291bnQ6IDE3NDggfSwNCiAgICAgICAgeyBsYWJlbDogIuiuuuWdmyIsIHZhbHVlOiAiZm9ydW0iLCBjb3VudDogNjczIH0NCiAgICAgIF0sDQoNCiAgICAgIGVtb3Rpb25PcHRpb25zOiBbDQogICAgICAgIHsgbGFiZWw6ICLlhajpg6giLCB2YWx1ZTogImFsbCIgfSwNCiAgICAgICAgeyBsYWJlbDogIuato+mdoiIsIHZhbHVlOiAicG9zaXRpdmUiIH0sDQogICAgICAgIHsgbGFiZWw6ICLotJ/pnaIiLCB2YWx1ZTogIm5lZ2F0aXZlIiB9LA0KICAgICAgICB7IGxhYmVsOiAi5Lit5oCnIiwgdmFsdWU6ICJuZXV0cmFsIiB9DQogICAgICBdLA0KDQogICAgICBzZWFyY2hSZXN1bHRzOiBbDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogIuS7juaUv+W6nOmDqOmXqDQ05LiJ5Liq5Liq5L2T55qE54Ot54K56Zeu6aKY77yM5Yiw5aqS5L2TMe+8jOimhueblueahO+8iOWQq+S5ie+8ie+8jOaWl+S6ieS4u+S5ieWPo+WPt++8jOeJm+W5tOS4u+imgeeahOmXrumimO+8jOWFseWQjOS6uuW3peaZuuiDveeahOmXrumimOS6uuWRmO+8jOWmguWxseWxseeahOmXrumimOeahOS4u+imgemXrumimO+8jOaWsOW3peS6uu+8jOaJk+W3pe+8jOeUqOWPi+S4ieWuti4uLiIsDQogICAgICAgICAgc291cmNlOiAi5paw5Y2O572RIiwNCiAgICAgICAgICBwdWJsaXNoVGltZTogIjIwMjItMDYtMjkgMjA6MDc6MDQiLA0KICAgICAgICAgIGF1dGhvcjogIjc35Lq66K6o6K66IiwNCiAgICAgICAgICBwbGF0Zm9ybTogIuW5s+WPsOadpea6kCIsDQogICAgICAgICAgcmVhZENvdW50OiAi5pegIiwNCiAgICAgICAgICBsb2NhdGlvbjogIuaXoOaJgOWcqOWcsCIsDQogICAgICAgICAgY2F0ZWdvcnk6ICLmlrDpl7siLA0KICAgICAgICAgIGNvbnRlbnQ6ICLku47mlL/lupzpg6jpl6g0NOS4ieS4quS4quS9k+eahOeDreeCuemXrumimO+8jOWIsOWqkuS9kzHvvIzopobnm5bnmoTvvIjlkKvkuYnvvInvvIzmlpfkuonkuLvkuYnlj6Plj7fvvIzniZvlubTkuLvopoHnmoTpl67popjvvIzlhbHlkIzkurrlt6Xmmbrog73nmoTpl67popjkurrlkZjvvIzlpoLlsbHlsbHnmoTpl67popjnmoTkuLvopoHpl67popjvvIzmlrDlt6XkurrvvIzmiZPlt6XvvIznlKjlj4vkuInlrrYuLi4iDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogIuS4reWkpy3orrrmloflj5HooagoMjAyNeW5tOS4reWkp+iuuuaWh+WPkeihqCnoh6rnhLbmjIfmlbDkuK3lpKct6K665paH5Y+R6KGo5LiW55WM5Zyw5L2N6K665Z2bLTIwMjXlubTnmoTku7flgLzkuK3lm73orrrmlociLA0KICAgICAgICAgIHNvdXJjZTogIuS4reWkp+iuuuaWh+WPkeihqCIsDQogICAgICAgICAgcHVibGlzaFRpbWU6ICIyMDIyLTA2LTI5IDIwOjA3OjA0IiwNCiAgICAgICAgICBhdXRob3I6ICI3N+S6uuiuqOiuuiIsDQogICAgICAgICAgcGxhdGZvcm06ICLlubPlj7DmnaXmupAiLA0KICAgICAgICAgIHJlYWRDb3VudDogIuaXoCIsDQogICAgICAgICAgbG9jYXRpb246ICLml6DmiYDlnKjlnLAiLA0KICAgICAgICAgIGNhdGVnb3J5OiAi6K665paHIiwNCiAgICAgICAgICBjb250ZW50OiAi5Lit5aSnLeiuuuaWh+WPkeihqCgyMDI15bm05Lit5aSn6K665paH5Y+R6KGoKeiHqueEtuaMh+aVsOS4reWkpy3orrrmloflj5HooajkuJbnlYzlnLDkvY3orrrlnZstMjAyNeW5tOeahOS7t+WAvOS4reWbveiuuuaWhy4uLiINCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiAi6L2s5Y+R5b6u5Y2aI+S4rSPlpKflrabnlJ/vvIzkurrmg4XkuJbmlYXjgIIiLA0KICAgICAgICAgIHNvdXJjZTogIuW+ruWNmiIsDQogICAgICAgICAgcHVibGlzaFRpbWU6ICIyMDIyLTA2LTI5IDIwOjA3OjA0IiwNCiAgICAgICAgICBhdXRob3I6ICI3N+S6uuiuqOiuuiIsDQogICAgICAgICAgcGxhdGZvcm06ICLlvq7ljZoiLA0KICAgICAgICAgIHJlYWRDb3VudDogIjEwMDAiLA0KICAgICAgICAgIGxvY2F0aW9uOiAi5YyX5LqsIiwNCiAgICAgICAgICBjYXRlZ29yeTogIuekvuS6pOWqkuS9kyIsDQogICAgICAgICAgY29udGVudDogIui9rOWPkeW+ruWNmiPkuK0j5aSn5a2m55Sf77yM5Lq65oOF5LiW5pWF44CC6L+Z5piv5LiA5p2h5YWz5LqO5aSn5a2m55Sf5Lq66ZmF5YWz57O755qE5b6u5Y2a5YaF5a65Li4uIg0KICAgICAgICB9DQogICAgICBdDQogICAgfTsNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICAvLyDpmpDol4/pobbpg6jlr7zoiKrmoI8NCiAgICB0aGlzLm9yaWdpbmFsVG9wTmF2ID0gdGhpcy4kc3RvcmUuc3RhdGUuc2V0dGluZ3MudG9wTmF2DQogICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ3NldHRpbmdzL2NoYW5nZVNldHRpbmcnLCB7DQogICAgICBrZXk6ICd0b3BOYXYnLA0KICAgICAgdmFsdWU6IGZhbHNlDQogICAgfSkNCg0KICAgIC8vIOiOt+WPllVSTOWPguaVsOS4reeahOaQnOe0ouWFs+mUruivjQ0KICAgIGlmICh0aGlzLiRyb3V0ZS5xdWVyeS5xKSB7DQogICAgICB0aGlzLnNlYXJjaFF1ZXJ5ID0gdGhpcy4kcm91dGUucXVlcnkucTsNCiAgICAgIHRoaXMuaGFuZGxlU2VhcmNoKCk7DQogICAgfQ0KICB9LA0KICBiZWZvcmVEZXN0cm95KCkgew0KICAgIC8vIOaBouWkjemhtumDqOWvvOiIquagj+iuvue9rg0KICAgIGlmICh0aGlzLm9yaWdpbmFsVG9wTmF2ICE9PSB1bmRlZmluZWQpIHsNCiAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdzZXR0aW5ncy9jaGFuZ2VTZXR0aW5nJywgew0KICAgICAgICBrZXk6ICd0b3BOYXYnLA0KICAgICAgICB2YWx1ZTogdGhpcy5vcmlnaW5hbFRvcE5hdg0KICAgICAgfSkNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBoYW5kbGVTZWFyY2goKSB7DQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOaQnOe0ojogJHt0aGlzLnNlYXJjaFF1ZXJ5fWApOw0KICAgICAgLy8g5a6e6ZmF5pCc57Si6YC76L6RDQogICAgfSwNCg0KICAgIHNlbGVjdFRpbWUodmFsdWUpIHsNCiAgICAgIHRoaXMuc2VsZWN0ZWRUaW1lID0gdmFsdWU7DQogICAgICB0aGlzLmhhbmRsZVNlYXJjaCgpOw0KICAgIH0sDQoNCiAgICBzZWxlY3RQbGF0Zm9ybSh2YWx1ZSkgew0KICAgICAgdGhpcy5zZWxlY3RlZFBsYXRmb3JtID0gdmFsdWU7DQogICAgICB0aGlzLmhhbmRsZVNlYXJjaCgpOw0KICAgIH0sDQoNCiAgICBzZWxlY3RFbW90aW9uKHZhbHVlKSB7DQogICAgICB0aGlzLnNlbGVjdGVkRW1vdGlvbiA9IHZhbHVlOw0KICAgICAgdGhpcy5oYW5kbGVTZWFyY2goKTsNCiAgICB9LA0KDQogICAgaGFuZGxlUGFnZUNoYW5nZShwYWdlKSB7DQogICAgICB0aGlzLmN1cnJlbnRQYWdlID0gcGFnZTsNCiAgICAgIC8vIOWKoOi9veWvueW6lOmhtemdouaVsOaNrg0KICAgIH0sDQoNCiAgICBnb0JhY2soKSB7DQogICAgICAvLyDov5Tlm57kuIrkuIDpobXvvIzlpoLmnpzmsqHmnInljoblj7LorrDlvZXliJnov5Tlm57kv6Hmga/msYfmgLvpobXpnaINCiAgICAgIGlmICh3aW5kb3cuaGlzdG9yeS5sZW5ndGggPiAxKSB7DQogICAgICAgIHRoaXMuJHJvdXRlci5nbygtMSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCgnL2luZm8tc3VtbWFyeScpOw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/search-results", "sourcesContent": ["<template>\r\n  <div class=\"search-results-container\">\r\n    <!-- 搜索区域 -->\r\n    <div class=\"search-section\">\r\n      <div class=\"search-header\">\r\n        <el-button type=\"text\" icon=\"el-icon-arrow-left\" @click=\"goBack\" class=\"back-button\">\r\n          返回\r\n        </el-button>\r\n        <div class=\"search-tabs\">\r\n          <div class=\"tab-item active\">全文检索</div>\r\n          <div class=\"tab-item\">可视化</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"search-box\">\r\n        <el-input\r\n          v-model=\"searchQuery\"\r\n          placeholder=\"请输入搜索关键词\"\r\n          class=\"search-input\"\r\n          @keyup.enter=\"handleSearch\"\r\n        >\r\n          <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"handleSearch\"></el-button>\r\n        </el-input>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 筛选条件区域 -->\r\n    <div class=\"filter-section\">\r\n      <!-- 时间筛选 -->\r\n      <div class=\"filter-row\">\r\n        <span class=\"filter-label\">时间范围:</span>\r\n        <div class=\"filter-options\">\r\n          <el-button\r\n            v-for=\"time in timeOptions\"\r\n            :key=\"time.value\"\r\n            :type=\"selectedTime === time.value ? 'primary' : ''\"\r\n            size=\"small\"\r\n            @click=\"selectTime(time.value)\"\r\n          >\r\n            {{ time.label }}\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 平台筛选 -->\r\n      <div class=\"filter-row\">\r\n        <span class=\"filter-label\">平台类型:</span>\r\n        <div class=\"filter-options\">\r\n          <el-button\r\n            v-for=\"platform in platformOptions\"\r\n            :key=\"platform.value\"\r\n            :type=\"selectedPlatform === platform.value ? 'primary' : ''\"\r\n            size=\"small\"\r\n            @click=\"selectPlatform(platform.value)\"\r\n          >\r\n            {{ platform.label }}\r\n            <span v-if=\"platform.count\" class=\"count\">({{ platform.count }})</span>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 情感筛选 -->\r\n      <div class=\"filter-row\">\r\n        <span class=\"filter-label\">情感类型:</span>\r\n        <div class=\"filter-options\">\r\n          <el-button\r\n            v-for=\"emotion in emotionOptions\"\r\n            :key=\"emotion.value\"\r\n            :type=\"selectedEmotion === emotion.value ? 'primary' : ''\"\r\n            size=\"small\"\r\n            @click=\"selectEmotion(emotion.value)\"\r\n          >\r\n            {{ emotion.label }}\r\n            <span v-if=\"emotion.count\" class=\"count\">({{ emotion.count }})</span>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 其他筛选 -->\r\n      <div class=\"filter-row\">\r\n        <span class=\"filter-label\">其他筛选:</span>\r\n        <div class=\"filter-options\">\r\n          <el-button size=\"small\">作者类型</el-button>\r\n          <el-button size=\"small\">地域</el-button>\r\n          <el-button size=\"small\">影响力</el-button>\r\n          <el-button size=\"small\">传播量</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 结果统计 -->\r\n    <div class=\"result-stats\">\r\n      <span>共{{ totalResults }}条结果</span>\r\n      <div class=\"action-buttons\">\r\n        <el-button size=\"small\">导出</el-button>\r\n        <el-button type=\"primary\" size=\"small\">分析</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索结果列表 -->\r\n    <div class=\"results-list\">\r\n      <div v-for=\"(item, index) in searchResults\" :key=\"index\" class=\"result-item\">\r\n        <div class=\"result-header\">\r\n          <h3 class=\"result-title\">{{ item.title }}</h3>\r\n          <div class=\"result-actions\">\r\n            <el-button type=\"text\" icon=\"el-icon-view\"></el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"result-meta\">\r\n          <span class=\"meta-item\">{{ item.source }}</span>\r\n          <span class=\"meta-item\">{{ item.publishTime }}</span>\r\n          <span class=\"meta-item\">{{ item.author }}</span>\r\n          <span class=\"meta-item\">{{ item.platform }}</span>\r\n          <span class=\"meta-item\">阅读量: {{ item.readCount }}</span>\r\n          <span class=\"meta-item\">{{ item.location }}</span>\r\n          <span class=\"meta-item\">{{ item.category }}</span>\r\n        </div>\r\n\r\n        <div class=\"result-content\">\r\n          {{ item.content }}\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 分页 -->\r\n    <div class=\"pagination-container\">\r\n      <el-pagination\r\n        background\r\n        layout=\"prev, pager, next\"\r\n        :total=\"totalResults\"\r\n        :current-page.sync=\"currentPage\"\r\n        :page-size=\"pageSize\"\r\n        @current-change=\"handlePageChange\"\r\n      ></el-pagination>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"SearchResults\",\r\n  data() {\r\n    return {\r\n      originalTopNav: undefined,\r\n      searchQuery: \"今日 万元\",\r\n      selectedTime: \"24h\",\r\n      selectedPlatform: \"all\",\r\n      selectedEmotion: \"all\",\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      totalResults: 10000,\r\n\r\n      timeOptions: [\r\n        { label: \"24小时\", value: \"24h\" },\r\n        { label: \"一周\", value: \"1w\" },\r\n        { label: \"半年\", value: \"6m\" },\r\n        { label: \"一年\", value: \"1y\" },\r\n        { label: \"自定义\", value: \"custom\" }\r\n      ],\r\n\r\n      platformOptions: [\r\n        { label: \"全部\", value: \"all\", count: 10540 },\r\n        { label: \"微信\", value: \"wechat\", count: 1847 },\r\n        { label: \"微博\", value: \"weibo\", count: 2008 },\r\n        { label: \"客户端\", value: \"app\", count: 1748 },\r\n        { label: \"论坛\", value: \"forum\", count: 673 }\r\n      ],\r\n\r\n      emotionOptions: [\r\n        { label: \"全部\", value: \"all\" },\r\n        { label: \"正面\", value: \"positive\" },\r\n        { label: \"负面\", value: \"negative\" },\r\n        { label: \"中性\", value: \"neutral\" }\r\n      ],\r\n\r\n      searchResults: [\r\n        {\r\n          title: \"从政府部门44三个个体的热点问题，到媒体1，覆盖的（含义），斗争主义口号，牛年主要的问题，共同人工智能的问题人员，如山山的问题的主要问题，新工人，打工，用友三家...\",\r\n          source: \"新华网\",\r\n          publishTime: \"2022-06-29 20:07:04\",\r\n          author: \"77人讨论\",\r\n          platform: \"平台来源\",\r\n          readCount: \"无\",\r\n          location: \"无所在地\",\r\n          category: \"新闻\",\r\n          content: \"从政府部门44三个个体的热点问题，到媒体1，覆盖的（含义），斗争主义口号，牛年主要的问题，共同人工智能的问题人员，如山山的问题的主要问题，新工人，打工，用友三家...\"\r\n        },\r\n        {\r\n          title: \"中大-论文发表(2025年中大论文发表)自然指数中大-论文发表世界地位论坛-2025年的价值中国论文\",\r\n          source: \"中大论文发表\",\r\n          publishTime: \"2022-06-29 20:07:04\",\r\n          author: \"77人讨论\",\r\n          platform: \"平台来源\",\r\n          readCount: \"无\",\r\n          location: \"无所在地\",\r\n          category: \"论文\",\r\n          content: \"中大-论文发表(2025年中大论文发表)自然指数中大-论文发表世界地位论坛-2025年的价值中国论文...\"\r\n        },\r\n        {\r\n          title: \"转发微博#中#大学生，人情世故。\",\r\n          source: \"微博\",\r\n          publishTime: \"2022-06-29 20:07:04\",\r\n          author: \"77人讨论\",\r\n          platform: \"微博\",\r\n          readCount: \"1000\",\r\n          location: \"北京\",\r\n          category: \"社交媒体\",\r\n          content: \"转发微博#中#大学生，人情世故。这是一条关于大学生人际关系的微博内容...\"\r\n        }\r\n      ]\r\n    };\r\n  },\r\n  mounted() {\r\n    // 隐藏顶部导航栏\r\n    this.originalTopNav = this.$store.state.settings.topNav\r\n    this.$store.dispatch('settings/changeSetting', {\r\n      key: 'topNav',\r\n      value: false\r\n    })\r\n\r\n    // 获取URL参数中的搜索关键词\r\n    if (this.$route.query.q) {\r\n      this.searchQuery = this.$route.query.q;\r\n      this.handleSearch();\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    // 恢复顶部导航栏设置\r\n    if (this.originalTopNav !== undefined) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'topNav',\r\n        value: this.originalTopNav\r\n      })\r\n    }\r\n  },\r\n  methods: {\r\n    handleSearch() {\r\n      this.$message.success(`搜索: ${this.searchQuery}`);\r\n      // 实际搜索逻辑\r\n    },\r\n\r\n    selectTime(value) {\r\n      this.selectedTime = value;\r\n      this.handleSearch();\r\n    },\r\n\r\n    selectPlatform(value) {\r\n      this.selectedPlatform = value;\r\n      this.handleSearch();\r\n    },\r\n\r\n    selectEmotion(value) {\r\n      this.selectedEmotion = value;\r\n      this.handleSearch();\r\n    },\r\n\r\n    handlePageChange(page) {\r\n      this.currentPage = page;\r\n      // 加载对应页面数据\r\n    },\r\n\r\n    goBack() {\r\n      // 返回上一页，如果没有历史记录则返回信息汇总页面\r\n      if (window.history.length > 1) {\r\n        this.$router.go(-1);\r\n      } else {\r\n        this.$router.push('/info-summary');\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.search-results-container {\r\n  padding: 20px;\r\n  background-color: #f5f5f5;\r\n  min-height: 100vh;\r\n}\r\n\r\n.search-section {\r\n  background: white;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.search-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.back-button {\r\n  margin-right: 20px;\r\n  color: #409EFF;\r\n  font-size: 14px;\r\n}\r\n\r\n.back-button:hover {\r\n  color: #66b1ff;\r\n}\r\n\r\n.search-tabs {\r\n  display: flex;\r\n}\r\n\r\n.tab-item {\r\n  padding: 8px 20px;\r\n  cursor: pointer;\r\n  border-bottom: 2px solid transparent;\r\n  color: #666;\r\n}\r\n\r\n.tab-item.active {\r\n  color: #409EFF;\r\n  border-bottom-color: #409EFF;\r\n}\r\n\r\n.search-box {\r\n  max-width: 600px;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n}\r\n\r\n.filter-section {\r\n  background: white;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.filter-row {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.filter-row:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.filter-label {\r\n  width: 80px;\r\n  color: #666;\r\n  font-size: 14px;\r\n}\r\n\r\n.filter-options {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 10px;\r\n}\r\n\r\n.count {\r\n  color: #999;\r\n  font-size: 12px;\r\n}\r\n\r\n.result-stats {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: white;\r\n  padding: 15px 20px;\r\n  margin-bottom: 20px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.results-list {\r\n  background: white;\r\n  border-radius: 4px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.result-item {\r\n  padding: 20px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.result-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.result-title {\r\n  font-size: 16px;\r\n  color: #333;\r\n  margin: 0;\r\n  line-height: 1.4;\r\n  flex: 1;\r\n  margin-right: 20px;\r\n}\r\n\r\n.result-meta {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n  margin-bottom: 10px;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.meta-item {\r\n  white-space: nowrap;\r\n}\r\n\r\n.result-content {\r\n  color: #666;\r\n  line-height: 1.6;\r\n  font-size: 14px;\r\n}\r\n\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  background: white;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n}\r\n</style>\r\n"]}]}