{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\tool\\build\\IconsDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\tool\\build\\IconsDialog.vue", "mtime": 1749104047651}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgaWNvbkxpc3QgZnJvbSAnQC91dGlscy9nZW5lcmF0b3IvaWNvbi5qc29uJw0KDQpjb25zdCBvcmlnaW5MaXN0ID0gaWNvbkxpc3QubWFwKG5hbWUgPT4gYGVsLWljb24tJHtuYW1lfWApDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgaW5oZXJpdEF0dHJzOiBmYWxzZSwNCiAgcHJvcHM6IFsnY3VycmVudCddLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBpY29uTGlzdDogb3JpZ2luTGlzdCwNCiAgICAgIGFjdGl2ZTogbnVsbCwNCiAgICAgIGtleTogJycNCiAgICB9DQogIH0sDQogIHdhdGNoOiB7DQogICAga2V5KHZhbCkgew0KICAgICAgaWYgKHZhbCkgew0KICAgICAgICB0aGlzLmljb25MaXN0ID0gb3JpZ2luTGlzdC5maWx0ZXIobmFtZSA9PiBuYW1lLmluZGV4T2YodmFsKSA+IC0xKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5pY29uTGlzdCA9IG9yaWdpbkxpc3QNCiAgICAgIH0NCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBvbk9wZW4oKSB7DQogICAgICB0aGlzLmFjdGl2ZSA9IHRoaXMuY3VycmVudA0KICAgICAgdGhpcy5rZXkgPSAnJw0KICAgIH0sDQogICAgb25DbG9zZSgpIHt9LA0KICAgIG9uU2VsZWN0KGljb24pIHsNCiAgICAgIHRoaXMuYWN0aXZlID0gaWNvbg0KICAgICAgdGhpcy4kZW1pdCgnc2VsZWN0JywgaWNvbikNCiAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZTp2aXNpYmxlJywgZmFsc2UpDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["IconsDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "IconsDialog.vue", "sourceRoot": "src/views/tool/build", "sourcesContent": ["<template>\r\n  <div class=\"icon-dialog\">\r\n    <el-dialog\r\n      v-bind=\"$attrs\"\r\n      width=\"980px\"\r\n      :modal-append-to-body=\"false\"\r\n      v-on=\"$listeners\"\r\n      @open=\"onOpen\"\r\n      @close=\"onClose\"\r\n    >\r\n      <div slot=\"title\">\r\n        选择图标\r\n        <el-input\r\n          v-model=\"key\"\r\n          size=\"mini\"\r\n          :style=\"{width: '260px'}\"\r\n          placeholder=\"请输入图标名称\"\r\n          prefix-icon=\"el-icon-search\"\r\n          clearable\r\n        />\r\n      </div>\r\n      <ul class=\"icon-ul\">\r\n        <li\r\n          v-for=\"icon in iconList\"\r\n          :key=\"icon\"\r\n          :class=\"active===icon?'active-item':''\"\r\n          @click=\"onSelect(icon)\"\r\n        >\r\n          <i :class=\"icon\" />\r\n          <div>{{ icon }}</div>\r\n        </li>\r\n      </ul>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\nimport iconList from '@/utils/generator/icon.json'\r\n\r\nconst originList = iconList.map(name => `el-icon-${name}`)\r\n\r\nexport default {\r\n  inheritAttrs: false,\r\n  props: ['current'],\r\n  data() {\r\n    return {\r\n      iconList: originList,\r\n      active: null,\r\n      key: ''\r\n    }\r\n  },\r\n  watch: {\r\n    key(val) {\r\n      if (val) {\r\n        this.iconList = originList.filter(name => name.indexOf(val) > -1)\r\n      } else {\r\n        this.iconList = originList\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    onOpen() {\r\n      this.active = this.current\r\n      this.key = ''\r\n    },\r\n    onClose() {},\r\n    onSelect(icon) {\r\n      this.active = icon\r\n      this.$emit('select', icon)\r\n      this.$emit('update:visible', false)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.icon-ul {\r\n  margin: 0;\r\n  padding: 0;\r\n  font-size: 0;\r\n  li {\r\n    list-style-type: none;\r\n    text-align: center;\r\n    font-size: 14px;\r\n    display: inline-block;\r\n    width: 16.66%;\r\n    box-sizing: border-box;\r\n    height: 108px;\r\n    padding: 15px 6px 6px 6px;\r\n    cursor: pointer;\r\n    overflow: hidden;\r\n    &:hover {\r\n      background: #f2f2f2;\r\n    }\r\n    &.active-item{\r\n      background: #e1f3fb;\r\n      color: #7a6df0\r\n    }\r\n    > i {\r\n      font-size: 30px;\r\n      line-height: 50px;\r\n    }\r\n  }\r\n}\r\n.icon-dialog {\r\n  ::v-deep .el-dialog {\r\n    border-radius: 8px;\r\n    margin-bottom: 0;\r\n    margin-top: 4vh !important;\r\n    display: flex;\r\n    flex-direction: column;\r\n    max-height: 92vh;\r\n    overflow: hidden;\r\n    box-sizing: border-box;\r\n    .el-dialog__header {\r\n      padding-top: 14px;\r\n    }\r\n    .el-dialog__body {\r\n      margin: 0 20px 20px 20px;\r\n      padding: 0;\r\n      overflow: auto;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}