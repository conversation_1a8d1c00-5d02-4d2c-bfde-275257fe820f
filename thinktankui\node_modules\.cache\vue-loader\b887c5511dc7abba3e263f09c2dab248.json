{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\event-analysis\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\event-analysis\\index.vue", "mtime": 1749104047639}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749104419874}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749104418470}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749104420491}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgKiBhcyBlY2hhcnRzIGZyb20gJ2VjaGFydHMnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ0V2ZW50QW5hbHlzaXMnLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHNlYXJjaFF1ZXJ5OiAnJywNCiAgICAgIGN1cnJlbnRBbmFseXNpczogbnVsbCwNCiAgICAgIHRpbWVSYW5nZTogJ3RvZGF5JywNCiAgICAgIGN1c3RvbVRpbWVSYW5nZTogW10sDQogICAgICBldmVudEZvcm06IHsNCiAgICAgICAgbmFtZTogJycsDQogICAgICAgIHRpbWVSYW5nZTogW10sDQogICAgICAgIGtleXdvcmRzOiAnJywNCiAgICAgICAgYW5hbHlzaXNXb3JkczogJycsDQogICAgICAgIGV4Y2x1ZGVXb3JkczogJycsDQogICAgICAgIG1vbml0b3JUeXBlczogWyd3ZWInLCAnd2VpYm8nXQ0KICAgICAgfSwNCiAgICAgIGFuYWx5c2lzTGlzdDogWw0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+aWueWkquWTgeeJjOS6i+S7tuWIhuaekCcsDQogICAgICAgICAgZGF0ZTogJzIwMjMtMDQtMjInLA0KICAgICAgICAgIHRvdGFsSW5mbzogJzQsNjUzJywNCiAgICAgICAgICBwb3NpdGl2ZUNvdW50OiAnNTgnLA0KICAgICAgICAgIG5ldXRyYWxDb3VudDogJzQsNTgzJywNCiAgICAgICAgICBuZWdhdGl2ZUNvdW50OiAnMTInDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBuYW1lOiAn56ue5ZOB5LqL5Lu25YiG5p6QJywNCiAgICAgICAgICBkYXRlOiAnMjAyMy0wNC0yMCcsDQogICAgICAgICAgdG90YWxJbmZvOiAnMywyNDUnLA0KICAgICAgICAgIHBvc2l0aXZlQ291bnQ6ICc0MicsDQogICAgICAgICAgbmV1dHJhbENvdW50OiAnMywxODAnLA0KICAgICAgICAgIG5lZ2F0aXZlQ291bnQ6ICcyMycNCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIGNoYXJ0czogew0KICAgICAgICB0cmVuZENoYXJ0OiBudWxsDQogICAgICB9DQogICAgfQ0KICB9LA0KICBjb21wdXRlZDogew0KICAgIGZpbHRlcmVkQW5hbHlzaXNMaXN0KCkgew0KICAgICAgaWYgKCF0aGlzLnNlYXJjaFF1ZXJ5KSByZXR1cm4gdGhpcy5hbmFseXNpc0xpc3QNCg0KICAgICAgY29uc3QgcXVlcnkgPSB0aGlzLnNlYXJjaFF1ZXJ5LnRvTG93ZXJDYXNlKCkNCiAgICAgIHJldHVybiB0aGlzLmFuYWx5c2lzTGlzdC5maWx0ZXIoaXRlbSA9Pg0KICAgICAgICBpdGVtLm5hbWUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhxdWVyeSkNCiAgICAgICkNCiAgICB9DQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5pbml0Q2hhcnRzKCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIHNlbGVjdEFuYWx5c2lzKGFuYWx5c2lzKSB7DQogICAgICB0aGlzLmN1cnJlbnRBbmFseXNpcyA9IGFuYWx5c2lzDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIHRoaXMucmVuZGVyQ2hhcnRzKCkNCiAgICAgIH0pDQogICAgfSwNCiAgICBzdWJtaXRFdmVudEZvcm0oKSB7DQogICAgICAvLyDlpITnkIbooajljZXmj5DkuqQNCiAgICAgIGNvbnN0IG5ld0FuYWx5c2lzID0gew0KICAgICAgICBuYW1lOiB0aGlzLmV2ZW50Rm9ybS5uYW1lLA0KICAgICAgICBkYXRlOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXSwNCiAgICAgICAgdG90YWxJbmZvOiAnMCcsDQogICAgICAgIHBvc2l0aXZlQ291bnQ6ICcwJywNCiAgICAgICAgbmV1dHJhbENvdW50OiAnMCcsDQogICAgICAgIG5lZ2F0aXZlQ291bnQ6ICcwJw0KICAgICAgfQ0KDQogICAgICB0aGlzLmFuYWx5c2lzTGlzdC51bnNoaWZ0KG5ld0FuYWx5c2lzKQ0KICAgICAgdGhpcy5zZWxlY3RBbmFseXNpcyhuZXdBbmFseXNpcykNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlDQoNCiAgICAgIC8vIOmHjee9ruihqOWNlQ0KICAgICAgdGhpcy5ldmVudEZvcm0gPSB7DQogICAgICAgIG5hbWU6ICcnLA0KICAgICAgICB0aW1lUmFuZ2U6IFtdLA0KICAgICAgICBrZXl3b3JkczogJycsDQogICAgICAgIGFuYWx5c2lzV29yZHM6ICcnLA0KICAgICAgICBleGNsdWRlV29yZHM6ICcnLA0KICAgICAgICBtb25pdG9yVHlwZXM6IFsnd2ViJywgJ3dlaWJvJ10NCiAgICAgIH0NCiAgICB9LA0KICAgIGluaXRDaGFydHMoKSB7DQogICAgICBpZiAodGhpcy5jdXJyZW50QW5hbHlzaXMpIHsNCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIGlmICh0aGlzLiRyZWZzLnRyZW5kQ2hhcnQpIHsNCiAgICAgICAgICAgIHRoaXMuY2hhcnRzLnRyZW5kQ2hhcnQgPSBlY2hhcnRzLmluaXQodGhpcy4kcmVmcy50cmVuZENoYXJ0KQ0KICAgICAgICAgICAgdGhpcy5yZW5kZXJUcmVuZENoYXJ0KCkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICByZW5kZXJDaGFydHMoKSB7DQogICAgICBpZiAodGhpcy4kcmVmcy50cmVuZENoYXJ0KSB7DQogICAgICAgIGlmICghdGhpcy5jaGFydHMudHJlbmRDaGFydCkgew0KICAgICAgICAgIHRoaXMuY2hhcnRzLnRyZW5kQ2hhcnQgPSBlY2hhcnRzLmluaXQodGhpcy4kcmVmcy50cmVuZENoYXJ0KQ0KICAgICAgICB9DQogICAgICAgIHRoaXMucmVuZGVyVHJlbmRDaGFydCgpDQogICAgICB9DQogICAgfSwNCiAgICByZW5kZXJUcmVuZENoYXJ0KCkgew0KICAgICAgaWYgKCF0aGlzLmNoYXJ0cy50cmVuZENoYXJ0KSByZXR1cm4NCg0KICAgICAgY29uc3Qgb3B0aW9uID0gew0KICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgdHJpZ2dlcjogJ2F4aXMnDQogICAgICAgIH0sDQogICAgICAgIGxlZ2VuZDogew0KICAgICAgICAgIGRhdGE6IFsn5oC76YePJywgJ+ato+mdoicsICfkuK3mgKcnLCAn6LSf6Z2iJ10NCiAgICAgICAgfSwNCiAgICAgICAgZ3JpZDogew0KICAgICAgICAgIGxlZnQ6ICczJScsDQogICAgICAgICAgcmlnaHQ6ICc0JScsDQogICAgICAgICAgYm90dG9tOiAnMyUnLA0KICAgICAgICAgIGNvbnRhaW5MYWJlbDogdHJ1ZQ0KICAgICAgICB9LA0KICAgICAgICB4QXhpczogew0KICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsDQogICAgICAgICAgYm91bmRhcnlHYXA6IGZhbHNlLA0KICAgICAgICAgIGRhdGE6IFsn5ZGo5LiAJywgJ+WRqOS6jCcsICflkajkuIknLCAn5ZGo5ZubJywgJ+WRqOS6lCcsICflkajlha0nLCAn5ZGo5pelJ10NCiAgICAgICAgfSwNCiAgICAgICAgeUF4aXM6IHsNCiAgICAgICAgICB0eXBlOiAndmFsdWUnDQogICAgICAgIH0sDQogICAgICAgIHNlcmllczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG5hbWU6ICfmgLvph48nLA0KICAgICAgICAgICAgdHlwZTogJ2xpbmUnLA0KICAgICAgICAgICAgZGF0YTogWzEyMCwgMTMyLCAxMDEsIDEzNCwgOTAsIDIzMCwgMjEwXQ0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogJ+ato+mdoicsDQogICAgICAgICAgICB0eXBlOiAnbGluZScsDQogICAgICAgICAgICBkYXRhOiBbMjAsIDMyLCAyMSwgMzQsIDIwLCAzMCwgMTBdDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAn5Lit5oCnJywNCiAgICAgICAgICAgIHR5cGU6ICdsaW5lJywNCiAgICAgICAgICAgIGRhdGE6IFs5MCwgODIsIDcxLCA4NCwgNjAsIDE5MCwgMTkwXQ0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogJ+i0n+mdoicsDQogICAgICAgICAgICB0eXBlOiAnbGluZScsDQogICAgICAgICAgICBkYXRhOiBbMTAsIDE4LCA5LCAxNiwgMTAsIDEwLCAxMF0NCiAgICAgICAgICB9DQogICAgICAgIF0NCiAgICAgIH0NCg0KICAgICAgdGhpcy5jaGFydHMudHJlbmRDaGFydC5zZXRPcHRpb24ob3B0aW9uKQ0KICAgIH0NCiAgfSwNCiAgYmVmb3JlRGVzdHJveSgpIHsNCiAgICAvLyDplIDmr4Hlm77ooajlrp7kvosNCiAgICBPYmplY3QudmFsdWVzKHRoaXMuY2hhcnRzKS5mb3JFYWNoKGNoYXJ0ID0+IHsNCiAgICAgIGNoYXJ0ICYmIGNoYXJ0LmRpc3Bvc2UoKQ0KICAgIH0pDQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2MA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/event-analysis", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"page-header\">\r\n      <h2>事件分析</h2>\r\n      <div class=\"header-actions\">\r\n        <el-button type=\"primary\" size=\"small\">新建分析</el-button>\r\n        <el-button size=\"small\">导出分析</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 创建事件分析对话框 -->\r\n    <el-dialog title=\"新建事件\" :visible.sync=\"dialogVisible\" width=\"600px\">\r\n      <div class=\"dialog-content\">\r\n        <el-form :model=\"eventForm\" label-width=\"80px\">\r\n          <el-form-item label=\"事件名称:\">\r\n            <el-input v-model=\"eventForm.name\" placeholder=\"请输入事件名称\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"时间范围:\">\r\n            <el-date-picker\r\n              v-model=\"eventForm.timeRange\"\r\n              type=\"daterange\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              style=\"width: 100%;\"\r\n            ></el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"关键词:\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              v-model=\"eventForm.keywords\"\r\n              placeholder=\"多个关键词用逗号分隔\"\r\n              :rows=\"3\"\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"分析词:\">\r\n            <div class=\"analysis-word-container\">\r\n              <div class=\"word-input-section\">\r\n                <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-plus\">+</el-button>\r\n                <el-button size=\"mini\">|</el-button>\r\n                <el-button size=\"mini\">||</el-button>\r\n                <el-button size=\"mini\">()</el-button>\r\n                <el-button type=\"primary\" size=\"mini\">获取分词</el-button>\r\n                <i class=\"el-icon-question analysis-help-icon\"></i>\r\n              </div>\r\n              <el-input\r\n                type=\"textarea\"\r\n                v-model=\"eventForm.analysisWords\"\r\n                placeholder=\"请输入分析词\"\r\n                :rows=\"3\"\r\n                class=\"analysis-textarea\"\r\n              ></el-input>\r\n            </div>\r\n          </el-form-item>\r\n          <el-form-item label=\"排除词:\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              v-model=\"eventForm.excludeWords\"\r\n              placeholder=\"多个排除词之间以\"，\"隔开，词汇、词组之间以\"与\"关系\"\r\n              :rows=\"3\"\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"监控内容:\">\r\n            <el-checkbox-group v-model=\"eventForm.monitorTypes\">\r\n              <el-checkbox label=\"web\">网页</el-checkbox>\r\n              <el-checkbox label=\"weibo\">微博</el-checkbox>\r\n              <el-checkbox label=\"toutiao\">头条号</el-checkbox>\r\n              <el-checkbox label=\"app\">APP</el-checkbox>\r\n              <el-checkbox label=\"video\">视频</el-checkbox>\r\n              <el-checkbox label=\"sms\">短信</el-checkbox>\r\n              <el-checkbox label=\"newspaper\">报刊</el-checkbox>\r\n            </el-checkbox-group>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitEventForm\">确定</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <div class=\"main-content\">\r\n      <!-- 左侧导航 -->\r\n      <div class=\"left-nav\">\r\n        <div class=\"nav-header\">\r\n          <span>事件分析</span>\r\n          <i class=\"el-icon-plus\" @click=\"dialogVisible = true\"></i>\r\n        </div>\r\n        <el-input\r\n          placeholder=\"搜索分析项\"\r\n          prefix-icon=\"el-icon-search\"\r\n          v-model=\"searchQuery\"\r\n          clearable\r\n          size=\"small\"\r\n          class=\"search-input\"\r\n        ></el-input>\r\n        <div class=\"nav-list\">\r\n          <div\r\n            v-for=\"(item, index) in filteredAnalysisList\"\r\n            :key=\"index\"\r\n            class=\"nav-item\"\r\n            :class=\"{ 'active': currentAnalysis === item }\"\r\n            @click=\"selectAnalysis(item)\"\r\n          >\r\n            <span>{{ item.name }}</span>\r\n            <span class=\"item-date\">{{ item.date }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧内容 -->\r\n      <div class=\"right-content\">\r\n        <div v-if=\"currentAnalysis\" class=\"analysis-detail\">\r\n          <div class=\"detail-header\">\r\n            <h3>{{ currentAnalysis.name }}</h3>\r\n            <div class=\"detail-actions\">\r\n              <el-button size=\"mini\" type=\"primary\" icon=\"el-icon-refresh\">刷新</el-button>\r\n              <el-button size=\"mini\" icon=\"el-icon-download\">导出</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 筛选条件 -->\r\n          <div class=\"filter-section\">\r\n            <el-form :inline=\"true\" size=\"small\">\r\n              <el-form-item label=\"时间范围:\">\r\n                <el-radio-group v-model=\"timeRange\">\r\n                  <el-radio-button label=\"today\">今天</el-radio-button>\r\n                  <el-radio-button label=\"yesterday\">昨天</el-radio-button>\r\n                  <el-radio-button label=\"7d\">近七天</el-radio-button>\r\n                  <el-radio-button label=\"30d\">近30天</el-radio-button>\r\n                  <el-radio-button label=\"custom\">自定义</el-radio-button>\r\n                </el-radio-group>\r\n                <el-date-picker\r\n                  v-if=\"timeRange === 'custom'\"\r\n                  v-model=\"customTimeRange\"\r\n                  type=\"daterange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  style=\"margin-left: 10px; width: 240px;\"\r\n                ></el-date-picker>\r\n              </el-form-item>\r\n            </el-form>\r\n          </div>\r\n\r\n          <!-- 数据概览 -->\r\n          <el-row :gutter=\"20\" class=\"data-overview\">\r\n            <el-col :span=\"6\">\r\n              <el-card shadow=\"hover\">\r\n                <div class=\"overview-item\">\r\n                  <div class=\"item-label\">信息总量</div>\r\n                  <div class=\"item-value\">{{ currentAnalysis.totalInfo }}</div>\r\n                </div>\r\n              </el-card>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-card shadow=\"hover\">\r\n                <div class=\"overview-item\">\r\n                  <div class=\"item-label\">正面声量</div>\r\n                  <div class=\"item-value positive\">{{ currentAnalysis.positiveCount }}</div>\r\n                </div>\r\n              </el-card>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-card shadow=\"hover\">\r\n                <div class=\"overview-item\">\r\n                  <div class=\"item-label\">中性声量</div>\r\n                  <div class=\"item-value neutral\">{{ currentAnalysis.neutralCount }}</div>\r\n                </div>\r\n              </el-card>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-card shadow=\"hover\">\r\n                <div class=\"overview-item\">\r\n                  <div class=\"item-label\">负面声量</div>\r\n                  <div class=\"item-value negative\">{{ currentAnalysis.negativeCount }}</div>\r\n                </div>\r\n              </el-card>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <!-- 图表区域 -->\r\n          <div class=\"charts-container\">\r\n            <el-card class=\"chart-card\">\r\n              <div slot=\"header\">\r\n                <span>事件趋势分析</span>\r\n              </div>\r\n              <div class=\"chart\" ref=\"trendChart\"></div>\r\n            </el-card>\r\n          </div>\r\n        </div>\r\n        <div v-else class=\"empty-state\">\r\n          <i class=\"el-icon-data-analysis\"></i>\r\n          <p>请选择或创建一个事件分析项目</p>\r\n          <el-button type=\"primary\" @click=\"dialogVisible = true\">创建分析</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: 'EventAnalysis',\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      searchQuery: '',\r\n      currentAnalysis: null,\r\n      timeRange: 'today',\r\n      customTimeRange: [],\r\n      eventForm: {\r\n        name: '',\r\n        timeRange: [],\r\n        keywords: '',\r\n        analysisWords: '',\r\n        excludeWords: '',\r\n        monitorTypes: ['web', 'weibo']\r\n      },\r\n      analysisList: [\r\n        {\r\n          name: '方太品牌事件分析',\r\n          date: '2023-04-22',\r\n          totalInfo: '4,653',\r\n          positiveCount: '58',\r\n          neutralCount: '4,583',\r\n          negativeCount: '12'\r\n        },\r\n        {\r\n          name: '竞品事件分析',\r\n          date: '2023-04-20',\r\n          totalInfo: '3,245',\r\n          positiveCount: '42',\r\n          neutralCount: '3,180',\r\n          negativeCount: '23'\r\n        }\r\n      ],\r\n      charts: {\r\n        trendChart: null\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    filteredAnalysisList() {\r\n      if (!this.searchQuery) return this.analysisList\r\n\r\n      const query = this.searchQuery.toLowerCase()\r\n      return this.analysisList.filter(item =>\r\n        item.name.toLowerCase().includes(query)\r\n      )\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initCharts()\r\n  },\r\n  methods: {\r\n    selectAnalysis(analysis) {\r\n      this.currentAnalysis = analysis\r\n      this.$nextTick(() => {\r\n        this.renderCharts()\r\n      })\r\n    },\r\n    submitEventForm() {\r\n      // 处理表单提交\r\n      const newAnalysis = {\r\n        name: this.eventForm.name,\r\n        date: new Date().toISOString().split('T')[0],\r\n        totalInfo: '0',\r\n        positiveCount: '0',\r\n        neutralCount: '0',\r\n        negativeCount: '0'\r\n      }\r\n\r\n      this.analysisList.unshift(newAnalysis)\r\n      this.selectAnalysis(newAnalysis)\r\n      this.dialogVisible = false\r\n\r\n      // 重置表单\r\n      this.eventForm = {\r\n        name: '',\r\n        timeRange: [],\r\n        keywords: '',\r\n        analysisWords: '',\r\n        excludeWords: '',\r\n        monitorTypes: ['web', 'weibo']\r\n      }\r\n    },\r\n    initCharts() {\r\n      if (this.currentAnalysis) {\r\n        this.$nextTick(() => {\r\n          if (this.$refs.trendChart) {\r\n            this.charts.trendChart = echarts.init(this.$refs.trendChart)\r\n            this.renderTrendChart()\r\n          }\r\n        })\r\n      }\r\n    },\r\n    renderCharts() {\r\n      if (this.$refs.trendChart) {\r\n        if (!this.charts.trendChart) {\r\n          this.charts.trendChart = echarts.init(this.$refs.trendChart)\r\n        }\r\n        this.renderTrendChart()\r\n      }\r\n    },\r\n    renderTrendChart() {\r\n      if (!this.charts.trendChart) return\r\n\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis'\r\n        },\r\n        legend: {\r\n          data: ['总量', '正面', '中性', '负面']\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']\r\n        },\r\n        yAxis: {\r\n          type: 'value'\r\n        },\r\n        series: [\r\n          {\r\n            name: '总量',\r\n            type: 'line',\r\n            data: [120, 132, 101, 134, 90, 230, 210]\r\n          },\r\n          {\r\n            name: '正面',\r\n            type: 'line',\r\n            data: [20, 32, 21, 34, 20, 30, 10]\r\n          },\r\n          {\r\n            name: '中性',\r\n            type: 'line',\r\n            data: [90, 82, 71, 84, 60, 190, 190]\r\n          },\r\n          {\r\n            name: '负面',\r\n            type: 'line',\r\n            data: [10, 18, 9, 16, 10, 10, 10]\r\n          }\r\n        ]\r\n      }\r\n\r\n      this.charts.trendChart.setOption(option)\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    // 销毁图表实例\r\n    Object.values(this.charts).forEach(chart => {\r\n      chart && chart.dispose()\r\n    })\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  padding: 20px;\r\n  background-color: #f0f2f5;\r\n  min-height: calc(100vh - 84px);\r\n}\r\n\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n\r\n  h2 {\r\n    margin: 0;\r\n    font-size: 20px;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n.main-content {\r\n  display: flex;\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\r\n  min-height: calc(100vh - 150px);\r\n}\r\n\r\n.left-nav {\r\n  width: 250px;\r\n  border-right: 1px solid #e6e6e6;\r\n\r\n  .nav-header {\r\n    padding: 15px;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    border-bottom: 1px solid #e6e6e6;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n\r\n    i {\r\n      cursor: pointer;\r\n      color: #409EFF;\r\n\r\n      &:hover {\r\n        color: #66b1ff;\r\n      }\r\n    }\r\n  }\r\n\r\n  .search-input {\r\n    padding: 10px;\r\n  }\r\n\r\n  .nav-list {\r\n    .nav-item {\r\n      padding: 12px 15px;\r\n      cursor: pointer;\r\n      border-bottom: 1px solid #f0f0f0;\r\n      display: flex;\r\n      flex-direction: column;\r\n\r\n      &:hover {\r\n        background-color: #f5f7fa;\r\n      }\r\n\r\n      &.active {\r\n        background-color: #ecf5ff;\r\n        color: #409EFF;\r\n        border-right: 3px solid #409EFF;\r\n      }\r\n\r\n      .item-date {\r\n        font-size: 12px;\r\n        color: #909399;\r\n        margin-top: 5px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.right-content {\r\n  flex: 1;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n\r\n  .detail-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 20px;\r\n\r\n    h3 {\r\n      margin: 0;\r\n      font-size: 18px;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n\r\n  .filter-section {\r\n    margin-bottom: 20px;\r\n    padding: 15px;\r\n    background-color: #f5f7fa;\r\n    border-radius: 4px;\r\n  }\r\n\r\n  .data-overview {\r\n    margin-bottom: 20px;\r\n\r\n    .overview-item {\r\n      text-align: center;\r\n\r\n      .item-label {\r\n        font-size: 14px;\r\n        color: #606266;\r\n        margin-bottom: 5px;\r\n      }\r\n\r\n      .item-value {\r\n        font-size: 24px;\r\n        font-weight: bold;\r\n        color: #303133;\r\n\r\n        &.positive {\r\n          color: #67C23A;\r\n        }\r\n\r\n        &.neutral {\r\n          color: #E6A23C;\r\n        }\r\n\r\n        &.negative {\r\n          color: #F56C6C;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .charts-container {\r\n    .chart-card {\r\n      margin-bottom: 20px;\r\n\r\n      .chart {\r\n        height: 300px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .empty-state {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 100%;\r\n    color: #909399;\r\n\r\n    i {\r\n      font-size: 48px;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    p {\r\n      margin-bottom: 20px;\r\n    }\r\n  }\r\n}\r\n\r\n.dialog-content {\r\n  max-height: 500px;\r\n  overflow-y: auto;\r\n}\r\n\r\n/* 分析词容器样式 */\r\n.analysis-word-container {\r\n  .word-input-section {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 10px;\r\n    gap: 8px;\r\n\r\n    .el-button {\r\n      padding: 5px 10px;\r\n      font-size: 12px;\r\n    }\r\n\r\n    .analysis-help-icon {\r\n      color: #909399;\r\n      cursor: pointer;\r\n      font-size: 16px;\r\n\r\n      &:hover {\r\n        color: #409EFF;\r\n      }\r\n    }\r\n  }\r\n\r\n  .analysis-textarea {\r\n    .el-textarea__inner {\r\n      border-radius: 4px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}